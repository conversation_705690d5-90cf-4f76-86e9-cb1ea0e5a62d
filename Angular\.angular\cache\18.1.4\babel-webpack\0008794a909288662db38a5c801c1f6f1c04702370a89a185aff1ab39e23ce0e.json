{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.buffer = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar noop_1 = require(\"../util/noop\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction buffer(closingNotifier) {\n  return lift_1.operate(function (source, subscriber) {\n    var currentBuffer = [];\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      return currentBuffer.push(value);\n    }, function () {\n      subscriber.next(currentBuffer);\n      subscriber.complete();\n    }));\n    innerFrom_1.innerFrom(closingNotifier).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n      var b = currentBuffer;\n      currentBuffer = [];\n      subscriber.next(b);\n    }, noop_1.noop));\n    return function () {\n      currentBuffer = null;\n    };\n  });\n}\nexports.buffer = buffer;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "buffer", "lift_1", "require", "noop_1", "OperatorSubscriber_1", "innerFrom_1", "closingNotifier", "operate", "source", "subscriber", "current<PERSON><PERSON><PERSON>", "subscribe", "createOperatorSubscriber", "push", "next", "complete", "innerFrom", "b", "noop"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/buffer.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.buffer = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar noop_1 = require(\"../util/noop\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction buffer(closingNotifier) {\n    return lift_1.operate(function (source, subscriber) {\n        var currentBuffer = [];\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) { return currentBuffer.push(value); }, function () {\n            subscriber.next(currentBuffer);\n            subscriber.complete();\n        }));\n        innerFrom_1.innerFrom(closingNotifier).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n            var b = currentBuffer;\n            currentBuffer = [];\n            subscriber.next(b);\n        }, noop_1.noop));\n        return function () {\n            currentBuffer = null;\n        };\n    });\n}\nexports.buffer = buffer;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,MAAM,GAAG,KAAK,CAAC;AACvB,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIG,WAAW,GAAGH,OAAO,CAAC,yBAAyB,CAAC;AACpD,SAASF,MAAMA,CAACM,eAAe,EAAE;EAC7B,OAAOL,MAAM,CAACM,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,aAAa,GAAG,EAAE;IACtBF,MAAM,CAACG,SAAS,CAACP,oBAAoB,CAACQ,wBAAwB,CAACH,UAAU,EAAE,UAAUV,KAAK,EAAE;MAAE,OAAOW,aAAa,CAACG,IAAI,CAACd,KAAK,CAAC;IAAE,CAAC,EAAE,YAAY;MAC3IU,UAAU,CAACK,IAAI,CAACJ,aAAa,CAAC;MAC9BD,UAAU,CAACM,QAAQ,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;IACHV,WAAW,CAACW,SAAS,CAACV,eAAe,CAAC,CAACK,SAAS,CAACP,oBAAoB,CAACQ,wBAAwB,CAACH,UAAU,EAAE,YAAY;MACnH,IAAIQ,CAAC,GAAGP,aAAa;MACrBA,aAAa,GAAG,EAAE;MAClBD,UAAU,CAACK,IAAI,CAACG,CAAC,CAAC;IACtB,CAAC,EAAEd,MAAM,CAACe,IAAI,CAAC,CAAC;IAChB,OAAO,YAAY;MACfR,aAAa,GAAG,IAAI;IACxB,CAAC;EACL,CAAC,CAAC;AACN;AACAZ,OAAO,CAACE,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}