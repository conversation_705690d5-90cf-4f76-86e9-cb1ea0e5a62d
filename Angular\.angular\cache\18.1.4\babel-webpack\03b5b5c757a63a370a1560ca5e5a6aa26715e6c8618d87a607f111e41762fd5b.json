{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.filter = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction filter(predicate, thisArg) {\n  return lift_1.operate(function (source, subscriber) {\n    var index = 0;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      return predicate.call(thisArg, value, index++) && subscriber.next(value);\n    }));\n  });\n}\nexports.filter = filter;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "filter", "lift_1", "require", "OperatorSubscriber_1", "predicate", "thisArg", "operate", "source", "subscriber", "index", "subscribe", "createOperatorSubscriber", "call", "next"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/filter.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.filter = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction filter(predicate, thisArg) {\n    return lift_1.operate(function (source, subscriber) {\n        var index = 0;\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) { return predicate.call(thisArg, value, index++) && subscriber.next(value); }));\n    });\n}\nexports.filter = filter;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,MAAM,GAAG,KAAK,CAAC;AACvB,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,MAAMA,CAACI,SAAS,EAAEC,OAAO,EAAE;EAChC,OAAOJ,MAAM,CAACK,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,KAAK,GAAG,CAAC;IACbF,MAAM,CAACG,SAAS,CAACP,oBAAoB,CAACQ,wBAAwB,CAACH,UAAU,EAAE,UAAUT,KAAK,EAAE;MAAE,OAAOK,SAAS,CAACQ,IAAI,CAACP,OAAO,EAAEN,KAAK,EAAEU,KAAK,EAAE,CAAC,IAAID,UAAU,CAACK,IAAI,CAACd,KAAK,CAAC;IAAE,CAAC,CAAC,CAAC;EAC/K,CAAC,CAAC;AACN;AACAD,OAAO,CAACE,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}