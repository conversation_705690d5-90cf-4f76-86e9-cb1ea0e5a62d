{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.subscribeOn = void 0;\nvar lift_1 = require(\"../util/lift\");\nfunction subscribeOn(scheduler, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  return lift_1.operate(function (source, subscriber) {\n    subscriber.add(scheduler.schedule(function () {\n      return source.subscribe(subscriber);\n    }, delay));\n  });\n}\nexports.subscribeOn = subscribeOn;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "subscribeOn", "lift_1", "require", "scheduler", "delay", "operate", "source", "subscriber", "add", "schedule", "subscribe"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/subscribeOn.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.subscribeOn = void 0;\nvar lift_1 = require(\"../util/lift\");\nfunction subscribeOn(scheduler, delay) {\n    if (delay === void 0) { delay = 0; }\n    return lift_1.operate(function (source, subscriber) {\n        subscriber.add(scheduler.schedule(function () { return source.subscribe(subscriber); }, delay));\n    });\n}\nexports.subscribeOn = subscribeOn;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,WAAW,GAAG,KAAK,CAAC;AAC5B,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,SAASF,WAAWA,CAACG,SAAS,EAAEC,KAAK,EAAE;EACnC,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IAAEA,KAAK,GAAG,CAAC;EAAE;EACnC,OAAOH,MAAM,CAACI,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChDA,UAAU,CAACC,GAAG,CAACL,SAAS,CAACM,QAAQ,CAAC,YAAY;MAAE,OAAOH,MAAM,CAACI,SAAS,CAACH,UAAU,CAAC;IAAE,CAAC,EAAEH,KAAK,CAAC,CAAC;EACnG,CAAC,CAAC;AACN;AACAN,OAAO,CAACE,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}