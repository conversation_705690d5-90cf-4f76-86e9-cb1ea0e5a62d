{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.popNumber = exports.popScheduler = exports.popResultSelector = void 0;\nvar isFunction_1 = require(\"./isFunction\");\nvar isScheduler_1 = require(\"./isScheduler\");\nfunction last(arr) {\n  return arr[arr.length - 1];\n}\nfunction popResultSelector(args) {\n  return isFunction_1.isFunction(last(args)) ? args.pop() : undefined;\n}\nexports.popResultSelector = popResultSelector;\nfunction popScheduler(args) {\n  return isScheduler_1.isScheduler(last(args)) ? args.pop() : undefined;\n}\nexports.popScheduler = popScheduler;\nfunction popNumber(args, defaultValue) {\n  return typeof last(args) === 'number' ? args.pop() : defaultValue;\n}\nexports.popNumber = popNumber;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "popNumber", "popScheduler", "popResultSelector", "isFunction_1", "require", "isScheduler_1", "last", "arr", "length", "args", "isFunction", "pop", "undefined", "isScheduler", "defaultValue"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/args.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.popNumber = exports.popScheduler = exports.popResultSelector = void 0;\nvar isFunction_1 = require(\"./isFunction\");\nvar isScheduler_1 = require(\"./isScheduler\");\nfunction last(arr) {\n    return arr[arr.length - 1];\n}\nfunction popResultSelector(args) {\n    return isFunction_1.isFunction(last(args)) ? args.pop() : undefined;\n}\nexports.popResultSelector = popResultSelector;\nfunction popScheduler(args) {\n    return isScheduler_1.isScheduler(last(args)) ? args.pop() : undefined;\n}\nexports.popScheduler = popScheduler;\nfunction popNumber(args, defaultValue) {\n    return typeof last(args) === 'number' ? args.pop() : defaultValue;\n}\nexports.popNumber = popNumber;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,iBAAiB,GAAG,KAAK,CAAC;AAC7E,IAAIC,YAAY,GAAGC,OAAO,CAAC,cAAc,CAAC;AAC1C,IAAIC,aAAa,GAAGD,OAAO,CAAC,eAAe,CAAC;AAC5C,SAASE,IAAIA,CAACC,GAAG,EAAE;EACf,OAAOA,GAAG,CAACA,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;AAC9B;AACA,SAASN,iBAAiBA,CAACO,IAAI,EAAE;EAC7B,OAAON,YAAY,CAACO,UAAU,CAACJ,IAAI,CAACG,IAAI,CAAC,CAAC,GAAGA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAGC,SAAS;AACvE;AACAd,OAAO,CAACI,iBAAiB,GAAGA,iBAAiB;AAC7C,SAASD,YAAYA,CAACQ,IAAI,EAAE;EACxB,OAAOJ,aAAa,CAACQ,WAAW,CAACP,IAAI,CAACG,IAAI,CAAC,CAAC,GAAGA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAGC,SAAS;AACzE;AACAd,OAAO,CAACG,YAAY,GAAGA,YAAY;AACnC,SAASD,SAASA,CAACS,IAAI,EAAEK,YAAY,EAAE;EACnC,OAAO,OAAOR,IAAI,CAACG,IAAI,CAAC,KAAK,QAAQ,GAAGA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAGG,YAAY;AACrE;AACAhB,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}