{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NgbModule } from \"@ng-bootstrap/ng-bootstrap\";\nimport { InlineSVGModule } from 'ng-inline-svg-2';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ShowImageComponent } from './show-image/show-image.component';\nimport { CheckboxGroupComponent } from './checkbox-group/checkbox-group.component';\nimport { CheckboxComponent } from './checkbox-group/checkbox.component';\nimport { TruncateTextPipe } from './truncate-text/truncate-text.pipe';\nimport { ConfirmationDialogComponent } from './confirmation-dialog/confirmation-dialog.component';\nimport * as i0 from \"@angular/core\";\nexport class SharedModule {\n  static ɵfac = function SharedModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SharedModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SharedModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, InlineSVGModule, NgbModule, FormsModule, ReactiveFormsModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    declarations: [CheckboxComponent, CheckboxGroupComponent, ShowImageComponent, TruncateTextPipe, ConfirmationDialogComponent],\n    imports: [CommonModule, InlineSVGModule, NgbModule, FormsModule, ReactiveFormsModule],\n    exports: [CheckboxComponent, CheckboxGroupComponent, TruncateTextPipe]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "NgbModule", "InlineSVGModule", "FormsModule", "ReactiveFormsModule", "ShowImageComponent", "CheckboxGroupComponent", "CheckboxComponent", "TruncateTextPipe", "ConfirmationDialogComponent", "SharedModule", "declarations", "imports", "exports"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NgbModule } from \"@ng-bootstrap/ng-bootstrap\";\r\nimport { InlineSVGModule } from 'ng-inline-svg-2';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { ShowImageComponent } from './show-image/show-image.component';\r\nimport { CheckboxGroupComponent } from './checkbox-group/checkbox-group.component';\r\nimport { CheckboxComponent } from './checkbox-group/checkbox.component';\r\nimport { TruncateTextPipe } from './truncate-text/truncate-text.pipe';\r\nimport { ConfirmationDialogComponent } from './confirmation-dialog/confirmation-dialog.component';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    CheckboxComponent,\r\n    CheckboxGroupComponent,\r\n    ShowImageComponent,\r\n    TruncateTextPipe,\r\n    ConfirmationDialogComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    InlineSVGModule,\r\n    NgbModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n  ],\r\n  exports:[\r\n     CheckboxComponent,\r\n    CheckboxGroupComponent,\r\n    TruncateTextPipe\r\n  ]\r\n})\r\nexport class SharedModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,2BAA2B,QAAQ,qDAAqD;;AAwBjG,OAAM,MAAOC,YAAY;;qCAAZA,YAAY;EAAA;;UAAZA;EAAY;;cAZrBV,YAAY,EACZE,eAAe,EACfD,SAAS,EACTE,WAAW,EACXC,mBAAmB;EAAA;;;2EAQVM,YAAY;IAAAC,YAAA,GAnBrBJ,iBAAiB,EACjBD,sBAAsB,EACtBD,kBAAkB,EAClBG,gBAAgB,EAChBC,2BAA2B;IAAAG,OAAA,GAG3BZ,YAAY,EACZE,eAAe,EACfD,SAAS,EACTE,WAAW,EACXC,mBAAmB;IAAAS,OAAA,GAGlBN,iBAAiB,EAClBD,sBAAsB,EACtBE,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}