{"ast": null, "code": "\"use strict\";\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from) {\n  for (var i = 0, il = from.length, j = to.length; i < il; i++, j++) to[j] = from[i];\n  return to;\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.combineLatest = void 0;\nvar combineLatest_1 = require(\"../observable/combineLatest\");\nvar lift_1 = require(\"../util/lift\");\nvar argsOrArgArray_1 = require(\"../util/argsOrArgArray\");\nvar mapOneOrManyArgs_1 = require(\"../util/mapOneOrManyArgs\");\nvar pipe_1 = require(\"../util/pipe\");\nvar args_1 = require(\"../util/args\");\nfunction combineLatest() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var resultSelector = args_1.popResultSelector(args);\n  return resultSelector ? pipe_1.pipe(combineLatest.apply(void 0, __spreadArray([], __read(args))), mapOneOrManyArgs_1.mapOneOrManyArgs(resultSelector)) : lift_1.operate(function (source, subscriber) {\n    combineLatest_1.combineLatestInit(__spreadArray([source], __read(argsOrArgArray_1.argsOrArgArray(args))))(subscriber);\n  });\n}\nexports.combineLatest = combineLatest;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "il", "length", "j", "Object", "defineProperty", "exports", "combineLatest", "combineLatest_1", "require", "lift_1", "argsOrArgArray_1", "mapOneOrManyArgs_1", "pipe_1", "args_1", "args", "_i", "arguments", "resultSelector", "popResultSelector", "pipe", "apply", "mapOneOrManyArgs", "operate", "source", "subscriber", "combineLatestInit", "argsOrArgArray"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/combineLatest.js"], "sourcesContent": ["\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from) {\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\n        to[j] = from[i];\n    return to;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.combineLatest = void 0;\nvar combineLatest_1 = require(\"../observable/combineLatest\");\nvar lift_1 = require(\"../util/lift\");\nvar argsOrArgArray_1 = require(\"../util/argsOrArgArray\");\nvar mapOneOrManyArgs_1 = require(\"../util/mapOneOrManyArgs\");\nvar pipe_1 = require(\"../util/pipe\");\nvar args_1 = require(\"../util/args\");\nfunction combineLatest() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var resultSelector = args_1.popResultSelector(args);\n    return resultSelector\n        ? pipe_1.pipe(combineLatest.apply(void 0, __spreadArray([], __read(args))), mapOneOrManyArgs_1.mapOneOrManyArgs(resultSelector))\n        : lift_1.operate(function (source, subscriber) {\n            combineLatest_1.combineLatestInit(__spreadArray([source], __read(argsOrArgArray_1.argsOrArgArray(args))))(subscriber);\n        });\n}\nexports.combineLatest = combineLatest;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,MAAM,GAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAK,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC;EAC1D,IAAI,CAACF,CAAC,EAAE,OAAOF,CAAC;EAChB,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAI,CAACN,CAAC,CAAC;IAAEO,CAAC;IAAEC,EAAE,GAAG,EAAE;IAAEC,CAAC;EAChC,IAAI;IACA,OAAO,CAACR,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAEH,EAAE,CAACI,IAAI,CAACL,CAAC,CAACM,KAAK,CAAC;EAC9E,CAAC,CACD,OAAOC,KAAK,EAAE;IAAEL,CAAC,GAAG;MAAEK,KAAK,EAAEA;IAAM,CAAC;EAAE,CAAC,SAC/B;IACJ,IAAI;MACA,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAI,KAAKT,CAAC,GAAGG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAEH,CAAC,CAACI,IAAI,CAACD,CAAC,CAAC;IACpD,CAAC,SACO;MAAE,IAAII,CAAC,EAAE,MAAMA,CAAC,CAACK,KAAK;IAAE;EACpC;EACA,OAAON,EAAE;AACb,CAAC;AACD,IAAIO,aAAa,GAAI,IAAI,IAAI,IAAI,CAACA,aAAa,IAAK,UAAUC,EAAE,EAAEC,IAAI,EAAE;EACpE,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEa,EAAE,GAAGD,IAAI,CAACE,MAAM,EAAEC,CAAC,GAAGJ,EAAE,CAACG,MAAM,EAAEd,CAAC,GAAGa,EAAE,EAAEb,CAAC,EAAE,EAAEe,CAAC,EAAE,EAC7DJ,EAAE,CAACI,CAAC,CAAC,GAAGH,IAAI,CAACZ,CAAC,CAAC;EACnB,OAAOW,EAAE;AACb,CAAC;AACDK,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEV,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DU,OAAO,CAACC,aAAa,GAAG,KAAK,CAAC;AAC9B,IAAIC,eAAe,GAAGC,OAAO,CAAC,6BAA6B,CAAC;AAC5D,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,gBAAgB,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AACxD,IAAIG,kBAAkB,GAAGH,OAAO,CAAC,0BAA0B,CAAC;AAC5D,IAAII,MAAM,GAAGJ,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIK,MAAM,GAAGL,OAAO,CAAC,cAAc,CAAC;AACpC,SAASF,aAAaA,CAAA,EAAG;EACrB,IAAIQ,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACf,MAAM,EAAEc,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC5B;EACA,IAAIE,cAAc,GAAGJ,MAAM,CAACK,iBAAiB,CAACJ,IAAI,CAAC;EACnD,OAAOG,cAAc,GACfL,MAAM,CAACO,IAAI,CAACb,aAAa,CAACc,KAAK,CAAC,KAAK,CAAC,EAAEvB,aAAa,CAAC,EAAE,EAAEhB,MAAM,CAACiC,IAAI,CAAC,CAAC,CAAC,EAAEH,kBAAkB,CAACU,gBAAgB,CAACJ,cAAc,CAAC,CAAC,GAC9HR,MAAM,CAACa,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAC3CjB,eAAe,CAACkB,iBAAiB,CAAC5B,aAAa,CAAC,CAAC0B,MAAM,CAAC,EAAE1C,MAAM,CAAC6B,gBAAgB,CAACgB,cAAc,CAACZ,IAAI,CAAC,CAAC,CAAC,CAAC,CAACU,UAAU,CAAC;EACzH,CAAC,CAAC;AACV;AACAnB,OAAO,CAACC,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}