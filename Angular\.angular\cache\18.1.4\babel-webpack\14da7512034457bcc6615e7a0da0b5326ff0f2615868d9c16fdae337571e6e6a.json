{"ast": null, "code": "\"use strict\";\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from) {\n  for (var i = 0, il = from.length, j = to.length; i < il; i++, j++) to[j] = from[i];\n  return to;\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.zip = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar innerFrom_1 = require(\"./innerFrom\");\nvar argsOrArgArray_1 = require(\"../util/argsOrArgArray\");\nvar empty_1 = require(\"./empty\");\nvar OperatorSubscriber_1 = require(\"../operators/OperatorSubscriber\");\nvar args_1 = require(\"../util/args\");\nfunction zip() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var resultSelector = args_1.popResultSelector(args);\n  var sources = argsOrArgArray_1.argsOrArgArray(args);\n  return sources.length ? new Observable_1.Observable(function (subscriber) {\n    var buffers = sources.map(function () {\n      return [];\n    });\n    var completed = sources.map(function () {\n      return false;\n    });\n    subscriber.add(function () {\n      buffers = completed = null;\n    });\n    var _loop_1 = function (sourceIndex) {\n      innerFrom_1.innerFrom(sources[sourceIndex]).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n        buffers[sourceIndex].push(value);\n        if (buffers.every(function (buffer) {\n          return buffer.length;\n        })) {\n          var result = buffers.map(function (buffer) {\n            return buffer.shift();\n          });\n          subscriber.next(resultSelector ? resultSelector.apply(void 0, __spreadArray([], __read(result))) : result);\n          if (buffers.some(function (buffer, i) {\n            return !buffer.length && completed[i];\n          })) {\n            subscriber.complete();\n          }\n        }\n      }, function () {\n        completed[sourceIndex] = true;\n        !buffers[sourceIndex].length && subscriber.complete();\n      }));\n    };\n    for (var sourceIndex = 0; !subscriber.closed && sourceIndex < sources.length; sourceIndex++) {\n      _loop_1(sourceIndex);\n    }\n    return function () {\n      buffers = completed = null;\n    };\n  }) : empty_1.EMPTY;\n}\nexports.zip = zip;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "il", "length", "j", "Object", "defineProperty", "exports", "zip", "Observable_1", "require", "innerFrom_1", "argsOrArgArray_1", "empty_1", "OperatorSubscriber_1", "args_1", "args", "_i", "arguments", "resultSelector", "popResultSelector", "sources", "argsOrArgArray", "Observable", "subscriber", "buffers", "map", "completed", "add", "_loop_1", "sourceIndex", "innerFrom", "subscribe", "createOperatorSubscriber", "every", "buffer", "result", "shift", "apply", "some", "complete", "closed", "EMPTY"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/zip.js"], "sourcesContent": ["\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from) {\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\n        to[j] = from[i];\n    return to;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.zip = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar innerFrom_1 = require(\"./innerFrom\");\nvar argsOrArgArray_1 = require(\"../util/argsOrArgArray\");\nvar empty_1 = require(\"./empty\");\nvar OperatorSubscriber_1 = require(\"../operators/OperatorSubscriber\");\nvar args_1 = require(\"../util/args\");\nfunction zip() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var resultSelector = args_1.popResultSelector(args);\n    var sources = argsOrArgArray_1.argsOrArgArray(args);\n    return sources.length\n        ? new Observable_1.Observable(function (subscriber) {\n            var buffers = sources.map(function () { return []; });\n            var completed = sources.map(function () { return false; });\n            subscriber.add(function () {\n                buffers = completed = null;\n            });\n            var _loop_1 = function (sourceIndex) {\n                innerFrom_1.innerFrom(sources[sourceIndex]).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n                    buffers[sourceIndex].push(value);\n                    if (buffers.every(function (buffer) { return buffer.length; })) {\n                        var result = buffers.map(function (buffer) { return buffer.shift(); });\n                        subscriber.next(resultSelector ? resultSelector.apply(void 0, __spreadArray([], __read(result))) : result);\n                        if (buffers.some(function (buffer, i) { return !buffer.length && completed[i]; })) {\n                            subscriber.complete();\n                        }\n                    }\n                }, function () {\n                    completed[sourceIndex] = true;\n                    !buffers[sourceIndex].length && subscriber.complete();\n                }));\n            };\n            for (var sourceIndex = 0; !subscriber.closed && sourceIndex < sources.length; sourceIndex++) {\n                _loop_1(sourceIndex);\n            }\n            return function () {\n                buffers = completed = null;\n            };\n        })\n        : empty_1.EMPTY;\n}\nexports.zip = zip;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,MAAM,GAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAK,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC;EAC1D,IAAI,CAACF,CAAC,EAAE,OAAOF,CAAC;EAChB,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAI,CAACN,CAAC,CAAC;IAAEO,CAAC;IAAEC,EAAE,GAAG,EAAE;IAAEC,CAAC;EAChC,IAAI;IACA,OAAO,CAACR,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAEH,EAAE,CAACI,IAAI,CAACL,CAAC,CAACM,KAAK,CAAC;EAC9E,CAAC,CACD,OAAOC,KAAK,EAAE;IAAEL,CAAC,GAAG;MAAEK,KAAK,EAAEA;IAAM,CAAC;EAAE,CAAC,SAC/B;IACJ,IAAI;MACA,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAI,KAAKT,CAAC,GAAGG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAEH,CAAC,CAACI,IAAI,CAACD,CAAC,CAAC;IACpD,CAAC,SACO;MAAE,IAAII,CAAC,EAAE,MAAMA,CAAC,CAACK,KAAK;IAAE;EACpC;EACA,OAAON,EAAE;AACb,CAAC;AACD,IAAIO,aAAa,GAAI,IAAI,IAAI,IAAI,CAACA,aAAa,IAAK,UAAUC,EAAE,EAAEC,IAAI,EAAE;EACpE,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEa,EAAE,GAAGD,IAAI,CAACE,MAAM,EAAEC,CAAC,GAAGJ,EAAE,CAACG,MAAM,EAAEd,CAAC,GAAGa,EAAE,EAAEb,CAAC,EAAE,EAAEe,CAAC,EAAE,EAC7DJ,EAAE,CAACI,CAAC,CAAC,GAAGH,IAAI,CAACZ,CAAC,CAAC;EACnB,OAAOW,EAAE;AACb,CAAC;AACDK,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEV,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DU,OAAO,CAACC,GAAG,GAAG,KAAK,CAAC;AACpB,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIC,WAAW,GAAGD,OAAO,CAAC,aAAa,CAAC;AACxC,IAAIE,gBAAgB,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AACxD,IAAIG,OAAO,GAAGH,OAAO,CAAC,SAAS,CAAC;AAChC,IAAII,oBAAoB,GAAGJ,OAAO,CAAC,iCAAiC,CAAC;AACrE,IAAIK,MAAM,GAAGL,OAAO,CAAC,cAAc,CAAC;AACpC,SAASF,GAAGA,CAAA,EAAG;EACX,IAAIQ,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACf,MAAM,EAAEc,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC5B;EACA,IAAIE,cAAc,GAAGJ,MAAM,CAACK,iBAAiB,CAACJ,IAAI,CAAC;EACnD,IAAIK,OAAO,GAAGT,gBAAgB,CAACU,cAAc,CAACN,IAAI,CAAC;EACnD,OAAOK,OAAO,CAAClB,MAAM,GACf,IAAIM,YAAY,CAACc,UAAU,CAAC,UAAUC,UAAU,EAAE;IAChD,IAAIC,OAAO,GAAGJ,OAAO,CAACK,GAAG,CAAC,YAAY;MAAE,OAAO,EAAE;IAAE,CAAC,CAAC;IACrD,IAAIC,SAAS,GAAGN,OAAO,CAACK,GAAG,CAAC,YAAY;MAAE,OAAO,KAAK;IAAE,CAAC,CAAC;IAC1DF,UAAU,CAACI,GAAG,CAAC,YAAY;MACvBH,OAAO,GAAGE,SAAS,GAAG,IAAI;IAC9B,CAAC,CAAC;IACF,IAAIE,OAAO,GAAG,SAAAA,CAAUC,WAAW,EAAE;MACjCnB,WAAW,CAACoB,SAAS,CAACV,OAAO,CAACS,WAAW,CAAC,CAAC,CAACE,SAAS,CAAClB,oBAAoB,CAACmB,wBAAwB,CAACT,UAAU,EAAE,UAAU3B,KAAK,EAAE;QAC7H4B,OAAO,CAACK,WAAW,CAAC,CAAClC,IAAI,CAACC,KAAK,CAAC;QAChC,IAAI4B,OAAO,CAACS,KAAK,CAAC,UAAUC,MAAM,EAAE;UAAE,OAAOA,MAAM,CAAChC,MAAM;QAAE,CAAC,CAAC,EAAE;UAC5D,IAAIiC,MAAM,GAAGX,OAAO,CAACC,GAAG,CAAC,UAAUS,MAAM,EAAE;YAAE,OAAOA,MAAM,CAACE,KAAK,CAAC,CAAC;UAAE,CAAC,CAAC;UACtEb,UAAU,CAAC9B,IAAI,CAACyB,cAAc,GAAGA,cAAc,CAACmB,KAAK,CAAC,KAAK,CAAC,EAAEvC,aAAa,CAAC,EAAE,EAAEhB,MAAM,CAACqD,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC;UAC1G,IAAIX,OAAO,CAACc,IAAI,CAAC,UAAUJ,MAAM,EAAE9C,CAAC,EAAE;YAAE,OAAO,CAAC8C,MAAM,CAAChC,MAAM,IAAIwB,SAAS,CAACtC,CAAC,CAAC;UAAE,CAAC,CAAC,EAAE;YAC/EmC,UAAU,CAACgB,QAAQ,CAAC,CAAC;UACzB;QACJ;MACJ,CAAC,EAAE,YAAY;QACXb,SAAS,CAACG,WAAW,CAAC,GAAG,IAAI;QAC7B,CAACL,OAAO,CAACK,WAAW,CAAC,CAAC3B,MAAM,IAAIqB,UAAU,CAACgB,QAAQ,CAAC,CAAC;MACzD,CAAC,CAAC,CAAC;IACP,CAAC;IACD,KAAK,IAAIV,WAAW,GAAG,CAAC,EAAE,CAACN,UAAU,CAACiB,MAAM,IAAIX,WAAW,GAAGT,OAAO,CAAClB,MAAM,EAAE2B,WAAW,EAAE,EAAE;MACzFD,OAAO,CAACC,WAAW,CAAC;IACxB;IACA,OAAO,YAAY;MACfL,OAAO,GAAGE,SAAS,GAAG,IAAI;IAC9B,CAAC;EACL,CAAC,CAAC,GACAd,OAAO,CAAC6B,KAAK;AACvB;AACAnC,OAAO,CAACC,GAAG,GAAGA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}