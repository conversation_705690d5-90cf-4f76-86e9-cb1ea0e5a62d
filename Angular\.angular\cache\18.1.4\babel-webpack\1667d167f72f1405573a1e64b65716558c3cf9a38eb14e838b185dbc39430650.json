{"ast": null, "code": "\"use strict\";\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from) {\n  for (var i = 0, il = from.length, j = to.length; i < il; i++, j++) to[j] = from[i];\n  return to;\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.withLatestFrom = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar identity_1 = require(\"../util/identity\");\nvar noop_1 = require(\"../util/noop\");\nvar args_1 = require(\"../util/args\");\nfunction withLatestFrom() {\n  var inputs = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    inputs[_i] = arguments[_i];\n  }\n  var project = args_1.popResultSelector(inputs);\n  return lift_1.operate(function (source, subscriber) {\n    var len = inputs.length;\n    var otherValues = new Array(len);\n    var hasValue = inputs.map(function () {\n      return false;\n    });\n    var ready = false;\n    var _loop_1 = function (i) {\n      innerFrom_1.innerFrom(inputs[i]).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n        otherValues[i] = value;\n        if (!ready && !hasValue[i]) {\n          hasValue[i] = true;\n          (ready = hasValue.every(identity_1.identity)) && (hasValue = null);\n        }\n      }, noop_1.noop));\n    };\n    for (var i = 0; i < len; i++) {\n      _loop_1(i);\n    }\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      if (ready) {\n        var values = __spreadArray([value], __read(otherValues));\n        subscriber.next(project ? project.apply(void 0, __spreadArray([], __read(values))) : values);\n      }\n    }));\n  });\n}\nexports.withLatestFrom = withLatestFrom;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "il", "length", "j", "Object", "defineProperty", "exports", "withLatestFrom", "lift_1", "require", "OperatorSubscriber_1", "innerFrom_1", "identity_1", "noop_1", "args_1", "inputs", "_i", "arguments", "project", "popResultSelector", "operate", "source", "subscriber", "len", "otherValues", "Array", "hasValue", "map", "ready", "_loop_1", "innerFrom", "subscribe", "createOperatorSubscriber", "every", "identity", "noop", "values", "apply"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/withLatestFrom.js"], "sourcesContent": ["\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from) {\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\n        to[j] = from[i];\n    return to;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.withLatestFrom = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar identity_1 = require(\"../util/identity\");\nvar noop_1 = require(\"../util/noop\");\nvar args_1 = require(\"../util/args\");\nfunction withLatestFrom() {\n    var inputs = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        inputs[_i] = arguments[_i];\n    }\n    var project = args_1.popResultSelector(inputs);\n    return lift_1.operate(function (source, subscriber) {\n        var len = inputs.length;\n        var otherValues = new Array(len);\n        var hasValue = inputs.map(function () { return false; });\n        var ready = false;\n        var _loop_1 = function (i) {\n            innerFrom_1.innerFrom(inputs[i]).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n                otherValues[i] = value;\n                if (!ready && !hasValue[i]) {\n                    hasValue[i] = true;\n                    (ready = hasValue.every(identity_1.identity)) && (hasValue = null);\n                }\n            }, noop_1.noop));\n        };\n        for (var i = 0; i < len; i++) {\n            _loop_1(i);\n        }\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            if (ready) {\n                var values = __spreadArray([value], __read(otherValues));\n                subscriber.next(project ? project.apply(void 0, __spreadArray([], __read(values))) : values);\n            }\n        }));\n    });\n}\nexports.withLatestFrom = withLatestFrom;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,MAAM,GAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAK,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC;EAC1D,IAAI,CAACF,CAAC,EAAE,OAAOF,CAAC;EAChB,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAI,CAACN,CAAC,CAAC;IAAEO,CAAC;IAAEC,EAAE,GAAG,EAAE;IAAEC,CAAC;EAChC,IAAI;IACA,OAAO,CAACR,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAEH,EAAE,CAACI,IAAI,CAACL,CAAC,CAACM,KAAK,CAAC;EAC9E,CAAC,CACD,OAAOC,KAAK,EAAE;IAAEL,CAAC,GAAG;MAAEK,KAAK,EAAEA;IAAM,CAAC;EAAE,CAAC,SAC/B;IACJ,IAAI;MACA,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAI,KAAKT,CAAC,GAAGG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAEH,CAAC,CAACI,IAAI,CAACD,CAAC,CAAC;IACpD,CAAC,SACO;MAAE,IAAII,CAAC,EAAE,MAAMA,CAAC,CAACK,KAAK;IAAE;EACpC;EACA,OAAON,EAAE;AACb,CAAC;AACD,IAAIO,aAAa,GAAI,IAAI,IAAI,IAAI,CAACA,aAAa,IAAK,UAAUC,EAAE,EAAEC,IAAI,EAAE;EACpE,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEa,EAAE,GAAGD,IAAI,CAACE,MAAM,EAAEC,CAAC,GAAGJ,EAAE,CAACG,MAAM,EAAEd,CAAC,GAAGa,EAAE,EAAEb,CAAC,EAAE,EAAEe,CAAC,EAAE,EAC7DJ,EAAE,CAACI,CAAC,CAAC,GAAGH,IAAI,CAACZ,CAAC,CAAC;EACnB,OAAOW,EAAE;AACb,CAAC;AACDK,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEV,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DU,OAAO,CAACC,cAAc,GAAG,KAAK,CAAC;AAC/B,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIE,WAAW,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AACpD,IAAIG,UAAU,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AAC5C,IAAII,MAAM,GAAGJ,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIK,MAAM,GAAGL,OAAO,CAAC,cAAc,CAAC;AACpC,SAASF,cAAcA,CAAA,EAAG;EACtB,IAAIQ,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACf,MAAM,EAAEc,EAAE,EAAE,EAAE;IAC1CD,MAAM,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC9B;EACA,IAAIE,OAAO,GAAGJ,MAAM,CAACK,iBAAiB,CAACJ,MAAM,CAAC;EAC9C,OAAOP,MAAM,CAACY,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,GAAG,GAAGR,MAAM,CAACb,MAAM;IACvB,IAAIsB,WAAW,GAAG,IAAIC,KAAK,CAACF,GAAG,CAAC;IAChC,IAAIG,QAAQ,GAAGX,MAAM,CAACY,GAAG,CAAC,YAAY;MAAE,OAAO,KAAK;IAAE,CAAC,CAAC;IACxD,IAAIC,KAAK,GAAG,KAAK;IACjB,IAAIC,OAAO,GAAG,SAAAA,CAAUzC,CAAC,EAAE;MACvBuB,WAAW,CAACmB,SAAS,CAACf,MAAM,CAAC3B,CAAC,CAAC,CAAC,CAAC2C,SAAS,CAACrB,oBAAoB,CAACsB,wBAAwB,CAACV,UAAU,EAAE,UAAU1B,KAAK,EAAE;QAClH4B,WAAW,CAACpC,CAAC,CAAC,GAAGQ,KAAK;QACtB,IAAI,CAACgC,KAAK,IAAI,CAACF,QAAQ,CAACtC,CAAC,CAAC,EAAE;UACxBsC,QAAQ,CAACtC,CAAC,CAAC,GAAG,IAAI;UAClB,CAACwC,KAAK,GAAGF,QAAQ,CAACO,KAAK,CAACrB,UAAU,CAACsB,QAAQ,CAAC,MAAMR,QAAQ,GAAG,IAAI,CAAC;QACtE;MACJ,CAAC,EAAEb,MAAM,CAACsB,IAAI,CAAC,CAAC;IACpB,CAAC;IACD,KAAK,IAAI/C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,GAAG,EAAEnC,CAAC,EAAE,EAAE;MAC1ByC,OAAO,CAACzC,CAAC,CAAC;IACd;IACAiC,MAAM,CAACU,SAAS,CAACrB,oBAAoB,CAACsB,wBAAwB,CAACV,UAAU,EAAE,UAAU1B,KAAK,EAAE;MACxF,IAAIgC,KAAK,EAAE;QACP,IAAIQ,MAAM,GAAGtC,aAAa,CAAC,CAACF,KAAK,CAAC,EAAEd,MAAM,CAAC0C,WAAW,CAAC,CAAC;QACxDF,UAAU,CAAC7B,IAAI,CAACyB,OAAO,GAAGA,OAAO,CAACmB,KAAK,CAAC,KAAK,CAAC,EAAEvC,aAAa,CAAC,EAAE,EAAEhB,MAAM,CAACsD,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC;MAChG;IACJ,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACA9B,OAAO,CAACC,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}