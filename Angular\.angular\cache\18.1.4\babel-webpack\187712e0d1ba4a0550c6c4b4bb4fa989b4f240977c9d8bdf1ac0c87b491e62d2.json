{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.fromSubscribable = void 0;\nvar Observable_1 = require(\"../Observable\");\nfunction fromSubscribable(subscribable) {\n  return new Observable_1.Observable(function (subscriber) {\n    return subscribable.subscribe(subscriber);\n  });\n}\nexports.fromSubscribable = fromSubscribable;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "fromSubscribable", "Observable_1", "require", "subscribable", "Observable", "subscriber", "subscribe"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/fromSubscribable.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.fromSubscribable = void 0;\nvar Observable_1 = require(\"../Observable\");\nfunction fromSubscribable(subscribable) {\n    return new Observable_1.Observable(function (subscriber) { return subscribable.subscribe(subscriber); });\n}\nexports.fromSubscribable = fromSubscribable;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,gBAAgB,GAAG,KAAK,CAAC;AACjC,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,SAASF,gBAAgBA,CAACG,YAAY,EAAE;EACpC,OAAO,IAAIF,YAAY,CAACG,UAAU,CAAC,UAAUC,UAAU,EAAE;IAAE,OAAOF,YAAY,CAACG,SAAS,CAACD,UAAU,CAAC;EAAE,CAAC,CAAC;AAC5G;AACAP,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}