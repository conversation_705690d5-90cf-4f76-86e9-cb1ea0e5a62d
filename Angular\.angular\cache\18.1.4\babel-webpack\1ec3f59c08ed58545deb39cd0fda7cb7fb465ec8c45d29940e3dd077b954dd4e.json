{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.bindNodeCallback = void 0;\nvar bindCallbackInternals_1 = require(\"./bindCallbackInternals\");\nfunction bindNodeCallback(callbackFunc, resultSelector, scheduler) {\n  return bindCallbackInternals_1.bindCallbackInternals(true, callbackFunc, resultSelector, scheduler);\n}\nexports.bindNodeCallback = bindNodeCallback;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "bindNodeCallback", "bindCallbackInternals_1", "require", "callback<PERSON><PERSON><PERSON>", "resultSelector", "scheduler", "bindCallbackInternals"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/bindNodeCallback.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bindNodeCallback = void 0;\nvar bindCallbackInternals_1 = require(\"./bindCallbackInternals\");\nfunction bindNodeCallback(callbackFunc, resultSelector, scheduler) {\n    return bindCallbackInternals_1.bindCallbackInternals(true, callbackFunc, resultSelector, scheduler);\n}\nexports.bindNodeCallback = bindNodeCallback;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,gBAAgB,GAAG,KAAK,CAAC;AACjC,IAAIC,uBAAuB,GAAGC,OAAO,CAAC,yBAAyB,CAAC;AAChE,SAASF,gBAAgBA,CAACG,YAAY,EAAEC,cAAc,EAAEC,SAAS,EAAE;EAC/D,OAAOJ,uBAAuB,CAACK,qBAAqB,CAAC,IAAI,EAAEH,YAAY,EAAEC,cAAc,EAAEC,SAAS,CAAC;AACvG;AACAP,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}