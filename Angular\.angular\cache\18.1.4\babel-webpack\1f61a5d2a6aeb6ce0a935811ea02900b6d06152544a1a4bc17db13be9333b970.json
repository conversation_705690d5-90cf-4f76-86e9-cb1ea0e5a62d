{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.throttleTime = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar throttle_1 = require(\"./throttle\");\nvar timer_1 = require(\"../observable/timer\");\nfunction throttleTime(duration, scheduler, config) {\n  if (scheduler === void 0) {\n    scheduler = async_1.asyncScheduler;\n  }\n  var duration$ = timer_1.timer(duration, scheduler);\n  return throttle_1.throttle(function () {\n    return duration$;\n  }, config);\n}\nexports.throttleTime = throttleTime;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "throttleTime", "async_1", "require", "throttle_1", "timer_1", "duration", "scheduler", "config", "asyncScheduler", "duration$", "timer", "throttle"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/throttleTime.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.throttleTime = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar throttle_1 = require(\"./throttle\");\nvar timer_1 = require(\"../observable/timer\");\nfunction throttleTime(duration, scheduler, config) {\n    if (scheduler === void 0) { scheduler = async_1.asyncScheduler; }\n    var duration$ = timer_1.timer(duration, scheduler);\n    return throttle_1.throttle(function () { return duration$; }, config);\n}\nexports.throttleTime = throttleTime;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,YAAY,GAAG,KAAK,CAAC;AAC7B,IAAIC,OAAO,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAC3C,IAAIC,UAAU,GAAGD,OAAO,CAAC,YAAY,CAAC;AACtC,IAAIE,OAAO,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AAC5C,SAASF,YAAYA,CAACK,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAE;EAC/C,IAAID,SAAS,KAAK,KAAK,CAAC,EAAE;IAAEA,SAAS,GAAGL,OAAO,CAACO,cAAc;EAAE;EAChE,IAAIC,SAAS,GAAGL,OAAO,CAACM,KAAK,CAACL,QAAQ,EAAEC,SAAS,CAAC;EAClD,OAAOH,UAAU,CAACQ,QAAQ,CAAC,YAAY;IAAE,OAAOF,SAAS;EAAE,CAAC,EAAEF,MAAM,CAAC;AACzE;AACAT,OAAO,CAACE,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}