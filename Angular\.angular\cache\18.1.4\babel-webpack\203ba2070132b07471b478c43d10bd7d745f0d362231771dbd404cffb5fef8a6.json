{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.dateTimestampProvider = void 0;\nexports.dateTimestampProvider = {\n  now: function () {\n    return (exports.dateTimestampProvider.delegate || Date).now();\n  },\n  delegate: undefined\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "dateTimestampProvider", "now", "delegate", "Date", "undefined"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/scheduler/dateTimestampProvider.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.dateTimestampProvider = void 0;\nexports.dateTimestampProvider = {\n    now: function () {\n        return (exports.dateTimestampProvider.delegate || Date).now();\n    },\n    delegate: undefined,\n};\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,qBAAqB,GAAG,KAAK,CAAC;AACtCF,OAAO,CAACE,qBAAqB,GAAG;EAC5BC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACb,OAAO,CAACH,OAAO,CAACE,qBAAqB,CAACE,QAAQ,IAAIC,IAAI,EAAEF,GAAG,CAAC,CAAC;EACjE,CAAC;EACDC,QAAQ,EAAEE;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}