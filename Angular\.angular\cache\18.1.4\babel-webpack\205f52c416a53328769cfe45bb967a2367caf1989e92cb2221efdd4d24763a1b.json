{"ast": null, "code": "\"use strict\";\n\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.OperatorSubscriber = exports.createOperatorSubscriber = void 0;\nvar Subscriber_1 = require(\"../Subscriber\");\nfunction createOperatorSubscriber(destination, onNext, onComplete, onError, onFinalize) {\n  return new OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize);\n}\nexports.createOperatorSubscriber = createOperatorSubscriber;\nvar OperatorSubscriber = function (_super) {\n  __extends(OperatorSubscriber, _super);\n  function OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize, shouldUnsubscribe) {\n    var _this = _super.call(this, destination) || this;\n    _this.onFinalize = onFinalize;\n    _this.shouldUnsubscribe = shouldUnsubscribe;\n    _this._next = onNext ? function (value) {\n      try {\n        onNext(value);\n      } catch (err) {\n        destination.error(err);\n      }\n    } : _super.prototype._next;\n    _this._error = onError ? function (err) {\n      try {\n        onError(err);\n      } catch (err) {\n        destination.error(err);\n      } finally {\n        this.unsubscribe();\n      }\n    } : _super.prototype._error;\n    _this._complete = onComplete ? function () {\n      try {\n        onComplete();\n      } catch (err) {\n        destination.error(err);\n      } finally {\n        this.unsubscribe();\n      }\n    } : _super.prototype._complete;\n    return _this;\n  }\n  OperatorSubscriber.prototype.unsubscribe = function () {\n    var _a;\n    if (!this.shouldUnsubscribe || this.shouldUnsubscribe()) {\n      var closed_1 = this.closed;\n      _super.prototype.unsubscribe.call(this);\n      !closed_1 && ((_a = this.onFinalize) === null || _a === void 0 ? void 0 : _a.call(this));\n    }\n  };\n  return OperatorSubscriber;\n}(Subscriber_1.Subscriber);\nexports.OperatorSubscriber = OperatorSubscriber;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "defineProperty", "exports", "value", "OperatorSubscriber", "createOperatorSubscriber", "Subscriber_1", "require", "destination", "onNext", "onComplete", "onError", "onFinalize", "_super", "shouldUnsubscribe", "_this", "_next", "err", "error", "_error", "unsubscribe", "_complete", "_a", "closed_1", "closed", "Subscriber"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js"], "sourcesContent": ["\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.OperatorSubscriber = exports.createOperatorSubscriber = void 0;\nvar Subscriber_1 = require(\"../Subscriber\");\nfunction createOperatorSubscriber(destination, onNext, onComplete, onError, onFinalize) {\n    return new OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize);\n}\nexports.createOperatorSubscriber = createOperatorSubscriber;\nvar OperatorSubscriber = (function (_super) {\n    __extends(OperatorSubscriber, _super);\n    function OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize, shouldUnsubscribe) {\n        var _this = _super.call(this, destination) || this;\n        _this.onFinalize = onFinalize;\n        _this.shouldUnsubscribe = shouldUnsubscribe;\n        _this._next = onNext\n            ? function (value) {\n                try {\n                    onNext(value);\n                }\n                catch (err) {\n                    destination.error(err);\n                }\n            }\n            : _super.prototype._next;\n        _this._error = onError\n            ? function (err) {\n                try {\n                    onError(err);\n                }\n                catch (err) {\n                    destination.error(err);\n                }\n                finally {\n                    this.unsubscribe();\n                }\n            }\n            : _super.prototype._error;\n        _this._complete = onComplete\n            ? function () {\n                try {\n                    onComplete();\n                }\n                catch (err) {\n                    destination.error(err);\n                }\n                finally {\n                    this.unsubscribe();\n                }\n            }\n            : _super.prototype._complete;\n        return _this;\n    }\n    OperatorSubscriber.prototype.unsubscribe = function () {\n        var _a;\n        if (!this.shouldUnsubscribe || this.shouldUnsubscribe()) {\n            var closed_1 = this.closed;\n            _super.prototype.unsubscribe.call(this);\n            !closed_1 && ((_a = this.onFinalize) === null || _a === void 0 ? void 0 : _a.call(this));\n        }\n    };\n    return OperatorSubscriber;\n}(Subscriber_1.Subscriber));\nexports.OperatorSubscriber = OperatorSubscriber;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIC,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IACrG,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnB,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAIA,CAAC,KAAK,IAAI,EACrC,MAAM,IAAIS,SAAS,CAAC,sBAAsB,GAAGC,MAAM,CAACV,CAAC,CAAC,GAAG,+BAA+B,CAAC;IAC7FF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASW,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGb,CAAC;IAAE;IACtCA,CAAC,CAACO,SAAS,GAAGN,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACY,MAAM,CAACb,CAAC,CAAC,IAAIW,EAAE,CAACL,SAAS,GAAGN,CAAC,CAACM,SAAS,EAAE,IAAIK,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJV,MAAM,CAACa,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,wBAAwB,GAAG,KAAK,CAAC;AACtE,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,SAASF,wBAAwBA,CAACG,WAAW,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAE;EACpF,OAAO,IAAIR,kBAAkB,CAACI,WAAW,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,CAAC;AACvF;AACAV,OAAO,CAACG,wBAAwB,GAAGA,wBAAwB;AAC3D,IAAID,kBAAkB,GAAI,UAAUS,MAAM,EAAE;EACxC7B,SAAS,CAACoB,kBAAkB,EAAES,MAAM,CAAC;EACrC,SAAST,kBAAkBA,CAACI,WAAW,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAEE,iBAAiB,EAAE;IACjG,IAAIC,KAAK,GAAGF,MAAM,CAAClB,IAAI,CAAC,IAAI,EAAEa,WAAW,CAAC,IAAI,IAAI;IAClDO,KAAK,CAACH,UAAU,GAAGA,UAAU;IAC7BG,KAAK,CAACD,iBAAiB,GAAGA,iBAAiB;IAC3CC,KAAK,CAACC,KAAK,GAAGP,MAAM,GACd,UAAUN,KAAK,EAAE;MACf,IAAI;QACAM,MAAM,CAACN,KAAK,CAAC;MACjB,CAAC,CACD,OAAOc,GAAG,EAAE;QACRT,WAAW,CAACU,KAAK,CAACD,GAAG,CAAC;MAC1B;IACJ,CAAC,GACCJ,MAAM,CAACpB,SAAS,CAACuB,KAAK;IAC5BD,KAAK,CAACI,MAAM,GAAGR,OAAO,GAChB,UAAUM,GAAG,EAAE;MACb,IAAI;QACAN,OAAO,CAACM,GAAG,CAAC;MAChB,CAAC,CACD,OAAOA,GAAG,EAAE;QACRT,WAAW,CAACU,KAAK,CAACD,GAAG,CAAC;MAC1B,CAAC,SACO;QACJ,IAAI,CAACG,WAAW,CAAC,CAAC;MACtB;IACJ,CAAC,GACCP,MAAM,CAACpB,SAAS,CAAC0B,MAAM;IAC7BJ,KAAK,CAACM,SAAS,GAAGX,UAAU,GACtB,YAAY;MACV,IAAI;QACAA,UAAU,CAAC,CAAC;MAChB,CAAC,CACD,OAAOO,GAAG,EAAE;QACRT,WAAW,CAACU,KAAK,CAACD,GAAG,CAAC;MAC1B,CAAC,SACO;QACJ,IAAI,CAACG,WAAW,CAAC,CAAC;MACtB;IACJ,CAAC,GACCP,MAAM,CAACpB,SAAS,CAAC4B,SAAS;IAChC,OAAON,KAAK;EAChB;EACAX,kBAAkB,CAACX,SAAS,CAAC2B,WAAW,GAAG,YAAY;IACnD,IAAIE,EAAE;IACN,IAAI,CAAC,IAAI,CAACR,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAAC,CAAC,EAAE;MACrD,IAAIS,QAAQ,GAAG,IAAI,CAACC,MAAM;MAC1BX,MAAM,CAACpB,SAAS,CAAC2B,WAAW,CAACzB,IAAI,CAAC,IAAI,CAAC;MACvC,CAAC4B,QAAQ,KAAK,CAACD,EAAE,GAAG,IAAI,CAACV,UAAU,MAAM,IAAI,IAAIU,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC3B,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5F;EACJ,CAAC;EACD,OAAOS,kBAAkB;AAC7B,CAAC,CAACE,YAAY,CAACmB,UAAU,CAAE;AAC3BvB,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}