{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.combineLatestInit = exports.combineLatest = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar argsArgArrayOrObject_1 = require(\"../util/argsArgArrayOrObject\");\nvar from_1 = require(\"./from\");\nvar identity_1 = require(\"../util/identity\");\nvar mapOneOrManyArgs_1 = require(\"../util/mapOneOrManyArgs\");\nvar args_1 = require(\"../util/args\");\nvar createObject_1 = require(\"../util/createObject\");\nvar OperatorSubscriber_1 = require(\"../operators/OperatorSubscriber\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nfunction combineLatest() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var scheduler = args_1.popScheduler(args);\n  var resultSelector = args_1.popResultSelector(args);\n  var _a = argsArgArrayOrObject_1.argsArgArrayOrObject(args),\n    observables = _a.args,\n    keys = _a.keys;\n  if (observables.length === 0) {\n    return from_1.from([], scheduler);\n  }\n  var result = new Observable_1.Observable(combineLatestInit(observables, scheduler, keys ? function (values) {\n    return createObject_1.createObject(keys, values);\n  } : identity_1.identity));\n  return resultSelector ? result.pipe(mapOneOrManyArgs_1.mapOneOrManyArgs(resultSelector)) : result;\n}\nexports.combineLatest = combineLatest;\nfunction combineLatestInit(observables, scheduler, valueTransform) {\n  if (valueTransform === void 0) {\n    valueTransform = identity_1.identity;\n  }\n  return function (subscriber) {\n    maybeSchedule(scheduler, function () {\n      var length = observables.length;\n      var values = new Array(length);\n      var active = length;\n      var remainingFirstValues = length;\n      var _loop_1 = function (i) {\n        maybeSchedule(scheduler, function () {\n          var source = from_1.from(observables[i], scheduler);\n          var hasFirstValue = false;\n          source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            values[i] = value;\n            if (!hasFirstValue) {\n              hasFirstValue = true;\n              remainingFirstValues--;\n            }\n            if (!remainingFirstValues) {\n              subscriber.next(valueTransform(values.slice()));\n            }\n          }, function () {\n            if (! --active) {\n              subscriber.complete();\n            }\n          }));\n        }, subscriber);\n      };\n      for (var i = 0; i < length; i++) {\n        _loop_1(i);\n      }\n    }, subscriber);\n  };\n}\nexports.combineLatestInit = combineLatestInit;\nfunction maybeSchedule(scheduler, execute, subscription) {\n  if (scheduler) {\n    executeSchedule_1.executeSchedule(subscription, scheduler, execute);\n  } else {\n    execute();\n  }\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "combineLatestInit", "combineLatest", "Observable_1", "require", "argsArgArrayOrObject_1", "from_1", "identity_1", "mapOneOrManyArgs_1", "args_1", "createObject_1", "OperatorSubscriber_1", "executeSchedule_1", "args", "_i", "arguments", "length", "scheduler", "popScheduler", "resultSelector", "popResultSelector", "_a", "argsArgArrayOrObject", "observables", "keys", "from", "result", "Observable", "values", "createObject", "identity", "pipe", "mapOneOrManyArgs", "valueTransform", "subscriber", "maybeSchedule", "Array", "active", "remainingFirstValues", "_loop_1", "i", "source", "hasFirstValue", "subscribe", "createOperatorSubscriber", "next", "slice", "complete", "execute", "subscription", "executeSchedule"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/combineLatest.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.combineLatestInit = exports.combineLatest = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar argsArgArrayOrObject_1 = require(\"../util/argsArgArrayOrObject\");\nvar from_1 = require(\"./from\");\nvar identity_1 = require(\"../util/identity\");\nvar mapOneOrManyArgs_1 = require(\"../util/mapOneOrManyArgs\");\nvar args_1 = require(\"../util/args\");\nvar createObject_1 = require(\"../util/createObject\");\nvar OperatorSubscriber_1 = require(\"../operators/OperatorSubscriber\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nfunction combineLatest() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var scheduler = args_1.popScheduler(args);\n    var resultSelector = args_1.popResultSelector(args);\n    var _a = argsArgArrayOrObject_1.argsArgArrayOrObject(args), observables = _a.args, keys = _a.keys;\n    if (observables.length === 0) {\n        return from_1.from([], scheduler);\n    }\n    var result = new Observable_1.Observable(combineLatestInit(observables, scheduler, keys\n        ?\n            function (values) { return createObject_1.createObject(keys, values); }\n        :\n            identity_1.identity));\n    return resultSelector ? result.pipe(mapOneOrManyArgs_1.mapOneOrManyArgs(resultSelector)) : result;\n}\nexports.combineLatest = combineLatest;\nfunction combineLatestInit(observables, scheduler, valueTransform) {\n    if (valueTransform === void 0) { valueTransform = identity_1.identity; }\n    return function (subscriber) {\n        maybeSchedule(scheduler, function () {\n            var length = observables.length;\n            var values = new Array(length);\n            var active = length;\n            var remainingFirstValues = length;\n            var _loop_1 = function (i) {\n                maybeSchedule(scheduler, function () {\n                    var source = from_1.from(observables[i], scheduler);\n                    var hasFirstValue = false;\n                    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n                        values[i] = value;\n                        if (!hasFirstValue) {\n                            hasFirstValue = true;\n                            remainingFirstValues--;\n                        }\n                        if (!remainingFirstValues) {\n                            subscriber.next(valueTransform(values.slice()));\n                        }\n                    }, function () {\n                        if (!--active) {\n                            subscriber.complete();\n                        }\n                    }));\n                }, subscriber);\n            };\n            for (var i = 0; i < length; i++) {\n                _loop_1(i);\n            }\n        }, subscriber);\n    };\n}\nexports.combineLatestInit = combineLatestInit;\nfunction maybeSchedule(scheduler, execute, subscription) {\n    if (scheduler) {\n        executeSchedule_1.executeSchedule(subscription, scheduler, execute);\n    }\n    else {\n        execute();\n    }\n}\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,iBAAiB,GAAGF,OAAO,CAACG,aAAa,GAAG,KAAK,CAAC;AAC1D,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8BAA8B,CAAC;AACpE,IAAIE,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;AAC9B,IAAIG,UAAU,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AAC5C,IAAII,kBAAkB,GAAGJ,OAAO,CAAC,0BAA0B,CAAC;AAC5D,IAAIK,MAAM,GAAGL,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIM,cAAc,GAAGN,OAAO,CAAC,sBAAsB,CAAC;AACpD,IAAIO,oBAAoB,GAAGP,OAAO,CAAC,iCAAiC,CAAC;AACrE,IAAIQ,iBAAiB,GAAGR,OAAO,CAAC,yBAAyB,CAAC;AAC1D,SAASF,aAAaA,CAAA,EAAG;EACrB,IAAIW,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC5B;EACA,IAAIG,SAAS,GAAGR,MAAM,CAACS,YAAY,CAACL,IAAI,CAAC;EACzC,IAAIM,cAAc,GAAGV,MAAM,CAACW,iBAAiB,CAACP,IAAI,CAAC;EACnD,IAAIQ,EAAE,GAAGhB,sBAAsB,CAACiB,oBAAoB,CAACT,IAAI,CAAC;IAAEU,WAAW,GAAGF,EAAE,CAACR,IAAI;IAAEW,IAAI,GAAGH,EAAE,CAACG,IAAI;EACjG,IAAID,WAAW,CAACP,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAOV,MAAM,CAACmB,IAAI,CAAC,EAAE,EAAER,SAAS,CAAC;EACrC;EACA,IAAIS,MAAM,GAAG,IAAIvB,YAAY,CAACwB,UAAU,CAAC1B,iBAAiB,CAACsB,WAAW,EAAEN,SAAS,EAAEO,IAAI,GAE/E,UAAUI,MAAM,EAAE;IAAE,OAAOlB,cAAc,CAACmB,YAAY,CAACL,IAAI,EAAEI,MAAM,CAAC;EAAE,CAAC,GAEvErB,UAAU,CAACuB,QAAQ,CAAC,CAAC;EAC7B,OAAOX,cAAc,GAAGO,MAAM,CAACK,IAAI,CAACvB,kBAAkB,CAACwB,gBAAgB,CAACb,cAAc,CAAC,CAAC,GAAGO,MAAM;AACrG;AACA3B,OAAO,CAACG,aAAa,GAAGA,aAAa;AACrC,SAASD,iBAAiBA,CAACsB,WAAW,EAAEN,SAAS,EAAEgB,cAAc,EAAE;EAC/D,IAAIA,cAAc,KAAK,KAAK,CAAC,EAAE;IAAEA,cAAc,GAAG1B,UAAU,CAACuB,QAAQ;EAAE;EACvE,OAAO,UAAUI,UAAU,EAAE;IACzBC,aAAa,CAAClB,SAAS,EAAE,YAAY;MACjC,IAAID,MAAM,GAAGO,WAAW,CAACP,MAAM;MAC/B,IAAIY,MAAM,GAAG,IAAIQ,KAAK,CAACpB,MAAM,CAAC;MAC9B,IAAIqB,MAAM,GAAGrB,MAAM;MACnB,IAAIsB,oBAAoB,GAAGtB,MAAM;MACjC,IAAIuB,OAAO,GAAG,SAAAA,CAAUC,CAAC,EAAE;QACvBL,aAAa,CAAClB,SAAS,EAAE,YAAY;UACjC,IAAIwB,MAAM,GAAGnC,MAAM,CAACmB,IAAI,CAACF,WAAW,CAACiB,CAAC,CAAC,EAAEvB,SAAS,CAAC;UACnD,IAAIyB,aAAa,GAAG,KAAK;UACzBD,MAAM,CAACE,SAAS,CAAChC,oBAAoB,CAACiC,wBAAwB,CAACV,UAAU,EAAE,UAAUlC,KAAK,EAAE;YACxF4B,MAAM,CAACY,CAAC,CAAC,GAAGxC,KAAK;YACjB,IAAI,CAAC0C,aAAa,EAAE;cAChBA,aAAa,GAAG,IAAI;cACpBJ,oBAAoB,EAAE;YAC1B;YACA,IAAI,CAACA,oBAAoB,EAAE;cACvBJ,UAAU,CAACW,IAAI,CAACZ,cAAc,CAACL,MAAM,CAACkB,KAAK,CAAC,CAAC,CAAC,CAAC;YACnD;UACJ,CAAC,EAAE,YAAY;YACX,IAAI,CAAC,GAAET,MAAM,EAAE;cACXH,UAAU,CAACa,QAAQ,CAAC,CAAC;YACzB;UACJ,CAAC,CAAC,CAAC;QACP,CAAC,EAAEb,UAAU,CAAC;MAClB,CAAC;MACD,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,MAAM,EAAEwB,CAAC,EAAE,EAAE;QAC7BD,OAAO,CAACC,CAAC,CAAC;MACd;IACJ,CAAC,EAAEN,UAAU,CAAC;EAClB,CAAC;AACL;AACAnC,OAAO,CAACE,iBAAiB,GAAGA,iBAAiB;AAC7C,SAASkC,aAAaA,CAAClB,SAAS,EAAE+B,OAAO,EAAEC,YAAY,EAAE;EACrD,IAAIhC,SAAS,EAAE;IACXL,iBAAiB,CAACsC,eAAe,CAACD,YAAY,EAAEhC,SAAS,EAAE+B,OAAO,CAAC;EACvE,CAAC,MACI;IACDA,OAAO,CAAC,CAAC;EACb;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}