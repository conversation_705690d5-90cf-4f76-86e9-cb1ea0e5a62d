{"ast": null, "code": "\"use strict\";\n\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.QueueAction = void 0;\nvar AsyncAction_1 = require(\"./AsyncAction\");\nvar QueueAction = function (_super) {\n  __extends(QueueAction, _super);\n  function QueueAction(scheduler, work) {\n    var _this = _super.call(this, scheduler, work) || this;\n    _this.scheduler = scheduler;\n    _this.work = work;\n    return _this;\n  }\n  QueueAction.prototype.schedule = function (state, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay > 0) {\n      return _super.prototype.schedule.call(this, state, delay);\n    }\n    this.delay = delay;\n    this.state = state;\n    this.scheduler.flush(this);\n    return this;\n  };\n  QueueAction.prototype.execute = function (state, delay) {\n    return delay > 0 || this.closed ? _super.prototype.execute.call(this, state, delay) : this._execute(state, delay);\n  };\n  QueueAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay != null && delay > 0 || delay == null && this.delay > 0) {\n      return _super.prototype.requestAsyncId.call(this, scheduler, id, delay);\n    }\n    scheduler.flush(this);\n    return 0;\n  };\n  return QueueAction;\n}(AsyncAction_1.AsyncAction);\nexports.QueueAction = QueueAction;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "defineProperty", "exports", "value", "QueueAction", "AsyncAction_1", "require", "_super", "scheduler", "work", "_this", "schedule", "state", "delay", "flush", "execute", "closed", "_execute", "requestAsyncId", "id", "AsyncAction"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/scheduler/QueueAction.js"], "sourcesContent": ["\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.QueueAction = void 0;\nvar AsyncAction_1 = require(\"./AsyncAction\");\nvar QueueAction = (function (_super) {\n    __extends(QueueAction, _super);\n    function QueueAction(scheduler, work) {\n        var _this = _super.call(this, scheduler, work) || this;\n        _this.scheduler = scheduler;\n        _this.work = work;\n        return _this;\n    }\n    QueueAction.prototype.schedule = function (state, delay) {\n        if (delay === void 0) { delay = 0; }\n        if (delay > 0) {\n            return _super.prototype.schedule.call(this, state, delay);\n        }\n        this.delay = delay;\n        this.state = state;\n        this.scheduler.flush(this);\n        return this;\n    };\n    QueueAction.prototype.execute = function (state, delay) {\n        return delay > 0 || this.closed ? _super.prototype.execute.call(this, state, delay) : this._execute(state, delay);\n    };\n    QueueAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n        if (delay === void 0) { delay = 0; }\n        if ((delay != null && delay > 0) || (delay == null && this.delay > 0)) {\n            return _super.prototype.requestAsyncId.call(this, scheduler, id, delay);\n        }\n        scheduler.flush(this);\n        return 0;\n    };\n    return QueueAction;\n}(AsyncAction_1.AsyncAction));\nexports.QueueAction = QueueAction;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIC,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IACrG,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnB,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAIA,CAAC,KAAK,IAAI,EACrC,MAAM,IAAIS,SAAS,CAAC,sBAAsB,GAAGC,MAAM,CAACV,CAAC,CAAC,GAAG,+BAA+B,CAAC;IAC7FF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASW,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGb,CAAC;IAAE;IACtCA,CAAC,CAACO,SAAS,GAAGN,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACY,MAAM,CAACb,CAAC,CAAC,IAAIW,EAAE,CAACL,SAAS,GAAGN,CAAC,CAACM,SAAS,EAAE,IAAIK,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJV,MAAM,CAACa,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,WAAW,GAAG,KAAK,CAAC;AAC5B,IAAIC,aAAa,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC5C,IAAIF,WAAW,GAAI,UAAUG,MAAM,EAAE;EACjCvB,SAAS,CAACoB,WAAW,EAAEG,MAAM,CAAC;EAC9B,SAASH,WAAWA,CAACI,SAAS,EAAEC,IAAI,EAAE;IAClC,IAAIC,KAAK,GAAGH,MAAM,CAACZ,IAAI,CAAC,IAAI,EAAEa,SAAS,EAAEC,IAAI,CAAC,IAAI,IAAI;IACtDC,KAAK,CAACF,SAAS,GAAGA,SAAS;IAC3BE,KAAK,CAACD,IAAI,GAAGA,IAAI;IACjB,OAAOC,KAAK;EAChB;EACAN,WAAW,CAACX,SAAS,CAACkB,QAAQ,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACrD,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,IAAIA,KAAK,GAAG,CAAC,EAAE;MACX,OAAON,MAAM,CAACd,SAAS,CAACkB,QAAQ,CAAChB,IAAI,CAAC,IAAI,EAAEiB,KAAK,EAAEC,KAAK,CAAC;IAC7D;IACA,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACJ,SAAS,CAACM,KAAK,CAAC,IAAI,CAAC;IAC1B,OAAO,IAAI;EACf,CAAC;EACDV,WAAW,CAACX,SAAS,CAACsB,OAAO,GAAG,UAAUH,KAAK,EAAEC,KAAK,EAAE;IACpD,OAAOA,KAAK,GAAG,CAAC,IAAI,IAAI,CAACG,MAAM,GAAGT,MAAM,CAACd,SAAS,CAACsB,OAAO,CAACpB,IAAI,CAAC,IAAI,EAAEiB,KAAK,EAAEC,KAAK,CAAC,GAAG,IAAI,CAACI,QAAQ,CAACL,KAAK,EAAEC,KAAK,CAAC;EACrH,CAAC;EACDT,WAAW,CAACX,SAAS,CAACyB,cAAc,GAAG,UAAUV,SAAS,EAAEW,EAAE,EAAEN,KAAK,EAAE;IACnE,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,IAAKA,KAAK,IAAI,IAAI,IAAIA,KAAK,GAAG,CAAC,IAAMA,KAAK,IAAI,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAAE,EAAE;MACnE,OAAON,MAAM,CAACd,SAAS,CAACyB,cAAc,CAACvB,IAAI,CAAC,IAAI,EAAEa,SAAS,EAAEW,EAAE,EAAEN,KAAK,CAAC;IAC3E;IACAL,SAAS,CAACM,KAAK,CAAC,IAAI,CAAC;IACrB,OAAO,CAAC;EACZ,CAAC;EACD,OAAOV,WAAW;AACtB,CAAC,CAACC,aAAa,CAACe,WAAW,CAAE;AAC7BlB,OAAO,CAACE,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}