{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ignoreElements = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar noop_1 = require(\"../util/noop\");\nfunction ignoreElements() {\n  return lift_1.operate(function (source, subscriber) {\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, noop_1.noop));\n  });\n}\nexports.ignoreElements = ignoreElements;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "ignoreElements", "lift_1", "require", "OperatorSubscriber_1", "noop_1", "operate", "source", "subscriber", "subscribe", "createOperatorSubscriber", "noop"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/ignoreElements.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ignoreElements = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar noop_1 = require(\"../util/noop\");\nfunction ignoreElements() {\n    return lift_1.operate(function (source, subscriber) {\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, noop_1.noop));\n    });\n}\nexports.ignoreElements = ignoreElements;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAG,KAAK,CAAC;AAC/B,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIE,MAAM,GAAGF,OAAO,CAAC,cAAc,CAAC;AACpC,SAASF,cAAcA,CAAA,EAAG;EACtB,OAAOC,MAAM,CAACI,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChDD,MAAM,CAACE,SAAS,CAACL,oBAAoB,CAACM,wBAAwB,CAACF,UAAU,EAAEH,MAAM,CAACM,IAAI,CAAC,CAAC;EAC5F,CAAC,CAAC;AACN;AACAZ,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}