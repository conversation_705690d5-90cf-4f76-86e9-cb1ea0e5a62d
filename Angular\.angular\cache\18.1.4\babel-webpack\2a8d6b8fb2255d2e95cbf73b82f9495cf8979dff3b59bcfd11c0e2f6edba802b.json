{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.throttle = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction throttle(durationSelector, config) {\n  return lift_1.operate(function (source, subscriber) {\n    var _a = config !== null && config !== void 0 ? config : {},\n      _b = _a.leading,\n      leading = _b === void 0 ? true : _b,\n      _c = _a.trailing,\n      trailing = _c === void 0 ? false : _c;\n    var hasValue = false;\n    var sendValue = null;\n    var throttled = null;\n    var isComplete = false;\n    var endThrottling = function () {\n      throttled === null || throttled === void 0 ? void 0 : throttled.unsubscribe();\n      throttled = null;\n      if (trailing) {\n        send();\n        isComplete && subscriber.complete();\n      }\n    };\n    var cleanupThrottling = function () {\n      throttled = null;\n      isComplete && subscriber.complete();\n    };\n    var startThrottle = function (value) {\n      return throttled = innerFrom_1.innerFrom(durationSelector(value)).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, endThrottling, cleanupThrottling));\n    };\n    var send = function () {\n      if (hasValue) {\n        hasValue = false;\n        var value = sendValue;\n        sendValue = null;\n        subscriber.next(value);\n        !isComplete && startThrottle(value);\n      }\n    };\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      hasValue = true;\n      sendValue = value;\n      !(throttled && !throttled.closed) && (leading ? send() : startThrottle(value));\n    }, function () {\n      isComplete = true;\n      !(trailing && hasValue && throttled && !throttled.closed) && subscriber.complete();\n    }));\n  });\n}\nexports.throttle = throttle;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "throttle", "lift_1", "require", "OperatorSubscriber_1", "innerFrom_1", "durationSelector", "config", "operate", "source", "subscriber", "_a", "_b", "leading", "_c", "trailing", "hasValue", "sendValue", "throttled", "isComplete", "endThrottling", "unsubscribe", "send", "complete", "cleanupThrottling", "startThrottle", "innerFrom", "subscribe", "createOperatorSubscriber", "next", "closed"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/throttle.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.throttle = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction throttle(durationSelector, config) {\n    return lift_1.operate(function (source, subscriber) {\n        var _a = config !== null && config !== void 0 ? config : {}, _b = _a.leading, leading = _b === void 0 ? true : _b, _c = _a.trailing, trailing = _c === void 0 ? false : _c;\n        var hasValue = false;\n        var sendValue = null;\n        var throttled = null;\n        var isComplete = false;\n        var endThrottling = function () {\n            throttled === null || throttled === void 0 ? void 0 : throttled.unsubscribe();\n            throttled = null;\n            if (trailing) {\n                send();\n                isComplete && subscriber.complete();\n            }\n        };\n        var cleanupThrottling = function () {\n            throttled = null;\n            isComplete && subscriber.complete();\n        };\n        var startThrottle = function (value) {\n            return (throttled = innerFrom_1.innerFrom(durationSelector(value)).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, endThrottling, cleanupThrottling)));\n        };\n        var send = function () {\n            if (hasValue) {\n                hasValue = false;\n                var value = sendValue;\n                sendValue = null;\n                subscriber.next(value);\n                !isComplete && startThrottle(value);\n            }\n        };\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            hasValue = true;\n            sendValue = value;\n            !(throttled && !throttled.closed) && (leading ? send() : startThrottle(value));\n        }, function () {\n            isComplete = true;\n            !(trailing && hasValue && throttled && !throttled.closed) && subscriber.complete();\n        }));\n    });\n}\nexports.throttle = throttle;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,QAAQ,GAAG,KAAK,CAAC;AACzB,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIE,WAAW,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AACpD,SAASF,QAAQA,CAACK,gBAAgB,EAAEC,MAAM,EAAE;EACxC,OAAOL,MAAM,CAACM,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,EAAE,GAAGJ,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,CAAC,CAAC;MAAEK,EAAE,GAAGD,EAAE,CAACE,OAAO;MAAEA,OAAO,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;MAAEE,EAAE,GAAGH,EAAE,CAACI,QAAQ;MAAEA,QAAQ,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;IAC1K,IAAIE,QAAQ,GAAG,KAAK;IACpB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAIC,aAAa,GAAG,SAAAA,CAAA,EAAY;MAC5BF,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACG,WAAW,CAAC,CAAC;MAC7EH,SAAS,GAAG,IAAI;MAChB,IAAIH,QAAQ,EAAE;QACVO,IAAI,CAAC,CAAC;QACNH,UAAU,IAAIT,UAAU,CAACa,QAAQ,CAAC,CAAC;MACvC;IACJ,CAAC;IACD,IAAIC,iBAAiB,GAAG,SAAAA,CAAA,EAAY;MAChCN,SAAS,GAAG,IAAI;MAChBC,UAAU,IAAIT,UAAU,CAACa,QAAQ,CAAC,CAAC;IACvC,CAAC;IACD,IAAIE,aAAa,GAAG,SAAAA,CAAUzB,KAAK,EAAE;MACjC,OAAQkB,SAAS,GAAGb,WAAW,CAACqB,SAAS,CAACpB,gBAAgB,CAACN,KAAK,CAAC,CAAC,CAAC2B,SAAS,CAACvB,oBAAoB,CAACwB,wBAAwB,CAAClB,UAAU,EAAEU,aAAa,EAAEI,iBAAiB,CAAC,CAAC;IAC7K,CAAC;IACD,IAAIF,IAAI,GAAG,SAAAA,CAAA,EAAY;MACnB,IAAIN,QAAQ,EAAE;QACVA,QAAQ,GAAG,KAAK;QAChB,IAAIhB,KAAK,GAAGiB,SAAS;QACrBA,SAAS,GAAG,IAAI;QAChBP,UAAU,CAACmB,IAAI,CAAC7B,KAAK,CAAC;QACtB,CAACmB,UAAU,IAAIM,aAAa,CAACzB,KAAK,CAAC;MACvC;IACJ,CAAC;IACDS,MAAM,CAACkB,SAAS,CAACvB,oBAAoB,CAACwB,wBAAwB,CAAClB,UAAU,EAAE,UAAUV,KAAK,EAAE;MACxFgB,QAAQ,GAAG,IAAI;MACfC,SAAS,GAAGjB,KAAK;MACjB,EAAEkB,SAAS,IAAI,CAACA,SAAS,CAACY,MAAM,CAAC,KAAKjB,OAAO,GAAGS,IAAI,CAAC,CAAC,GAAGG,aAAa,CAACzB,KAAK,CAAC,CAAC;IAClF,CAAC,EAAE,YAAY;MACXmB,UAAU,GAAG,IAAI;MACjB,EAAEJ,QAAQ,IAAIC,QAAQ,IAAIE,SAAS,IAAI,CAACA,SAAS,CAACY,MAAM,CAAC,IAAIpB,UAAU,CAACa,QAAQ,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACAxB,OAAO,CAACE,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}