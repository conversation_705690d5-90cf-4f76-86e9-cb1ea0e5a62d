{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.pairs = void 0;\nvar from_1 = require(\"./from\");\nfunction pairs(obj, scheduler) {\n  return from_1.from(Object.entries(obj), scheduler);\n}\nexports.pairs = pairs;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "pairs", "from_1", "require", "obj", "scheduler", "from", "entries"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/pairs.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.pairs = void 0;\nvar from_1 = require(\"./from\");\nfunction pairs(obj, scheduler) {\n    return from_1.from(Object.entries(obj), scheduler);\n}\nexports.pairs = pairs;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,KAAK,GAAG,KAAK,CAAC;AACtB,IAAIC,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAC9B,SAASF,KAAKA,CAACG,GAAG,EAAEC,SAAS,EAAE;EAC3B,OAAOH,MAAM,CAACI,IAAI,CAACT,MAAM,CAACU,OAAO,CAACH,GAAG,CAAC,EAAEC,SAAS,CAAC;AACtD;AACAN,OAAO,CAACE,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}