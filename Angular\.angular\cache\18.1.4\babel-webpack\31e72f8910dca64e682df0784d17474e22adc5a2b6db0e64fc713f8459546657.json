{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.firstValueFrom = void 0;\nvar EmptyError_1 = require(\"./util/EmptyError\");\nvar Subscriber_1 = require(\"./Subscriber\");\nfunction firstValueFrom(source, config) {\n  var hasConfig = typeof config === 'object';\n  return new Promise(function (resolve, reject) {\n    var subscriber = new Subscriber_1.SafeSubscriber({\n      next: function (value) {\n        resolve(value);\n        subscriber.unsubscribe();\n      },\n      error: reject,\n      complete: function () {\n        if (hasConfig) {\n          resolve(config.defaultValue);\n        } else {\n          reject(new EmptyError_1.EmptyError());\n        }\n      }\n    });\n    source.subscribe(subscriber);\n  });\n}\nexports.firstValueFrom = firstValueFrom;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "firstValueFrom", "EmptyError_1", "require", "Subscriber_1", "source", "config", "hasConfig", "Promise", "resolve", "reject", "subscriber", "SafeSubscriber", "next", "unsubscribe", "error", "complete", "defaultValue", "EmptyError", "subscribe"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/firstValueFrom.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.firstValueFrom = void 0;\nvar EmptyError_1 = require(\"./util/EmptyError\");\nvar Subscriber_1 = require(\"./Subscriber\");\nfunction firstValueFrom(source, config) {\n    var hasConfig = typeof config === 'object';\n    return new Promise(function (resolve, reject) {\n        var subscriber = new Subscriber_1.SafeSubscriber({\n            next: function (value) {\n                resolve(value);\n                subscriber.unsubscribe();\n            },\n            error: reject,\n            complete: function () {\n                if (hasConfig) {\n                    resolve(config.defaultValue);\n                }\n                else {\n                    reject(new EmptyError_1.EmptyError());\n                }\n            },\n        });\n        source.subscribe(subscriber);\n    });\n}\nexports.firstValueFrom = firstValueFrom;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAG,KAAK,CAAC;AAC/B,IAAIC,YAAY,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AAC/C,IAAIC,YAAY,GAAGD,OAAO,CAAC,cAAc,CAAC;AAC1C,SAASF,cAAcA,CAACI,MAAM,EAAEC,MAAM,EAAE;EACpC,IAAIC,SAAS,GAAG,OAAOD,MAAM,KAAK,QAAQ;EAC1C,OAAO,IAAIE,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;IAC1C,IAAIC,UAAU,GAAG,IAAIP,YAAY,CAACQ,cAAc,CAAC;MAC7CC,IAAI,EAAE,SAAAA,CAAUb,KAAK,EAAE;QACnBS,OAAO,CAACT,KAAK,CAAC;QACdW,UAAU,CAACG,WAAW,CAAC,CAAC;MAC5B,CAAC;MACDC,KAAK,EAAEL,MAAM;MACbM,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,IAAIT,SAAS,EAAE;UACXE,OAAO,CAACH,MAAM,CAACW,YAAY,CAAC;QAChC,CAAC,MACI;UACDP,MAAM,CAAC,IAAIR,YAAY,CAACgB,UAAU,CAAC,CAAC,CAAC;QACzC;MACJ;IACJ,CAAC,CAAC;IACFb,MAAM,CAACc,SAAS,CAACR,UAAU,CAAC;EAChC,CAAC,CAAC;AACN;AACAZ,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}