{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.distinctUntilKeyChanged = void 0;\nvar distinctUntilChanged_1 = require(\"./distinctUntilChanged\");\nfunction distinctUntilKeyChanged(key, compare) {\n  return distinctUntilChanged_1.distinctUntilChanged(function (x, y) {\n    return compare ? compare(x[key], y[key]) : x[key] === y[key];\n  });\n}\nexports.distinctUntilKeyChanged = distinctUntilKeyChanged;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "distinctUntilKeyChanged", "distinctUntilChanged_1", "require", "key", "compare", "distinctUntilChanged", "x", "y"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/distinctUntilKeyChanged.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.distinctUntilKeyChanged = void 0;\nvar distinctUntilChanged_1 = require(\"./distinctUntilChanged\");\nfunction distinctUntilKeyChanged(key, compare) {\n    return distinctUntilChanged_1.distinctUntilChanged(function (x, y) { return compare ? compare(x[key], y[key]) : x[key] === y[key]; });\n}\nexports.distinctUntilKeyChanged = distinctUntilKeyChanged;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,uBAAuB,GAAG,KAAK,CAAC;AACxC,IAAIC,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAC9D,SAASF,uBAAuBA,CAACG,GAAG,EAAEC,OAAO,EAAE;EAC3C,OAAOH,sBAAsB,CAACI,oBAAoB,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAAE,OAAOH,OAAO,GAAGA,OAAO,CAACE,CAAC,CAACH,GAAG,CAAC,EAAEI,CAAC,CAACJ,GAAG,CAAC,CAAC,GAAGG,CAAC,CAACH,GAAG,CAAC,KAAKI,CAAC,CAACJ,GAAG,CAAC;EAAE,CAAC,CAAC;AACzI;AACAL,OAAO,CAACE,uBAAuB,GAAGA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}