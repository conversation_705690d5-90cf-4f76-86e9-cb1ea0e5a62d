{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.skip = void 0;\nvar filter_1 = require(\"./filter\");\nfunction skip(count) {\n  return filter_1.filter(function (_, index) {\n    return count <= index;\n  });\n}\nexports.skip = skip;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "skip", "filter_1", "require", "count", "filter", "_", "index"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/skip.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.skip = void 0;\nvar filter_1 = require(\"./filter\");\nfunction skip(count) {\n    return filter_1.filter(function (_, index) { return count <= index; });\n}\nexports.skip = skip;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,IAAI,GAAG,KAAK,CAAC;AACrB,IAAIC,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;AAClC,SAASF,IAAIA,CAACG,KAAK,EAAE;EACjB,OAAOF,QAAQ,CAACG,MAAM,CAAC,UAAUC,CAAC,EAAEC,KAAK,EAAE;IAAE,OAAOH,KAAK,IAAIG,KAAK;EAAE,CAAC,CAAC;AAC1E;AACAR,OAAO,CAACE,IAAI,GAAGA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}