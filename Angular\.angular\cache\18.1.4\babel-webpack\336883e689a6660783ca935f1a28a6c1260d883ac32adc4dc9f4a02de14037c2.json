{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.repeatWhen = void 0;\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar Subject_1 = require(\"../Subject\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction repeatWhen(notifier) {\n  return lift_1.operate(function (source, subscriber) {\n    var innerSub;\n    var syncResub = false;\n    var completions$;\n    var isNotifierComplete = false;\n    var isMainComplete = false;\n    var checkComplete = function () {\n      return isMainComplete && isNotifierComplete && (subscriber.complete(), true);\n    };\n    var getCompletionSubject = function () {\n      if (!completions$) {\n        completions$ = new Subject_1.Subject();\n        innerFrom_1.innerFrom(notifier(completions$)).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n          if (innerSub) {\n            subscribeForRepeatWhen();\n          } else {\n            syncResub = true;\n          }\n        }, function () {\n          isNotifierComplete = true;\n          checkComplete();\n        }));\n      }\n      return completions$;\n    };\n    var subscribeForRepeatWhen = function () {\n      isMainComplete = false;\n      innerSub = source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, undefined, function () {\n        isMainComplete = true;\n        !checkComplete() && getCompletionSubject().next();\n      }));\n      if (syncResub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        syncResub = false;\n        subscribeForRepeatWhen();\n      }\n    };\n    subscribeForRepeatWhen();\n  });\n}\nexports.repeatWhen = repeatWhen;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "repeatWhen", "innerFrom_1", "require", "Subject_1", "lift_1", "OperatorSubscriber_1", "notifier", "operate", "source", "subscriber", "innerSub", "syncResub", "completions$", "isNotifierComplete", "isMainComplete", "checkComplete", "complete", "getCompletionSubject", "Subject", "innerFrom", "subscribe", "createOperatorSubscriber", "subscribeForRepeatWhen", "undefined", "next", "unsubscribe"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/repeatWhen.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.repeatWhen = void 0;\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar Subject_1 = require(\"../Subject\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction repeatWhen(notifier) {\n    return lift_1.operate(function (source, subscriber) {\n        var innerSub;\n        var syncResub = false;\n        var completions$;\n        var isNotifierComplete = false;\n        var isMainComplete = false;\n        var checkComplete = function () { return isMainComplete && isNotifierComplete && (subscriber.complete(), true); };\n        var getCompletionSubject = function () {\n            if (!completions$) {\n                completions$ = new Subject_1.Subject();\n                innerFrom_1.innerFrom(notifier(completions$)).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n                    if (innerSub) {\n                        subscribeForRepeatWhen();\n                    }\n                    else {\n                        syncResub = true;\n                    }\n                }, function () {\n                    isNotifierComplete = true;\n                    checkComplete();\n                }));\n            }\n            return completions$;\n        };\n        var subscribeForRepeatWhen = function () {\n            isMainComplete = false;\n            innerSub = source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, undefined, function () {\n                isMainComplete = true;\n                !checkComplete() && getCompletionSubject().next();\n            }));\n            if (syncResub) {\n                innerSub.unsubscribe();\n                innerSub = null;\n                syncResub = false;\n                subscribeForRepeatWhen();\n            }\n        };\n        subscribeForRepeatWhen();\n    });\n}\nexports.repeatWhen = repeatWhen;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAIC,WAAW,GAAGC,OAAO,CAAC,yBAAyB,CAAC;AACpD,IAAIC,SAAS,GAAGD,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIE,MAAM,GAAGF,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIG,oBAAoB,GAAGH,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,UAAUA,CAACM,QAAQ,EAAE;EAC1B,OAAOF,MAAM,CAACG,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,QAAQ;IACZ,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,YAAY;IAChB,IAAIC,kBAAkB,GAAG,KAAK;IAC9B,IAAIC,cAAc,GAAG,KAAK;IAC1B,IAAIC,aAAa,GAAG,SAAAA,CAAA,EAAY;MAAE,OAAOD,cAAc,IAAID,kBAAkB,KAAKJ,UAAU,CAACO,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC;IAAE,CAAC;IACjH,IAAIC,oBAAoB,GAAG,SAAAA,CAAA,EAAY;MACnC,IAAI,CAACL,YAAY,EAAE;QACfA,YAAY,GAAG,IAAIT,SAAS,CAACe,OAAO,CAAC,CAAC;QACtCjB,WAAW,CAACkB,SAAS,CAACb,QAAQ,CAACM,YAAY,CAAC,CAAC,CAACQ,SAAS,CAACf,oBAAoB,CAACgB,wBAAwB,CAACZ,UAAU,EAAE,YAAY;UAC1H,IAAIC,QAAQ,EAAE;YACVY,sBAAsB,CAAC,CAAC;UAC5B,CAAC,MACI;YACDX,SAAS,GAAG,IAAI;UACpB;QACJ,CAAC,EAAE,YAAY;UACXE,kBAAkB,GAAG,IAAI;UACzBE,aAAa,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;MACP;MACA,OAAOH,YAAY;IACvB,CAAC;IACD,IAAIU,sBAAsB,GAAG,SAAAA,CAAA,EAAY;MACrCR,cAAc,GAAG,KAAK;MACtBJ,QAAQ,GAAGF,MAAM,CAACY,SAAS,CAACf,oBAAoB,CAACgB,wBAAwB,CAACZ,UAAU,EAAEc,SAAS,EAAE,YAAY;QACzGT,cAAc,GAAG,IAAI;QACrB,CAACC,aAAa,CAAC,CAAC,IAAIE,oBAAoB,CAAC,CAAC,CAACO,IAAI,CAAC,CAAC;MACrD,CAAC,CAAC,CAAC;MACH,IAAIb,SAAS,EAAE;QACXD,QAAQ,CAACe,WAAW,CAAC,CAAC;QACtBf,QAAQ,GAAG,IAAI;QACfC,SAAS,GAAG,KAAK;QACjBW,sBAAsB,CAAC,CAAC;MAC5B;IACJ,CAAC;IACDA,sBAAsB,CAAC,CAAC;EAC5B,CAAC,CAAC;AACN;AACAxB,OAAO,CAACE,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}