{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.dematerialize = void 0;\nvar Notification_1 = require(\"../Notification\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction dematerialize() {\n  return lift_1.operate(function (source, subscriber) {\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (notification) {\n      return Notification_1.observeNotification(notification, subscriber);\n    }));\n  });\n}\nexports.dematerialize = dematerialize;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "dematerialize", "Notification_1", "require", "lift_1", "OperatorSubscriber_1", "operate", "source", "subscriber", "subscribe", "createOperatorSubscriber", "notification", "observeNotification"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/dematerialize.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.dematerialize = void 0;\nvar Notification_1 = require(\"../Notification\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction dematerialize() {\n    return lift_1.operate(function (source, subscriber) {\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (notification) { return Notification_1.observeNotification(notification, subscriber); }));\n    });\n}\nexports.dematerialize = dematerialize;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,aAAa,GAAG,KAAK,CAAC;AAC9B,IAAIC,cAAc,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAC/C,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,aAAaA,CAAA,EAAG;EACrB,OAAOG,MAAM,CAACE,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChDD,MAAM,CAACE,SAAS,CAACJ,oBAAoB,CAACK,wBAAwB,CAACF,UAAU,EAAE,UAAUG,YAAY,EAAE;MAAE,OAAOT,cAAc,CAACU,mBAAmB,CAACD,YAAY,EAAEH,UAAU,CAAC;IAAE,CAAC,CAAC,CAAC;EACjL,CAAC,CAAC;AACN;AACAT,OAAO,CAACE,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}