{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isScheduler = void 0;\nvar isFunction_1 = require(\"./isFunction\");\nfunction isScheduler(value) {\n  return value && isFunction_1.isFunction(value.schedule);\n}\nexports.isScheduler = isScheduler;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "isScheduler", "isFunction_1", "require", "isFunction", "schedule"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/isScheduler.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isScheduler = void 0;\nvar isFunction_1 = require(\"./isFunction\");\nfunction isScheduler(value) {\n    return value && isFunction_1.isFunction(value.schedule);\n}\nexports.isScheduler = isScheduler;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,WAAW,GAAG,KAAK,CAAC;AAC5B,IAAIC,YAAY,GAAGC,OAAO,CAAC,cAAc,CAAC;AAC1C,SAASF,WAAWA,CAACD,KAAK,EAAE;EACxB,OAAOA,KAAK,IAAIE,YAAY,CAACE,UAAU,CAACJ,KAAK,CAACK,QAAQ,CAAC;AAC3D;AACAN,OAAO,CAACE,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}