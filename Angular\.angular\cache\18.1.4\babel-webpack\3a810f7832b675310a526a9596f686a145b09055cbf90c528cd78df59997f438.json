{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.flatMap = void 0;\nvar mergeMap_1 = require(\"./mergeMap\");\nexports.flatMap = mergeMap_1.mergeMap;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "flatMap", "mergeMap_1", "require", "mergeMap"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/flatMap.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.flatMap = void 0;\nvar mergeMap_1 = require(\"./mergeMap\");\nexports.flatMap = mergeMap_1.mergeMap;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,UAAU,GAAGC,OAAO,CAAC,YAAY,CAAC;AACtCJ,OAAO,CAACE,OAAO,GAAGC,UAAU,CAACE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}