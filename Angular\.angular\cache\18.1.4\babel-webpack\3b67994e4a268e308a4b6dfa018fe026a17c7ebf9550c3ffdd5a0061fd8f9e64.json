{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.shareReplay = void 0;\nvar ReplaySubject_1 = require(\"../ReplaySubject\");\nvar share_1 = require(\"./share\");\nfunction shareReplay(configOrBufferSize, windowTime, scheduler) {\n  var _a, _b, _c;\n  var bufferSize;\n  var refCount = false;\n  if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n    _a = configOrBufferSize.bufferSize, bufferSize = _a === void 0 ? Infinity : _a, _b = configOrBufferSize.windowTime, windowTime = _b === void 0 ? Infinity : _b, _c = configOrBufferSize.refCount, refCount = _c === void 0 ? false : _c, scheduler = configOrBufferSize.scheduler;\n  } else {\n    bufferSize = configOrBufferSize !== null && configOrBufferSize !== void 0 ? configOrBufferSize : Infinity;\n  }\n  return share_1.share({\n    connector: function () {\n      return new ReplaySubject_1.ReplaySubject(bufferSize, windowTime, scheduler);\n    },\n    resetOnError: true,\n    resetOnComplete: false,\n    resetOnRefCountZero: refCount\n  });\n}\nexports.shareReplay = shareReplay;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "shareReplay", "ReplaySubject_1", "require", "share_1", "configOrBufferSize", "windowTime", "scheduler", "_a", "_b", "_c", "bufferSize", "refCount", "Infinity", "share", "connector", "ReplaySubject", "resetOnError", "resetOnComplete", "resetOnRefCountZero"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/shareReplay.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.shareReplay = void 0;\nvar ReplaySubject_1 = require(\"../ReplaySubject\");\nvar share_1 = require(\"./share\");\nfunction shareReplay(configOrBufferSize, windowTime, scheduler) {\n    var _a, _b, _c;\n    var bufferSize;\n    var refCount = false;\n    if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n        (_a = configOrBufferSize.bufferSize, bufferSize = _a === void 0 ? Infinity : _a, _b = configOrBufferSize.windowTime, windowTime = _b === void 0 ? Infinity : _b, _c = configOrBufferSize.refCount, refCount = _c === void 0 ? false : _c, scheduler = configOrBufferSize.scheduler);\n    }\n    else {\n        bufferSize = (configOrBufferSize !== null && configOrBufferSize !== void 0 ? configOrBufferSize : Infinity);\n    }\n    return share_1.share({\n        connector: function () { return new ReplaySubject_1.ReplaySubject(bufferSize, windowTime, scheduler); },\n        resetOnError: true,\n        resetOnComplete: false,\n        resetOnRefCountZero: refCount,\n    });\n}\nexports.shareReplay = shareReplay;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,WAAW,GAAG,KAAK,CAAC;AAC5B,IAAIC,eAAe,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AACjD,IAAIC,OAAO,GAAGD,OAAO,CAAC,SAAS,CAAC;AAChC,SAASF,WAAWA,CAACI,kBAAkB,EAAEC,UAAU,EAAEC,SAAS,EAAE;EAC5D,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,IAAIC,UAAU;EACd,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAIP,kBAAkB,IAAI,OAAOA,kBAAkB,KAAK,QAAQ,EAAE;IAC7DG,EAAE,GAAGH,kBAAkB,CAACM,UAAU,EAAEA,UAAU,GAAGH,EAAE,KAAK,KAAK,CAAC,GAAGK,QAAQ,GAAGL,EAAE,EAAEC,EAAE,GAAGJ,kBAAkB,CAACC,UAAU,EAAEA,UAAU,GAAGG,EAAE,KAAK,KAAK,CAAC,GAAGI,QAAQ,GAAGJ,EAAE,EAAEC,EAAE,GAAGL,kBAAkB,CAACO,QAAQ,EAAEA,QAAQ,GAAGF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE,EAAEH,SAAS,GAAGF,kBAAkB,CAACE,SAAS;EACtR,CAAC,MACI;IACDI,UAAU,GAAIN,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAGQ,QAAS;EAC/G;EACA,OAAOT,OAAO,CAACU,KAAK,CAAC;IACjBC,SAAS,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAO,IAAIb,eAAe,CAACc,aAAa,CAACL,UAAU,EAAEL,UAAU,EAAEC,SAAS,CAAC;IAAE,CAAC;IACvGU,YAAY,EAAE,IAAI;IAClBC,eAAe,EAAE,KAAK;IACtBC,mBAAmB,EAAEP;EACzB,CAAC,CAAC;AACN;AACAb,OAAO,CAACE,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}