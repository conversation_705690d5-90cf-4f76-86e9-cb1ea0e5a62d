{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.expand = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar mergeInternals_1 = require(\"./mergeInternals\");\nfunction expand(project, concurrent, scheduler) {\n  if (concurrent === void 0) {\n    concurrent = Infinity;\n  }\n  concurrent = (concurrent || 0) < 1 ? Infinity : concurrent;\n  return lift_1.operate(function (source, subscriber) {\n    return mergeInternals_1.mergeInternals(source, subscriber, project, concurrent, undefined, true, scheduler);\n  });\n}\nexports.expand = expand;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "expand", "lift_1", "require", "mergeInternals_1", "project", "concurrent", "scheduler", "Infinity", "operate", "source", "subscriber", "mergeInternals", "undefined"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/expand.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.expand = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar mergeInternals_1 = require(\"./mergeInternals\");\nfunction expand(project, concurrent, scheduler) {\n    if (concurrent === void 0) { concurrent = Infinity; }\n    concurrent = (concurrent || 0) < 1 ? Infinity : concurrent;\n    return lift_1.operate(function (source, subscriber) {\n        return mergeInternals_1.mergeInternals(source, subscriber, project, concurrent, undefined, true, scheduler);\n    });\n}\nexports.expand = expand;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,MAAM,GAAG,KAAK,CAAC;AACvB,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,gBAAgB,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AAClD,SAASF,MAAMA,CAACI,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAE;EAC5C,IAAID,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAGE,QAAQ;EAAE;EACpDF,UAAU,GAAG,CAACA,UAAU,IAAI,CAAC,IAAI,CAAC,GAAGE,QAAQ,GAAGF,UAAU;EAC1D,OAAOJ,MAAM,CAACO,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,OAAOP,gBAAgB,CAACQ,cAAc,CAACF,MAAM,EAAEC,UAAU,EAAEN,OAAO,EAAEC,UAAU,EAAEO,SAAS,EAAE,IAAI,EAAEN,SAAS,CAAC;EAC/G,CAAC,CAAC;AACN;AACAR,OAAO,CAACE,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}