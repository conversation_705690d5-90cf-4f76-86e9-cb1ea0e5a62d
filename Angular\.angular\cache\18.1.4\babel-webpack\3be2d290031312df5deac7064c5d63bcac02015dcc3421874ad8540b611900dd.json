{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.sequenceEqual = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction sequenceEqual(compareTo, comparator) {\n  if (comparator === void 0) {\n    comparator = function (a, b) {\n      return a === b;\n    };\n  }\n  return lift_1.operate(function (source, subscriber) {\n    var aState = createState();\n    var bState = createState();\n    var emit = function (isEqual) {\n      subscriber.next(isEqual);\n      subscriber.complete();\n    };\n    var createSubscriber = function (selfState, otherState) {\n      var sequenceEqualSubscriber = OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (a) {\n        var buffer = otherState.buffer,\n          complete = otherState.complete;\n        if (buffer.length === 0) {\n          complete ? emit(false) : selfState.buffer.push(a);\n        } else {\n          !comparator(a, buffer.shift()) && emit(false);\n        }\n      }, function () {\n        selfState.complete = true;\n        var complete = otherState.complete,\n          buffer = otherState.buffer;\n        complete && emit(buffer.length === 0);\n        sequenceEqualSubscriber === null || sequenceEqualSubscriber === void 0 ? void 0 : sequenceEqualSubscriber.unsubscribe();\n      });\n      return sequenceEqualSubscriber;\n    };\n    source.subscribe(createSubscriber(aState, bState));\n    innerFrom_1.innerFrom(compareTo).subscribe(createSubscriber(bState, aState));\n  });\n}\nexports.sequenceEqual = sequenceEqual;\nfunction createState() {\n  return {\n    buffer: [],\n    complete: false\n  };\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "sequenceEqual", "lift_1", "require", "OperatorSubscriber_1", "innerFrom_1", "compareTo", "comparator", "a", "b", "operate", "source", "subscriber", "aState", "createState", "bState", "emit", "isEqual", "next", "complete", "createSubscriber", "selfState", "otherState", "sequenceEqualSubscriber", "createOperatorSubscriber", "buffer", "length", "push", "shift", "unsubscribe", "subscribe", "innerFrom"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/sequenceEqual.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sequenceEqual = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction sequenceEqual(compareTo, comparator) {\n    if (comparator === void 0) { comparator = function (a, b) { return a === b; }; }\n    return lift_1.operate(function (source, subscriber) {\n        var aState = createState();\n        var bState = createState();\n        var emit = function (isEqual) {\n            subscriber.next(isEqual);\n            subscriber.complete();\n        };\n        var createSubscriber = function (selfState, otherState) {\n            var sequenceEqualSubscriber = OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (a) {\n                var buffer = otherState.buffer, complete = otherState.complete;\n                if (buffer.length === 0) {\n                    complete ? emit(false) : selfState.buffer.push(a);\n                }\n                else {\n                    !comparator(a, buffer.shift()) && emit(false);\n                }\n            }, function () {\n                selfState.complete = true;\n                var complete = otherState.complete, buffer = otherState.buffer;\n                complete && emit(buffer.length === 0);\n                sequenceEqualSubscriber === null || sequenceEqualSubscriber === void 0 ? void 0 : sequenceEqualSubscriber.unsubscribe();\n            });\n            return sequenceEqualSubscriber;\n        };\n        source.subscribe(createSubscriber(aState, bState));\n        innerFrom_1.innerFrom(compareTo).subscribe(createSubscriber(bState, aState));\n    });\n}\nexports.sequenceEqual = sequenceEqual;\nfunction createState() {\n    return {\n        buffer: [],\n        complete: false,\n    };\n}\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,aAAa,GAAG,KAAK,CAAC;AAC9B,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIE,WAAW,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AACpD,SAASF,aAAaA,CAACK,SAAS,EAAEC,UAAU,EAAE;EAC1C,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;MAAE,OAAOD,CAAC,KAAKC,CAAC;IAAE,CAAC;EAAE;EAC/E,OAAOP,MAAM,CAACQ,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,MAAM,GAAGC,WAAW,CAAC,CAAC;IAC1B,IAAIC,MAAM,GAAGD,WAAW,CAAC,CAAC;IAC1B,IAAIE,IAAI,GAAG,SAAAA,CAAUC,OAAO,EAAE;MAC1BL,UAAU,CAACM,IAAI,CAACD,OAAO,CAAC;MACxBL,UAAU,CAACO,QAAQ,CAAC,CAAC;IACzB,CAAC;IACD,IAAIC,gBAAgB,GAAG,SAAAA,CAAUC,SAAS,EAAEC,UAAU,EAAE;MACpD,IAAIC,uBAAuB,GAAGnB,oBAAoB,CAACoB,wBAAwB,CAACZ,UAAU,EAAE,UAAUJ,CAAC,EAAE;QACjG,IAAIiB,MAAM,GAAGH,UAAU,CAACG,MAAM;UAAEN,QAAQ,GAAGG,UAAU,CAACH,QAAQ;QAC9D,IAAIM,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;UACrBP,QAAQ,GAAGH,IAAI,CAAC,KAAK,CAAC,GAAGK,SAAS,CAACI,MAAM,CAACE,IAAI,CAACnB,CAAC,CAAC;QACrD,CAAC,MACI;UACD,CAACD,UAAU,CAACC,CAAC,EAAEiB,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,IAAIZ,IAAI,CAAC,KAAK,CAAC;QACjD;MACJ,CAAC,EAAE,YAAY;QACXK,SAAS,CAACF,QAAQ,GAAG,IAAI;QACzB,IAAIA,QAAQ,GAAGG,UAAU,CAACH,QAAQ;UAAEM,MAAM,GAAGH,UAAU,CAACG,MAAM;QAC9DN,QAAQ,IAAIH,IAAI,CAACS,MAAM,CAACC,MAAM,KAAK,CAAC,CAAC;QACrCH,uBAAuB,KAAK,IAAI,IAAIA,uBAAuB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,uBAAuB,CAACM,WAAW,CAAC,CAAC;MAC3H,CAAC,CAAC;MACF,OAAON,uBAAuB;IAClC,CAAC;IACDZ,MAAM,CAACmB,SAAS,CAACV,gBAAgB,CAACP,MAAM,EAAEE,MAAM,CAAC,CAAC;IAClDV,WAAW,CAAC0B,SAAS,CAACzB,SAAS,CAAC,CAACwB,SAAS,CAACV,gBAAgB,CAACL,MAAM,EAAEF,MAAM,CAAC,CAAC;EAChF,CAAC,CAAC;AACN;AACAd,OAAO,CAACE,aAAa,GAAGA,aAAa;AACrC,SAASa,WAAWA,CAAA,EAAG;EACnB,OAAO;IACHW,MAAM,EAAE,EAAE;IACVN,QAAQ,EAAE;EACd,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}