{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.throwError = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar isFunction_1 = require(\"../util/isFunction\");\nfunction throwError(errorOrErrorFactory, scheduler) {\n  var errorFactory = isFunction_1.isFunction(errorOrErrorFactory) ? errorOrErrorFactory : function () {\n    return errorOrErrorFactory;\n  };\n  var init = function (subscriber) {\n    return subscriber.error(errorFactory());\n  };\n  return new Observable_1.Observable(scheduler ? function (subscriber) {\n    return scheduler.schedule(init, 0, subscriber);\n  } : init);\n}\nexports.throwError = throwError;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "throwError", "Observable_1", "require", "isFunction_1", "errorOrErrorFactory", "scheduler", "errorFactory", "isFunction", "init", "subscriber", "error", "Observable", "schedule"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/throwError.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.throwError = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar isFunction_1 = require(\"../util/isFunction\");\nfunction throwError(errorOrErrorFactory, scheduler) {\n    var errorFactory = isFunction_1.isFunction(errorOrErrorFactory) ? errorOrErrorFactory : function () { return errorOrErrorFactory; };\n    var init = function (subscriber) { return subscriber.error(errorFactory()); };\n    return new Observable_1.Observable(scheduler ? function (subscriber) { return scheduler.schedule(init, 0, subscriber); } : init);\n}\nexports.throwError = throwError;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIC,YAAY,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AAChD,SAASF,UAAUA,CAACI,mBAAmB,EAAEC,SAAS,EAAE;EAChD,IAAIC,YAAY,GAAGH,YAAY,CAACI,UAAU,CAACH,mBAAmB,CAAC,GAAGA,mBAAmB,GAAG,YAAY;IAAE,OAAOA,mBAAmB;EAAE,CAAC;EACnI,IAAII,IAAI,GAAG,SAAAA,CAAUC,UAAU,EAAE;IAAE,OAAOA,UAAU,CAACC,KAAK,CAACJ,YAAY,CAAC,CAAC,CAAC;EAAE,CAAC;EAC7E,OAAO,IAAIL,YAAY,CAACU,UAAU,CAACN,SAAS,GAAG,UAAUI,UAAU,EAAE;IAAE,OAAOJ,SAAS,CAACO,QAAQ,CAACJ,IAAI,EAAE,CAAC,EAAEC,UAAU,CAAC;EAAE,CAAC,GAAGD,IAAI,CAAC;AACpI;AACAV,OAAO,CAACE,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}