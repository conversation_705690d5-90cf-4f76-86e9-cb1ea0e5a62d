{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.defer = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar innerFrom_1 = require(\"./innerFrom\");\nfunction defer(observableFactory) {\n  return new Observable_1.Observable(function (subscriber) {\n    innerFrom_1.innerFrom(observableFactory()).subscribe(subscriber);\n  });\n}\nexports.defer = defer;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "defer", "Observable_1", "require", "innerFrom_1", "observableFactory", "Observable", "subscriber", "innerFrom", "subscribe"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/defer.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.defer = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar innerFrom_1 = require(\"./innerFrom\");\nfunction defer(observableFactory) {\n    return new Observable_1.Observable(function (subscriber) {\n        innerFrom_1.innerFrom(observableFactory()).subscribe(subscriber);\n    });\n}\nexports.defer = defer;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,KAAK,GAAG,KAAK,CAAC;AACtB,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIC,WAAW,GAAGD,OAAO,CAAC,aAAa,CAAC;AACxC,SAASF,KAAKA,CAACI,iBAAiB,EAAE;EAC9B,OAAO,IAAIH,YAAY,CAACI,UAAU,CAAC,UAAUC,UAAU,EAAE;IACrDH,WAAW,CAACI,SAAS,CAACH,iBAAiB,CAAC,CAAC,CAAC,CAACI,SAAS,CAACF,UAAU,CAAC;EACpE,CAAC,CAAC;AACN;AACAR,OAAO,CAACE,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}