{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.queue = exports.queueScheduler = void 0;\nvar QueueAction_1 = require(\"./QueueAction\");\nvar QueueScheduler_1 = require(\"./QueueScheduler\");\nexports.queueScheduler = new QueueScheduler_1.QueueScheduler(QueueAction_1.QueueAction);\nexports.queue = exports.queueScheduler;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "queue", "queueScheduler", "QueueAction_1", "require", "QueueScheduler_1", "QueueScheduler", "QueueAction"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/scheduler/queue.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.queue = exports.queueScheduler = void 0;\nvar QueueAction_1 = require(\"./QueueAction\");\nvar QueueScheduler_1 = require(\"./QueueScheduler\");\nexports.queueScheduler = new QueueScheduler_1.QueueScheduler(QueueAction_1.QueueAction);\nexports.queue = exports.queueScheduler;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,KAAK,GAAGF,OAAO,CAACG,cAAc,GAAG,KAAK,CAAC;AAC/C,IAAIC,aAAa,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC5C,IAAIC,gBAAgB,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AAClDL,OAAO,CAACG,cAAc,GAAG,IAAIG,gBAAgB,CAACC,cAAc,CAACH,aAAa,CAACI,WAAW,CAAC;AACvFR,OAAO,CAACE,KAAK,GAAGF,OAAO,CAACG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}