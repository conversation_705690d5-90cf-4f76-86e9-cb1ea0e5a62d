{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.materialize = void 0;\nvar Notification_1 = require(\"../Notification\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction materialize() {\n  return lift_1.operate(function (source, subscriber) {\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      subscriber.next(Notification_1.Notification.createNext(value));\n    }, function () {\n      subscriber.next(Notification_1.Notification.createComplete());\n      subscriber.complete();\n    }, function (err) {\n      subscriber.next(Notification_1.Notification.createError(err));\n      subscriber.complete();\n    }));\n  });\n}\nexports.materialize = materialize;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "materialize", "Notification_1", "require", "lift_1", "OperatorSubscriber_1", "operate", "source", "subscriber", "subscribe", "createOperatorSubscriber", "next", "Notification", "createNext", "createComplete", "complete", "err", "createError"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/materialize.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.materialize = void 0;\nvar Notification_1 = require(\"../Notification\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction materialize() {\n    return lift_1.operate(function (source, subscriber) {\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            subscriber.next(Notification_1.Notification.createNext(value));\n        }, function () {\n            subscriber.next(Notification_1.Notification.createComplete());\n            subscriber.complete();\n        }, function (err) {\n            subscriber.next(Notification_1.Notification.createError(err));\n            subscriber.complete();\n        }));\n    });\n}\nexports.materialize = materialize;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,WAAW,GAAG,KAAK,CAAC;AAC5B,IAAIC,cAAc,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAC/C,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,WAAWA,CAAA,EAAG;EACnB,OAAOG,MAAM,CAACE,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChDD,MAAM,CAACE,SAAS,CAACJ,oBAAoB,CAACK,wBAAwB,CAACF,UAAU,EAAE,UAAUR,KAAK,EAAE;MACxFQ,UAAU,CAACG,IAAI,CAACT,cAAc,CAACU,YAAY,CAACC,UAAU,CAACb,KAAK,CAAC,CAAC;IAClE,CAAC,EAAE,YAAY;MACXQ,UAAU,CAACG,IAAI,CAACT,cAAc,CAACU,YAAY,CAACE,cAAc,CAAC,CAAC,CAAC;MAC7DN,UAAU,CAACO,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAE,UAAUC,GAAG,EAAE;MACdR,UAAU,CAACG,IAAI,CAACT,cAAc,CAACU,YAAY,CAACK,WAAW,CAACD,GAAG,CAAC,CAAC;MAC7DR,UAAU,CAACO,QAAQ,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACAhB,OAAO,CAACE,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}