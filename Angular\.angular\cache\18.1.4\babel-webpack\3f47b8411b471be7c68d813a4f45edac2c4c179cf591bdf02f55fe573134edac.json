{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isAsyncIterable = void 0;\nvar isFunction_1 = require(\"./isFunction\");\nfunction isAsyncIterable(obj) {\n  return Symbol.asyncIterator && isFunction_1.isFunction(obj === null || obj === void 0 ? void 0 : obj[Symbol.asyncIterator]);\n}\nexports.isAsyncIterable = isAsyncIterable;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "isAsyncIterable", "isFunction_1", "require", "obj", "Symbol", "asyncIterator", "isFunction"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/isAsyncIterable.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isAsyncIterable = void 0;\nvar isFunction_1 = require(\"./isFunction\");\nfunction isAsyncIterable(obj) {\n    return Symbol.asyncIterator && isFunction_1.isFunction(obj === null || obj === void 0 ? void 0 : obj[Symbol.asyncIterator]);\n}\nexports.isAsyncIterable = isAsyncIterable;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,eAAe,GAAG,KAAK,CAAC;AAChC,IAAIC,YAAY,GAAGC,OAAO,CAAC,cAAc,CAAC;AAC1C,SAASF,eAAeA,CAACG,GAAG,EAAE;EAC1B,OAAOC,MAAM,CAACC,aAAa,IAAIJ,YAAY,CAACK,UAAU,CAACH,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACC,MAAM,CAACC,aAAa,CAAC,CAAC;AAC/H;AACAP,OAAO,CAACE,eAAe,GAAGA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}