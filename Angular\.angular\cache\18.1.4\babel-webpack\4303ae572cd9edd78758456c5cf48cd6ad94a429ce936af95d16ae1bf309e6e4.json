{"ast": null, "code": "\"use strict\";\n\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ConnectableObservable = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar Subscription_1 = require(\"../Subscription\");\nvar refCount_1 = require(\"../operators/refCount\");\nvar OperatorSubscriber_1 = require(\"../operators/OperatorSubscriber\");\nvar lift_1 = require(\"../util/lift\");\nvar ConnectableObservable = function (_super) {\n  __extends(ConnectableObservable, _super);\n  function ConnectableObservable(source, subjectFactory) {\n    var _this = _super.call(this) || this;\n    _this.source = source;\n    _this.subjectFactory = subjectFactory;\n    _this._subject = null;\n    _this._refCount = 0;\n    _this._connection = null;\n    if (lift_1.hasLift(source)) {\n      _this.lift = source.lift;\n    }\n    return _this;\n  }\n  ConnectableObservable.prototype._subscribe = function (subscriber) {\n    return this.getSubject().subscribe(subscriber);\n  };\n  ConnectableObservable.prototype.getSubject = function () {\n    var subject = this._subject;\n    if (!subject || subject.isStopped) {\n      this._subject = this.subjectFactory();\n    }\n    return this._subject;\n  };\n  ConnectableObservable.prototype._teardown = function () {\n    this._refCount = 0;\n    var _connection = this._connection;\n    this._subject = this._connection = null;\n    _connection === null || _connection === void 0 ? void 0 : _connection.unsubscribe();\n  };\n  ConnectableObservable.prototype.connect = function () {\n    var _this = this;\n    var connection = this._connection;\n    if (!connection) {\n      connection = this._connection = new Subscription_1.Subscription();\n      var subject_1 = this.getSubject();\n      connection.add(this.source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subject_1, undefined, function () {\n        _this._teardown();\n        subject_1.complete();\n      }, function (err) {\n        _this._teardown();\n        subject_1.error(err);\n      }, function () {\n        return _this._teardown();\n      })));\n      if (connection.closed) {\n        this._connection = null;\n        connection = Subscription_1.Subscription.EMPTY;\n      }\n    }\n    return connection;\n  };\n  ConnectableObservable.prototype.refCount = function () {\n    return refCount_1.refCount()(this);\n  };\n  return ConnectableObservable;\n}(Observable_1.Observable);\nexports.ConnectableObservable = ConnectableObservable;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "defineProperty", "exports", "value", "ConnectableObservable", "Observable_1", "require", "Subscription_1", "refCount_1", "OperatorSubscriber_1", "lift_1", "_super", "source", "subjectFactory", "_this", "_subject", "_refCount", "_connection", "hasLift", "lift", "_subscribe", "subscriber", "getSubject", "subscribe", "subject", "isStopped", "_teardown", "unsubscribe", "connect", "connection", "Subscription", "subject_1", "add", "createOperatorSubscriber", "undefined", "complete", "err", "error", "closed", "EMPTY", "refCount", "Observable"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/ConnectableObservable.js"], "sourcesContent": ["\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ConnectableObservable = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar Subscription_1 = require(\"../Subscription\");\nvar refCount_1 = require(\"../operators/refCount\");\nvar OperatorSubscriber_1 = require(\"../operators/OperatorSubscriber\");\nvar lift_1 = require(\"../util/lift\");\nvar ConnectableObservable = (function (_super) {\n    __extends(ConnectableObservable, _super);\n    function ConnectableObservable(source, subjectFactory) {\n        var _this = _super.call(this) || this;\n        _this.source = source;\n        _this.subjectFactory = subjectFactory;\n        _this._subject = null;\n        _this._refCount = 0;\n        _this._connection = null;\n        if (lift_1.hasLift(source)) {\n            _this.lift = source.lift;\n        }\n        return _this;\n    }\n    ConnectableObservable.prototype._subscribe = function (subscriber) {\n        return this.getSubject().subscribe(subscriber);\n    };\n    ConnectableObservable.prototype.getSubject = function () {\n        var subject = this._subject;\n        if (!subject || subject.isStopped) {\n            this._subject = this.subjectFactory();\n        }\n        return this._subject;\n    };\n    ConnectableObservable.prototype._teardown = function () {\n        this._refCount = 0;\n        var _connection = this._connection;\n        this._subject = this._connection = null;\n        _connection === null || _connection === void 0 ? void 0 : _connection.unsubscribe();\n    };\n    ConnectableObservable.prototype.connect = function () {\n        var _this = this;\n        var connection = this._connection;\n        if (!connection) {\n            connection = this._connection = new Subscription_1.Subscription();\n            var subject_1 = this.getSubject();\n            connection.add(this.source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subject_1, undefined, function () {\n                _this._teardown();\n                subject_1.complete();\n            }, function (err) {\n                _this._teardown();\n                subject_1.error(err);\n            }, function () { return _this._teardown(); })));\n            if (connection.closed) {\n                this._connection = null;\n                connection = Subscription_1.Subscription.EMPTY;\n            }\n        }\n        return connection;\n    };\n    ConnectableObservable.prototype.refCount = function () {\n        return refCount_1.refCount()(this);\n    };\n    return ConnectableObservable;\n}(Observable_1.Observable));\nexports.ConnectableObservable = ConnectableObservable;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIC,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IACrG,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnB,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAIA,CAAC,KAAK,IAAI,EACrC,MAAM,IAAIS,SAAS,CAAC,sBAAsB,GAAGC,MAAM,CAACV,CAAC,CAAC,GAAG,+BAA+B,CAAC;IAC7FF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASW,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGb,CAAC;IAAE;IACtCA,CAAC,CAACO,SAAS,GAAGN,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACY,MAAM,CAACb,CAAC,CAAC,IAAIW,EAAE,CAACL,SAAS,GAAGN,CAAC,CAACM,SAAS,EAAE,IAAIK,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJV,MAAM,CAACa,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,qBAAqB,GAAG,KAAK,CAAC;AACtC,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIC,cAAc,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAC/C,IAAIE,UAAU,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AACjD,IAAIG,oBAAoB,GAAGH,OAAO,CAAC,iCAAiC,CAAC;AACrE,IAAII,MAAM,GAAGJ,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIF,qBAAqB,GAAI,UAAUO,MAAM,EAAE;EAC3C3B,SAAS,CAACoB,qBAAqB,EAAEO,MAAM,CAAC;EACxC,SAASP,qBAAqBA,CAACQ,MAAM,EAAEC,cAAc,EAAE;IACnD,IAAIC,KAAK,GAAGH,MAAM,CAAChB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCmB,KAAK,CAACF,MAAM,GAAGA,MAAM;IACrBE,KAAK,CAACD,cAAc,GAAGA,cAAc;IACrCC,KAAK,CAACC,QAAQ,GAAG,IAAI;IACrBD,KAAK,CAACE,SAAS,GAAG,CAAC;IACnBF,KAAK,CAACG,WAAW,GAAG,IAAI;IACxB,IAAIP,MAAM,CAACQ,OAAO,CAACN,MAAM,CAAC,EAAE;MACxBE,KAAK,CAACK,IAAI,GAAGP,MAAM,CAACO,IAAI;IAC5B;IACA,OAAOL,KAAK;EAChB;EACAV,qBAAqB,CAACX,SAAS,CAAC2B,UAAU,GAAG,UAAUC,UAAU,EAAE;IAC/D,OAAO,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,SAAS,CAACF,UAAU,CAAC;EAClD,CAAC;EACDjB,qBAAqB,CAACX,SAAS,CAAC6B,UAAU,GAAG,YAAY;IACrD,IAAIE,OAAO,GAAG,IAAI,CAACT,QAAQ;IAC3B,IAAI,CAACS,OAAO,IAAIA,OAAO,CAACC,SAAS,EAAE;MAC/B,IAAI,CAACV,QAAQ,GAAG,IAAI,CAACF,cAAc,CAAC,CAAC;IACzC;IACA,OAAO,IAAI,CAACE,QAAQ;EACxB,CAAC;EACDX,qBAAqB,CAACX,SAAS,CAACiC,SAAS,GAAG,YAAY;IACpD,IAAI,CAACV,SAAS,GAAG,CAAC;IAClB,IAAIC,WAAW,GAAG,IAAI,CAACA,WAAW;IAClC,IAAI,CAACF,QAAQ,GAAG,IAAI,CAACE,WAAW,GAAG,IAAI;IACvCA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACU,WAAW,CAAC,CAAC;EACvF,CAAC;EACDvB,qBAAqB,CAACX,SAAS,CAACmC,OAAO,GAAG,YAAY;IAClD,IAAId,KAAK,GAAG,IAAI;IAChB,IAAIe,UAAU,GAAG,IAAI,CAACZ,WAAW;IACjC,IAAI,CAACY,UAAU,EAAE;MACbA,UAAU,GAAG,IAAI,CAACZ,WAAW,GAAG,IAAIV,cAAc,CAACuB,YAAY,CAAC,CAAC;MACjE,IAAIC,SAAS,GAAG,IAAI,CAACT,UAAU,CAAC,CAAC;MACjCO,UAAU,CAACG,GAAG,CAAC,IAAI,CAACpB,MAAM,CAACW,SAAS,CAACd,oBAAoB,CAACwB,wBAAwB,CAACF,SAAS,EAAEG,SAAS,EAAE,YAAY;QACjHpB,KAAK,CAACY,SAAS,CAAC,CAAC;QACjBK,SAAS,CAACI,QAAQ,CAAC,CAAC;MACxB,CAAC,EAAE,UAAUC,GAAG,EAAE;QACdtB,KAAK,CAACY,SAAS,CAAC,CAAC;QACjBK,SAAS,CAACM,KAAK,CAACD,GAAG,CAAC;MACxB,CAAC,EAAE,YAAY;QAAE,OAAOtB,KAAK,CAACY,SAAS,CAAC,CAAC;MAAE,CAAC,CAAC,CAAC,CAAC;MAC/C,IAAIG,UAAU,CAACS,MAAM,EAAE;QACnB,IAAI,CAACrB,WAAW,GAAG,IAAI;QACvBY,UAAU,GAAGtB,cAAc,CAACuB,YAAY,CAACS,KAAK;MAClD;IACJ;IACA,OAAOV,UAAU;EACrB,CAAC;EACDzB,qBAAqB,CAACX,SAAS,CAAC+C,QAAQ,GAAG,YAAY;IACnD,OAAOhC,UAAU,CAACgC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;EACtC,CAAC;EACD,OAAOpC,qBAAqB;AAChC,CAAC,CAACC,YAAY,CAACoC,UAAU,CAAE;AAC3BvC,OAAO,CAACE,qBAAqB,GAAGA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}