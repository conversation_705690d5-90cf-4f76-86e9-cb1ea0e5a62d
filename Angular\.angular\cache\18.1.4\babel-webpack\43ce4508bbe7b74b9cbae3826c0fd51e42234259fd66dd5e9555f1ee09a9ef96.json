{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.defaultIfEmpty = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction defaultIfEmpty(defaultValue) {\n  return lift_1.operate(function (source, subscriber) {\n    var hasValue = false;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      hasValue = true;\n      subscriber.next(value);\n    }, function () {\n      if (!hasValue) {\n        subscriber.next(defaultValue);\n      }\n      subscriber.complete();\n    }));\n  });\n}\nexports.defaultIfEmpty = defaultIfEmpty;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "defaultIfEmpty", "lift_1", "require", "OperatorSubscriber_1", "defaultValue", "operate", "source", "subscriber", "hasValue", "subscribe", "createOperatorSubscriber", "next", "complete"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/defaultIfEmpty.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.defaultIfEmpty = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction defaultIfEmpty(defaultValue) {\n    return lift_1.operate(function (source, subscriber) {\n        var hasValue = false;\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            hasValue = true;\n            subscriber.next(value);\n        }, function () {\n            if (!hasValue) {\n                subscriber.next(defaultValue);\n            }\n            subscriber.complete();\n        }));\n    });\n}\nexports.defaultIfEmpty = defaultIfEmpty;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAG,KAAK,CAAC;AAC/B,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,cAAcA,CAACI,YAAY,EAAE;EAClC,OAAOH,MAAM,CAACI,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,QAAQ,GAAG,KAAK;IACpBF,MAAM,CAACG,SAAS,CAACN,oBAAoB,CAACO,wBAAwB,CAACH,UAAU,EAAE,UAAUR,KAAK,EAAE;MACxFS,QAAQ,GAAG,IAAI;MACfD,UAAU,CAACI,IAAI,CAACZ,KAAK,CAAC;IAC1B,CAAC,EAAE,YAAY;MACX,IAAI,CAACS,QAAQ,EAAE;QACXD,UAAU,CAACI,IAAI,CAACP,YAAY,CAAC;MACjC;MACAG,UAAU,CAACK,QAAQ,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACAd,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}