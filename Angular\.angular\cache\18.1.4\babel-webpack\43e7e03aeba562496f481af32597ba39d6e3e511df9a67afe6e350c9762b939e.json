{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isEmpty = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction isEmpty() {\n  return lift_1.operate(function (source, subscriber) {\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n      subscriber.next(false);\n      subscriber.complete();\n    }, function () {\n      subscriber.next(true);\n      subscriber.complete();\n    }));\n  });\n}\nexports.isEmpty = isEmpty;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "isEmpty", "lift_1", "require", "OperatorSubscriber_1", "operate", "source", "subscriber", "subscribe", "createOperatorSubscriber", "next", "complete"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/isEmpty.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isEmpty = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction isEmpty() {\n    return lift_1.operate(function (source, subscriber) {\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n            subscriber.next(false);\n            subscriber.complete();\n        }, function () {\n            subscriber.next(true);\n            subscriber.complete();\n        }));\n    });\n}\nexports.isEmpty = isEmpty;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,OAAOA,CAAA,EAAG;EACf,OAAOC,MAAM,CAACG,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChDD,MAAM,CAACE,SAAS,CAACJ,oBAAoB,CAACK,wBAAwB,CAACF,UAAU,EAAE,YAAY;MACnFA,UAAU,CAACG,IAAI,CAAC,KAAK,CAAC;MACtBH,UAAU,CAACI,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAE,YAAY;MACXJ,UAAU,CAACG,IAAI,CAAC,IAAI,CAAC;MACrBH,UAAU,CAACI,QAAQ,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACAZ,OAAO,CAACE,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}