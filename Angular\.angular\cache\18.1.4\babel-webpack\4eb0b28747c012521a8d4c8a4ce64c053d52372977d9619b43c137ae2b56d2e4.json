{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createObject = void 0;\nfunction createObject(keys, values) {\n  return keys.reduce(function (result, key, i) {\n    return result[key] = values[i], result;\n  }, {});\n}\nexports.createObject = createObject;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "createObject", "keys", "values", "reduce", "result", "key", "i"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/createObject.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createObject = void 0;\nfunction createObject(keys, values) {\n    return keys.reduce(function (result, key, i) { return ((result[key] = values[i]), result); }, {});\n}\nexports.createObject = createObject;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,YAAY,GAAG,KAAK,CAAC;AAC7B,SAASA,YAAYA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAChC,OAAOD,IAAI,CAACE,MAAM,CAAC,UAAUC,MAAM,EAAEC,GAAG,EAAEC,CAAC,EAAE;IAAE,OAASF,MAAM,CAACC,GAAG,CAAC,GAAGH,MAAM,CAACI,CAAC,CAAC,EAAGF,MAAM;EAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AACrG;AACAN,OAAO,CAACE,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}