{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.audit = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction audit(durationSelector) {\n  return lift_1.operate(function (source, subscriber) {\n    var hasValue = false;\n    var lastValue = null;\n    var durationSubscriber = null;\n    var isComplete = false;\n    var endDuration = function () {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      durationSubscriber = null;\n      if (hasValue) {\n        hasValue = false;\n        var value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n      isComplete && subscriber.complete();\n    };\n    var cleanupDuration = function () {\n      durationSubscriber = null;\n      isComplete && subscriber.complete();\n    };\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      hasValue = true;\n      lastValue = value;\n      if (!durationSubscriber) {\n        innerFrom_1.innerFrom(durationSelector(value)).subscribe(durationSubscriber = OperatorSubscriber_1.createOperatorSubscriber(subscriber, endDuration, cleanupDuration));\n      }\n    }, function () {\n      isComplete = true;\n      (!hasValue || !durationSubscriber || durationSubscriber.closed) && subscriber.complete();\n    }));\n  });\n}\nexports.audit = audit;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "audit", "lift_1", "require", "innerFrom_1", "OperatorSubscriber_1", "durationSelector", "operate", "source", "subscriber", "hasValue", "lastValue", "durationSubscriber", "isComplete", "endDuration", "unsubscribe", "next", "complete", "cleanupDuration", "subscribe", "createOperatorSubscriber", "innerFrom", "closed"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/audit.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.audit = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction audit(durationSelector) {\n    return lift_1.operate(function (source, subscriber) {\n        var hasValue = false;\n        var lastValue = null;\n        var durationSubscriber = null;\n        var isComplete = false;\n        var endDuration = function () {\n            durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n            durationSubscriber = null;\n            if (hasValue) {\n                hasValue = false;\n                var value = lastValue;\n                lastValue = null;\n                subscriber.next(value);\n            }\n            isComplete && subscriber.complete();\n        };\n        var cleanupDuration = function () {\n            durationSubscriber = null;\n            isComplete && subscriber.complete();\n        };\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            hasValue = true;\n            lastValue = value;\n            if (!durationSubscriber) {\n                innerFrom_1.innerFrom(durationSelector(value)).subscribe((durationSubscriber = OperatorSubscriber_1.createOperatorSubscriber(subscriber, endDuration, cleanupDuration)));\n            }\n        }, function () {\n            isComplete = true;\n            (!hasValue || !durationSubscriber || durationSubscriber.closed) && subscriber.complete();\n        }));\n    });\n}\nexports.audit = audit;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,KAAK,GAAG,KAAK,CAAC;AACtB,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,WAAW,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AACpD,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,KAAKA,CAACK,gBAAgB,EAAE;EAC7B,OAAOJ,MAAM,CAACK,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,kBAAkB,GAAG,IAAI;IAC7B,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAIC,WAAW,GAAG,SAAAA,CAAA,EAAY;MAC1BF,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACG,WAAW,CAAC,CAAC;MACxGH,kBAAkB,GAAG,IAAI;MACzB,IAAIF,QAAQ,EAAE;QACVA,QAAQ,GAAG,KAAK;QAChB,IAAIV,KAAK,GAAGW,SAAS;QACrBA,SAAS,GAAG,IAAI;QAChBF,UAAU,CAACO,IAAI,CAAChB,KAAK,CAAC;MAC1B;MACAa,UAAU,IAAIJ,UAAU,CAACQ,QAAQ,CAAC,CAAC;IACvC,CAAC;IACD,IAAIC,eAAe,GAAG,SAAAA,CAAA,EAAY;MAC9BN,kBAAkB,GAAG,IAAI;MACzBC,UAAU,IAAIJ,UAAU,CAACQ,QAAQ,CAAC,CAAC;IACvC,CAAC;IACDT,MAAM,CAACW,SAAS,CAACd,oBAAoB,CAACe,wBAAwB,CAACX,UAAU,EAAE,UAAUT,KAAK,EAAE;MACxFU,QAAQ,GAAG,IAAI;MACfC,SAAS,GAAGX,KAAK;MACjB,IAAI,CAACY,kBAAkB,EAAE;QACrBR,WAAW,CAACiB,SAAS,CAACf,gBAAgB,CAACN,KAAK,CAAC,CAAC,CAACmB,SAAS,CAAEP,kBAAkB,GAAGP,oBAAoB,CAACe,wBAAwB,CAACX,UAAU,EAAEK,WAAW,EAAEI,eAAe,CAAE,CAAC;MAC5K;IACJ,CAAC,EAAE,YAAY;MACXL,UAAU,GAAG,IAAI;MACjB,CAAC,CAACH,QAAQ,IAAI,CAACE,kBAAkB,IAAIA,kBAAkB,CAACU,MAAM,KAAKb,UAAU,CAACQ,QAAQ,CAAC,CAAC;IAC5F,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACAlB,OAAO,CAACE,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}