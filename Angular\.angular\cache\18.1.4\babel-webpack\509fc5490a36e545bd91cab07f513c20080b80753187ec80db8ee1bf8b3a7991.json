{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.retry = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar identity_1 = require(\"../util/identity\");\nvar timer_1 = require(\"../observable/timer\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction retry(configOrCount) {\n  if (configOrCount === void 0) {\n    configOrCount = Infinity;\n  }\n  var config;\n  if (configOrCount && typeof configOrCount === 'object') {\n    config = configOrCount;\n  } else {\n    config = {\n      count: configOrCount\n    };\n  }\n  var _a = config.count,\n    count = _a === void 0 ? Infinity : _a,\n    delay = config.delay,\n    _b = config.resetOnSuccess,\n    resetOnSuccess = _b === void 0 ? false : _b;\n  return count <= 0 ? identity_1.identity : lift_1.operate(function (source, subscriber) {\n    var soFar = 0;\n    var innerSub;\n    var subscribeForRetry = function () {\n      var syncUnsub = false;\n      innerSub = source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n        if (resetOnSuccess) {\n          soFar = 0;\n        }\n        subscriber.next(value);\n      }, undefined, function (err) {\n        if (soFar++ < count) {\n          var resub_1 = function () {\n            if (innerSub) {\n              innerSub.unsubscribe();\n              innerSub = null;\n              subscribeForRetry();\n            } else {\n              syncUnsub = true;\n            }\n          };\n          if (delay != null) {\n            var notifier = typeof delay === 'number' ? timer_1.timer(delay) : innerFrom_1.innerFrom(delay(err, soFar));\n            var notifierSubscriber_1 = OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n              notifierSubscriber_1.unsubscribe();\n              resub_1();\n            }, function () {\n              subscriber.complete();\n            });\n            notifier.subscribe(notifierSubscriber_1);\n          } else {\n            resub_1();\n          }\n        } else {\n          subscriber.error(err);\n        }\n      }));\n      if (syncUnsub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        subscribeForRetry();\n      }\n    };\n    subscribeForRetry();\n  });\n}\nexports.retry = retry;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "retry", "lift_1", "require", "OperatorSubscriber_1", "identity_1", "timer_1", "innerFrom_1", "config<PERSON>r<PERSON>ount", "Infinity", "config", "count", "_a", "delay", "_b", "resetOnSuccess", "identity", "operate", "source", "subscriber", "soFar", "innerSub", "subscribeForRetry", "syncUnsub", "subscribe", "createOperatorSubscriber", "next", "undefined", "err", "resub_1", "unsubscribe", "notifier", "timer", "innerFrom", "notifierSubscriber_1", "complete", "error"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/retry.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.retry = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar identity_1 = require(\"../util/identity\");\nvar timer_1 = require(\"../observable/timer\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction retry(configOrCount) {\n    if (configOrCount === void 0) { configOrCount = Infinity; }\n    var config;\n    if (configOrCount && typeof configOrCount === 'object') {\n        config = configOrCount;\n    }\n    else {\n        config = {\n            count: configOrCount,\n        };\n    }\n    var _a = config.count, count = _a === void 0 ? Infinity : _a, delay = config.delay, _b = config.resetOnSuccess, resetOnSuccess = _b === void 0 ? false : _b;\n    return count <= 0\n        ? identity_1.identity\n        : lift_1.operate(function (source, subscriber) {\n            var soFar = 0;\n            var innerSub;\n            var subscribeForRetry = function () {\n                var syncUnsub = false;\n                innerSub = source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n                    if (resetOnSuccess) {\n                        soFar = 0;\n                    }\n                    subscriber.next(value);\n                }, undefined, function (err) {\n                    if (soFar++ < count) {\n                        var resub_1 = function () {\n                            if (innerSub) {\n                                innerSub.unsubscribe();\n                                innerSub = null;\n                                subscribeForRetry();\n                            }\n                            else {\n                                syncUnsub = true;\n                            }\n                        };\n                        if (delay != null) {\n                            var notifier = typeof delay === 'number' ? timer_1.timer(delay) : innerFrom_1.innerFrom(delay(err, soFar));\n                            var notifierSubscriber_1 = OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n                                notifierSubscriber_1.unsubscribe();\n                                resub_1();\n                            }, function () {\n                                subscriber.complete();\n                            });\n                            notifier.subscribe(notifierSubscriber_1);\n                        }\n                        else {\n                            resub_1();\n                        }\n                    }\n                    else {\n                        subscriber.error(err);\n                    }\n                }));\n                if (syncUnsub) {\n                    innerSub.unsubscribe();\n                    innerSub = null;\n                    subscribeForRetry();\n                }\n            };\n            subscribeForRetry();\n        });\n}\nexports.retry = retry;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,KAAK,GAAG,KAAK,CAAC;AACtB,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIE,UAAU,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AAC5C,IAAIG,OAAO,GAAGH,OAAO,CAAC,qBAAqB,CAAC;AAC5C,IAAII,WAAW,GAAGJ,OAAO,CAAC,yBAAyB,CAAC;AACpD,SAASF,KAAKA,CAACO,aAAa,EAAE;EAC1B,IAAIA,aAAa,KAAK,KAAK,CAAC,EAAE;IAAEA,aAAa,GAAGC,QAAQ;EAAE;EAC1D,IAAIC,MAAM;EACV,IAAIF,aAAa,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;IACpDE,MAAM,GAAGF,aAAa;EAC1B,CAAC,MACI;IACDE,MAAM,GAAG;MACLC,KAAK,EAAEH;IACX,CAAC;EACL;EACA,IAAII,EAAE,GAAGF,MAAM,CAACC,KAAK;IAAEA,KAAK,GAAGC,EAAE,KAAK,KAAK,CAAC,GAAGH,QAAQ,GAAGG,EAAE;IAAEC,KAAK,GAAGH,MAAM,CAACG,KAAK;IAAEC,EAAE,GAAGJ,MAAM,CAACK,cAAc;IAAEA,cAAc,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;EAC3J,OAAOH,KAAK,IAAI,CAAC,GACXN,UAAU,CAACW,QAAQ,GACnBd,MAAM,CAACe,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAC3C,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,QAAQ;IACZ,IAAIC,iBAAiB,GAAG,SAAAA,CAAA,EAAY;MAChC,IAAIC,SAAS,GAAG,KAAK;MACrBF,QAAQ,GAAGH,MAAM,CAACM,SAAS,CAACpB,oBAAoB,CAACqB,wBAAwB,CAACN,UAAU,EAAE,UAAUnB,KAAK,EAAE;QACnG,IAAIe,cAAc,EAAE;UAChBK,KAAK,GAAG,CAAC;QACb;QACAD,UAAU,CAACO,IAAI,CAAC1B,KAAK,CAAC;MAC1B,CAAC,EAAE2B,SAAS,EAAE,UAAUC,GAAG,EAAE;QACzB,IAAIR,KAAK,EAAE,GAAGT,KAAK,EAAE;UACjB,IAAIkB,OAAO,GAAG,SAAAA,CAAA,EAAY;YACtB,IAAIR,QAAQ,EAAE;cACVA,QAAQ,CAACS,WAAW,CAAC,CAAC;cACtBT,QAAQ,GAAG,IAAI;cACfC,iBAAiB,CAAC,CAAC;YACvB,CAAC,MACI;cACDC,SAAS,GAAG,IAAI;YACpB;UACJ,CAAC;UACD,IAAIV,KAAK,IAAI,IAAI,EAAE;YACf,IAAIkB,QAAQ,GAAG,OAAOlB,KAAK,KAAK,QAAQ,GAAGP,OAAO,CAAC0B,KAAK,CAACnB,KAAK,CAAC,GAAGN,WAAW,CAAC0B,SAAS,CAACpB,KAAK,CAACe,GAAG,EAAER,KAAK,CAAC,CAAC;YAC1G,IAAIc,oBAAoB,GAAG9B,oBAAoB,CAACqB,wBAAwB,CAACN,UAAU,EAAE,YAAY;cAC7Fe,oBAAoB,CAACJ,WAAW,CAAC,CAAC;cAClCD,OAAO,CAAC,CAAC;YACb,CAAC,EAAE,YAAY;cACXV,UAAU,CAACgB,QAAQ,CAAC,CAAC;YACzB,CAAC,CAAC;YACFJ,QAAQ,CAACP,SAAS,CAACU,oBAAoB,CAAC;UAC5C,CAAC,MACI;YACDL,OAAO,CAAC,CAAC;UACb;QACJ,CAAC,MACI;UACDV,UAAU,CAACiB,KAAK,CAACR,GAAG,CAAC;QACzB;MACJ,CAAC,CAAC,CAAC;MACH,IAAIL,SAAS,EAAE;QACXF,QAAQ,CAACS,WAAW,CAAC,CAAC;QACtBT,QAAQ,GAAG,IAAI;QACfC,iBAAiB,CAAC,CAAC;MACvB;IACJ,CAAC;IACDA,iBAAiB,CAAC,CAAC;EACvB,CAAC,CAAC;AACV;AACAvB,OAAO,CAACE,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}