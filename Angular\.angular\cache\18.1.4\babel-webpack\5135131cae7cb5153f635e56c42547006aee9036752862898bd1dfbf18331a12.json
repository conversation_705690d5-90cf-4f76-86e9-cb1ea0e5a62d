{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.iif = void 0;\nvar defer_1 = require(\"./defer\");\nfunction iif(condition, trueResult, falseResult) {\n  return defer_1.defer(function () {\n    return condition() ? trueResult : falseResult;\n  });\n}\nexports.iif = iif;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "iif", "defer_1", "require", "condition", "trueResult", "falseResult", "defer"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/iif.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.iif = void 0;\nvar defer_1 = require(\"./defer\");\nfunction iif(condition, trueResult, falseResult) {\n    return defer_1.defer(function () { return (condition() ? trueResult : falseResult); });\n}\nexports.iif = iif;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,GAAG,GAAG,KAAK,CAAC;AACpB,IAAIC,OAAO,GAAGC,OAAO,CAAC,SAAS,CAAC;AAChC,SAASF,GAAGA,CAACG,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAE;EAC7C,OAAOJ,OAAO,CAACK,KAAK,CAAC,YAAY;IAAE,OAAQH,SAAS,CAAC,CAAC,GAAGC,UAAU,GAAGC,WAAW;EAAG,CAAC,CAAC;AAC1F;AACAP,OAAO,CAACE,GAAG,GAAGA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}