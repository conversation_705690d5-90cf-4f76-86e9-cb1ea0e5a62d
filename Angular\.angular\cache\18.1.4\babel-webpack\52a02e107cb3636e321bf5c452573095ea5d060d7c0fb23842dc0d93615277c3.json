{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Scheduler = void 0;\nvar dateTimestampProvider_1 = require(\"./scheduler/dateTimestampProvider\");\nvar Scheduler = function () {\n  function Scheduler(schedulerActionCtor, now) {\n    if (now === void 0) {\n      now = Scheduler.now;\n    }\n    this.schedulerActionCtor = schedulerActionCtor;\n    this.now = now;\n  }\n  Scheduler.prototype.schedule = function (work, delay, state) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    return new this.schedulerActionCtor(this, work).schedule(state, delay);\n  };\n  Scheduler.now = dateTimestampProvider_1.dateTimestampProvider.now;\n  return Scheduler;\n}();\nexports.Scheduler = Scheduler;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Scheduler", "dateTimestampProvider_1", "require", "schedulerActionCtor", "now", "prototype", "schedule", "work", "delay", "state", "dateTimestampProvider"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/Scheduler.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Scheduler = void 0;\nvar dateTimestampProvider_1 = require(\"./scheduler/dateTimestampProvider\");\nvar Scheduler = (function () {\n    function Scheduler(schedulerActionCtor, now) {\n        if (now === void 0) { now = Scheduler.now; }\n        this.schedulerActionCtor = schedulerActionCtor;\n        this.now = now;\n    }\n    Scheduler.prototype.schedule = function (work, delay, state) {\n        if (delay === void 0) { delay = 0; }\n        return new this.schedulerActionCtor(this, work).schedule(state, delay);\n    };\n    Scheduler.now = dateTimestampProvider_1.dateTimestampProvider.now;\n    return Scheduler;\n}());\nexports.Scheduler = Scheduler;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,uBAAuB,GAAGC,OAAO,CAAC,mCAAmC,CAAC;AAC1E,IAAIF,SAAS,GAAI,YAAY;EACzB,SAASA,SAASA,CAACG,mBAAmB,EAAEC,GAAG,EAAE;IACzC,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;MAAEA,GAAG,GAAGJ,SAAS,CAACI,GAAG;IAAE;IAC3C,IAAI,CAACD,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;EACAJ,SAAS,CAACK,SAAS,CAACC,QAAQ,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAE;IACzD,IAAID,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,OAAO,IAAI,IAAI,CAACL,mBAAmB,CAAC,IAAI,EAAEI,IAAI,CAAC,CAACD,QAAQ,CAACG,KAAK,EAAED,KAAK,CAAC;EAC1E,CAAC;EACDR,SAAS,CAACI,GAAG,GAAGH,uBAAuB,CAACS,qBAAqB,CAACN,GAAG;EACjE,OAAOJ,SAAS;AACpB,CAAC,CAAC,CAAE;AACJF,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}