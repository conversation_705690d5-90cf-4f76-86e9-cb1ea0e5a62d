{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.never = exports.NEVER = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar noop_1 = require(\"../util/noop\");\nexports.NEVER = new Observable_1.Observable(noop_1.noop);\nfunction never() {\n  return exports.NEVER;\n}\nexports.never = never;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "never", "NEVER", "Observable_1", "require", "noop_1", "Observable", "noop"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/never.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.never = exports.NEVER = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar noop_1 = require(\"../util/noop\");\nexports.NEVER = new Observable_1.Observable(noop_1.noop);\nfunction never() {\n    return exports.NEVER;\n}\nexports.never = never;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,KAAK,GAAGF,OAAO,CAACG,KAAK,GAAG,KAAK,CAAC;AACtC,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpCL,OAAO,CAACG,KAAK,GAAG,IAAIC,YAAY,CAACG,UAAU,CAACD,MAAM,CAACE,IAAI,CAAC;AACxD,SAASN,KAAKA,CAAA,EAAG;EACb,OAAOF,OAAO,CAACG,KAAK;AACxB;AACAH,OAAO,CAACE,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}