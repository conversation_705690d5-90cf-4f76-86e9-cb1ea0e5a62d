{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.identity = void 0;\nfunction identity(x) {\n  return x;\n}\nexports.identity = identity;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "identity", "x"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/identity.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.identity = void 0;\nfunction identity(x) {\n    return x;\n}\nexports.identity = identity;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,QAAQ,GAAG,KAAK,CAAC;AACzB,SAASA,QAAQA,CAACC,CAAC,EAAE;EACjB,OAAOA,CAAC;AACZ;AACAH,OAAO,CAACE,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}