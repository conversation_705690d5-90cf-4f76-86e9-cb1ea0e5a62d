{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.tap = void 0;\nvar isFunction_1 = require(\"../util/isFunction\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar identity_1 = require(\"../util/identity\");\nfunction tap(observerOrNext, error, complete) {\n  var tapObserver = isFunction_1.isFunction(observerOrNext) || error || complete ? {\n    next: observerOrNext,\n    error: error,\n    complete: complete\n  } : observerOrNext;\n  return tapObserver ? lift_1.operate(function (source, subscriber) {\n    var _a;\n    (_a = tapObserver.subscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n    var isUnsub = true;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var _a;\n      (_a = tapObserver.next) === null || _a === void 0 ? void 0 : _a.call(tapObserver, value);\n      subscriber.next(value);\n    }, function () {\n      var _a;\n      isUnsub = false;\n      (_a = tapObserver.complete) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n      subscriber.complete();\n    }, function (err) {\n      var _a;\n      isUnsub = false;\n      (_a = tapObserver.error) === null || _a === void 0 ? void 0 : _a.call(tapObserver, err);\n      subscriber.error(err);\n    }, function () {\n      var _a, _b;\n      if (isUnsub) {\n        (_a = tapObserver.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n      }\n      (_b = tapObserver.finalize) === null || _b === void 0 ? void 0 : _b.call(tapObserver);\n    }));\n  }) : identity_1.identity;\n}\nexports.tap = tap;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "tap", "isFunction_1", "require", "lift_1", "OperatorSubscriber_1", "identity_1", "observerOrNext", "error", "complete", "tapObserver", "isFunction", "next", "operate", "source", "subscriber", "_a", "subscribe", "call", "isUnsub", "createOperatorSubscriber", "err", "_b", "unsubscribe", "finalize", "identity"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/tap.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.tap = void 0;\nvar isFunction_1 = require(\"../util/isFunction\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar identity_1 = require(\"../util/identity\");\nfunction tap(observerOrNext, error, complete) {\n    var tapObserver = isFunction_1.isFunction(observerOrNext) || error || complete\n        ?\n            { next: observerOrNext, error: error, complete: complete }\n        : observerOrNext;\n    return tapObserver\n        ? lift_1.operate(function (source, subscriber) {\n            var _a;\n            (_a = tapObserver.subscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n            var isUnsub = true;\n            source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n                var _a;\n                (_a = tapObserver.next) === null || _a === void 0 ? void 0 : _a.call(tapObserver, value);\n                subscriber.next(value);\n            }, function () {\n                var _a;\n                isUnsub = false;\n                (_a = tapObserver.complete) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n                subscriber.complete();\n            }, function (err) {\n                var _a;\n                isUnsub = false;\n                (_a = tapObserver.error) === null || _a === void 0 ? void 0 : _a.call(tapObserver, err);\n                subscriber.error(err);\n            }, function () {\n                var _a, _b;\n                if (isUnsub) {\n                    (_a = tapObserver.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n                }\n                (_b = tapObserver.finalize) === null || _b === void 0 ? void 0 : _b.call(tapObserver);\n            }));\n        })\n        :\n            identity_1.identity;\n}\nexports.tap = tap;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,GAAG,GAAG,KAAK,CAAC;AACpB,IAAIC,YAAY,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAChD,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIG,UAAU,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AAC5C,SAASF,GAAGA,CAACM,cAAc,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EAC1C,IAAIC,WAAW,GAAGR,YAAY,CAACS,UAAU,CAACJ,cAAc,CAAC,IAAIC,KAAK,IAAIC,QAAQ,GAEtE;IAAEG,IAAI,EAAEL,cAAc;IAAEC,KAAK,EAAEA,KAAK;IAAEC,QAAQ,EAAEA;EAAS,CAAC,GAC5DF,cAAc;EACpB,OAAOG,WAAW,GACZN,MAAM,CAACS,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAC3C,IAAIC,EAAE;IACN,CAACA,EAAE,GAAGN,WAAW,CAACO,SAAS,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACR,WAAW,CAAC;IACtF,IAAIS,OAAO,GAAG,IAAI;IAClBL,MAAM,CAACG,SAAS,CAACZ,oBAAoB,CAACe,wBAAwB,CAACL,UAAU,EAAE,UAAUf,KAAK,EAAE;MACxF,IAAIgB,EAAE;MACN,CAACA,EAAE,GAAGN,WAAW,CAACE,IAAI,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACR,WAAW,EAAEV,KAAK,CAAC;MACxFe,UAAU,CAACH,IAAI,CAACZ,KAAK,CAAC;IAC1B,CAAC,EAAE,YAAY;MACX,IAAIgB,EAAE;MACNG,OAAO,GAAG,KAAK;MACf,CAACH,EAAE,GAAGN,WAAW,CAACD,QAAQ,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACR,WAAW,CAAC;MACrFK,UAAU,CAACN,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAE,UAAUY,GAAG,EAAE;MACd,IAAIL,EAAE;MACNG,OAAO,GAAG,KAAK;MACf,CAACH,EAAE,GAAGN,WAAW,CAACF,KAAK,MAAM,IAAI,IAAIQ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACR,WAAW,EAAEW,GAAG,CAAC;MACvFN,UAAU,CAACP,KAAK,CAACa,GAAG,CAAC;IACzB,CAAC,EAAE,YAAY;MACX,IAAIL,EAAE,EAAEM,EAAE;MACV,IAAIH,OAAO,EAAE;QACT,CAACH,EAAE,GAAGN,WAAW,CAACa,WAAW,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACR,WAAW,CAAC;MAC5F;MACA,CAACY,EAAE,GAAGZ,WAAW,CAACc,QAAQ,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACJ,IAAI,CAACR,WAAW,CAAC;IACzF,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,GAEEJ,UAAU,CAACmB,QAAQ;AAC/B;AACA1B,OAAO,CAACE,GAAG,GAAGA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}