{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.last = void 0;\nvar EmptyError_1 = require(\"../util/EmptyError\");\nvar filter_1 = require(\"./filter\");\nvar takeLast_1 = require(\"./takeLast\");\nvar throwIfEmpty_1 = require(\"./throwIfEmpty\");\nvar defaultIfEmpty_1 = require(\"./defaultIfEmpty\");\nvar identity_1 = require(\"../util/identity\");\nfunction last(predicate, defaultValue) {\n  var hasDefaultValue = arguments.length >= 2;\n  return function (source) {\n    return source.pipe(predicate ? filter_1.filter(function (v, i) {\n      return predicate(v, i, source);\n    }) : identity_1.identity, takeLast_1.takeLast(1), hasDefaultValue ? defaultIfEmpty_1.defaultIfEmpty(defaultValue) : throwIfEmpty_1.throwIfEmpty(function () {\n      return new EmptyError_1.EmptyError();\n    }));\n  };\n}\nexports.last = last;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "last", "EmptyError_1", "require", "filter_1", "takeLast_1", "throwIfEmpty_1", "defaultIfEmpty_1", "identity_1", "predicate", "defaultValue", "hasDefaultValue", "arguments", "length", "source", "pipe", "filter", "v", "i", "identity", "takeLast", "defaultIfEmpty", "throwIfEmpty", "EmptyError"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/last.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.last = void 0;\nvar EmptyError_1 = require(\"../util/EmptyError\");\nvar filter_1 = require(\"./filter\");\nvar takeLast_1 = require(\"./takeLast\");\nvar throwIfEmpty_1 = require(\"./throwIfEmpty\");\nvar defaultIfEmpty_1 = require(\"./defaultIfEmpty\");\nvar identity_1 = require(\"../util/identity\");\nfunction last(predicate, defaultValue) {\n    var hasDefaultValue = arguments.length >= 2;\n    return function (source) {\n        return source.pipe(predicate ? filter_1.filter(function (v, i) { return predicate(v, i, source); }) : identity_1.identity, takeLast_1.takeLast(1), hasDefaultValue ? defaultIfEmpty_1.defaultIfEmpty(defaultValue) : throwIfEmpty_1.throwIfEmpty(function () { return new EmptyError_1.EmptyError(); }));\n    };\n}\nexports.last = last;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,IAAI,GAAG,KAAK,CAAC;AACrB,IAAIC,YAAY,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAChD,IAAIC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;AAClC,IAAIE,UAAU,GAAGF,OAAO,CAAC,YAAY,CAAC;AACtC,IAAIG,cAAc,GAAGH,OAAO,CAAC,gBAAgB,CAAC;AAC9C,IAAII,gBAAgB,GAAGJ,OAAO,CAAC,kBAAkB,CAAC;AAClD,IAAIK,UAAU,GAAGL,OAAO,CAAC,kBAAkB,CAAC;AAC5C,SAASF,IAAIA,CAACQ,SAAS,EAAEC,YAAY,EAAE;EACnC,IAAIC,eAAe,GAAGC,SAAS,CAACC,MAAM,IAAI,CAAC;EAC3C,OAAO,UAAUC,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACC,IAAI,CAACN,SAAS,GAAGL,QAAQ,CAACY,MAAM,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MAAE,OAAOT,SAAS,CAACQ,CAAC,EAAEC,CAAC,EAAEJ,MAAM,CAAC;IAAE,CAAC,CAAC,GAAGN,UAAU,CAACW,QAAQ,EAAEd,UAAU,CAACe,QAAQ,CAAC,CAAC,CAAC,EAAET,eAAe,GAAGJ,gBAAgB,CAACc,cAAc,CAACX,YAAY,CAAC,GAAGJ,cAAc,CAACgB,YAAY,CAAC,YAAY;MAAE,OAAO,IAAIpB,YAAY,CAACqB,UAAU,CAAC,CAAC;IAAE,CAAC,CAAC,CAAC;EAC5S,CAAC;AACL;AACAxB,OAAO,CAACE,IAAI,GAAGA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}