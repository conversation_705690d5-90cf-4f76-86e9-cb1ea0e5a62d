{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.performanceTimestampProvider = void 0;\nexports.performanceTimestampProvider = {\n  now: function () {\n    return (exports.performanceTimestampProvider.delegate || performance).now();\n  },\n  delegate: undefined\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "performanceTimestampProvider", "now", "delegate", "performance", "undefined"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/scheduler/performanceTimestampProvider.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.performanceTimestampProvider = void 0;\nexports.performanceTimestampProvider = {\n    now: function () {\n        return (exports.performanceTimestampProvider.delegate || performance).now();\n    },\n    delegate: undefined,\n};\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,4BAA4B,GAAG,KAAK,CAAC;AAC7CF,OAAO,CAACE,4BAA4B,GAAG;EACnCC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACb,OAAO,CAACH,OAAO,CAACE,4BAA4B,CAACE,QAAQ,IAAIC,WAAW,EAAEF,GAAG,CAAC,CAAC;EAC/E,CAAC;EACDC,QAAQ,EAAEE;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}