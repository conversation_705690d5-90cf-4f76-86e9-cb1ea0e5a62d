{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.SequenceError = void 0;\nvar createErrorClass_1 = require(\"./createErrorClass\");\nexports.SequenceError = createErrorClass_1.createErrorClass(function (_super) {\n  return function SequenceErrorImpl(message) {\n    _super(this);\n    this.name = 'SequenceError';\n    this.message = message;\n  };\n});", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "SequenceError", "createErrorClass_1", "require", "createErrorClass", "_super", "SequenceErrorImpl", "message", "name"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/SequenceError.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SequenceError = void 0;\nvar createErrorClass_1 = require(\"./createErrorClass\");\nexports.SequenceError = createErrorClass_1.createErrorClass(function (_super) {\n    return function SequenceErrorImpl(message) {\n        _super(this);\n        this.name = 'SequenceError';\n        this.message = message;\n    };\n});\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,aAAa,GAAG,KAAK,CAAC;AAC9B,IAAIC,kBAAkB,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACtDJ,OAAO,CAACE,aAAa,GAAGC,kBAAkB,CAACE,gBAAgB,CAAC,UAAUC,MAAM,EAAE;EAC1E,OAAO,SAASC,iBAAiBA,CAACC,OAAO,EAAE;IACvCF,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACG,IAAI,GAAG,eAAe;IAC3B,IAAI,CAACD,OAAO,GAAGA,OAAO;EAC1B,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}