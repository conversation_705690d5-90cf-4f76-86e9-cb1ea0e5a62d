{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.observable = void 0;\nexports.observable = function () {\n  return typeof Symbol === 'function' && Symbol.observable || '@@observable';\n}();", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "observable", "Symbol"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/symbol/observable.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.observable = void 0;\nexports.observable = (function () { return (typeof Symbol === 'function' && Symbol.observable) || '@@observable'; })();\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,UAAU,GAAG,KAAK,CAAC;AAC3BF,OAAO,CAACE,UAAU,GAAI,YAAY;EAAE,OAAQ,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACD,UAAU,IAAK,cAAc;AAAE,CAAC,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}