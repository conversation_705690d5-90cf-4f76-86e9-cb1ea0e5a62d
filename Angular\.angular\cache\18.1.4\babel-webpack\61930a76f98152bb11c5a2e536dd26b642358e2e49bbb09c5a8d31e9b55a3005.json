{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.startWith = void 0;\nvar concat_1 = require(\"../observable/concat\");\nvar args_1 = require(\"../util/args\");\nvar lift_1 = require(\"../util/lift\");\nfunction startWith() {\n  var values = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    values[_i] = arguments[_i];\n  }\n  var scheduler = args_1.popScheduler(values);\n  return lift_1.operate(function (source, subscriber) {\n    (scheduler ? concat_1.concat(values, source, scheduler) : concat_1.concat(values, source)).subscribe(subscriber);\n  });\n}\nexports.startWith = startWith;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "startWith", "concat_1", "require", "args_1", "lift_1", "values", "_i", "arguments", "length", "scheduler", "popScheduler", "operate", "source", "subscriber", "concat", "subscribe"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/startWith.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.startWith = void 0;\nvar concat_1 = require(\"../observable/concat\");\nvar args_1 = require(\"../util/args\");\nvar lift_1 = require(\"../util/lift\");\nfunction startWith() {\n    var values = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        values[_i] = arguments[_i];\n    }\n    var scheduler = args_1.popScheduler(values);\n    return lift_1.operate(function (source, subscriber) {\n        (scheduler ? concat_1.concat(values, source, scheduler) : concat_1.concat(values, source)).subscribe(subscriber);\n    });\n}\nexports.startWith = startWith;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,QAAQ,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC9C,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,MAAM,GAAGF,OAAO,CAAC,cAAc,CAAC;AACpC,SAASF,SAASA,CAAA,EAAG;EACjB,IAAIK,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,MAAM,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC9B;EACA,IAAIG,SAAS,GAAGN,MAAM,CAACO,YAAY,CAACL,MAAM,CAAC;EAC3C,OAAOD,MAAM,CAACO,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,CAACJ,SAAS,GAAGR,QAAQ,CAACa,MAAM,CAACT,MAAM,EAAEO,MAAM,EAAEH,SAAS,CAAC,GAAGR,QAAQ,CAACa,MAAM,CAACT,MAAM,EAAEO,MAAM,CAAC,EAAEG,SAAS,CAACF,UAAU,CAAC;EACpH,CAAC,CAAC;AACN;AACAf,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}