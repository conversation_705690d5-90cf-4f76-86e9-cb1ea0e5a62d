{"ast": null, "code": "import _asyncToGenerator from \"D:/permittracker/Angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"../../services/permits.service\";\nimport * as i4 from \"../../services/custom-layout.utils.service\";\nimport * as i5 from \"../../services/http-utils.service\";\nimport * as i6 from \"@angular/common\";\nfunction AddEditInternalReviewDetailComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Review Detail - \", ctx_r1.reviewCategory || \"\", \"\");\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Add Review Detail - \", ctx_r1.reviewCategory || \"\", \"\");\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_27_tr_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditInternalReviewDetailComponent_div_27_tr_29_Template_input_ngModelChange_2_listener($event) {\n      const r_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      i0.ɵɵtwoWayBindingSet(r_r6.sheetNumber, $event) || (r_r6.sheetNumber = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"input\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditInternalReviewDetailComponent_div_27_tr_29_Template_input_ngModelChange_4_listener($event) {\n      const r_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      i0.ɵɵtwoWayBindingSet(r_r6.codeRef, $event) || (r_r6.codeRef = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\")(6, \"input\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditInternalReviewDetailComponent_div_27_tr_29_Template_input_ngModelChange_6_listener($event) {\n      const r_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      i0.ɵɵtwoWayBindingSet(r_r6.codeDescription, $event) || (r_r6.codeDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"input\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditInternalReviewDetailComponent_div_27_tr_29_Template_input_ngModelChange_8_listener($event) {\n      const r_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      i0.ɵɵtwoWayBindingSet(r_r6.reasoning, $event) || (r_r6.reasoning = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\")(10, \"input\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditInternalReviewDetailComponent_div_27_tr_29_Template_input_ngModelChange_10_listener($event) {\n      const r_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      i0.ɵɵtwoWayBindingSet(r_r6.nonCompliance, $event) || (r_r6.nonCompliance = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\")(12, \"input\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditInternalReviewDetailComponent_div_27_tr_29_Template_input_ngModelChange_12_listener($event) {\n      const r_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      i0.ɵɵtwoWayBindingSet(r_r6.actionableStep, $event) || (r_r6.actionableStep = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\", 30)(14, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_div_27_tr_29_Template_button_click_14_listener() {\n      const i_r7 = i0.ɵɵrestoreView(_r5).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteImportedRow(i_r7));\n    });\n    i0.ɵɵelement(15, \"i\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const r_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"sheetNumber\", i_r7, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", r_r6.sheetNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"codeRef\", i_r7, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", r_r6.codeRef);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"codeDescription\", i_r7, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", r_r6.codeDescription);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"reasoning\", i_r7, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", r_r6.reasoning);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"nonCompliance\", i_r7, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", r_r6.nonCompliance);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"actionableStep\", i_r7, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", r_r6.actionableStep);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"h6\", 20);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\")(5, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_div_27_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addImportedRow());\n    });\n    i0.ɵɵelement(6, \"i\", 22);\n    i0.ɵɵtext(7, \" Add Row \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_div_27_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveAllImported());\n    });\n    i0.ɵɵtext(9, \"Save All\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 24)(11, \"table\", 25)(12, \"thead\", 26)(13, \"tr\")(14, \"th\");\n    i0.ɵɵtext(15, \"Sheet Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Code Ref\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\");\n    i0.ɵɵtext(19, \"Code Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\");\n    i0.ɵɵtext(21, \"Reasoning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\");\n    i0.ɵɵtext(23, \"Non Compliance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\");\n    i0.ɵɵtext(25, \"Actionable Step\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\", 27);\n    i0.ɵɵtext(27, \"Action\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"tbody\");\n    i0.ɵɵtemplate(29, AddEditInternalReviewDetailComponent_div_27_tr_29_Template, 16, 19, \"tr\", 28);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Imported Review Details (\", ctx_r1.importedRows.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || ctx_r1.isImporting);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || ctx_r1.isImporting);\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.importedRows);\n  }\n}\nfunction AddEditInternalReviewDetailComponent_form_28_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_form_28_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_form_28_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_form_28_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_form_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 33);\n    i0.ɵɵlistener(\"ngSubmit\", function AddEditInternalReviewDetailComponent_form_28_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 34)(2, \"div\", 35)(3, \"label\", 36);\n    i0.ɵɵtext(4, \"Sheet Number \");\n    i0.ɵɵelementStart(5, \"span\", 37);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"input\", 38);\n    i0.ɵɵtemplate(8, AddEditInternalReviewDetailComponent_form_28_div_8_Template, 2, 0, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 35)(10, \"label\", 36);\n    i0.ɵɵtext(11, \"Code Ref \");\n    i0.ɵɵelementStart(12, \"span\", 37);\n    i0.ɵɵtext(13, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(14, \"input\", 40);\n    i0.ɵɵtemplate(15, AddEditInternalReviewDetailComponent_form_28_div_15_Template, 2, 0, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 41)(17, \"div\", 42)(18, \"label\", 36);\n    i0.ɵɵtext(19, \"Code Description \");\n    i0.ɵɵelementStart(20, \"span\", 37);\n    i0.ɵɵtext(21, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(22, \"textarea\", 43);\n    i0.ɵɵtemplate(23, AddEditInternalReviewDetailComponent_form_28_div_23_Template, 2, 0, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 41)(25, \"div\", 42)(26, \"label\", 36);\n    i0.ɵɵtext(27, \"Reasoning \");\n    i0.ɵɵelementStart(28, \"span\", 37);\n    i0.ɵɵtext(29, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(30, \"textarea\", 44);\n    i0.ɵɵtemplate(31, AddEditInternalReviewDetailComponent_form_28_div_31_Template, 2, 0, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 41)(33, \"div\", 42)(34, \"label\", 36);\n    i0.ɵɵtext(35, \"Non Compliance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"textarea\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 41)(38, \"div\", 42)(39, \"label\", 36);\n    i0.ɵɵtext(40, \"Actionable Step\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(41, \"textarea\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 47)(43, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_form_28_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCancel());\n    });\n    i0.ɵɵtext(44, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"button\", 49);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.detailForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"sheetNumber\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"sheetNumber\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"codeRef\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"codeRef\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"codeDescription\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"codeDescription\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"reasoning\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"reasoning\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.isEdit ? \"Update\" : \"Create\");\n  }\n}\nexport class AddEditInternalReviewDetailComponent {\n  fb;\n  modal;\n  permitsService;\n  customLayoutUtilsService;\n  httpUtilsService;\n  permitId = null;\n  permitNumber = '';\n  reviewCategory = '';\n  internalCommentsId = null;\n  detailData = null; // For edit mode\n  loggedInUserId = 'user'; // Should be passed from parent\n  detailForm;\n  isEdit = false;\n  isLoading = false;\n  // Tabs removed; single-view form\n  formSubmitted = false;\n  showForm = false;\n  templateUrlPath = '/assets/excel/claimstempate.xlsx';\n  importedRows = [];\n  isImporting = false;\n  constructor(fb, modal, permitsService, customLayoutUtilsService, httpUtilsService) {\n    this.fb = fb;\n    this.modal = modal;\n    this.permitsService = permitsService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.httpUtilsService = httpUtilsService;\n  }\n  ngOnInit() {\n    this.isEdit = !!this.detailData;\n    this.detailForm = this.fb.group({\n      sheetNumber: [this.detailData?.sheetNumber || '', Validators.required],\n      codeRef: [this.detailData?.codeRef || '', Validators.required],\n      codeDescription: [this.detailData?.codeDescription || '', Validators.required],\n      reasoning: [this.detailData?.reasoning || '', Validators.required],\n      nonCompliance: [this.detailData?.nonCompliance || ''],\n      actionableStep: [this.detailData?.actionableStep || ''],\n      aeResponse: [this.detailData?.aeResponse || ''],\n      commentResponsedBy: [this.detailData?.commentResponsedBy || '']\n    });\n  }\n  // Tab navigation removed\n  shouldShowValidationError(fieldName) {\n    // Only show validation errors when form has been submitted\n    if (!this.formSubmitted) {\n      return false;\n    }\n    const field = this.detailForm.get(fieldName);\n    return !!(field && field.invalid);\n  }\n  onSubmit() {\n    this.formSubmitted = true;\n    if (this.detailForm.valid && this.permitId) {\n      this.isLoading = true;\n      // Enable common loader\n      this.httpUtilsService.loadingSubject.next(true);\n      const formData = {\n        ...this.detailForm.value,\n        permitId: this.permitId,\n        permitNumber: this.permitNumber,\n        reviewCategory: this.reviewCategory,\n        internalCommentsId: this.internalCommentsId || this.detailData?.internalCommentsId,\n        loggedInUserId: this.loggedInUserId\n      };\n      if (this.isEdit && this.detailData?.internalReviewCommentsId) {\n        formData.internalReviewCommentsId = this.detailData.internalReviewCommentsId;\n        this.permitsService.updateInternalPlanReviewDetail(formData).subscribe({\n          next: res => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to update review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess(res.responseData?.message || 'Review detail updated successfully!', '');\n              this.modal.close('updated');\n            }\n          },\n          error: err => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error updating review detail', '');\n            console.error(err);\n          }\n        });\n      } else {\n        this.permitsService.addInternalPlanReviewDetail(formData).subscribe({\n          next: res => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess('Review detail created successfully!', '');\n              this.modal.close('created');\n            }\n          },\n          error: err => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error creating review detail', '');\n            console.error(err);\n          }\n        });\n      }\n    } else {\n      this.detailForm.markAllAsTouched();\n      if (!this.permitId) {\n        this.customLayoutUtilsService.showError('Permit Id is required', '');\n      }\n    }\n  }\n  onCancel() {\n    this.modal.dismiss('cancelled');\n  }\n  onBack() {\n    this.modal.dismiss('back');\n  }\n  triggerFileImport(fileInput) {\n    if (this.isLoading) {\n      return;\n    }\n    fileInput.click();\n  }\n  onFileSelected(event) {\n    var _this = this;\n    const input = event.target;\n    const file = input?.files && input.files.length ? input.files[0] : null;\n    if (!file) {\n      return;\n    }\n    this.isImporting = true;\n    // Enable common loader\n    this.httpUtilsService.loadingSubject.next(true);\n    const reader = new FileReader();\n    reader.onload = /*#__PURE__*/_asyncToGenerator(function* () {\n      try {\n        const XLSX = yield import('xlsx');\n        const data = new Uint8Array(reader.result);\n        const workbook = XLSX.read(data, {\n          type: 'array'\n        });\n        const firstSheetName = workbook.SheetNames[0];\n        const worksheet = workbook.Sheets[firstSheetName];\n        const json = XLSX.utils.sheet_to_json(worksheet, {\n          defval: ''\n        });\n        _this.importedRows = _this.mapExcelRows(json);\n        if (!_this.importedRows.length) {\n          _this.customLayoutUtilsService.showError('No rows found in the uploaded Excel.', '');\n        } else {\n          _this.customLayoutUtilsService.showSuccess(`Imported ${_this.importedRows.length} row(s).`, '');\n        }\n      } catch (err) {\n        console.error('Error parsing Excel', err);\n        _this.importedRows = [];\n        _this.customLayoutUtilsService.showError('Failed to parse Excel file. Please check the template.', '');\n      } finally {\n        _this.isImporting = false;\n        // Disable common loader\n        _this.httpUtilsService.loadingSubject.next(false);\n        // Reset file input so the same file can be selected again if needed\n        input.value = '';\n      }\n    });\n    reader.readAsArrayBuffer(file);\n  }\n  normalizeHeader(header) {\n    return (header || '').toString().trim().toLowerCase().replace(/\\s+/g, '');\n  }\n  mapExcelRows(jsonRows) {\n    if (!jsonRows || !jsonRows.length) {\n      return [];\n    }\n    const headerMap = {};\n    // Build header map from first row's keys\n    const firstRow = jsonRows[0];\n    Object.keys(firstRow).forEach(key => {\n      const norm = this.normalizeHeader(key);\n      headerMap[norm] = key;\n    });\n    const pick = (row, keyCandidates) => {\n      for (const candidate of keyCandidates) {\n        const norm = this.normalizeHeader(candidate);\n        const actual = headerMap[norm];\n        if (actual && row.hasOwnProperty(actual)) {\n          return (row[actual] ?? '').toString();\n        }\n      }\n      return '';\n    };\n    // Expected columns with common variants\n    const rows = jsonRows.map(row => {\n      return {\n        sheetNumber: pick(row, ['Sheet Number', 'SheetNumber', 'Sheet#', 'Sheet No']),\n        codeRef: pick(row, ['Code Ref', 'Code Reference', 'CodeRef']),\n        codeDescription: pick(row, ['Code Description', 'Description']),\n        reasoning: pick(row, ['Reasoning', 'Reason']),\n        nonCompliance: pick(row, ['Non Compliance', 'Non-Compliance', 'NonCompliance']),\n        actionableStep: pick(row, ['Actionable Step', 'ActionableStep', 'Action Step'])\n      };\n    });\n    // Filter out completely empty rows\n    return rows.filter(r => Object.values(r).some(v => (v || '').toString().trim() !== ''));\n  }\n  deleteImportedRow(index) {\n    if (index < 0 || index >= this.importedRows.length) {\n      return;\n    }\n    this.importedRows.splice(index, 1);\n  }\n  addImportedRow() {\n    this.importedRows.unshift({\n      sheetNumber: '',\n      codeRef: '',\n      codeDescription: '',\n      reasoning: '',\n      nonCompliance: '',\n      actionableStep: ''\n    });\n  }\n  saveAllImported() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.permitId) {\n        _this2.customLayoutUtilsService.showError('Permit Id is required to save imported rows', '');\n        return;\n      }\n      if (!_this2.reviewCategory) {\n        _this2.customLayoutUtilsService.showError('Review Category is required to save imported rows', '');\n        return;\n      }\n      if (!_this2.importedRows.length) {\n        _this2.customLayoutUtilsService.showError('No imported rows to save', '');\n        return;\n      }\n      _this2.isLoading = true;\n      // Enable common loader\n      _this2.httpUtilsService.loadingSubject.next(true);\n      try {\n        const requests = _this2.importedRows.map(r => {\n          const payload = {\n            sheetNumber: r.sheetNumber || '',\n            codeRef: r.codeRef || '',\n            codeDescription: r.codeDescription || '',\n            reasoning: r.reasoning || '',\n            nonCompliance: r.nonCompliance || '',\n            actionableStep: r.actionableStep || '',\n            permitId: _this2.permitId,\n            permitNumber: _this2.permitNumber,\n            reviewCategory: _this2.reviewCategory,\n            internalCommentsId: _this2.internalCommentsId,\n            loggedInUserId: _this2.loggedInUserId\n          };\n          return _this2.permitsService.addInternalPlanReviewDetail(payload);\n        });\n        // Execute all in parallel\n        yield new Promise((resolve, reject) => {\n          const {\n            forkJoin\n          } = require('rxjs');\n          forkJoin(requests).subscribe({\n            next: resolve,\n            error: reject\n          });\n        });\n        _this2.customLayoutUtilsService.showSuccess('All imported rows saved successfully!', '');\n        _this2.importedRows = [];\n        // Optionally close modal or refresh parent via close value\n        _this2.modal.close('bulk-created');\n      } catch (err) {\n        console.error('Error saving imported rows', err);\n        _this2.customLayoutUtilsService.showError('Failed to save one or more imported rows', '');\n      } finally {\n        _this2.isLoading = false;\n        // Disable common loader\n        _this2.httpUtilsService.loadingSubject.next(false);\n      }\n    })();\n  }\n  get isFormValid() {\n    return this.detailForm.valid;\n  }\n  get isDetailsValid() {\n    if (!this.detailForm) {\n      return false;\n    }\n    const controls = this.detailForm.controls;\n    return !!controls.sheetNumber?.valid && !!controls.codeRef?.valid && !!controls.codeDescription?.valid && !!controls.reasoning?.valid;\n  }\n  static ɵfac = function AddEditInternalReviewDetailComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AddEditInternalReviewDetailComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.NgbActiveModal), i0.ɵɵdirectiveInject(i3.PermitsService), i0.ɵɵdirectiveInject(i4.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i5.HttpUtilsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddEditInternalReviewDetailComponent,\n    selectors: [[\"app-add-edit-internal-review-detail\"]],\n    inputs: {\n      permitId: \"permitId\",\n      permitNumber: \"permitNumber\",\n      reviewCategory: \"reviewCategory\",\n      internalCommentsId: \"internalCommentsId\",\n      detailData: \"detailData\",\n      loggedInUserId: \"loggedInUserId\"\n    },\n    decls: 29,\n    vars: 6,\n    consts: [[\"fileInput\", \"\"], [1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", \"btn-sm\", 3, \"click\"], [1, \"d-flex\", \"align-items-center\"], [\"download\", \"\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"href\"], [\"type\", \"file\", \"accept\", \".xlsx,.xls\", 1, \"d-none\", 3, \"change\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"me-2\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [\"role\", \"alert\", 1, \"alert\", \"alert-info\", \"mt-4\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [1, \"mt-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [1, \"mb-0\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", \"btn-sm\", \"me-2\", 3, \"click\", \"disabled\"], [1, \"fa\", \"fa-plus\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"table-responsive\"], [1, \"table\", \"table-sm\", \"table-bordered\", \"align-middle\"], [1, \"table-light\"], [2, \"width\", \"60px\"], [4, \"ngFor\", \"ngForOf\"], [1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\", \"name\"], [1, \"text-center\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"btn-light-danger\", 3, \"click\", \"disabled\"], [1, \"fa\", \"fa-trash\"], [\"novalidate\", \"\", 3, \"ngSubmit\", \"formGroup\"], [1, \"row\", \"mt-3\"], [1, \"col-xl-6\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"text-danger\"], [\"type\", \"text\", \"formControlName\", \"sheetNumber\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"codeRef\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [1, \"row\", \"mt-4\"], [1, \"col-xl-12\"], [\"formControlName\", \"codeDescription\", \"rows\", \"4\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"reasoning\", \"rows\", \"4\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"nonCompliance\", \"rows\", \"4\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"actionableStep\", \"rows\", \"4\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [1, \"modal-footer\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"me-2\", \"mr-2\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"disabled\"], [1, \"invalid-feedback\"]],\n    template: function AddEditInternalReviewDetailComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵtemplate(4, AddEditInternalReviewDetailComponent_div_4_Template, 2, 1, \"div\", 4)(5, AddEditInternalReviewDetailComponent_div_5_Template, 2, 1, \"div\", 4);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 5)(7, \"i\", 6);\n        i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_Template_i_click_7_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onCancel());\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8)(10, \"div\")(11, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_Template_button_click_11_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onBack());\n        });\n        i0.ɵɵtext(12, \"Back\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"div\", 10)(14, \"a\", 11);\n        i0.ɵɵtext(15, \"Download Template\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"input\", 12, 0);\n        i0.ɵɵlistener(\"change\", function AddEditInternalReviewDetailComponent_Template_input_change_16_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onFileSelected($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"button\", 13);\n        i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_Template_button_click_18_listener() {\n          i0.ɵɵrestoreView(_r1);\n          const fileInput_r3 = i0.ɵɵreference(17);\n          return i0.ɵɵresetView(ctx.triggerFileImport(fileInput_r3));\n        });\n        i0.ɵɵtext(19, \"Import Excel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"button\", 14);\n        i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_Template_button_click_20_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.showForm = true);\n        });\n        i0.ɵɵtext(21, \"Add Review Detail\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(22, \"div\", 15);\n        i0.ɵɵtext(23, \" You can upload review details by downloading the template, filling it, and importing the Excel. Or click \");\n        i0.ɵɵelementStart(24, \"strong\");\n        i0.ɵɵtext(25, \"Add Review Detail\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(26, \" to enter details manually. \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(27, AddEditInternalReviewDetailComponent_div_27_Template, 30, 4, \"div\", 16)(28, AddEditInternalReviewDetailComponent_form_28_Template, 47, 22, \"form\", 17);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.isEdit);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isEdit);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"href\", ctx.templateUrlPath, i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngIf\", ctx.importedRows == null ? null : ctx.importedRows.length);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showForm);\n      }\n    },\n    dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.FormGroupDirective, i1.FormControlName],\n    styles: [\".modal-dialog {\\n  max-width: 1100px;\\n}\\n\\n  .modal-body {\\n  max-height: calc(100vh - 220px);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9wZXJtaXRzL2FkZC1lZGl0LWludGVybmFsLXJldmlldy1kZXRhaWwvYWRkLWVkaXQtaW50ZXJuYWwtcmV2aWV3LWRldGFpbC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSwrQkFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwIC5tb2RhbC1kaWFsb2cge1xyXG4gIG1heC13aWR0aDogMTEwMHB4O1xyXG59XHJcblxyXG46Om5nLWRlZXAgLm1vZGFsLWJvZHkge1xyXG4gIG1heC1oZWlnaHQ6IGNhbGMoMTAwdmggLSAyMjBweCk7XHJcbn1cclxuXHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "reviewCategory", "ɵɵtwoWayListener", "AddEditInternalReviewDetailComponent_div_27_tr_29_Template_input_ngModelChange_2_listener", "$event", "r_r6", "ɵɵrestoreView", "_r5", "$implicit", "ɵɵtwoWayBindingSet", "sheetNumber", "ɵɵresetView", "AddEditInternalReviewDetailComponent_div_27_tr_29_Template_input_ngModelChange_4_listener", "codeRef", "AddEditInternalReviewDetailComponent_div_27_tr_29_Template_input_ngModelChange_6_listener", "codeDescription", "AddEditInternalReviewDetailComponent_div_27_tr_29_Template_input_ngModelChange_8_listener", "reasoning", "AddEditInternalReviewDetailComponent_div_27_tr_29_Template_input_ngModelChange_10_listener", "nonCompliance", "AddEditInternalReviewDetailComponent_div_27_tr_29_Template_input_ngModelChange_12_listener", "actionableStep", "ɵɵlistener", "AddEditInternalReviewDetailComponent_div_27_tr_29_Template_button_click_14_listener", "i_r7", "index", "ɵɵnextContext", "deleteImportedRow", "ɵɵelement", "ɵɵpropertyInterpolate1", "ɵɵtwoWayProperty", "ɵɵproperty", "isLoading", "AddEditInternalReviewDetailComponent_div_27_Template_button_click_5_listener", "_r4", "addImportedRow", "AddEditInternalReviewDetailComponent_div_27_Template_button_click_8_listener", "saveAllImported", "ɵɵtemplate", "AddEditInternalReviewDetailComponent_div_27_tr_29_Template", "importedRows", "length", "isImporting", "AddEditInternalReviewDetailComponent_form_28_Template_form_ngSubmit_0_listener", "_r8", "onSubmit", "AddEditInternalReviewDetailComponent_form_28_div_8_Template", "AddEditInternalReviewDetailComponent_form_28_div_15_Template", "AddEditInternalReviewDetailComponent_form_28_div_23_Template", "AddEditInternalReviewDetailComponent_form_28_div_31_Template", "AddEditInternalReviewDetailComponent_form_28_Template_button_click_43_listener", "onCancel", "detailForm", "ɵɵclassProp", "shouldShowValidationError", "ɵɵtextInterpolate", "isEdit", "AddEditInternalReviewDetailComponent", "fb", "modal", "permitsService", "customLayoutUtilsService", "httpUtilsService", "permitId", "permitNumber", "internalCommentsId", "detailData", "loggedInUserId", "formSubmitted", "showForm", "templateUrlPath", "constructor", "ngOnInit", "group", "required", "aeResponse", "commentResponsedBy", "fieldName", "field", "get", "invalid", "valid", "loadingSubject", "next", "formData", "value", "internalReviewCommentsId", "updateInternalPlanReviewDetail", "subscribe", "res", "<PERSON><PERSON><PERSON>", "showError", "faultMessage", "showSuccess", "responseData", "message", "close", "error", "err", "console", "addInternalPlanReviewDetail", "mark<PERSON>llAsTouched", "dismiss", "onBack", "triggerFileImport", "fileInput", "click", "onFileSelected", "event", "_this", "input", "target", "file", "files", "reader", "FileReader", "onload", "_asyncToGenerator", "XLSX", "data", "Uint8Array", "result", "workbook", "read", "type", "firstSheetName", "SheetNames", "worksheet", "Sheets", "json", "utils", "sheet_to_json", "defval", "mapExcelRows", "readAsA<PERSON>y<PERSON><PERSON>er", "normalizeHeader", "header", "toString", "trim", "toLowerCase", "replace", "jsonRows", "headerMap", "firstRow", "Object", "keys", "for<PERSON>ach", "key", "norm", "pick", "row", "keyCandidates", "candidate", "actual", "hasOwnProperty", "rows", "map", "filter", "r", "values", "some", "v", "splice", "unshift", "_this2", "requests", "payload", "Promise", "resolve", "reject", "fork<PERSON><PERSON>n", "require", "isFormValid", "isDetailsValid", "controls", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "NgbActiveModal", "i3", "PermitsService", "i4", "CustomLayoutUtilsService", "i5", "HttpUtilsService", "selectors", "inputs", "decls", "vars", "consts", "template", "AddEditInternalReviewDetailComponent_Template", "rf", "ctx", "ɵɵelementContainerStart", "AddEditInternalReviewDetailComponent_div_4_Template", "AddEditInternalReviewDetailComponent_div_5_Template", "AddEditInternalReviewDetailComponent_Template_i_click_7_listener", "_r1", "AddEditInternalReviewDetailComponent_Template_button_click_11_listener", "AddEditInternalReviewDetailComponent_Template_input_change_16_listener", "AddEditInternalReviewDetailComponent_Template_button_click_18_listener", "fileInput_r3", "ɵɵreference", "AddEditInternalReviewDetailComponent_Template_button_click_20_listener", "AddEditInternalReviewDetailComponent_div_27_Template", "AddEditInternalReviewDetailComponent_form_28_Template", "ɵɵsanitizeUrl"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\add-edit-internal-review-detail\\add-edit-internal-review-detail.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\add-edit-internal-review-detail\\add-edit-internal-review-detail.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\nimport { PermitsService } from '../../services/permits.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { HttpUtilsService } from '../../services/http-utils.service';\n\n@Component({\n  selector: 'app-add-edit-internal-review-detail',\n  templateUrl: './add-edit-internal-review-detail.component.html',\n  styleUrls: ['./add-edit-internal-review-detail.component.scss']\n})\nexport class AddEditInternalReviewDetailComponent implements OnInit {\n  @Input() permitId: number | null = null;\n  @Input() permitNumber: string = '';\n  @Input() reviewCategory: string = '';\n  @Input() internalCommentsId: number | null = null;\n  @Input() detailData: any = null; // For edit mode\n  @Input() loggedInUserId: string = 'user'; // Should be passed from parent\n\n  detailForm!: FormGroup;\n  isEdit: boolean = false;\n  isLoading: boolean = false;\n  // Tabs removed; single-view form\n  formSubmitted: boolean = false;\n  showForm: boolean = false;\n  templateUrlPath: string = '/assets/excel/claimstempate.xlsx';\n  importedRows: Array<any> = [];\n  isImporting: boolean = false;\n\n  constructor(\n    private fb: FormBuilder,\n    public modal: NgbActiveModal,\n    private permitsService: PermitsService,\n    private customLayoutUtilsService: CustomLayoutUtilsService,\n    private httpUtilsService: HttpUtilsService\n  ) {}\n\n  ngOnInit(): void {\n    this.isEdit = !!this.detailData;\n    this.detailForm = this.fb.group({\n      sheetNumber: [this.detailData?.sheetNumber || '', Validators.required],\n      codeRef: [this.detailData?.codeRef || '', Validators.required],\n      codeDescription: [this.detailData?.codeDescription || '', Validators.required],\n      reasoning: [this.detailData?.reasoning || '', Validators.required],\n      nonCompliance: [this.detailData?.nonCompliance || ''],\n      actionableStep: [this.detailData?.actionableStep || ''],\n      aeResponse: [this.detailData?.aeResponse || ''],\n      commentResponsedBy: [this.detailData?.commentResponsedBy || ''],\n    });\n  }\n\n  // Tab navigation removed\n\n  shouldShowValidationError(fieldName: string): boolean {\n    // Only show validation errors when form has been submitted\n    if (!this.formSubmitted) {\n      return false;\n    }\n    \n    const field = this.detailForm.get(fieldName);\n    return !!(field && field.invalid);\n  }\n\n  onSubmit(): void {\n    this.formSubmitted = true;\n    if (this.detailForm.valid && this.permitId) {\n      this.isLoading = true;\n      // Enable common loader\n      this.httpUtilsService.loadingSubject.next(true);\n      \n      const formData: any = {\n        ...this.detailForm.value,\n        permitId: this.permitId,\n        permitNumber: this.permitNumber,\n        reviewCategory: this.reviewCategory,\n        internalCommentsId: this.internalCommentsId || this.detailData?.internalCommentsId,\n        loggedInUserId: this.loggedInUserId\n      };\n      if (this.isEdit && this.detailData?.internalReviewCommentsId) {\n        formData.internalReviewCommentsId = this.detailData.internalReviewCommentsId;\n        this.permitsService.updateInternalPlanReviewDetail(formData).subscribe({\n          next: (res: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to update review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess(res.responseData?.message || 'Review detail updated successfully!', '');\n              this.modal.close('updated');\n            }\n          },\n          error: (err: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error updating review detail', '');\n            console.error(err);\n          }\n        });\n      } else {\n        this.permitsService.addInternalPlanReviewDetail(formData).subscribe({\n          next: (res: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess('Review detail created successfully!', '');\n              this.modal.close('created');\n            }\n          },\n          error: (err: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error creating review detail', '');\n            console.error(err);\n          }\n        });\n      }\n    } else {\n      this.detailForm.markAllAsTouched();\n      if (!this.permitId) {\n        this.customLayoutUtilsService.showError('Permit Id is required', '');\n      }\n    }\n  }\n\n  onCancel(): void {\n    this.modal.dismiss('cancelled');\n  }\n\n  onBack(): void {\n    this.modal.dismiss('back');\n  }\n\n  triggerFileImport(fileInput: HTMLInputElement): void {\n    if (this.isLoading) { return; }\n    fileInput.click();\n  }\n\n  onFileSelected(event: Event): void {\n    const input = event.target as HTMLInputElement;\n    const file = input?.files && input.files.length ? input.files[0] : null;\n    if (!file) { return; }\n    this.isImporting = true;\n    // Enable common loader\n    this.httpUtilsService.loadingSubject.next(true);\n    const reader = new FileReader();\n    reader.onload = async () => {\n      try {\n        const XLSX: any = await import('xlsx');\n        const data = new Uint8Array(reader.result as ArrayBuffer);\n        const workbook = XLSX.read(data, { type: 'array' });\n        const firstSheetName = workbook.SheetNames[0];\n        const worksheet = workbook.Sheets[firstSheetName];\n        const json: any[] = XLSX.utils.sheet_to_json(worksheet, { defval: '' });\n        this.importedRows = this.mapExcelRows(json);\n        if (!this.importedRows.length) {\n          this.customLayoutUtilsService.showError('No rows found in the uploaded Excel.', '');\n        } else {\n          this.customLayoutUtilsService.showSuccess(`Imported ${this.importedRows.length} row(s).`, '');\n        }\n      } catch (err) {\n        console.error('Error parsing Excel', err);\n        this.importedRows = [];\n        this.customLayoutUtilsService.showError('Failed to parse Excel file. Please check the template.', '');\n      } finally {\n        this.isImporting = false;\n        // Disable common loader\n        this.httpUtilsService.loadingSubject.next(false);\n        // Reset file input so the same file can be selected again if needed\n        input.value = '';\n      }\n    };\n    reader.readAsArrayBuffer(file);\n  }\n\n  private normalizeHeader(header: string): string {\n    return (header || '').toString().trim().toLowerCase().replace(/\\s+/g, '');\n  }\n\n  private mapExcelRows(jsonRows: any[]): Array<any> {\n    if (!jsonRows || !jsonRows.length) { return []; }\n    const headerMap: any = {};\n    // Build header map from first row's keys\n    const firstRow = jsonRows[0];\n    Object.keys(firstRow).forEach((key) => {\n      const norm = this.normalizeHeader(key);\n      headerMap[norm] = key;\n    });\n\n    const pick = (row: any, keyCandidates: string[]): string => {\n      for (const candidate of keyCandidates) {\n        const norm = this.normalizeHeader(candidate);\n        const actual = headerMap[norm];\n        if (actual && row.hasOwnProperty(actual)) {\n          return (row[actual] ?? '').toString();\n        }\n      }\n      return '';\n    };\n\n    // Expected columns with common variants\n    const rows = jsonRows.map((row) => {\n      return {\n        sheetNumber: pick(row, ['Sheet Number', 'SheetNumber', 'Sheet#', 'Sheet No']),\n        codeRef: pick(row, ['Code Ref', 'Code Reference', 'CodeRef']),\n        codeDescription: pick(row, ['Code Description', 'Description']),\n        reasoning: pick(row, ['Reasoning', 'Reason']),\n        nonCompliance: pick(row, ['Non Compliance', 'Non-Compliance', 'NonCompliance']),\n        actionableStep: pick(row, ['Actionable Step', 'ActionableStep', 'Action Step'])\n      };\n    });\n    // Filter out completely empty rows\n    return rows.filter((r) => Object.values(r).some((v) => (v || '').toString().trim() !== ''));\n  }\n\n  deleteImportedRow(index: number): void {\n    if (index < 0 || index >= this.importedRows.length) { return; }\n    this.importedRows.splice(index, 1);\n  }\n\n  addImportedRow(): void {\n    this.importedRows.unshift({\n      sheetNumber: '',\n      codeRef: '',\n      codeDescription: '',\n      reasoning: '',\n      nonCompliance: '',\n      actionableStep: ''\n    });\n  }\n\n  async saveAllImported(): Promise<void> {\n    if (!this.permitId) {\n      this.customLayoutUtilsService.showError('Permit Id is required to save imported rows', '');\n      return;\n    }\n    if (!this.reviewCategory) {\n      this.customLayoutUtilsService.showError('Review Category is required to save imported rows', '');\n      return;\n    }\n    if (!this.importedRows.length) {\n      this.customLayoutUtilsService.showError('No imported rows to save', '');\n      return;\n    }\n    this.isLoading = true;\n    // Enable common loader\n    this.httpUtilsService.loadingSubject.next(true);\n    try {\n      const requests = this.importedRows.map((r) => {\n        const payload: any = {\n          sheetNumber: r.sheetNumber || '',\n          codeRef: r.codeRef || '',\n          codeDescription: r.codeDescription || '',\n          reasoning: r.reasoning || '',\n          nonCompliance: r.nonCompliance || '',\n          actionableStep: r.actionableStep || '',\n          permitId: this.permitId,\n          permitNumber: this.permitNumber,\n          reviewCategory: this.reviewCategory,\n          internalCommentsId: this.internalCommentsId,\n          loggedInUserId: this.loggedInUserId\n        };\n        return this.permitsService.addInternalPlanReviewDetail(payload);\n      });\n      // Execute all in parallel\n      await new Promise((resolve, reject) => {\n        const { forkJoin } = require('rxjs');\n        forkJoin(requests).subscribe({ next: resolve, error: reject });\n      });\n      this.customLayoutUtilsService.showSuccess('All imported rows saved successfully!', '');\n      this.importedRows = [];\n      // Optionally close modal or refresh parent via close value\n      this.modal.close('bulk-created');\n    } catch (err) {\n      console.error('Error saving imported rows', err);\n      this.customLayoutUtilsService.showError('Failed to save one or more imported rows', '');\n    } finally {\n      this.isLoading = false;\n      // Disable common loader\n      this.httpUtilsService.loadingSubject.next(false);\n    }\n  }\n\n  get isFormValid(): boolean {\n    return this.detailForm.valid;\n  }\n\n  get isDetailsValid(): boolean {\n    if (!this.detailForm) { return false; }\n    const controls = this.detailForm.controls as any;\n    return (\n      !!controls.sheetNumber?.valid &&\n      !!controls.codeRef?.valid &&\n      !!controls.codeDescription?.valid &&\n      !!controls.reasoning?.valid\n    );\n  }\n}\n", "<div class=\"modal-content h-auto\">\n  <!-- Header -->\n  <div class=\"modal-header bg-light-primary\">\n    <div class=\"modal-title h5 fs-3\">\n      <ng-container>\n        <div *ngIf=\"isEdit\">Edit Review Detail - {{ reviewCategory || '' }}</div>\n        <div *ngIf=\"!isEdit\">Add Review Detail - {{ reviewCategory || '' }}</div>\n      </ng-container>\n    </div>\n    <div class=\"float-right\">\n      <i class=\"fa-solid fs-2 fa-xmark text-white\" (click)=\"onCancel()\"></i>\n    </div>\n  </div>\n\n  <!-- Body -->\n  <div class=\"modal-body\">\n    <div class=\"d-flex justify-content-between align-items-center\">\n      <div>\n        <button type=\"button\" class=\"btn btn-light btn-sm\" (click)=\"onBack()\">Back</button>\n      </div>\n      <div class=\"d-flex align-items-center\">\n        <a [href]=\"templateUrlPath\" download class=\"btn btn-outline-secondary btn-sm me-2\">Download Template</a>\n        <input #fileInput type=\"file\" class=\"d-none\" accept=\".xlsx,.xls\" (change)=\"onFileSelected($event)\" />\n        <button type=\"button\" class=\"btn btn-outline-primary btn-sm me-2\" (click)=\"triggerFileImport(fileInput)\" [disabled]=\"isLoading\">Import Excel</button>\n        <button type=\"button\" class=\"btn btn-primary btn-sm\" (click)=\"showForm = true\">Add Review Detail</button>\n      </div>\n    </div>\n\n    <div class=\"alert alert-info mt-4\" role=\"alert\">\n      You can upload review details by downloading the template, filling it, and importing the Excel. Or click <strong>Add Review Detail</strong> to enter details manually.\n    </div>\n\n    <!-- Imported rows editable table -->\n    <div *ngIf=\"importedRows?.length\" class=\"mt-4\">\n      <div class=\"d-flex justify-content-between align-items-center mb-2\">\n        <h6 class=\"mb-0\">Imported Review Details ({{ importedRows.length }})</h6>\n        <div>\n          <button type=\"button\" class=\"btn btn-light btn-sm me-2\" (click)=\"addImportedRow()\" [disabled]=\"isLoading || isImporting\">\n            <i class=\"fa fa-plus me-1\"></i> Add Row\n          </button>\n          <button type=\"button\" class=\"btn btn-success btn-sm\" (click)=\"saveAllImported()\" [disabled]=\"isLoading || isImporting\">Save All</button>\n        </div>\n      </div>\n      <div class=\"table-responsive\">\n        <table class=\"table table-sm table-bordered align-middle\">\n          <thead class=\"table-light\">\n            <tr>\n              <th>Sheet Number</th>\n              <th>Code Ref</th>\n              <th>Code Description</th>\n              <th>Reasoning</th>\n              <th>Non Compliance</th>\n              <th>Actionable Step</th>\n              <th style=\"width: 60px;\">Action</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr *ngFor=\"let r of importedRows; let i = index\">\n              <td><input [(ngModel)]=\"r.sheetNumber\" name=\"sheetNumber{{i}}\" class=\"form-control form-control-sm\" /></td>\n              <td><input [(ngModel)]=\"r.codeRef\" name=\"codeRef{{i}}\" class=\"form-control form-control-sm\" /></td>\n              <td><input [(ngModel)]=\"r.codeDescription\" name=\"codeDescription{{i}}\" class=\"form-control form-control-sm\" /></td>\n              <td><input [(ngModel)]=\"r.reasoning\" name=\"reasoning{{i}}\" class=\"form-control form-control-sm\" /></td>\n              <td><input [(ngModel)]=\"r.nonCompliance\" name=\"nonCompliance{{i}}\" class=\"form-control form-control-sm\" /></td>\n              <td><input [(ngModel)]=\"r.actionableStep\" name=\"actionableStep{{i}}\" class=\"form-control form-control-sm\" /></td>\n              <td class=\"text-center\">\n                <button type=\"button\" class=\"btn btn-icon btn-sm btn-light-danger\" (click)=\"deleteImportedRow(i)\" [disabled]=\"isLoading\">\n                  <i class=\"fa fa-trash\"></i>\n                </button>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Form: single view (Review Details + Actionable Step) -->\n    <form *ngIf=\"showForm\" [formGroup]=\"detailForm\" (ngSubmit)=\"onSubmit()\" novalidate>\n      <div class=\"row mt-3\">\n        <div class=\"col-xl-6\">\n          <label class=\"fw-bold form-label mb-2\">Sheet Number <span class=\"text-danger\">*</span></label>\n          <input type=\"text\" formControlName=\"sheetNumber\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('sheetNumber')\" placeholder=\"Type here\" [disabled]=\"isLoading\" />\n          <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('sheetNumber')\">Required Field</div>\n        </div>\n        <div class=\"col-xl-6\">\n          <label class=\"fw-bold form-label mb-2\">Code Ref <span class=\"text-danger\">*</span></label>\n          <input type=\"text\" formControlName=\"codeRef\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('codeRef')\" placeholder=\"Type here\" [disabled]=\"isLoading\" />\n          <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('codeRef')\">Required Field</div>\n        </div>\n      </div>\n      <div class=\"row mt-4\">\n        <div class=\"col-xl-12\">\n          <label class=\"fw-bold form-label mb-2\">Code Description <span class=\"text-danger\">*</span></label>\n          <textarea formControlName=\"codeDescription\" rows=\"4\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('codeDescription')\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n          <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('codeDescription')\">Required Field</div>\n        </div>\n      </div>\n      <div class=\"row mt-4\">\n        <div class=\"col-xl-12\">\n          <label class=\"fw-bold form-label mb-2\">Reasoning <span class=\"text-danger\">*</span></label>\n          <textarea formControlName=\"reasoning\" rows=\"4\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('reasoning')\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n          <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('reasoning')\">Required Field</div>\n        </div>\n      </div>\n      <div class=\"row mt-4\">\n        <div class=\"col-xl-12\">\n          <label class=\"fw-bold form-label mb-2\">Non Compliance</label>\n          <textarea formControlName=\"nonCompliance\" rows=\"4\" class=\"form-control form-control-sm\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n        </div>\n      </div>\n      <div class=\"row mt-4\">\n        <div class=\"col-xl-12\">\n          <label class=\"fw-bold form-label mb-2\">Actionable Step</label>\n          <textarea formControlName=\"actionableStep\" rows=\"4\" class=\"form-control form-control-sm\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n        </div>\n      </div>\n\n      <div class=\"modal-footer justify-content-end\">\n        <button type=\"button\" class=\"btn btn-danger btn-sm btn-elevate me-2 mr-2\" (click)=\"onCancel()\" [disabled]=\"isLoading\">Cancel</button>\n        <button type=\"submit\" class=\"btn btn-primary btn-sm\" [disabled]=\"isLoading\">{{ isEdit ? 'Update' : 'Create' }}</button>\n      </div>\n    </form>\n  </div>\n</div>\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;ICI3DC,EAAA,CAAAC,cAAA,UAAoB;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAArDH,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAK,kBAAA,0BAAAC,MAAA,CAAAC,cAAA,WAA+C;;;;;IACnEP,EAAA,CAAAC,cAAA,UAAqB;IAAAD,EAAA,CAAAE,MAAA,GAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAApDH,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAK,kBAAA,yBAAAC,MAAA,CAAAC,cAAA,WAA8C;;;;;;IAoDzDP,EADN,CAAAC,cAAA,SAAkD,SAC5C,gBAAkG;IAA3FD,EAAA,CAAAQ,gBAAA,2BAAAC,0FAAAC,MAAA;MAAA,MAAAC,IAAA,GAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAd,EAAA,CAAAe,kBAAA,CAAAJ,IAAA,CAAAK,WAAA,EAAAN,MAAA,MAAAC,IAAA,CAAAK,WAAA,GAAAN,MAAA;MAAA,OAAAV,EAAA,CAAAiB,WAAA,CAAAP,MAAA;IAAA,EAA2B;IAAgEV,EAAlG,CAAAG,YAAA,EAAkG,EAAK;IACvGH,EAAJ,CAAAC,cAAA,SAAI,gBAA0F;IAAnFD,EAAA,CAAAQ,gBAAA,2BAAAU,0FAAAR,MAAA;MAAA,MAAAC,IAAA,GAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAd,EAAA,CAAAe,kBAAA,CAAAJ,IAAA,CAAAQ,OAAA,EAAAT,MAAA,MAAAC,IAAA,CAAAQ,OAAA,GAAAT,MAAA;MAAA,OAAAV,EAAA,CAAAiB,WAAA,CAAAP,MAAA;IAAA,EAAuB;IAA4DV,EAA1F,CAAAG,YAAA,EAA0F,EAAK;IAC/FH,EAAJ,CAAAC,cAAA,SAAI,gBAA0G;IAAnGD,EAAA,CAAAQ,gBAAA,2BAAAY,0FAAAV,MAAA;MAAA,MAAAC,IAAA,GAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAd,EAAA,CAAAe,kBAAA,CAAAJ,IAAA,CAAAU,eAAA,EAAAX,MAAA,MAAAC,IAAA,CAAAU,eAAA,GAAAX,MAAA;MAAA,OAAAV,EAAA,CAAAiB,WAAA,CAAAP,MAAA;IAAA,EAA+B;IAAoEV,EAA1G,CAAAG,YAAA,EAA0G,EAAK;IAC/GH,EAAJ,CAAAC,cAAA,SAAI,gBAA8F;IAAvFD,EAAA,CAAAQ,gBAAA,2BAAAc,0FAAAZ,MAAA;MAAA,MAAAC,IAAA,GAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAd,EAAA,CAAAe,kBAAA,CAAAJ,IAAA,CAAAY,SAAA,EAAAb,MAAA,MAAAC,IAAA,CAAAY,SAAA,GAAAb,MAAA;MAAA,OAAAV,EAAA,CAAAiB,WAAA,CAAAP,MAAA;IAAA,EAAyB;IAA8DV,EAA9F,CAAAG,YAAA,EAA8F,EAAK;IACnGH,EAAJ,CAAAC,cAAA,SAAI,iBAAsG;IAA/FD,EAAA,CAAAQ,gBAAA,2BAAAgB,2FAAAd,MAAA;MAAA,MAAAC,IAAA,GAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAd,EAAA,CAAAe,kBAAA,CAAAJ,IAAA,CAAAc,aAAA,EAAAf,MAAA,MAAAC,IAAA,CAAAc,aAAA,GAAAf,MAAA;MAAA,OAAAV,EAAA,CAAAiB,WAAA,CAAAP,MAAA;IAAA,EAA6B;IAAkEV,EAAtG,CAAAG,YAAA,EAAsG,EAAK;IAC3GH,EAAJ,CAAAC,cAAA,UAAI,iBAAwG;IAAjGD,EAAA,CAAAQ,gBAAA,2BAAAkB,2FAAAhB,MAAA;MAAA,MAAAC,IAAA,GAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAd,EAAA,CAAAe,kBAAA,CAAAJ,IAAA,CAAAgB,cAAA,EAAAjB,MAAA,MAAAC,IAAA,CAAAgB,cAAA,GAAAjB,MAAA;MAAA,OAAAV,EAAA,CAAAiB,WAAA,CAAAP,MAAA;IAAA,EAA8B;IAAmEV,EAAxG,CAAAG,YAAA,EAAwG,EAAK;IAE/GH,EADF,CAAAC,cAAA,cAAwB,kBACmG;IAAtDD,EAAA,CAAA4B,UAAA,mBAAAC,oFAAA;MAAA,MAAAC,IAAA,GAAA9B,EAAA,CAAAY,aAAA,CAAAC,GAAA,EAAAkB,KAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAiB,WAAA,CAASX,MAAA,CAAA2B,iBAAA,CAAAH,IAAA,CAAoB;IAAA,EAAC;IAC/F9B,EAAA,CAAAkC,SAAA,aAA2B;IAGjClC,EAFI,CAAAG,YAAA,EAAS,EACN,EACF;;;;;;IAXoCH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAmC,sBAAA,wBAAAL,IAAA,KAAuB;IAAnD9B,EAAA,CAAAoC,gBAAA,YAAAzB,IAAA,CAAAK,WAAA,CAA2B;IACHhB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAmC,sBAAA,oBAAAL,IAAA,KAAmB;IAA3C9B,EAAA,CAAAoC,gBAAA,YAAAzB,IAAA,CAAAQ,OAAA,CAAuB;IACSnB,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAmC,sBAAA,4BAAAL,IAAA,KAA2B;IAA3D9B,EAAA,CAAAoC,gBAAA,YAAAzB,IAAA,CAAAU,eAAA,CAA+B;IACLrB,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAmC,sBAAA,sBAAAL,IAAA,KAAqB;IAA/C9B,EAAA,CAAAoC,gBAAA,YAAAzB,IAAA,CAAAY,SAAA,CAAyB;IACKvB,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAmC,sBAAA,0BAAAL,IAAA,KAAyB;IAAvD9B,EAAA,CAAAoC,gBAAA,YAAAzB,IAAA,CAAAc,aAAA,CAA6B;IACEzB,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAmC,sBAAA,2BAAAL,IAAA,KAA0B;IAAzD9B,EAAA,CAAAoC,gBAAA,YAAAzB,IAAA,CAAAgB,cAAA,CAA8B;IAE2D3B,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAqC,UAAA,aAAA/B,MAAA,CAAAgC,SAAA,CAAsB;;;;;;IA9BhItC,EAFJ,CAAAC,cAAA,cAA+C,cACuB,aACjD;IAAAD,EAAA,CAAAE,MAAA,GAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEvEH,EADF,CAAAC,cAAA,UAAK,iBACsH;IAAjED,EAAA,CAAA4B,UAAA,mBAAAW,6EAAA;MAAAvC,EAAA,CAAAY,aAAA,CAAA4B,GAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAiB,WAAA,CAASX,MAAA,CAAAmC,cAAA,EAAgB;IAAA,EAAC;IAChFzC,EAAA,CAAAkC,SAAA,YAA+B;IAAClC,EAAA,CAAAE,MAAA,gBAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAuH;IAAlED,EAAA,CAAA4B,UAAA,mBAAAc,6EAAA;MAAA1C,EAAA,CAAAY,aAAA,CAAA4B,GAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAiB,WAAA,CAASX,MAAA,CAAAqC,eAAA,EAAiB;IAAA,EAAC;IAAuC3C,EAAA,CAAAE,MAAA,eAAQ;IAEnIF,EAFmI,CAAAG,YAAA,EAAS,EACpI,EACF;IAKEH,EAJR,CAAAC,cAAA,eAA8B,iBAC8B,iBAC7B,UACrB,UACE;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAEnCF,EAFmC,CAAAG,YAAA,EAAK,EACjC,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA4C,UAAA,KAAAC,0DAAA,mBAAkD;IAgB1D7C,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACF;;;;IAtCeH,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,kBAAA,8BAAAC,MAAA,CAAAwC,YAAA,CAAAC,MAAA,MAAmD;IAEiB/C,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAqC,UAAA,aAAA/B,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAA0C,WAAA,CAAqC;IAGvChD,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAqC,UAAA,aAAA/B,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAA0C,WAAA,CAAqC;IAiBlGhD,EAAA,CAAAI,SAAA,IAAiB;IAAjBJ,EAAA,CAAAqC,UAAA,YAAA/B,MAAA,CAAAwC,YAAA,CAAiB;;;;;IAwBrC9C,EAAA,CAAAC,cAAA,cAA+E;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKnGH,EAAA,CAAAC,cAAA,cAA2E;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAO/FH,EAAA,CAAAC,cAAA,cAAmF;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAOvGH,EAAA,CAAAC,cAAA,cAA6E;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAxBvGH,EAAA,CAAAC,cAAA,eAAmF;IAAnCD,EAAA,CAAA4B,UAAA,sBAAAqB,+EAAA;MAAAjD,EAAA,CAAAY,aAAA,CAAAsC,GAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAiB,WAAA,CAAYX,MAAA,CAAA6C,QAAA,EAAU;IAAA,EAAC;IAGjEnD,EAFJ,CAAAC,cAAA,cAAsB,cACE,gBACmB;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC9FH,EAAA,CAAAkC,SAAA,gBAAqM;IACrMlC,EAAA,CAAA4C,UAAA,IAAAQ,2DAAA,kBAA+E;IACjFpD,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC1FH,EAAA,CAAAkC,SAAA,iBAA6L;IAC7LlC,EAAA,CAAA4C,UAAA,KAAAS,4DAAA,kBAA2E;IAE/ErD,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAClGH,EAAA,CAAAkC,SAAA,oBAAsN;IACtNlC,EAAA,CAAA4C,UAAA,KAAAU,4DAAA,kBAAmF;IAEvFtD,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC3FH,EAAA,CAAAkC,SAAA,oBAA0M;IAC1MlC,EAAA,CAAA4C,UAAA,KAAAW,4DAAA,kBAA6E;IAEjFvD,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7DH,EAAA,CAAAkC,SAAA,oBAAkJ;IAEtJlC,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAkC,SAAA,oBAAmJ;IAEvJlC,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAA8C,kBAC0E;IAA5CD,EAAA,CAAA4B,UAAA,mBAAA4B,+EAAA;MAAAxD,EAAA,CAAAY,aAAA,CAAAsC,GAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAiB,WAAA,CAASX,MAAA,CAAAmD,QAAA,EAAU;IAAA,EAAC;IAAwBzD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrIH,EAAA,CAAAC,cAAA,kBAA4E;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAElHF,EAFkH,CAAAG,YAAA,EAAS,EACnH,EACD;;;;IA5CgBH,EAAA,CAAAqC,UAAA,cAAA/B,MAAA,CAAAoD,UAAA,CAAwB;IAI6C1D,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAA2D,WAAA,eAAArD,MAAA,CAAAsD,yBAAA,gBAA6D;IAAyB5D,EAAA,CAAAqC,UAAA,aAAA/B,MAAA,CAAAgC,SAAA,CAAsB;IACnKtC,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAqC,UAAA,SAAA/B,MAAA,CAAAsD,yBAAA,gBAA8C;IAIK5D,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAA2D,WAAA,eAAArD,MAAA,CAAAsD,yBAAA,YAAyD;IAAyB5D,EAAA,CAAAqC,UAAA,aAAA/B,MAAA,CAAAgC,SAAA,CAAsB;IAC3JtC,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAqC,UAAA,SAAA/B,MAAA,CAAAsD,yBAAA,YAA0C;IAMiB5D,EAAA,CAAAI,SAAA,GAAiE;IAAjEJ,EAAA,CAAA2D,WAAA,eAAArD,MAAA,CAAAsD,yBAAA,oBAAiE;IAAyB5D,EAAA,CAAAqC,UAAA,aAAA/B,MAAA,CAAAgC,SAAA,CAAsB;IAC3KtC,EAAA,CAAAI,SAAA,EAAkD;IAAlDJ,EAAA,CAAAqC,UAAA,SAAA/B,MAAA,CAAAsD,yBAAA,oBAAkD;IAMG5D,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAA2D,WAAA,eAAArD,MAAA,CAAAsD,yBAAA,cAA2D;IAAyB5D,EAAA,CAAAqC,UAAA,aAAA/B,MAAA,CAAAgC,SAAA,CAAsB;IAC/JtC,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAAqC,UAAA,SAAA/B,MAAA,CAAAsD,yBAAA,cAA4C;IAMqC5D,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAqC,UAAA,aAAA/B,MAAA,CAAAgC,SAAA,CAAsB;IAMrBtC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAqC,UAAA,aAAA/B,MAAA,CAAAgC,SAAA,CAAsB;IAK1CtC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAqC,UAAA,aAAA/B,MAAA,CAAAgC,SAAA,CAAsB;IAChEtC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAqC,UAAA,aAAA/B,MAAA,CAAAgC,SAAA,CAAsB;IAACtC,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAA6D,iBAAA,CAAAvD,MAAA,CAAAwD,MAAA,uBAAkC;;;AD1GtH,OAAM,MAAOC,oCAAoC;EAmBrCC,EAAA;EACDC,KAAA;EACCC,cAAA;EACAC,wBAAA;EACAC,gBAAA;EAtBDC,QAAQ,GAAkB,IAAI;EAC9BC,YAAY,GAAW,EAAE;EACzB/D,cAAc,GAAW,EAAE;EAC3BgE,kBAAkB,GAAkB,IAAI;EACxCC,UAAU,GAAQ,IAAI,CAAC,CAAC;EACxBC,cAAc,GAAW,MAAM,CAAC,CAAC;EAE1Cf,UAAU;EACVI,MAAM,GAAY,KAAK;EACvBxB,SAAS,GAAY,KAAK;EAC1B;EACAoC,aAAa,GAAY,KAAK;EAC9BC,QAAQ,GAAY,KAAK;EACzBC,eAAe,GAAW,kCAAkC;EAC5D9B,YAAY,GAAe,EAAE;EAC7BE,WAAW,GAAY,KAAK;EAE5B6B,YACUb,EAAe,EAChBC,KAAqB,EACpBC,cAA8B,EAC9BC,wBAAkD,EAClDC,gBAAkC;IAJlC,KAAAJ,EAAE,GAAFA,EAAE;IACH,KAAAC,KAAK,GAALA,KAAK;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,gBAAgB,GAAhBA,gBAAgB;EACvB;EAEHU,QAAQA,CAAA;IACN,IAAI,CAAChB,MAAM,GAAG,CAAC,CAAC,IAAI,CAACU,UAAU;IAC/B,IAAI,CAACd,UAAU,GAAG,IAAI,CAACM,EAAE,CAACe,KAAK,CAAC;MAC9B/D,WAAW,EAAE,CAAC,IAAI,CAACwD,UAAU,EAAExD,WAAW,IAAI,EAAE,EAAEjB,UAAU,CAACiF,QAAQ,CAAC;MACtE7D,OAAO,EAAE,CAAC,IAAI,CAACqD,UAAU,EAAErD,OAAO,IAAI,EAAE,EAAEpB,UAAU,CAACiF,QAAQ,CAAC;MAC9D3D,eAAe,EAAE,CAAC,IAAI,CAACmD,UAAU,EAAEnD,eAAe,IAAI,EAAE,EAAEtB,UAAU,CAACiF,QAAQ,CAAC;MAC9EzD,SAAS,EAAE,CAAC,IAAI,CAACiD,UAAU,EAAEjD,SAAS,IAAI,EAAE,EAAExB,UAAU,CAACiF,QAAQ,CAAC;MAClEvD,aAAa,EAAE,CAAC,IAAI,CAAC+C,UAAU,EAAE/C,aAAa,IAAI,EAAE,CAAC;MACrDE,cAAc,EAAE,CAAC,IAAI,CAAC6C,UAAU,EAAE7C,cAAc,IAAI,EAAE,CAAC;MACvDsD,UAAU,EAAE,CAAC,IAAI,CAACT,UAAU,EAAES,UAAU,IAAI,EAAE,CAAC;MAC/CC,kBAAkB,EAAE,CAAC,IAAI,CAACV,UAAU,EAAEU,kBAAkB,IAAI,EAAE;KAC/D,CAAC;EACJ;EAEA;EAEAtB,yBAAyBA,CAACuB,SAAiB;IACzC;IACA,IAAI,CAAC,IAAI,CAACT,aAAa,EAAE;MACvB,OAAO,KAAK;IACd;IAEA,MAAMU,KAAK,GAAG,IAAI,CAAC1B,UAAU,CAAC2B,GAAG,CAACF,SAAS,CAAC;IAC5C,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAACE,OAAO,CAAC;EACnC;EAEAnC,QAAQA,CAAA;IACN,IAAI,CAACuB,aAAa,GAAG,IAAI;IACzB,IAAI,IAAI,CAAChB,UAAU,CAAC6B,KAAK,IAAI,IAAI,CAAClB,QAAQ,EAAE;MAC1C,IAAI,CAAC/B,SAAS,GAAG,IAAI;MACrB;MACA,IAAI,CAAC8B,gBAAgB,CAACoB,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;MAE/C,MAAMC,QAAQ,GAAQ;QACpB,GAAG,IAAI,CAAChC,UAAU,CAACiC,KAAK;QACxBtB,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBC,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/B/D,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCgE,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,IAAI,IAAI,CAACC,UAAU,EAAED,kBAAkB;QAClFE,cAAc,EAAE,IAAI,CAACA;OACtB;MACD,IAAI,IAAI,CAACX,MAAM,IAAI,IAAI,CAACU,UAAU,EAAEoB,wBAAwB,EAAE;QAC5DF,QAAQ,CAACE,wBAAwB,GAAG,IAAI,CAACpB,UAAU,CAACoB,wBAAwB;QAC5E,IAAI,CAAC1B,cAAc,CAAC2B,8BAA8B,CAACH,QAAQ,CAAC,CAACI,SAAS,CAAC;UACrEL,IAAI,EAAGM,GAAQ,IAAI;YACjB,IAAI,CAACzD,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAAC8B,gBAAgB,CAACoB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAIM,GAAG,EAAEC,OAAO,EAAE;cAChB,IAAI,CAAC7B,wBAAwB,CAAC8B,SAAS,CAACF,GAAG,CAACG,YAAY,IAAI,gCAAgC,EAAE,EAAE,CAAC;YACnG,CAAC,MAAM;cACL,IAAI,CAAC/B,wBAAwB,CAACgC,WAAW,CAACJ,GAAG,CAACK,YAAY,EAAEC,OAAO,IAAI,qCAAqC,EAAE,EAAE,CAAC;cACjH,IAAI,CAACpC,KAAK,CAACqC,KAAK,CAAC,SAAS,CAAC;YAC7B;UACF,CAAC;UACDC,KAAK,EAAGC,GAAQ,IAAI;YAClB,IAAI,CAAClE,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAAC8B,gBAAgB,CAACoB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAI,CAACtB,wBAAwB,CAAC8B,SAAS,CAAC,8BAA8B,EAAE,EAAE,CAAC;YAC3EQ,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;UACpB;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACtC,cAAc,CAACwC,2BAA2B,CAAChB,QAAQ,CAAC,CAACI,SAAS,CAAC;UAClEL,IAAI,EAAGM,GAAQ,IAAI;YACjB,IAAI,CAACzD,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAAC8B,gBAAgB,CAACoB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAIM,GAAG,EAAEC,OAAO,EAAE;cAChB,IAAI,CAAC7B,wBAAwB,CAAC8B,SAAS,CAACF,GAAG,CAACG,YAAY,IAAI,gCAAgC,EAAE,EAAE,CAAC;YACnG,CAAC,MAAM;cACL,IAAI,CAAC/B,wBAAwB,CAACgC,WAAW,CAAC,qCAAqC,EAAE,EAAE,CAAC;cACpF,IAAI,CAAClC,KAAK,CAACqC,KAAK,CAAC,SAAS,CAAC;YAC7B;UACF,CAAC;UACDC,KAAK,EAAGC,GAAQ,IAAI;YAClB,IAAI,CAAClE,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAAC8B,gBAAgB,CAACoB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAI,CAACtB,wBAAwB,CAAC8B,SAAS,CAAC,8BAA8B,EAAE,EAAE,CAAC;YAC3EQ,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;UACpB;SACD,CAAC;MACJ;IACF,CAAC,MAAM;MACL,IAAI,CAAC9C,UAAU,CAACiD,gBAAgB,EAAE;MAClC,IAAI,CAAC,IAAI,CAACtC,QAAQ,EAAE;QAClB,IAAI,CAACF,wBAAwB,CAAC8B,SAAS,CAAC,uBAAuB,EAAE,EAAE,CAAC;MACtE;IACF;EACF;EAEAxC,QAAQA,CAAA;IACN,IAAI,CAACQ,KAAK,CAAC2C,OAAO,CAAC,WAAW,CAAC;EACjC;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAAC5C,KAAK,CAAC2C,OAAO,CAAC,MAAM,CAAC;EAC5B;EAEAE,iBAAiBA,CAACC,SAA2B;IAC3C,IAAI,IAAI,CAACzE,SAAS,EAAE;MAAE;IAAQ;IAC9ByE,SAAS,CAACC,KAAK,EAAE;EACnB;EAEAC,cAAcA,CAACC,KAAY;IAAA,IAAAC,KAAA;IACzB,MAAMC,KAAK,GAAGF,KAAK,CAACG,MAA0B;IAC9C,MAAMC,IAAI,GAAGF,KAAK,EAAEG,KAAK,IAAIH,KAAK,CAACG,KAAK,CAACxE,MAAM,GAAGqE,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;IACvE,IAAI,CAACD,IAAI,EAAE;MAAE;IAAQ;IACrB,IAAI,CAACtE,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACoB,gBAAgB,CAACoB,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC/C,MAAM+B,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,gBAAAC,iBAAA,CAAG,aAAW;MACzB,IAAI;QACF,MAAMC,IAAI,SAAc,MAAM,CAAC,MAAM,CAAC;QACtC,MAAMC,IAAI,GAAG,IAAIC,UAAU,CAACN,MAAM,CAACO,MAAqB,CAAC;QACzD,MAAMC,QAAQ,GAAGJ,IAAI,CAACK,IAAI,CAACJ,IAAI,EAAE;UAAEK,IAAI,EAAE;QAAO,CAAE,CAAC;QACnD,MAAMC,cAAc,GAAGH,QAAQ,CAACI,UAAU,CAAC,CAAC,CAAC;QAC7C,MAAMC,SAAS,GAAGL,QAAQ,CAACM,MAAM,CAACH,cAAc,CAAC;QACjD,MAAMI,IAAI,GAAUX,IAAI,CAACY,KAAK,CAACC,aAAa,CAACJ,SAAS,EAAE;UAAEK,MAAM,EAAE;QAAE,CAAE,CAAC;QACvEvB,KAAI,CAACrE,YAAY,GAAGqE,KAAI,CAACwB,YAAY,CAACJ,IAAI,CAAC;QAC3C,IAAI,CAACpB,KAAI,CAACrE,YAAY,CAACC,MAAM,EAAE;UAC7BoE,KAAI,CAAChD,wBAAwB,CAAC8B,SAAS,CAAC,sCAAsC,EAAE,EAAE,CAAC;QACrF,CAAC,MAAM;UACLkB,KAAI,CAAChD,wBAAwB,CAACgC,WAAW,CAAC,YAAYgB,KAAI,CAACrE,YAAY,CAACC,MAAM,UAAU,EAAE,EAAE,CAAC;QAC/F;MACF,CAAC,CAAC,OAAOyD,GAAG,EAAE;QACZC,OAAO,CAACF,KAAK,CAAC,qBAAqB,EAAEC,GAAG,CAAC;QACzCW,KAAI,CAACrE,YAAY,GAAG,EAAE;QACtBqE,KAAI,CAAChD,wBAAwB,CAAC8B,SAAS,CAAC,wDAAwD,EAAE,EAAE,CAAC;MACvG,CAAC,SAAS;QACRkB,KAAI,CAACnE,WAAW,GAAG,KAAK;QACxB;QACAmE,KAAI,CAAC/C,gBAAgB,CAACoB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAChD;QACA2B,KAAK,CAACzB,KAAK,GAAG,EAAE;MAClB;IACF,CAAC;IACD6B,MAAM,CAACoB,iBAAiB,CAACtB,IAAI,CAAC;EAChC;EAEQuB,eAAeA,CAACC,MAAc;IACpC,OAAO,CAACA,MAAM,IAAI,EAAE,EAAEC,QAAQ,EAAE,CAACC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;EAC3E;EAEQP,YAAYA,CAACQ,QAAe;IAClC,IAAI,CAACA,QAAQ,IAAI,CAACA,QAAQ,CAACpG,MAAM,EAAE;MAAE,OAAO,EAAE;IAAE;IAChD,MAAMqG,SAAS,GAAQ,EAAE;IACzB;IACA,MAAMC,QAAQ,GAAGF,QAAQ,CAAC,CAAC,CAAC;IAC5BG,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,OAAO,CAAEC,GAAG,IAAI;MACpC,MAAMC,IAAI,GAAG,IAAI,CAACb,eAAe,CAACY,GAAG,CAAC;MACtCL,SAAS,CAACM,IAAI,CAAC,GAAGD,GAAG;IACvB,CAAC,CAAC;IAEF,MAAME,IAAI,GAAGA,CAACC,GAAQ,EAAEC,aAAuB,KAAY;MACzD,KAAK,MAAMC,SAAS,IAAID,aAAa,EAAE;QACrC,MAAMH,IAAI,GAAG,IAAI,CAACb,eAAe,CAACiB,SAAS,CAAC;QAC5C,MAAMC,MAAM,GAAGX,SAAS,CAACM,IAAI,CAAC;QAC9B,IAAIK,MAAM,IAAIH,GAAG,CAACI,cAAc,CAACD,MAAM,CAAC,EAAE;UACxC,OAAO,CAACH,GAAG,CAACG,MAAM,CAAC,IAAI,EAAE,EAAEhB,QAAQ,EAAE;QACvC;MACF;MACA,OAAO,EAAE;IACX,CAAC;IAED;IACA,MAAMkB,IAAI,GAAGd,QAAQ,CAACe,GAAG,CAAEN,GAAG,IAAI;MAChC,OAAO;QACL5I,WAAW,EAAE2I,IAAI,CAACC,GAAG,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QAC7EzI,OAAO,EAAEwI,IAAI,CAACC,GAAG,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAC7DvI,eAAe,EAAEsI,IAAI,CAACC,GAAG,EAAE,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;QAC/DrI,SAAS,EAAEoI,IAAI,CAACC,GAAG,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAC7CnI,aAAa,EAAEkI,IAAI,CAACC,GAAG,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;QAC/EjI,cAAc,EAAEgI,IAAI,CAACC,GAAG,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,aAAa,CAAC;OAC/E;IACH,CAAC,CAAC;IACF;IACA,OAAOK,IAAI,CAACE,MAAM,CAAEC,CAAC,IAAKd,MAAM,CAACe,MAAM,CAACD,CAAC,CAAC,CAACE,IAAI,CAAEC,CAAC,IAAK,CAACA,CAAC,IAAI,EAAE,EAAExB,QAAQ,EAAE,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;EAC7F;EAEA/G,iBAAiBA,CAACF,KAAa;IAC7B,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACe,YAAY,CAACC,MAAM,EAAE;MAAE;IAAQ;IAC9D,IAAI,CAACD,YAAY,CAAC0H,MAAM,CAACzI,KAAK,EAAE,CAAC,CAAC;EACpC;EAEAU,cAAcA,CAAA;IACZ,IAAI,CAACK,YAAY,CAAC2H,OAAO,CAAC;MACxBzJ,WAAW,EAAE,EAAE;MACfG,OAAO,EAAE,EAAE;MACXE,eAAe,EAAE,EAAE;MACnBE,SAAS,EAAE,EAAE;MACbE,aAAa,EAAE,EAAE;MACjBE,cAAc,EAAE;KACjB,CAAC;EACJ;EAEMgB,eAAeA,CAAA;IAAA,IAAA+H,MAAA;IAAA,OAAA/C,iBAAA;MACnB,IAAI,CAAC+C,MAAI,CAACrG,QAAQ,EAAE;QAClBqG,MAAI,CAACvG,wBAAwB,CAAC8B,SAAS,CAAC,6CAA6C,EAAE,EAAE,CAAC;QAC1F;MACF;MACA,IAAI,CAACyE,MAAI,CAACnK,cAAc,EAAE;QACxBmK,MAAI,CAACvG,wBAAwB,CAAC8B,SAAS,CAAC,mDAAmD,EAAE,EAAE,CAAC;QAChG;MACF;MACA,IAAI,CAACyE,MAAI,CAAC5H,YAAY,CAACC,MAAM,EAAE;QAC7B2H,MAAI,CAACvG,wBAAwB,CAAC8B,SAAS,CAAC,0BAA0B,EAAE,EAAE,CAAC;QACvE;MACF;MACAyE,MAAI,CAACpI,SAAS,GAAG,IAAI;MACrB;MACAoI,MAAI,CAACtG,gBAAgB,CAACoB,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;MAC/C,IAAI;QACF,MAAMkF,QAAQ,GAAGD,MAAI,CAAC5H,YAAY,CAACoH,GAAG,CAAEE,CAAC,IAAI;UAC3C,MAAMQ,OAAO,GAAQ;YACnB5J,WAAW,EAAEoJ,CAAC,CAACpJ,WAAW,IAAI,EAAE;YAChCG,OAAO,EAAEiJ,CAAC,CAACjJ,OAAO,IAAI,EAAE;YACxBE,eAAe,EAAE+I,CAAC,CAAC/I,eAAe,IAAI,EAAE;YACxCE,SAAS,EAAE6I,CAAC,CAAC7I,SAAS,IAAI,EAAE;YAC5BE,aAAa,EAAE2I,CAAC,CAAC3I,aAAa,IAAI,EAAE;YACpCE,cAAc,EAAEyI,CAAC,CAACzI,cAAc,IAAI,EAAE;YACtC0C,QAAQ,EAAEqG,MAAI,CAACrG,QAAQ;YACvBC,YAAY,EAAEoG,MAAI,CAACpG,YAAY;YAC/B/D,cAAc,EAAEmK,MAAI,CAACnK,cAAc;YACnCgE,kBAAkB,EAAEmG,MAAI,CAACnG,kBAAkB;YAC3CE,cAAc,EAAEiG,MAAI,CAACjG;WACtB;UACD,OAAOiG,MAAI,CAACxG,cAAc,CAACwC,2BAA2B,CAACkE,OAAO,CAAC;QACjE,CAAC,CAAC;QACF;QACA,MAAM,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;UACpC,MAAM;YAAEC;UAAQ,CAAE,GAAGC,OAAO,CAAC,MAAM,CAAC;UACpCD,QAAQ,CAACL,QAAQ,CAAC,CAAC7E,SAAS,CAAC;YAAEL,IAAI,EAAEqF,OAAO;YAAEvE,KAAK,EAAEwE;UAAM,CAAE,CAAC;QAChE,CAAC,CAAC;QACFL,MAAI,CAACvG,wBAAwB,CAACgC,WAAW,CAAC,uCAAuC,EAAE,EAAE,CAAC;QACtFuE,MAAI,CAAC5H,YAAY,GAAG,EAAE;QACtB;QACA4H,MAAI,CAACzG,KAAK,CAACqC,KAAK,CAAC,cAAc,CAAC;MAClC,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZC,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEC,GAAG,CAAC;QAChDkE,MAAI,CAACvG,wBAAwB,CAAC8B,SAAS,CAAC,0CAA0C,EAAE,EAAE,CAAC;MACzF,CAAC,SAAS;QACRyE,MAAI,CAACpI,SAAS,GAAG,KAAK;QACtB;QACAoI,MAAI,CAACtG,gBAAgB,CAACoB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAClD;IAAC;EACH;EAEA,IAAIyF,WAAWA,CAAA;IACb,OAAO,IAAI,CAACxH,UAAU,CAAC6B,KAAK;EAC9B;EAEA,IAAI4F,cAAcA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACzH,UAAU,EAAE;MAAE,OAAO,KAAK;IAAE;IACtC,MAAM0H,QAAQ,GAAG,IAAI,CAAC1H,UAAU,CAAC0H,QAAe;IAChD,OACE,CAAC,CAACA,QAAQ,CAACpK,WAAW,EAAEuE,KAAK,IAC7B,CAAC,CAAC6F,QAAQ,CAACjK,OAAO,EAAEoE,KAAK,IACzB,CAAC,CAAC6F,QAAQ,CAAC/J,eAAe,EAAEkE,KAAK,IACjC,CAAC,CAAC6F,QAAQ,CAAC7J,SAAS,EAAEgE,KAAK;EAE/B;;qCAlSWxB,oCAAoC,EAAA/D,EAAA,CAAAqL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvL,EAAA,CAAAqL,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAzL,EAAA,CAAAqL,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA3L,EAAA,CAAAqL,iBAAA,CAAAO,EAAA,CAAAC,wBAAA,GAAA7L,EAAA,CAAAqL,iBAAA,CAAAS,EAAA,CAAAC,gBAAA;EAAA;;UAApChI,oCAAoC;IAAAiI,SAAA;IAAAC,MAAA;MAAA5H,QAAA;MAAAC,YAAA;MAAA/D,cAAA;MAAAgE,kBAAA;MAAAC,UAAA;MAAAC,cAAA;IAAA;IAAAyH,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCT7CvM,EAHJ,CAAAC,cAAA,aAAkC,aAEW,aACR;QAC/BD,EAAA,CAAAyM,uBAAA,GAAc;QAEZzM,EADA,CAAA4C,UAAA,IAAA8J,mDAAA,iBAAoB,IAAAC,mDAAA,iBACC;;QAEzB3M,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAAyB,WAC2C;QAArBD,EAAA,CAAA4B,UAAA,mBAAAgL,iEAAA;UAAA5M,EAAA,CAAAY,aAAA,CAAAiM,GAAA;UAAA,OAAA7M,EAAA,CAAAiB,WAAA,CAASuL,GAAA,CAAA/I,QAAA,EAAU;QAAA,EAAC;QAErEzD,EAFsE,CAAAG,YAAA,EAAI,EAClE,EACF;QAMAH,EAHN,CAAAC,cAAA,aAAwB,aACyC,WACxD,iBACmE;QAAnBD,EAAA,CAAA4B,UAAA,mBAAAkL,uEAAA;UAAA9M,EAAA,CAAAY,aAAA,CAAAiM,GAAA;UAAA,OAAA7M,EAAA,CAAAiB,WAAA,CAASuL,GAAA,CAAA3F,MAAA,EAAQ;QAAA,EAAC;QAAC7G,EAAA,CAAAE,MAAA,YAAI;QAC5EF,EAD4E,CAAAG,YAAA,EAAS,EAC/E;QAEJH,EADF,CAAAC,cAAA,eAAuC,aAC8C;QAAAD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACxGH,EAAA,CAAAC,cAAA,oBAAqG;QAApCD,EAAA,CAAA4B,UAAA,oBAAAmL,uEAAArM,MAAA;UAAAV,EAAA,CAAAY,aAAA,CAAAiM,GAAA;UAAA,OAAA7M,EAAA,CAAAiB,WAAA,CAAUuL,GAAA,CAAAvF,cAAA,CAAAvG,MAAA,CAAsB;QAAA,EAAC;QAAlGV,EAAA,CAAAG,YAAA,EAAqG;QACrGH,EAAA,CAAAC,cAAA,kBAAgI;QAA9DD,EAAA,CAAA4B,UAAA,mBAAAoL,uEAAA;UAAAhN,EAAA,CAAAY,aAAA,CAAAiM,GAAA;UAAA,MAAAI,YAAA,GAAAjN,EAAA,CAAAkN,WAAA;UAAA,OAAAlN,EAAA,CAAAiB,WAAA,CAASuL,GAAA,CAAA1F,iBAAA,CAAAmG,YAAA,CAA4B;QAAA,EAAC;QAAwBjN,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACrJH,EAAA,CAAAC,cAAA,kBAA+E;QAA1BD,EAAA,CAAA4B,UAAA,mBAAAuL,uEAAA;UAAAnN,EAAA,CAAAY,aAAA,CAAAiM,GAAA;UAAA,OAAA7M,EAAA,CAAAiB,WAAA,CAAAuL,GAAA,CAAA7H,QAAA,GAAoB,IAAI;QAAA,EAAC;QAAC3E,EAAA,CAAAE,MAAA,yBAAiB;QAEpGF,EAFoG,CAAAG,YAAA,EAAS,EACrG,EACF;QAENH,EAAA,CAAAC,cAAA,eAAgD;QAC9CD,EAAA,CAAAE,MAAA,kHAAyG;QAAAF,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAE,MAAA,oCAC9I;QAAAF,EAAA,CAAAG,YAAA,EAAM;QA8CNH,EA3CA,CAAA4C,UAAA,KAAAwK,oDAAA,mBAA+C,KAAAC,qDAAA,qBA2CoC;QA8CvFrN,EADE,CAAAG,YAAA,EAAM,EACF;;;QArHQH,EAAA,CAAAI,SAAA,GAAY;QAAZJ,EAAA,CAAAqC,UAAA,SAAAmK,GAAA,CAAA1I,MAAA,CAAY;QACZ9D,EAAA,CAAAI,SAAA,EAAa;QAAbJ,EAAA,CAAAqC,UAAA,UAAAmK,GAAA,CAAA1I,MAAA,CAAa;QAehB9D,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAqC,UAAA,SAAAmK,GAAA,CAAA5H,eAAA,EAAA5E,EAAA,CAAAsN,aAAA,CAAwB;QAE8EtN,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAqC,UAAA,aAAAmK,GAAA,CAAAlK,SAAA,CAAsB;QAU7HtC,EAAA,CAAAI,SAAA,GAA0B;QAA1BJ,EAAA,CAAAqC,UAAA,SAAAmK,GAAA,CAAA1J,YAAA,kBAAA0J,GAAA,CAAA1J,YAAA,CAAAC,MAAA,CAA0B;QA2CzB/C,EAAA,CAAAI,SAAA,EAAc;QAAdJ,EAAA,CAAAqC,UAAA,SAAAmK,GAAA,CAAA7H,QAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}