{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.timestamp = void 0;\nvar dateTimestampProvider_1 = require(\"../scheduler/dateTimestampProvider\");\nvar map_1 = require(\"./map\");\nfunction timestamp(timestampProvider) {\n  if (timestampProvider === void 0) {\n    timestampProvider = dateTimestampProvider_1.dateTimestampProvider;\n  }\n  return map_1.map(function (value) {\n    return {\n      value: value,\n      timestamp: timestampProvider.now()\n    };\n  });\n}\nexports.timestamp = timestamp;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "timestamp", "dateTimestampProvider_1", "require", "map_1", "timestampProvider", "dateTimestampProvider", "map", "now"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/timestamp.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.timestamp = void 0;\nvar dateTimestampProvider_1 = require(\"../scheduler/dateTimestampProvider\");\nvar map_1 = require(\"./map\");\nfunction timestamp(timestampProvider) {\n    if (timestampProvider === void 0) { timestampProvider = dateTimestampProvider_1.dateTimestampProvider; }\n    return map_1.map(function (value) { return ({ value: value, timestamp: timestampProvider.now() }); });\n}\nexports.timestamp = timestamp;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,uBAAuB,GAAGC,OAAO,CAAC,oCAAoC,CAAC;AAC3E,IAAIC,KAAK,GAAGD,OAAO,CAAC,OAAO,CAAC;AAC5B,SAASF,SAASA,CAACI,iBAAiB,EAAE;EAClC,IAAIA,iBAAiB,KAAK,KAAK,CAAC,EAAE;IAAEA,iBAAiB,GAAGH,uBAAuB,CAACI,qBAAqB;EAAE;EACvG,OAAOF,KAAK,CAACG,GAAG,CAAC,UAAUP,KAAK,EAAE;IAAE,OAAQ;MAAEA,KAAK,EAAEA,KAAK;MAAEC,SAAS,EAAEI,iBAAiB,CAACG,GAAG,CAAC;IAAE,CAAC;EAAG,CAAC,CAAC;AACzG;AACAT,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}