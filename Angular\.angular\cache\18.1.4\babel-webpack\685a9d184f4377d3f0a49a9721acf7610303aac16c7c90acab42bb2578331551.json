{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.operate = exports.hasLift = void 0;\nvar isFunction_1 = require(\"./isFunction\");\nfunction hasLift(source) {\n  return isFunction_1.isFunction(source === null || source === void 0 ? void 0 : source.lift);\n}\nexports.hasLift = hasLift;\nfunction operate(init) {\n  return function (source) {\n    if (hasLift(source)) {\n      return source.lift(function (liftedSource) {\n        try {\n          return init(liftedSource, this);\n        } catch (err) {\n          this.error(err);\n        }\n      });\n    }\n    throw new TypeError('Unable to lift unknown Observable type');\n  };\n}\nexports.operate = operate;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "operate", "hasLift", "isFunction_1", "require", "source", "isFunction", "lift", "init", "liftedSource", "err", "error", "TypeError"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/lift.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.operate = exports.hasLift = void 0;\nvar isFunction_1 = require(\"./isFunction\");\nfunction hasLift(source) {\n    return isFunction_1.isFunction(source === null || source === void 0 ? void 0 : source.lift);\n}\nexports.hasLift = hasLift;\nfunction operate(init) {\n    return function (source) {\n        if (hasLift(source)) {\n            return source.lift(function (liftedSource) {\n                try {\n                    return init(liftedSource, this);\n                }\n                catch (err) {\n                    this.error(err);\n                }\n            });\n        }\n        throw new TypeError('Unable to lift unknown Observable type');\n    };\n}\nexports.operate = operate;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,OAAO,GAAGF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AAC1C,IAAIC,YAAY,GAAGC,OAAO,CAAC,cAAc,CAAC;AAC1C,SAASF,OAAOA,CAACG,MAAM,EAAE;EACrB,OAAOF,YAAY,CAACG,UAAU,CAACD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACE,IAAI,CAAC;AAC/F;AACAR,OAAO,CAACG,OAAO,GAAGA,OAAO;AACzB,SAASD,OAAOA,CAACO,IAAI,EAAE;EACnB,OAAO,UAAUH,MAAM,EAAE;IACrB,IAAIH,OAAO,CAACG,MAAM,CAAC,EAAE;MACjB,OAAOA,MAAM,CAACE,IAAI,CAAC,UAAUE,YAAY,EAAE;QACvC,IAAI;UACA,OAAOD,IAAI,CAACC,YAAY,EAAE,IAAI,CAAC;QACnC,CAAC,CACD,OAAOC,GAAG,EAAE;UACR,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC;QACnB;MACJ,CAAC,CAAC;IACN;IACA,MAAM,IAAIE,SAAS,CAAC,wCAAwC,CAAC;EACjE,CAAC;AACL;AACAb,OAAO,CAACE,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}