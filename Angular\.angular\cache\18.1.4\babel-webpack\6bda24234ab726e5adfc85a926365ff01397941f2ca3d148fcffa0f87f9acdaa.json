{"ast": null, "code": "\"use strict\";\n\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.bufferToggle = void 0;\nvar Subscription_1 = require(\"../Subscription\");\nvar lift_1 = require(\"../util/lift\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar noop_1 = require(\"../util/noop\");\nvar arrRemove_1 = require(\"../util/arrRemove\");\nfunction bufferToggle(openings, closingSelector) {\n  return lift_1.operate(function (source, subscriber) {\n    var buffers = [];\n    innerFrom_1.innerFrom(openings).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (openValue) {\n      var buffer = [];\n      buffers.push(buffer);\n      var closingSubscription = new Subscription_1.Subscription();\n      var emitBuffer = function () {\n        arrRemove_1.arrRemove(buffers, buffer);\n        subscriber.next(buffer);\n        closingSubscription.unsubscribe();\n      };\n      closingSubscription.add(innerFrom_1.innerFrom(closingSelector(openValue)).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, emitBuffer, noop_1.noop)));\n    }, noop_1.noop));\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var e_1, _a;\n      try {\n        for (var buffers_1 = __values(buffers), buffers_1_1 = buffers_1.next(); !buffers_1_1.done; buffers_1_1 = buffers_1.next()) {\n          var buffer = buffers_1_1.value;\n          buffer.push(value);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (buffers_1_1 && !buffers_1_1.done && (_a = buffers_1.return)) _a.call(buffers_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }, function () {\n      while (buffers.length > 0) {\n        subscriber.next(buffers.shift());\n      }\n      subscriber.complete();\n    }));\n  });\n}\nexports.bufferToggle = bufferToggle;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "Object", "defineProperty", "exports", "bufferToggle", "Subscription_1", "require", "lift_1", "innerFrom_1", "OperatorSubscriber_1", "noop_1", "arrRemove_1", "openings", "closingSelector", "operate", "source", "subscriber", "buffers", "innerFrom", "subscribe", "createOperatorSubscriber", "openValue", "buffer", "push", "closingSubscription", "Subscription", "emitB<PERSON>er", "arr<PERSON><PERSON><PERSON>", "unsubscribe", "add", "noop", "e_1", "_a", "buffers_1", "buffers_1_1", "e_1_1", "error", "return", "shift", "complete"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/bufferToggle.js"], "sourcesContent": ["\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bufferToggle = void 0;\nvar Subscription_1 = require(\"../Subscription\");\nvar lift_1 = require(\"../util/lift\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar noop_1 = require(\"../util/noop\");\nvar arrRemove_1 = require(\"../util/arrRemove\");\nfunction bufferToggle(openings, closingSelector) {\n    return lift_1.operate(function (source, subscriber) {\n        var buffers = [];\n        innerFrom_1.innerFrom(openings).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (openValue) {\n            var buffer = [];\n            buffers.push(buffer);\n            var closingSubscription = new Subscription_1.Subscription();\n            var emitBuffer = function () {\n                arrRemove_1.arrRemove(buffers, buffer);\n                subscriber.next(buffer);\n                closingSubscription.unsubscribe();\n            };\n            closingSubscription.add(innerFrom_1.innerFrom(closingSelector(openValue)).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, emitBuffer, noop_1.noop)));\n        }, noop_1.noop));\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            var e_1, _a;\n            try {\n                for (var buffers_1 = __values(buffers), buffers_1_1 = buffers_1.next(); !buffers_1_1.done; buffers_1_1 = buffers_1.next()) {\n                    var buffer = buffers_1_1.value;\n                    buffer.push(value);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (buffers_1_1 && !buffers_1_1.done && (_a = buffers_1.return)) _a.call(buffers_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }, function () {\n            while (buffers.length > 0) {\n                subscriber.next(buffers.shift());\n            }\n            subscriber.complete();\n        }));\n    });\n}\nexports.bufferToggle = bufferToggle;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACDW,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEL,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DK,OAAO,CAACC,YAAY,GAAG,KAAK,CAAC;AAC7B,IAAIC,cAAc,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAC/C,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,WAAW,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AACpD,IAAIG,oBAAoB,GAAGH,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAII,MAAM,GAAGJ,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIK,WAAW,GAAGL,OAAO,CAAC,mBAAmB,CAAC;AAC9C,SAASF,YAAYA,CAACQ,QAAQ,EAAEC,eAAe,EAAE;EAC7C,OAAON,MAAM,CAACO,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,OAAO,GAAG,EAAE;IAChBT,WAAW,CAACU,SAAS,CAACN,QAAQ,CAAC,CAACO,SAAS,CAACV,oBAAoB,CAACW,wBAAwB,CAACJ,UAAU,EAAE,UAAUK,SAAS,EAAE;MACrH,IAAIC,MAAM,GAAG,EAAE;MACfL,OAAO,CAACM,IAAI,CAACD,MAAM,CAAC;MACpB,IAAIE,mBAAmB,GAAG,IAAInB,cAAc,CAACoB,YAAY,CAAC,CAAC;MAC3D,IAAIC,UAAU,GAAG,SAAAA,CAAA,EAAY;QACzBf,WAAW,CAACgB,SAAS,CAACV,OAAO,EAAEK,MAAM,CAAC;QACtCN,UAAU,CAACnB,IAAI,CAACyB,MAAM,CAAC;QACvBE,mBAAmB,CAACI,WAAW,CAAC,CAAC;MACrC,CAAC;MACDJ,mBAAmB,CAACK,GAAG,CAACrB,WAAW,CAACU,SAAS,CAACL,eAAe,CAACQ,SAAS,CAAC,CAAC,CAACF,SAAS,CAACV,oBAAoB,CAACW,wBAAwB,CAACJ,UAAU,EAAEU,UAAU,EAAEhB,MAAM,CAACoB,IAAI,CAAC,CAAC,CAAC;IAC5K,CAAC,EAAEpB,MAAM,CAACoB,IAAI,CAAC,CAAC;IAChBf,MAAM,CAACI,SAAS,CAACV,oBAAoB,CAACW,wBAAwB,CAACJ,UAAU,EAAE,UAAUlB,KAAK,EAAE;MACxF,IAAIiC,GAAG,EAAEC,EAAE;MACX,IAAI;QACA,KAAK,IAAIC,SAAS,GAAG7C,QAAQ,CAAC6B,OAAO,CAAC,EAAEiB,WAAW,GAAGD,SAAS,CAACpC,IAAI,CAAC,CAAC,EAAE,CAACqC,WAAW,CAACnC,IAAI,EAAEmC,WAAW,GAAGD,SAAS,CAACpC,IAAI,CAAC,CAAC,EAAE;UACvH,IAAIyB,MAAM,GAAGY,WAAW,CAACpC,KAAK;UAC9BwB,MAAM,CAACC,IAAI,CAACzB,KAAK,CAAC;QACtB;MACJ,CAAC,CACD,OAAOqC,KAAK,EAAE;QAAEJ,GAAG,GAAG;UAAEK,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAID,WAAW,IAAI,CAACA,WAAW,CAACnC,IAAI,KAAKiC,EAAE,GAAGC,SAAS,CAACI,MAAM,CAAC,EAAEL,EAAE,CAACrC,IAAI,CAACsC,SAAS,CAAC;QACvF,CAAC,SACO;UAAE,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAACK,KAAK;QAAE;MACxC;IACJ,CAAC,EAAE,YAAY;MACX,OAAOnB,OAAO,CAACrB,MAAM,GAAG,CAAC,EAAE;QACvBoB,UAAU,CAACnB,IAAI,CAACoB,OAAO,CAACqB,KAAK,CAAC,CAAC,CAAC;MACpC;MACAtB,UAAU,CAACuB,QAAQ,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACApC,OAAO,CAACC,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}