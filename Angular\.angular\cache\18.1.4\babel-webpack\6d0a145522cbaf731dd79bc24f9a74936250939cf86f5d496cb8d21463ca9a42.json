{"ast": null, "code": "\"use strict\";\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from) {\n  for (var i = 0, il = from.length, j = to.length; i < il; i++, j++) to[j] = from[i];\n  return to;\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.bindCallbackInternals = void 0;\nvar isScheduler_1 = require(\"../util/isScheduler\");\nvar Observable_1 = require(\"../Observable\");\nvar subscribeOn_1 = require(\"../operators/subscribeOn\");\nvar mapOneOrManyArgs_1 = require(\"../util/mapOneOrManyArgs\");\nvar observeOn_1 = require(\"../operators/observeOn\");\nvar AsyncSubject_1 = require(\"../AsyncSubject\");\nfunction bindCallbackInternals(isNodeStyle, callbackFunc, resultSelector, scheduler) {\n  if (resultSelector) {\n    if (isScheduler_1.isScheduler(resultSelector)) {\n      scheduler = resultSelector;\n    } else {\n      return function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return bindCallbackInternals(isNodeStyle, callbackFunc, scheduler).apply(this, args).pipe(mapOneOrManyArgs_1.mapOneOrManyArgs(resultSelector));\n      };\n    }\n  }\n  if (scheduler) {\n    return function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return bindCallbackInternals(isNodeStyle, callbackFunc).apply(this, args).pipe(subscribeOn_1.subscribeOn(scheduler), observeOn_1.observeOn(scheduler));\n    };\n  }\n  return function () {\n    var _this = this;\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var subject = new AsyncSubject_1.AsyncSubject();\n    var uninitialized = true;\n    return new Observable_1.Observable(function (subscriber) {\n      var subs = subject.subscribe(subscriber);\n      if (uninitialized) {\n        uninitialized = false;\n        var isAsync_1 = false;\n        var isComplete_1 = false;\n        callbackFunc.apply(_this, __spreadArray(__spreadArray([], __read(args)), [function () {\n          var results = [];\n          for (var _i = 0; _i < arguments.length; _i++) {\n            results[_i] = arguments[_i];\n          }\n          if (isNodeStyle) {\n            var err = results.shift();\n            if (err != null) {\n              subject.error(err);\n              return;\n            }\n          }\n          subject.next(1 < results.length ? results : results[0]);\n          isComplete_1 = true;\n          if (isAsync_1) {\n            subject.complete();\n          }\n        }]));\n        if (isComplete_1) {\n          subject.complete();\n        }\n        isAsync_1 = true;\n      }\n      return subs;\n    });\n  };\n}\nexports.bindCallbackInternals = bindCallbackInternals;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "il", "length", "j", "Object", "defineProperty", "exports", "bindCallbackInternals", "isScheduler_1", "require", "Observable_1", "subscribeOn_1", "mapOneOrManyArgs_1", "observeOn_1", "AsyncSubject_1", "isNodeStyle", "callback<PERSON><PERSON><PERSON>", "resultSelector", "scheduler", "isScheduler", "args", "_i", "arguments", "apply", "pipe", "mapOneOrManyArgs", "subscribeOn", "observeOn", "_this", "subject", "AsyncSubject", "uninitialized", "Observable", "subscriber", "subs", "subscribe", "isAsync_1", "isComplete_1", "results", "err", "shift", "complete"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/bindCallbackInternals.js"], "sourcesContent": ["\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from) {\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\n        to[j] = from[i];\n    return to;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bindCallbackInternals = void 0;\nvar isScheduler_1 = require(\"../util/isScheduler\");\nvar Observable_1 = require(\"../Observable\");\nvar subscribeOn_1 = require(\"../operators/subscribeOn\");\nvar mapOneOrManyArgs_1 = require(\"../util/mapOneOrManyArgs\");\nvar observeOn_1 = require(\"../operators/observeOn\");\nvar AsyncSubject_1 = require(\"../AsyncSubject\");\nfunction bindCallbackInternals(isNodeStyle, callbackFunc, resultSelector, scheduler) {\n    if (resultSelector) {\n        if (isScheduler_1.isScheduler(resultSelector)) {\n            scheduler = resultSelector;\n        }\n        else {\n            return function () {\n                var args = [];\n                for (var _i = 0; _i < arguments.length; _i++) {\n                    args[_i] = arguments[_i];\n                }\n                return bindCallbackInternals(isNodeStyle, callbackFunc, scheduler)\n                    .apply(this, args)\n                    .pipe(mapOneOrManyArgs_1.mapOneOrManyArgs(resultSelector));\n            };\n        }\n    }\n    if (scheduler) {\n        return function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return bindCallbackInternals(isNodeStyle, callbackFunc)\n                .apply(this, args)\n                .pipe(subscribeOn_1.subscribeOn(scheduler), observeOn_1.observeOn(scheduler));\n        };\n    }\n    return function () {\n        var _this = this;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var subject = new AsyncSubject_1.AsyncSubject();\n        var uninitialized = true;\n        return new Observable_1.Observable(function (subscriber) {\n            var subs = subject.subscribe(subscriber);\n            if (uninitialized) {\n                uninitialized = false;\n                var isAsync_1 = false;\n                var isComplete_1 = false;\n                callbackFunc.apply(_this, __spreadArray(__spreadArray([], __read(args)), [\n                    function () {\n                        var results = [];\n                        for (var _i = 0; _i < arguments.length; _i++) {\n                            results[_i] = arguments[_i];\n                        }\n                        if (isNodeStyle) {\n                            var err = results.shift();\n                            if (err != null) {\n                                subject.error(err);\n                                return;\n                            }\n                        }\n                        subject.next(1 < results.length ? results : results[0]);\n                        isComplete_1 = true;\n                        if (isAsync_1) {\n                            subject.complete();\n                        }\n                    },\n                ]));\n                if (isComplete_1) {\n                    subject.complete();\n                }\n                isAsync_1 = true;\n            }\n            return subs;\n        });\n    };\n}\nexports.bindCallbackInternals = bindCallbackInternals;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,MAAM,GAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAK,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC;EAC1D,IAAI,CAACF,CAAC,EAAE,OAAOF,CAAC;EAChB,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAI,CAACN,CAAC,CAAC;IAAEO,CAAC;IAAEC,EAAE,GAAG,EAAE;IAAEC,CAAC;EAChC,IAAI;IACA,OAAO,CAACR,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAEH,EAAE,CAACI,IAAI,CAACL,CAAC,CAACM,KAAK,CAAC;EAC9E,CAAC,CACD,OAAOC,KAAK,EAAE;IAAEL,CAAC,GAAG;MAAEK,KAAK,EAAEA;IAAM,CAAC;EAAE,CAAC,SAC/B;IACJ,IAAI;MACA,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAI,KAAKT,CAAC,GAAGG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAEH,CAAC,CAACI,IAAI,CAACD,CAAC,CAAC;IACpD,CAAC,SACO;MAAE,IAAII,CAAC,EAAE,MAAMA,CAAC,CAACK,KAAK;IAAE;EACpC;EACA,OAAON,EAAE;AACb,CAAC;AACD,IAAIO,aAAa,GAAI,IAAI,IAAI,IAAI,CAACA,aAAa,IAAK,UAAUC,EAAE,EAAEC,IAAI,EAAE;EACpE,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEa,EAAE,GAAGD,IAAI,CAACE,MAAM,EAAEC,CAAC,GAAGJ,EAAE,CAACG,MAAM,EAAEd,CAAC,GAAGa,EAAE,EAAEb,CAAC,EAAE,EAAEe,CAAC,EAAE,EAC7DJ,EAAE,CAACI,CAAC,CAAC,GAAGH,IAAI,CAACZ,CAAC,CAAC;EACnB,OAAOW,EAAE;AACb,CAAC;AACDK,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEV,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DU,OAAO,CAACC,qBAAqB,GAAG,KAAK,CAAC;AACtC,IAAIC,aAAa,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAClD,IAAIC,YAAY,GAAGD,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIE,aAAa,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AACvD,IAAIG,kBAAkB,GAAGH,OAAO,CAAC,0BAA0B,CAAC;AAC5D,IAAII,WAAW,GAAGJ,OAAO,CAAC,wBAAwB,CAAC;AACnD,IAAIK,cAAc,GAAGL,OAAO,CAAC,iBAAiB,CAAC;AAC/C,SAASF,qBAAqBA,CAACQ,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,SAAS,EAAE;EACjF,IAAID,cAAc,EAAE;IAChB,IAAIT,aAAa,CAACW,WAAW,CAACF,cAAc,CAAC,EAAE;MAC3CC,SAAS,GAAGD,cAAc;IAC9B,CAAC,MACI;MACD,OAAO,YAAY;QACf,IAAIG,IAAI,GAAG,EAAE;QACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACpB,MAAM,EAAEmB,EAAE,EAAE,EAAE;UAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;QAC5B;QACA,OAAOd,qBAAqB,CAACQ,WAAW,EAAEC,YAAY,EAAEE,SAAS,CAAC,CAC7DK,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC,CACjBI,IAAI,CAACZ,kBAAkB,CAACa,gBAAgB,CAACR,cAAc,CAAC,CAAC;MAClE,CAAC;IACL;EACJ;EACA,IAAIC,SAAS,EAAE;IACX,OAAO,YAAY;MACf,IAAIE,IAAI,GAAG,EAAE;MACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACpB,MAAM,EAAEmB,EAAE,EAAE,EAAE;QAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;MAC5B;MACA,OAAOd,qBAAqB,CAACQ,WAAW,EAAEC,YAAY,CAAC,CAClDO,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC,CACjBI,IAAI,CAACb,aAAa,CAACe,WAAW,CAACR,SAAS,CAAC,EAAEL,WAAW,CAACc,SAAS,CAACT,SAAS,CAAC,CAAC;IACrF,CAAC;EACL;EACA,OAAO,YAAY;IACf,IAAIU,KAAK,GAAG,IAAI;IAChB,IAAIR,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACpB,MAAM,EAAEmB,EAAE,EAAE,EAAE;MAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC5B;IACA,IAAIQ,OAAO,GAAG,IAAIf,cAAc,CAACgB,YAAY,CAAC,CAAC;IAC/C,IAAIC,aAAa,GAAG,IAAI;IACxB,OAAO,IAAIrB,YAAY,CAACsB,UAAU,CAAC,UAAUC,UAAU,EAAE;MACrD,IAAIC,IAAI,GAAGL,OAAO,CAACM,SAAS,CAACF,UAAU,CAAC;MACxC,IAAIF,aAAa,EAAE;QACfA,aAAa,GAAG,KAAK;QACrB,IAAIK,SAAS,GAAG,KAAK;QACrB,IAAIC,YAAY,GAAG,KAAK;QACxBrB,YAAY,CAACO,KAAK,CAACK,KAAK,EAAE9B,aAAa,CAACA,aAAa,CAAC,EAAE,EAAEhB,MAAM,CAACsC,IAAI,CAAC,CAAC,EAAE,CACrE,YAAY;UACR,IAAIkB,OAAO,GAAG,EAAE;UAChB,KAAK,IAAIjB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACpB,MAAM,EAAEmB,EAAE,EAAE,EAAE;YAC1CiB,OAAO,CAACjB,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;UAC/B;UACA,IAAIN,WAAW,EAAE;YACb,IAAIwB,GAAG,GAAGD,OAAO,CAACE,KAAK,CAAC,CAAC;YACzB,IAAID,GAAG,IAAI,IAAI,EAAE;cACbV,OAAO,CAAChC,KAAK,CAAC0C,GAAG,CAAC;cAClB;YACJ;UACJ;UACAV,OAAO,CAACpC,IAAI,CAAC,CAAC,GAAG6C,OAAO,CAACpC,MAAM,GAAGoC,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,CAAC;UACvDD,YAAY,GAAG,IAAI;UACnB,IAAID,SAAS,EAAE;YACXP,OAAO,CAACY,QAAQ,CAAC,CAAC;UACtB;QACJ,CAAC,CACJ,CAAC,CAAC;QACH,IAAIJ,YAAY,EAAE;UACdR,OAAO,CAACY,QAAQ,CAAC,CAAC;QACtB;QACAL,SAAS,GAAG,IAAI;MACpB;MACA,OAAOF,IAAI;IACf,CAAC,CAAC;EACN,CAAC;AACL;AACA5B,OAAO,CAACC,qBAAqB,GAAGA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}