{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.TimeInterval = exports.timeInterval = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction timeInterval(scheduler) {\n  if (scheduler === void 0) {\n    scheduler = async_1.asyncScheduler;\n  }\n  return lift_1.operate(function (source, subscriber) {\n    var last = scheduler.now();\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var now = scheduler.now();\n      var interval = now - last;\n      last = now;\n      subscriber.next(new TimeInterval(value, interval));\n    }));\n  });\n}\nexports.timeInterval = timeInterval;\nvar TimeInterval = function () {\n  function TimeInterval(value, interval) {\n    this.value = value;\n    this.interval = interval;\n  }\n  return TimeInterval;\n}();\nexports.TimeInterval = TimeInterval;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "TimeInterval", "timeInterval", "async_1", "require", "lift_1", "OperatorSubscriber_1", "scheduler", "asyncScheduler", "operate", "source", "subscriber", "last", "now", "subscribe", "createOperatorSubscriber", "interval", "next"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/timeInterval.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TimeInterval = exports.timeInterval = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction timeInterval(scheduler) {\n    if (scheduler === void 0) { scheduler = async_1.asyncScheduler; }\n    return lift_1.operate(function (source, subscriber) {\n        var last = scheduler.now();\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            var now = scheduler.now();\n            var interval = now - last;\n            last = now;\n            subscriber.next(new TimeInterval(value, interval));\n        }));\n    });\n}\nexports.timeInterval = timeInterval;\nvar TimeInterval = (function () {\n    function TimeInterval(value, interval) {\n        this.value = value;\n        this.interval = interval;\n    }\n    return TimeInterval;\n}());\nexports.TimeInterval = TimeInterval;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACG,YAAY,GAAG,KAAK,CAAC;AACpD,IAAIC,OAAO,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAC3C,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,YAAYA,CAACK,SAAS,EAAE;EAC7B,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IAAEA,SAAS,GAAGJ,OAAO,CAACK,cAAc;EAAE;EAChE,OAAOH,MAAM,CAACI,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,IAAI,GAAGL,SAAS,CAACM,GAAG,CAAC,CAAC;IAC1BH,MAAM,CAACI,SAAS,CAACR,oBAAoB,CAACS,wBAAwB,CAACJ,UAAU,EAAE,UAAUX,KAAK,EAAE;MACxF,IAAIa,GAAG,GAAGN,SAAS,CAACM,GAAG,CAAC,CAAC;MACzB,IAAIG,QAAQ,GAAGH,GAAG,GAAGD,IAAI;MACzBA,IAAI,GAAGC,GAAG;MACVF,UAAU,CAACM,IAAI,CAAC,IAAIhB,YAAY,CAACD,KAAK,EAAEgB,QAAQ,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACAjB,OAAO,CAACG,YAAY,GAAGA,YAAY;AACnC,IAAID,YAAY,GAAI,YAAY;EAC5B,SAASA,YAAYA,CAACD,KAAK,EAAEgB,QAAQ,EAAE;IACnC,IAAI,CAAChB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACgB,QAAQ,GAAGA,QAAQ;EAC5B;EACA,OAAOf,YAAY;AACvB,CAAC,CAAC,CAAE;AACJF,OAAO,CAACE,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}