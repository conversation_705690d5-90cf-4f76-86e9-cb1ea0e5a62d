{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.arrRemove = void 0;\nfunction arrRemove(arr, item) {\n  if (arr) {\n    var index = arr.indexOf(item);\n    0 <= index && arr.splice(index, 1);\n  }\n}\nexports.arrRemove = arrRemove;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "arr<PERSON><PERSON><PERSON>", "arr", "item", "index", "indexOf", "splice"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/arrRemove.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.arrRemove = void 0;\nfunction arrRemove(arr, item) {\n    if (arr) {\n        var index = arr.indexOf(item);\n        0 <= index && arr.splice(index, 1);\n    }\n}\nexports.arrRemove = arrRemove;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,SAASA,SAASA,CAACC,GAAG,EAAEC,IAAI,EAAE;EAC1B,IAAID,GAAG,EAAE;IACL,IAAIE,KAAK,GAAGF,GAAG,CAACG,OAAO,CAACF,IAAI,CAAC;IAC7B,CAAC,IAAIC,KAAK,IAAIF,GAAG,CAACI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EACtC;AACJ;AACAL,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}