{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.groupBy = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar Subject_1 = require(\"../Subject\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction groupBy(keySelector, elementOrOptions, duration, connector) {\n  return lift_1.operate(function (source, subscriber) {\n    var element;\n    if (!elementOrOptions || typeof elementOrOptions === 'function') {\n      element = elementOrOptions;\n    } else {\n      duration = elementOrOptions.duration, element = elementOrOptions.element, connector = elementOrOptions.connector;\n    }\n    var groups = new Map();\n    var notify = function (cb) {\n      groups.forEach(cb);\n      cb(subscriber);\n    };\n    var handleError = function (err) {\n      return notify(function (consumer) {\n        return consumer.error(err);\n      });\n    };\n    var activeGroups = 0;\n    var teardownAttempted = false;\n    var groupBySourceSubscriber = new OperatorSubscriber_1.OperatorSubscriber(subscriber, function (value) {\n      try {\n        var key_1 = keySelector(value);\n        var group_1 = groups.get(key_1);\n        if (!group_1) {\n          groups.set(key_1, group_1 = connector ? connector() : new Subject_1.Subject());\n          var grouped = createGroupedObservable(key_1, group_1);\n          subscriber.next(grouped);\n          if (duration) {\n            var durationSubscriber_1 = OperatorSubscriber_1.createOperatorSubscriber(group_1, function () {\n              group_1.complete();\n              durationSubscriber_1 === null || durationSubscriber_1 === void 0 ? void 0 : durationSubscriber_1.unsubscribe();\n            }, undefined, undefined, function () {\n              return groups.delete(key_1);\n            });\n            groupBySourceSubscriber.add(innerFrom_1.innerFrom(duration(grouped)).subscribe(durationSubscriber_1));\n          }\n        }\n        group_1.next(element ? element(value) : value);\n      } catch (err) {\n        handleError(err);\n      }\n    }, function () {\n      return notify(function (consumer) {\n        return consumer.complete();\n      });\n    }, handleError, function () {\n      return groups.clear();\n    }, function () {\n      teardownAttempted = true;\n      return activeGroups === 0;\n    });\n    source.subscribe(groupBySourceSubscriber);\n    function createGroupedObservable(key, groupSubject) {\n      var result = new Observable_1.Observable(function (groupSubscriber) {\n        activeGroups++;\n        var innerSub = groupSubject.subscribe(groupSubscriber);\n        return function () {\n          innerSub.unsubscribe();\n          --activeGroups === 0 && teardownAttempted && groupBySourceSubscriber.unsubscribe();\n        };\n      });\n      result.key = key;\n      return result;\n    }\n  });\n}\nexports.groupBy = groupBy;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "groupBy", "Observable_1", "require", "innerFrom_1", "Subject_1", "lift_1", "OperatorSubscriber_1", "keySelector", "elementOrOptions", "duration", "connector", "operate", "source", "subscriber", "element", "groups", "Map", "notify", "cb", "for<PERSON>ach", "handleError", "err", "consumer", "error", "activeGroups", "teardownAttempted", "groupBySourceSubscriber", "OperatorSubscriber", "key_1", "group_1", "get", "set", "Subject", "grouped", "createGroupedObservable", "next", "durationSubscriber_1", "createOperatorSubscriber", "complete", "unsubscribe", "undefined", "delete", "add", "innerFrom", "subscribe", "clear", "key", "groupSubject", "result", "Observable", "groupSubscriber", "innerSub"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/groupBy.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.groupBy = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar Subject_1 = require(\"../Subject\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction groupBy(keySelector, elementOrOptions, duration, connector) {\n    return lift_1.operate(function (source, subscriber) {\n        var element;\n        if (!elementOrOptions || typeof elementOrOptions === 'function') {\n            element = elementOrOptions;\n        }\n        else {\n            (duration = elementOrOptions.duration, element = elementOrOptions.element, connector = elementOrOptions.connector);\n        }\n        var groups = new Map();\n        var notify = function (cb) {\n            groups.forEach(cb);\n            cb(subscriber);\n        };\n        var handleError = function (err) { return notify(function (consumer) { return consumer.error(err); }); };\n        var activeGroups = 0;\n        var teardownAttempted = false;\n        var groupBySourceSubscriber = new OperatorSubscriber_1.OperatorSubscriber(subscriber, function (value) {\n            try {\n                var key_1 = keySelector(value);\n                var group_1 = groups.get(key_1);\n                if (!group_1) {\n                    groups.set(key_1, (group_1 = connector ? connector() : new Subject_1.Subject()));\n                    var grouped = createGroupedObservable(key_1, group_1);\n                    subscriber.next(grouped);\n                    if (duration) {\n                        var durationSubscriber_1 = OperatorSubscriber_1.createOperatorSubscriber(group_1, function () {\n                            group_1.complete();\n                            durationSubscriber_1 === null || durationSubscriber_1 === void 0 ? void 0 : durationSubscriber_1.unsubscribe();\n                        }, undefined, undefined, function () { return groups.delete(key_1); });\n                        groupBySourceSubscriber.add(innerFrom_1.innerFrom(duration(grouped)).subscribe(durationSubscriber_1));\n                    }\n                }\n                group_1.next(element ? element(value) : value);\n            }\n            catch (err) {\n                handleError(err);\n            }\n        }, function () { return notify(function (consumer) { return consumer.complete(); }); }, handleError, function () { return groups.clear(); }, function () {\n            teardownAttempted = true;\n            return activeGroups === 0;\n        });\n        source.subscribe(groupBySourceSubscriber);\n        function createGroupedObservable(key, groupSubject) {\n            var result = new Observable_1.Observable(function (groupSubscriber) {\n                activeGroups++;\n                var innerSub = groupSubject.subscribe(groupSubscriber);\n                return function () {\n                    innerSub.unsubscribe();\n                    --activeGroups === 0 && teardownAttempted && groupBySourceSubscriber.unsubscribe();\n                };\n            });\n            result.key = key;\n            return result;\n        }\n    });\n}\nexports.groupBy = groupBy;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIC,WAAW,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AACpD,IAAIE,SAAS,GAAGF,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIG,MAAM,GAAGH,OAAO,CAAC,cAAc,CAAC;AACpC,IAAII,oBAAoB,GAAGJ,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,OAAOA,CAACO,WAAW,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,SAAS,EAAE;EACjE,OAAOL,MAAM,CAACM,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,OAAO;IACX,IAAI,CAACN,gBAAgB,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;MAC7DM,OAAO,GAAGN,gBAAgB;IAC9B,CAAC,MACI;MACAC,QAAQ,GAAGD,gBAAgB,CAACC,QAAQ,EAAEK,OAAO,GAAGN,gBAAgB,CAACM,OAAO,EAAEJ,SAAS,GAAGF,gBAAgB,CAACE,SAAS;IACrH;IACA,IAAIK,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAIC,MAAM,GAAG,SAAAA,CAAUC,EAAE,EAAE;MACvBH,MAAM,CAACI,OAAO,CAACD,EAAE,CAAC;MAClBA,EAAE,CAACL,UAAU,CAAC;IAClB,CAAC;IACD,IAAIO,WAAW,GAAG,SAAAA,CAAUC,GAAG,EAAE;MAAE,OAAOJ,MAAM,CAAC,UAAUK,QAAQ,EAAE;QAAE,OAAOA,QAAQ,CAACC,KAAK,CAACF,GAAG,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC;IACxG,IAAIG,YAAY,GAAG,CAAC;IACpB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,uBAAuB,GAAG,IAAIpB,oBAAoB,CAACqB,kBAAkB,CAACd,UAAU,EAAE,UAAUd,KAAK,EAAE;MACnG,IAAI;QACA,IAAI6B,KAAK,GAAGrB,WAAW,CAACR,KAAK,CAAC;QAC9B,IAAI8B,OAAO,GAAGd,MAAM,CAACe,GAAG,CAACF,KAAK,CAAC;QAC/B,IAAI,CAACC,OAAO,EAAE;UACVd,MAAM,CAACgB,GAAG,CAACH,KAAK,EAAGC,OAAO,GAAGnB,SAAS,GAAGA,SAAS,CAAC,CAAC,GAAG,IAAIN,SAAS,CAAC4B,OAAO,CAAC,CAAE,CAAC;UAChF,IAAIC,OAAO,GAAGC,uBAAuB,CAACN,KAAK,EAAEC,OAAO,CAAC;UACrDhB,UAAU,CAACsB,IAAI,CAACF,OAAO,CAAC;UACxB,IAAIxB,QAAQ,EAAE;YACV,IAAI2B,oBAAoB,GAAG9B,oBAAoB,CAAC+B,wBAAwB,CAACR,OAAO,EAAE,YAAY;cAC1FA,OAAO,CAACS,QAAQ,CAAC,CAAC;cAClBF,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACG,WAAW,CAAC,CAAC;YAClH,CAAC,EAAEC,SAAS,EAAEA,SAAS,EAAE,YAAY;cAAE,OAAOzB,MAAM,CAAC0B,MAAM,CAACb,KAAK,CAAC;YAAE,CAAC,CAAC;YACtEF,uBAAuB,CAACgB,GAAG,CAACvC,WAAW,CAACwC,SAAS,CAAClC,QAAQ,CAACwB,OAAO,CAAC,CAAC,CAACW,SAAS,CAACR,oBAAoB,CAAC,CAAC;UACzG;QACJ;QACAP,OAAO,CAACM,IAAI,CAACrB,OAAO,GAAGA,OAAO,CAACf,KAAK,CAAC,GAAGA,KAAK,CAAC;MAClD,CAAC,CACD,OAAOsB,GAAG,EAAE;QACRD,WAAW,CAACC,GAAG,CAAC;MACpB;IACJ,CAAC,EAAE,YAAY;MAAE,OAAOJ,MAAM,CAAC,UAAUK,QAAQ,EAAE;QAAE,OAAOA,QAAQ,CAACgB,QAAQ,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,EAAElB,WAAW,EAAE,YAAY;MAAE,OAAOL,MAAM,CAAC8B,KAAK,CAAC,CAAC;IAAE,CAAC,EAAE,YAAY;MACrJpB,iBAAiB,GAAG,IAAI;MACxB,OAAOD,YAAY,KAAK,CAAC;IAC7B,CAAC,CAAC;IACFZ,MAAM,CAACgC,SAAS,CAAClB,uBAAuB,CAAC;IACzC,SAASQ,uBAAuBA,CAACY,GAAG,EAAEC,YAAY,EAAE;MAChD,IAAIC,MAAM,GAAG,IAAI/C,YAAY,CAACgD,UAAU,CAAC,UAAUC,eAAe,EAAE;QAChE1B,YAAY,EAAE;QACd,IAAI2B,QAAQ,GAAGJ,YAAY,CAACH,SAAS,CAACM,eAAe,CAAC;QACtD,OAAO,YAAY;UACfC,QAAQ,CAACZ,WAAW,CAAC,CAAC;UACtB,EAAEf,YAAY,KAAK,CAAC,IAAIC,iBAAiB,IAAIC,uBAAuB,CAACa,WAAW,CAAC,CAAC;QACtF,CAAC;MACL,CAAC,CAAC;MACFS,MAAM,CAACF,GAAG,GAAGA,GAAG;MAChB,OAAOE,MAAM;IACjB;EACJ,CAAC,CAAC;AACN;AACAlD,OAAO,CAACE,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}