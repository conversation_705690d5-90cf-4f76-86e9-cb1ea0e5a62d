{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.empty = exports.EMPTY = void 0;\nvar Observable_1 = require(\"../Observable\");\nexports.EMPTY = new Observable_1.Observable(function (subscriber) {\n  return subscriber.complete();\n});\nfunction empty(scheduler) {\n  return scheduler ? emptyScheduled(scheduler) : exports.EMPTY;\n}\nexports.empty = empty;\nfunction emptyScheduled(scheduler) {\n  return new Observable_1.Observable(function (subscriber) {\n    return scheduler.schedule(function () {\n      return subscriber.complete();\n    });\n  });\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "empty", "EMPTY", "Observable_1", "require", "Observable", "subscriber", "complete", "scheduler", "emptyScheduled", "schedule"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/empty.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.empty = exports.EMPTY = void 0;\nvar Observable_1 = require(\"../Observable\");\nexports.EMPTY = new Observable_1.Observable(function (subscriber) { return subscriber.complete(); });\nfunction empty(scheduler) {\n    return scheduler ? emptyScheduled(scheduler) : exports.EMPTY;\n}\nexports.empty = empty;\nfunction emptyScheduled(scheduler) {\n    return new Observable_1.Observable(function (subscriber) { return scheduler.schedule(function () { return subscriber.complete(); }); });\n}\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,KAAK,GAAGF,OAAO,CAACG,KAAK,GAAG,KAAK,CAAC;AACtC,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3CL,OAAO,CAACG,KAAK,GAAG,IAAIC,YAAY,CAACE,UAAU,CAAC,UAAUC,UAAU,EAAE;EAAE,OAAOA,UAAU,CAACC,QAAQ,CAAC,CAAC;AAAE,CAAC,CAAC;AACpG,SAASN,KAAKA,CAACO,SAAS,EAAE;EACtB,OAAOA,SAAS,GAAGC,cAAc,CAACD,SAAS,CAAC,GAAGT,OAAO,CAACG,KAAK;AAChE;AACAH,OAAO,CAACE,KAAK,GAAGA,KAAK;AACrB,SAASQ,cAAcA,CAACD,SAAS,EAAE;EAC/B,OAAO,IAAIL,YAAY,CAACE,UAAU,CAAC,UAAUC,UAAU,EAAE;IAAE,OAAOE,SAAS,CAACE,QAAQ,CAAC,YAAY;MAAE,OAAOJ,UAAU,CAACC,QAAQ,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,CAAC;AAC3I", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}