{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.using = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar innerFrom_1 = require(\"./innerFrom\");\nvar empty_1 = require(\"./empty\");\nfunction using(resourceFactory, observableFactory) {\n  return new Observable_1.Observable(function (subscriber) {\n    var resource = resourceFactory();\n    var result = observableFactory(resource);\n    var source = result ? innerFrom_1.innerFrom(result) : empty_1.EMPTY;\n    source.subscribe(subscriber);\n    return function () {\n      if (resource) {\n        resource.unsubscribe();\n      }\n    };\n  });\n}\nexports.using = using;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "using", "Observable_1", "require", "innerFrom_1", "empty_1", "resourceFactory", "observableFactory", "Observable", "subscriber", "resource", "result", "source", "innerFrom", "EMPTY", "subscribe", "unsubscribe"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/using.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.using = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar innerFrom_1 = require(\"./innerFrom\");\nvar empty_1 = require(\"./empty\");\nfunction using(resourceFactory, observableFactory) {\n    return new Observable_1.Observable(function (subscriber) {\n        var resource = resourceFactory();\n        var result = observableFactory(resource);\n        var source = result ? innerFrom_1.innerFrom(result) : empty_1.EMPTY;\n        source.subscribe(subscriber);\n        return function () {\n            if (resource) {\n                resource.unsubscribe();\n            }\n        };\n    });\n}\nexports.using = using;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,KAAK,GAAG,KAAK,CAAC;AACtB,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIC,WAAW,GAAGD,OAAO,CAAC,aAAa,CAAC;AACxC,IAAIE,OAAO,GAAGF,OAAO,CAAC,SAAS,CAAC;AAChC,SAASF,KAAKA,CAACK,eAAe,EAAEC,iBAAiB,EAAE;EAC/C,OAAO,IAAIL,YAAY,CAACM,UAAU,CAAC,UAAUC,UAAU,EAAE;IACrD,IAAIC,QAAQ,GAAGJ,eAAe,CAAC,CAAC;IAChC,IAAIK,MAAM,GAAGJ,iBAAiB,CAACG,QAAQ,CAAC;IACxC,IAAIE,MAAM,GAAGD,MAAM,GAAGP,WAAW,CAACS,SAAS,CAACF,MAAM,CAAC,GAAGN,OAAO,CAACS,KAAK;IACnEF,MAAM,CAACG,SAAS,CAACN,UAAU,CAAC;IAC5B,OAAO,YAAY;MACf,IAAIC,QAAQ,EAAE;QACVA,QAAQ,CAACM,WAAW,CAAC,CAAC;MAC1B;IACJ,CAAC;EACL,CAAC,CAAC;AACN;AACAjB,OAAO,CAACE,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}