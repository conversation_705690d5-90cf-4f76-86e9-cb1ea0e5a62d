{"ast": null, "code": "import { each } from 'lodash';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\nimport { ConfirmationDialogComponent } from '../../shared/confirmation-dialog/confirmation-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/exceljs.service\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/http-utils.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/kendo-column.service\";\nimport * as i7 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i8 from \"../../services/app.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"@progress/kendo-angular-grid\";\nimport * as i12 from \"@progress/kendo-angular-inputs\";\nimport * as i13 from \"@progress/kendo-angular-dropdowns\";\nimport * as i14 from \"@progress/kendo-angular-buttons\";\nimport * as i15 from \"../../shared/truncate-text/truncate-text.pipe\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  allowUnsort: true,\n  mode: \"single\"\n});\nconst _c4 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c5 = () => ({\n  filter: true\n});\nconst _c6 = () => ({\n  \"background-color\": \"#edf0f3\",\n  \"font-weight\": \"600\"\n});\nfunction ProjectListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"div\", 13)(3, \"span\", 14);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 15);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectListComponent_ng_template_4_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"a\", 34);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_div_18_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.onExportClick({\n        value: \"all\"\n      });\n      ctx_r2.closeExcelDropdown();\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵtext(2, \"All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 34);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_div_18_Template_a_click_3_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.onExportClick({\n        value: \"selected\"\n      });\n      ctx_r2.closeExcelDropdown();\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵtext(4, \"Page Results\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"top\", ctx_r2.dropdownTop, \"px\")(\"left\", ctx_r2.dropdownLeft, \"px\");\n  }\n}\nfunction ProjectListComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"kendo-textbox\", 17);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function ProjectListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"blur\", function ProjectListComponent_ng_template_4_Template_kendo_textbox_blur_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchBlur());\n    })(\"valueChange\", function ProjectListComponent_ng_template_4_Template_kendo_textbox_valueChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchValueChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 18)(4, \"span\", 19);\n    i0.ɵɵtext(5, \"Total: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 20);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.add());\n    });\n    i0.ɵɵelement(9, \"i\", 22);\n    i0.ɵɵtext(10, \"Add \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExpand());\n    });\n    i0.ɵɵelement(12, \"i\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 25, 3)(15, \"button\", 26, 4);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_Template_button_click_15_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExcelDropdown($event));\n    });\n    i0.ɵɵelement(17, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ProjectListComponent_ng_template_4_div_18_Template, 5, 4, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(20, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_4_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(22, \"i\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"fa-expand\", !ctx_r2.isExpanded)(\"fa-compress\", ctx_r2.isExpanded);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"show\", ctx_r2.isExcelDropdownOpen);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isExcelDropdownOpen);\n  }\n}\nfunction ProjectListComponent_ng_template_5_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"div\", 38)(3, \"label\", 39);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 41)(7, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_5_div_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵelement(8, \"i\", 43);\n    i0.ɵɵtext(9, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_5_div_0_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearAllFilters());\n    });\n    i0.ɵɵelement(11, \"i\", 45);\n    i0.ɵɵtext(12, \" Clear \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n  }\n}\nfunction ProjectListComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ProjectListComponent_ng_template_5_div_0_Template, 13, 2, \"div\", 35);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAdvancedFilters);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 62);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_3_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const dataItem_r7 = i0.ɵɵnextContext().dataItem;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      const deleteModal_r9 = i0.ɵɵreference(9);\n      return i0.ɵɵresetView(ctx_r2.deletePop(deleteModal_r9, dataItem_r7.projectId, dataItem_r7.projectName, dataItem_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 63);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 62);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_4_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const dataItem_r7 = i0.ɵɵnextContext().dataItem;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      const deleteModal_r9 = i0.ɵɵreference(9);\n      return i0.ɵɵresetView(ctx_r2.deletePop(deleteModal_r9, dataItem_r7.projectId, dataItem_r7.projectName, dataItem_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r7 = i0.ɵɵnextContext().dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"invisible\", !dataItem_r7.isDeletable && ctx_r2.loginUser.userId === dataItem_r7.createdBy)(\"disabled\", !dataItem_r7.isDeletable && ctx_r2.loginUser.userId === dataItem_r7.createdBy);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"a\", 58);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_1_listener() {\n      const dataItem_r7 = i0.ɵɵrestoreView(_r6).dataItem;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.edit(dataItem_r7.projectId));\n    });\n    i0.ɵɵelement(2, \"i\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_3_Template, 2, 0, \"a\", 60)(4, ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_4_Template, 2, 4, \"a\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loginUser.roleName === \"Admin\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loginUser.roleName === \"Internal PM\");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 55);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template, 5, 2, \"ng-template\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 90)(\"includeInChooser\", false)(\"columnMenu\", false)(\"hidden\", ctx_r2.getHiddenField(\"action\"));\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"truncateText\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r11 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, dataItem_r11.projectName, 45));\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 67);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r12 = ctx.$implicit;\n    const column_r13 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r13)(\"filter\", filter_r12)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 64);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template, 3, 4, \"ng-template\", 56)(2, ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template, 2, 3, \"ng-template\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 200)(\"includeInChooser\", false)(\"hidden\", ctx_r2.getHiddenField(\"projectName\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"truncateText\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r14 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, dataItem_r14.internalProjectNumber, 45));\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 67);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r15 = ctx.$implicit;\n    const column_r16 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r16)(\"filter\", filter_r15)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 68);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template, 3, 4, \"ng-template\", 56)(2, ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template, 2, 3, \"ng-template\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 150)(\"includeInChooser\", false)(\"hidden\", ctx_r2.getHiddenField(\"internalProjectNumber\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r17 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r17.projectStartDate), \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 67);\n    i0.ɵɵelement(1, \"kendo-filter-gte-operator\")(2, \"kendo-filter-lte-operator\")(3, \"kendo-filter-eq-operator\")(4, \"kendo-filter-neq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r18 = ctx.$implicit;\n    const column_r19 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r19)(\"filter\", filter_r18)(\"extra\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 69);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template, 1, 1, \"ng-template\", 56)(2, ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template, 5, 3, \"ng-template\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"headerStyle\", i0.ɵɵpureFunction0(4, _c6))(\"hidden\", ctx_r2.getHiddenField(\"projectStartDate\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r20 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r20.projectEndDate), \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 67);\n    i0.ɵɵelement(1, \"kendo-filter-gte-operator\")(2, \"kendo-filter-lte-operator\")(3, \"kendo-filter-eq-operator\")(4, \"kendo-filter-neq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r21 = ctx.$implicit;\n    const column_r22 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r22)(\"filter\", filter_r21)(\"extra\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 70);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template, 1, 1, \"ng-template\", 56)(2, ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template, 5, 3, \"ng-template\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"headerStyle\", i0.ɵɵpureFunction0(4, _c6))(\"hidden\", ctx_r2.getHiddenField(\"projectEndDate\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"truncateText\");\n  }\n  if (rf & 2) {\n    const dataItem_r23 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, dataItem_r23.projectLocation, 45), \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 67);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r24 = ctx.$implicit;\n    const column_r25 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r25)(\"filter\", filter_r24)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 71);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template, 2, 4, \"ng-template\", 56)(2, ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template, 2, 3, \"ng-template\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 180)(\"headerStyle\", i0.ɵɵpureFunction0(4, _c6))(\"hidden\", ctx_r2.getHiddenField(\"projectLocation\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"truncateText\");\n  }\n  if (rf & 2) {\n    const dataItem_r26 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, dataItem_r26.internalProjectManagerName, 45), \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 67);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r27 = ctx.$implicit;\n    const column_r28 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r28)(\"filter\", filter_r27)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 72);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template, 2, 4, \"ng-template\", 56)(2, ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template, 2, 3, \"ng-template\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 180)(\"headerStyle\", i0.ɵɵpureFunction0(4, _c6))(\"hidden\", ctx_r2.getHiddenField(\"internalProjectManagerName\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"truncateText\");\n  }\n  if (rf & 2) {\n    const dataItem_r29 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, dataItem_r29.externalPMNames, 45), \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 67);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r30 = ctx.$implicit;\n    const column_r31 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r31)(\"filter\", filter_r30)(\"extra\", false);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 73);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template, 2, 4, \"ng-template\", 56)(2, ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template, 2, 3, \"ng-template\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 220)(\"headerStyle\", i0.ɵɵpureFunction0(4, _c6))(\"hidden\", ctx_r2.getHiddenField(\"externalPMNames\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r32 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r32.lastUpdatedDate), \" \");\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 67);\n    i0.ɵɵelement(1, \"kendo-filter-gte-operator\")(2, \"kendo-filter-lte-operator\")(3, \"kendo-filter-eq-operator\")(4, \"kendo-filter-neq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r33 = ctx.$implicit;\n    const column_r34 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r34)(\"filter\", filter_r33)(\"extra\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_kendo_grid_column_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 74);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_Template, 1, 1, \"ng-template\", 56)(2, ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_2_Template, 5, 3, \"ng-template\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"headerStyle\", i0.ɵɵpureFunction0(4, _c6))(\"hidden\", ctx_r2.getHiddenField(\"lastUpdatedDate\"))(\"filterable\", true);\n  }\n}\nfunction ProjectListComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectListComponent_ng_container_6_kendo_grid_column_1_Template, 2, 4, \"kendo-grid-column\", 46)(2, ProjectListComponent_ng_container_6_kendo_grid_column_2_Template, 3, 4, \"kendo-grid-column\", 47)(3, ProjectListComponent_ng_container_6_kendo_grid_column_3_Template, 3, 4, \"kendo-grid-column\", 48)(4, ProjectListComponent_ng_container_6_kendo_grid_column_4_Template, 3, 5, \"kendo-grid-column\", 49)(5, ProjectListComponent_ng_container_6_kendo_grid_column_5_Template, 3, 5, \"kendo-grid-column\", 50)(6, ProjectListComponent_ng_container_6_kendo_grid_column_6_Template, 3, 5, \"kendo-grid-column\", 51)(7, ProjectListComponent_ng_container_6_kendo_grid_column_7_Template, 3, 5, \"kendo-grid-column\", 52)(8, ProjectListComponent_ng_container_6_kendo_grid_column_8_Template, 3, 5, \"kendo-grid-column\", 53)(9, ProjectListComponent_ng_container_6_kendo_grid_column_9_Template, 3, 5, \"kendo-grid-column\", 54);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r35 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"projectName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"internalProjectNumber\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"projectStartDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"projectEndDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"projectLocation\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"internalProjectManagerName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"externalPMNames\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r35 === \"lastUpdatedDate\");\n  }\n}\nfunction ProjectListComponent_ng_template_7_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"div\", 77)(2, \"p\", 19);\n    i0.ɵɵtext(3, \"No data found\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectListComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ProjectListComponent_ng_template_7_div_0_Template, 4, 0, \"div\", 75);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && !ctx_r2.isLoading);\n  }\n}\nfunction ProjectListComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"h5\", 79);\n    i0.ɵɵtext(2, \"Confirm Delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_8_Template_button_click_3_listener() {\n      const modal_r37 = i0.ɵɵrestoreView(_r36).$implicit;\n      return i0.ɵɵresetView(modal_r37.dismiss());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 81)(5, \"p\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 83)(8, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_8_Template_button_click_8_listener() {\n      const modal_r37 = i0.ɵɵrestoreView(_r36).$implicit;\n      return i0.ɵɵresetView(modal_r37.dismiss());\n    });\n    i0.ɵɵtext(9, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_8_Template_button_click_10_listener() {\n      const modal_r37 = i0.ɵɵrestoreView(_r36).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.confirmDelete();\n      return i0.ɵɵresetView(modal_r37.close());\n    });\n    i0.ɵɵtext(11, \" Delete \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Are you sure you want to delete this project? - \", ctx_r2.projectName, \" \");\n  }\n}\nfunction ProjectListComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"h5\", 79);\n    i0.ɵɵtext(2, \"Warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_10_Template_button_click_3_listener() {\n      const modal_r39 = i0.ɵɵrestoreView(_r38).$implicit;\n      return i0.ɵɵresetView(modal_r39.dismiss());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 81)(5, \"p\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 83)(8, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_ng_template_10_Template_button_click_8_listener() {\n      const modal_r39 = i0.ɵɵrestoreView(_r38).$implicit;\n      return i0.ɵɵresetView(modal_r39.close());\n    });\n    i0.ɵɵtext(9, \" Ok \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" This project (\", ctx_r2.projectName, \") has permits. Please delete the permits first before deleting the project. \");\n  }\n}\nexport class ProjectListComponent {\n  router;\n  execeljsservice;\n  route;\n  projectsService;\n  httpUtilService;\n  customLayoutUtilsService;\n  kendoColumnService;\n  modalService;\n  cdr;\n  appService;\n  grid;\n  // Data\n  serverSideRowData = [];\n  gridData = [];\n  IsListHasValue = false;\n  loading = false;\n  isLoading = false;\n  loginUser = {};\n  // Search\n  searchData = '';\n  // Removed debounced search - now only triggers on Enter or blur\n  // private searchTerms = new Subject<string>();\n  // private searchSubscription: Subscription;\n  // Enhanced Filters for Kendo UI\n  filter = {\n    logic: 'and',\n    filters: []\n  };\n  gridFilter = {\n    logic: 'and',\n    filters: []\n  };\n  activeFilters = [];\n  filterOptions = [{\n    text: 'All',\n    value: null\n  }, {\n    text: 'Current',\n    value: 'Current'\n  }, {\n    text: 'Completed',\n    value: 'Completed'\n  }, {\n    text: 'Cancelled & Archived',\n    value: 'Cancelled & Archived'\n  }, {\n    text: 'Closed & Archived',\n    value: 'Closed & Archived'\n  }];\n  // Advanced filter options\n  advancedFilterOptions = {\n    status: [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Current',\n      value: 'Current'\n    }, {\n      text: 'Completed',\n      value: 'Completed'\n    }, {\n      text: 'Cancelled & Archived',\n      value: 'Cancelled & Archived'\n    }, {\n      text: 'Closed & Archived',\n      value: 'Closed & Archived'\n    }],\n    centers: []\n  };\n  // Filter state\n  showAdvancedFilters = false;\n  appliedFilters = {};\n  // Column visibility system\n  kendoHide;\n  hiddenData = [];\n  kendoColOrder = [];\n  kendoInitColOrder = [];\n  hiddenFields = [];\n  // Column configuration\n  gridColumns = [];\n  defaultColumns = [];\n  normalGrid;\n  expandedGrid;\n  isExpanded = false;\n  // Custom reorderable configuration that prevents fixed column reordering\n  customReorderableConfig = false;\n  // Enhanced Columns with Kendo UI features\n  gridColumnConfig = [{\n    field: 'action',\n    title: 'Action',\n    width: 100,\n    isFixed: true,\n    type: 'action',\n    order: 1\n  }, {\n    field: 'projectName',\n    title: 'Project Name',\n    width: 200,\n    isFixed: true,\n    type: 'text',\n    filterable: true,\n    order: 2\n  }, {\n    field: 'internalProjectNumber',\n    title: 'Internal Project #',\n    width: 120,\n    isFixed: true,\n    type: 'text',\n    filterable: true,\n    order: 3\n  }, {\n    field: 'projectStartDate',\n    title: 'Start Date',\n    width: 120,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 4\n  }, {\n    field: 'projectEndDate',\n    title: 'End Date',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 5\n  }, {\n    field: 'projectLocation',\n    title: 'Location',\n    width: 150,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 6\n  }, {\n    field: 'internalProjectManagerName',\n    title: 'Manager',\n    width: 150,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 7\n  }, {\n    field: 'externalPMNames',\n    title: 'External PM',\n    width: 220,\n    type: 'status',\n    isFixed: false,\n    filterable: true,\n    order: 8\n  }, {\n    field: 'lastUpdatedDate',\n    title: 'Updated Date',\n    width: 120,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 9\n  }];\n  // State\n  sort = [{\n    field: 'lastUpdatedDate',\n    dir: 'desc'\n  }];\n  page = {\n    size: 15,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'lastUpdatedDate',\n    orderDir: 'desc'\n  };\n  skip = 0;\n  // Selection\n  selectedRows = [];\n  isAllSelected = false;\n  // Export options\n  exportOptions = [{\n    text: 'All',\n    value: 'all'\n  }, {\n    text: 'Page Results',\n    value: 'selected'\n  }\n  // { text: 'Export Filtered', value: 'filtered' },\n  ];\n  // Custom dropdown state\n  isExcelDropdownOpen = false;\n  dropdownTop = 0;\n  dropdownLeft = 0;\n  projectName;\n  projectId;\n  constructor(router, execeljsservice, route, projectsService, httpUtilService, customLayoutUtilsService, kendoColumnService, modalService, cdr, appService) {\n    this.router = router;\n    this.execeljsservice = execeljsservice;\n    this.route = route;\n    this.projectsService = projectsService;\n    this.httpUtilService = httpUtilService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.kendoColumnService = kendoColumnService;\n    this.modalService = modalService;\n    this.cdr = cdr;\n    this.appService = appService;\n    // Removed debounced search subscription - search now only triggers on Enter or blur\n  }\n  ngOnInit() {\n    this.initializeComponent();\n    this.loadTable();\n  }\n  ngAfterViewInit() {\n    this.initializeGrid();\n  }\n  ngOnDestroy() {\n    // Removed search subscription cleanup - no longer needed\n  }\n  initializeComponent() {\n    // Get login user info\n    // this.loginUser = this.customLayoutUtils.getLoginUser();\n    this.loginUser = this.appService.getLoggedInUser();\n    console.log(\"this.loginUser\", this.loginUser);\n    // Initialize column visibility system\n    this.initializeColumnVisibility();\n  }\n  initializeColumnVisibility() {\n    // Set up column arrays first\n    this.setupColumnArrays();\n    // Try to load from local storage first\n    const savedConfig = this.kendoColumnService.getFromLocalStorage('projects', this.loginUser.userId);\n    if (savedConfig) {\n      // Load saved settings from local storage\n      this.kendoHide = savedConfig.hiddenData || [];\n      this.kendoColOrder = savedConfig.kendoColOrder || [...this.defaultColumns];\n      this.kendoInitColOrder = [...this.kendoColOrder];\n    } else {\n      // Initialize with default values\n      this.kendoHide = [];\n      this.hiddenData = [];\n      this.kendoColOrder = [...this.defaultColumns];\n      this.kendoInitColOrder = [...this.defaultColumns];\n    }\n    // Apply settings\n    this.applySavedColumnSettings();\n  }\n  loadColumnSettingsFromServer() {\n    const config = {\n      pageName: 'projects',\n      userID: this.loginUser.userId\n    };\n    this.kendoColumnService.getHideFields(config).subscribe({\n      next: response => {\n        if (response.isFault === false && response.Data) {\n          // Parse the saved settings\n          this.kendoHide = response.Data.hideData ? JSON.parse(response.Data.hideData) : [];\n          this.kendoColOrder = response.Data.kendoColOrder ? JSON.parse(response.Data.kendoColOrder) : [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.kendoColOrder];\n          // Apply the settings\n          this.applySavedColumnSettings();\n          console.log('Column settings loaded from server:', {\n            kendoHide: this.kendoHide,\n            kendoColOrder: this.kendoColOrder\n          });\n        } else {\n          // No saved settings, use defaults\n          this.kendoHide = [];\n          this.kendoColOrder = [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.defaultColumns];\n          this.applySavedColumnSettings();\n        }\n      },\n      error: error => {\n        console.error('Error loading column settings:', error);\n        // Use defaults on error\n        this.kendoHide = [];\n        this.kendoColOrder = [...this.defaultColumns];\n        this.kendoInitColOrder = [...this.defaultColumns];\n        this.applySavedColumnSettings();\n      }\n    });\n  }\n  setupColumnArrays() {\n    this.gridColumns = this.gridColumnConfig.map(col => col.field);\n    this.defaultColumns = [...this.gridColumns];\n  }\n  initializeGrid() {\n    if (this.grid) {\n      // Apply saved column settings\n      this.applySavedColumnSettings();\n    }\n  }\n  applySavedColumnSettings() {\n    if (this.kendoHide && this.kendoHide.length > 0) {\n      this.hiddenFields = this.kendoHide;\n    }\n    if (this.kendoColOrder && this.kendoColOrder.length > 0) {\n      // Apply column order\n      this.gridColumnConfig.sort((a, b) => {\n        const aOrder = this.kendoColOrder.indexOf(a.field);\n        const bOrder = this.kendoColOrder.indexOf(b.field);\n        return aOrder - bOrder;\n      });\n    }\n  }\n  // Load table data\n  loadTable() {\n    this.loadTableWithKendoEndpoint();\n  }\n  // New method to load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n    // Safety timeout to prevent loader from getting stuck\n    const loadingTimeout = setTimeout(() => {\n      console.warn('Loading timeout reached, resetting loading states');\n      this.resetLoadingStates();\n    }, 15000); // 15 seconds timeout - reduced from 30 seconds\n    // Prepare state object for Kendo UI endpoint\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: this.sort,\n      filter: this.filter.filters,\n      search: this.searchData,\n      loggedInUserId: this.loginUser.userId,\n      role: this.loginUser.roleName || 1,\n      userId: this.loginUser.userId\n    };\n    console.log('Search request state:', {\n      searchTerm: this.searchData,\n      state: state\n    });\n    this.projectsService.getProjectsForKendoGrid(state).subscribe({\n      next: data => {\n        // Clear the safety timeout since we got a response\n        clearTimeout(loadingTimeout);\n        console.log('API Response:', data);\n        // Handle the new API response structure\n        if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n          // Check if this is an authentication error\n          if (data.responseData?.status === 401 || data.status === 401) {\n            console.warn('Authentication error - token may be expired');\n            // Don't handle empty response here, let the interceptor handle auth\n            return;\n          }\n          this.handleEmptyResponse();\n          // Always reset loading states regardless of data content\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const projectData = responseData.data || [];\n          const total = responseData.total || 0;\n          this.IsListHasValue = projectData.length !== 0;\n          this.serverSideRowData = projectData;\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n          // Create a data source with total count for Kendo Grid\n          this.gridData = {\n            data: projectData,\n            total: total\n          };\n          console.log('this.serverSideRowData ', this.serverSideRowData);\n          console.log('this.gridData ', this.gridData);\n          console.log('this.IsListHasValue ', this.IsListHasValue);\n          console.log('this.page ', this.page);\n          this.cdr.markForCheck();\n          // Always reset loading states regardless of data content\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        }\n      },\n      error: error => {\n        // Clear the safety timeout since we got an error\n        clearTimeout(loadingTimeout);\n        console.error('Error loading data with Kendo UI endpoint:', error);\n        // Check if this is an authentication error\n        if (error && typeof error === 'object' && 'status' in error) {\n          const httpError = error;\n          if (httpError.status === 401) {\n            console.warn('Authentication error - token may be expired');\n            // Don't handle empty response here, let the interceptor handle auth\n            return;\n          }\n        }\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      complete: () => {\n        // Clear the safety timeout\n        clearTimeout(loadingTimeout);\n        // Ensure loading states are reset in complete block as well\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      }\n    });\n  }\n  handleEmptyResponse() {\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n    // Ensure loading states are reset when handling empty response\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n  }\n  // Method to manually reset loading states if they get stuck\n  resetLoadingStates() {\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n  }\n  // Public method to manually refresh the grid and reset any stuck loading states\n  refreshGrid() {\n    console.log('Manually refreshing grid...');\n    this.resetLoadingStates();\n    this.loadTable();\n  }\n  // Search functionality\n  onSearchKeyDown(event) {\n    if (event.key === 'Enter') {\n      console.log('Search triggered by Enter key:', this.searchData);\n      // Set loading state immediately for search\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      this.loadTable();\n    }\n  }\n  onSearchBlur() {\n    console.log('Search blur triggered:', this.searchData);\n    // Trigger search when user clicks outside the input\n    this.loadTable();\n  }\n  onSearchValueChange() {\n    console.log('Search value changed:', this.searchData);\n    // This method handles value changes, including when clear button is clicked\n    // Only trigger search if the value is empty (clear button was clicked)\n    if (!this.searchData || this.searchData.trim() === '') {\n      this.loadTable();\n    }\n  }\n  onSearchChange() {\n    console.log('Search changed:', this.searchData);\n    // This method is no longer used but kept for compatibility\n    // Search now only triggers on Enter key or blur event\n  }\n  applySearch() {\n    // Set loading state immediately for search\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.loadTable();\n  }\n  clearSearch() {\n    this.searchData = '';\n    this.loadTable();\n  }\n  // Test method for External PM search\n  testExternalPMSearch() {\n    console.log('Testing External PM search...');\n    this.searchData = 'External PM'; // Test search term\n    // Set loading state immediately for test search\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.loadTable();\n  }\n  // Filter functionality\n  filterChange(filter) {\n    this.filter = filter;\n    // Set loading state immediately for filtering\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.loadTable();\n  }\n  applyAdvancedFilters() {\n    // Apply status filter (use new 'status' field)\n    if (this.appliedFilters.status) {\n      this.filter.filters = this.filter.filters.filter(f => {\n        if ('field' in f) {\n          return f.field !== 'status' && f.field !== 'projectStatus';\n        }\n        return true;\n      });\n      this.filter.filters.push({\n        field: 'status',\n        operator: 'eq',\n        value: this.appliedFilters.status\n      });\n    }\n    // Apply center filter\n    if (this.appliedFilters.center) {\n      this.filter.filters = this.filter.filters.filter(f => {\n        if ('field' in f) {\n          return f.field !== 'centerId';\n        }\n        return true;\n      });\n      this.filter.filters.push({\n        field: 'centerId',\n        operator: 'eq',\n        value: this.appliedFilters.center\n      });\n    }\n    this.loadTable();\n  }\n  clearAdvancedFilters() {\n    this.appliedFilters = {};\n    this.filter.filters = [];\n    // Set loading state immediately for clearing filters\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.loadTable();\n  }\n  clearAllFilters() {\n    this.appliedFilters = {};\n    this.filter.filters = [];\n    // Set loading state immediately for clearing filters\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.loadTable();\n  }\n  // Sorting functionality\n  onSortChange(sort) {\n    // Check if this is the 3rd click (dir is undefined)\n    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;\n    if (isThirdClick) {\n      // 3rd click - clear sort and use default\n      this.sort = [];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n    } else if (sort.length > 0 && sort[0] && sort[0].dir) {\n      // Valid sort with direction\n      this.sort = sort;\n      this.page.orderBy = sort[0].field || 'lastUpdatedDate';\n      this.page.orderDir = sort[0].dir;\n    } else {\n      // Empty sort array or invalid sort\n      this.sort = [];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n    }\n    // Reset to first page\n    this.skip = 0;\n    this.page.pageNumber = 0;\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    setTimeout(() => this.loadTable(), 0);\n  }\n  // Pagination functionality\n  pageChange(event) {\n    // Use Kendo's provided values as source of truth\n    this.skip = event.skip;\n    this.page.size = event.take || this.page.size;\n    this.page.pageNumber = Math.floor(this.skip / this.page.size);\n    // Set loading state immediately for pagination\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.loadTable();\n  }\n  updateColumnVisibility(event) {\n    // Handle column visibility changes\n    const hiddenColumns = event.hiddenColumns || [];\n    this.hiddenFields = hiddenColumns;\n  }\n  getHiddenField(fieldName) {\n    return this.hiddenFields.includes(fieldName);\n  }\n  // Selection functionality\n  onSelectionChange(event) {\n    this.selectedRows = event.selectedRows || [];\n    this.isAllSelected = this.selectedRows.length === this.serverSideRowData.length;\n  }\n  selectAll() {\n    if (this.isAllSelected) {\n      this.selectedRows = [];\n      this.isAllSelected = false;\n    } else {\n      this.selectedRows = [...this.serverSideRowData];\n      this.isAllSelected = true;\n    }\n  }\n  // Grid expansion\n  toggleExpand() {\n    // Find grid container element and toggle fullscreen class\n    const gridContainer = document.querySelector('.grid-container');\n    if (gridContainer) {\n      gridContainer.classList.toggle('fullscreen-grid');\n      this.isExpanded = !this.isExpanded;\n      // Refresh grid after resize to ensure proper rendering\n      if (this.grid) {\n        this.grid.refresh();\n      }\n    }\n  }\n  // Export functionality\n  // public onExportClick(event: any): void {\n  //   const exportType = event.item.value;\n  //   let selectedIds: number[] = [];\n  //   switch (exportType) {\n  //     case 'selected':\n  //       selectedIds = this.selectedRows.map((row) => row.projectId);\n  //       if (selectedIds.length === 0) {\n  //         //alert('Please select projects to export');\n  //         return;\n  //       }\n  //       break;\n  //     case 'filtered':\n  //       // Export filtered data\n  //       break;\n  //     case 'all':\n  //     default:\n  //       // Export all data\n  //       break;\n  //   }\n  //   this.exportProjects(exportType, selectedIds);\n  // }\n  // private exportProjects(exportType: string, selectedIds: number[]): void {\n  //   this.projectsService.exportProjects(exportType, selectedIds).subscribe({\n  //     next: (response: any) => {\n  //       if (response.data) {\n  //         const blob = new Blob([response.data], {\n  //           type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n  //         });\n  //         saveAs(\n  //           blob,\n  //           `projects_${exportType}_${\n  //             new Date().toISOString().split('T')[0]\n  //           }.xlsx`\n  //         );\n  //       }\n  //     },\n  //     error: (error: any) => {\n  //       console.error('Export error:', error);\n  //       //alert('Error exporting projects data');\n  //     },\n  //   });\n  // }\n  onExportClick(event) {\n    const selectedOption = event.value; // Get selected option\n    let prdItems = [];\n    if (selectedOption === 'selected') {\n      prdItems = this.serverSideRowData;\n      // declare the title and header data for excel\n      // get the data for excel in a array format\n      this.exportExcel(prdItems);\n    } else if (selectedOption === 'all') {\n      const queryparamsExcel = {\n        pageSize: this.page.totalElements,\n        sortOrder: this.page.orderDir,\n        sortField: this.page.orderBy,\n        pageNumber: this.page.pageNumber\n        // filter: this.filterConfiguration()\n      };\n      // Enable loading indicator\n      this.httpUtilService.loadingSubject.next(true);\n      // API call\n      this.projectsService.getAllProjects(queryparamsExcel)\n      // .pipe(map((data: any) => data as any))\n      .subscribe(data => {\n        // Disable loading indicator\n        this.httpUtilService.loadingSubject.next(false);\n        if (data.isFault) {\n          this.IsListHasValue = false;\n          this.cdr.markForCheck();\n          return; // Exit early if the response has a fault\n        }\n        this.IsListHasValue = true;\n        prdItems = data.responseData.data || [];\n        this.cdr.detectChanges(); // Manually trigger UI update\n        this.exportExcel(prdItems);\n      });\n    }\n  }\n  exportExcel(listOfItems) {\n    // Define local variables for the items and current date\n    let prdItems = listOfItems;\n    let currentDate = this.appService.formatMonthDate(new Date());\n    console.log('prdItems', prdItems);\n    // Check if the data exists and is not empty\n    if (prdItems !== undefined && prdItems.length > 0) {\n      // Define the title for the Excel file\n      const tableTitle = 'Events';\n      // Filter out hidden columns and sort by order\n      // const visibleColumns = this.columnJSONFormat\n      //   .filter((col: any) => !col.hidden)\n      //   .sort((a: any, b: any) => a.order - b.order);\n      // Create header from visible columns\n      const headerArray = ['Project Name', 'Internal Projet #', 'Start Date', 'End Date', 'Location', 'Manager', 'External PM'];\n      // ...visibleColumns.map((col: any) => col.title),\n      // Define which columns should have currency and percentage formatting\n      // const currencyColumns: any = [\n      //   'Pending',\n      //   'ACAT',\n      //   'Annuity',\n      //   'AUM',\n      //   'Total Assets',\n      //   'Event Cost',\n      //   'Gross Profit',\n      // ].filter((col) => headerArray.includes(col));\n      const percentageColumns = [];\n      // Get the data for excel in an array format\n      const respResult = [];\n      // Prepare the data for export based on visible columns\n      each(prdItems, prdItem => {\n        // Create an array with the same length as headerArray\n        const respData = Array(headerArray.length).fill(null);\n        respData[0] = prdItem.eventDescription;\n        respData[1] = this.appService.formatMonthDate(prdItem.event_date);\n        // Fill in data for each visible column\n        headerArray.forEach((col, i) => {\n          const adjustedIndex = i; // +2 for 'Name' and 'Hot'\n          switch (col) {\n            case 'Project Name':\n              respData[adjustedIndex] = prdItem.projectName;\n              break;\n            case 'Internal Projet #':\n              respData[adjustedIndex] = prdItem.internalProjectNumber;\n              break;\n            case 'Start Date':\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.projectStartDate);\n              break;\n            case 'End Date':\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.projectEndDate);\n              break;\n            case 'Location':\n              respData[adjustedIndex] = prdItem.projectLocation;\n              break;\n            case 'Manager':\n              respData[adjustedIndex] = prdItem.internalProjectManagerName;\n              break;\n            case 'External PM':\n              respData[adjustedIndex] = prdItem.externalPMNames;\n              break;\n            // case 'kept_appointments':\n            //   respData[adjustedIndex] = prdItem.kept_appointments;\n            //   break;\n            // case 'kept_appt_ratio':\n            //   respData[adjustedIndex] = prdItem.kept_appt_ratio;\n            //   break;\n            // case 'apptKeptNo':\n            //   respData[adjustedIndex] = prdItem.apptKeptNo;\n            //   break;\n            // case 'has_assets':\n            //   respData[adjustedIndex] = prdItem.has_assets;\n            //   break;\n            // case 'prospects_closed':\n            //   respData[adjustedIndex] = prdItem.prospects_closed;\n            //   break;\n            // case 'closing_ratio':\n            //   respData[adjustedIndex] = prdItem.closing_ratio;\n            //   break;\n            // case 'totalPending':\n            //   respData[adjustedIndex] = prdItem.totalPending;\n            //   break;\n            // case 'acatproduction':\n            //   respData[adjustedIndex] = prdItem.acatproduction;\n            //   break;\n            // case 'annuityproduction':\n            //   respData[adjustedIndex] = prdItem.annuityproduction;\n            //   break;\n            // case 'aumproduction':\n            //   respData[adjustedIndex] = prdItem.aumproduction;\n            //   break;\n            // case 'totalAssets':\n            //   respData[adjustedIndex] = prdItem.totalAssets;\n            //   break;\n            // case 'eventCost':\n            //   respData[adjustedIndex] = prdItem.eventCost;\n            //   break;\n            // case 'grossProfit':\n            //   respData[adjustedIndex] = prdItem.grossProfit;\n            //   break;\n            // case 'status':\n            //   respData[adjustedIndex] = prdItem.status;\n            //   break;\n          }\n        });\n        respResult.push(respData);\n      });\n      // Define column sizes for the Excel file\n      const colSize = headerArray.map((header, index) => ({\n        id: index + 1,\n        width: 20\n      }));\n      // Generate the Excel file using the exceljsService\n      this.execeljsservice.generateExcel(tableTitle, headerArray, respResult, colSize\n      // currencyColumns,\n      // percentageColumns\n      );\n    } else {\n      const message = 'There are no records available to export.';\n      // this.layoutUtilService.showError(message, '');\n    }\n  }\n  // Column settings management\n  saveHead() {\n    const settings = {\n      kendoHide: this.hiddenFields,\n      kendoColOrder: this.kendoColOrder,\n      kendoInitColOrder: this.kendoInitColOrder\n    };\n    // Save to local storage only\n    this.kendoColumnService.saveToLocalStorage({\n      pageName: 'projects',\n      userID: this.loginUser.userId,\n      hiddenData: settings.kendoHide,\n      kendoColOrder: settings.kendoColOrder,\n      LoggedId: this.loginUser.userId\n    });\n    console.log('Column settings saved locally:', settings);\n    this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');\n    //alert('Column settings saved locally');\n  }\n  saveColumnSettingsToServer(settings) {\n    const config = {\n      pageName: 'projects',\n      userID: this.loginUser.userId,\n      hiddenData: settings.kendoHide,\n      kendoColOrder: settings.kendoColOrder,\n      LoggedId: this.loginUser.userId\n    };\n    this.kendoColumnService.createHideFields(config).subscribe({\n      next: response => {\n        if (response.isFault === false) {\n          console.log('Column settings saved successfully:', response);\n          this.customLayoutUtilsService.showSuccess('Column settings saved successfully', '');\n          //alert('Column settings saved successfully');\n        } else {\n          console.error('Failed to save column settings:', response.message);\n          this.customLayoutUtilsService.showError('Column settings failed to save', '');\n          //alert('Failed to save column settings: ' + response.message);\n        }\n      },\n      error: error => {\n        console.error('Error saving column settings:', error);\n        this.customLayoutUtilsService.showError('Error saving column settings', '');\n        //alert('Error saving column settings. Please try again.');\n      }\n    });\n  }\n  saveResetToServer() {\n    // First delete existing settings\n    const deleteConfig = {\n      pageName: 'projects',\n      userID: this.loginUser.userId\n    };\n    this.kendoColumnService.deleteHideFields(deleteConfig).subscribe({\n      next: response => {\n        console.log('Existing settings deleted:', response);\n        // Then save the reset state (all columns visible)\n        this.saveColumnSettingsToServer({\n          kendoHide: [],\n          kendoColOrder: this.defaultColumns,\n          kendoInitColOrder: this.defaultColumns\n        });\n      },\n      error: error => {\n        console.error('Error deleting existing settings:', error);\n        // Still try to save the reset state\n        this.saveColumnSettingsToServer({\n          kendoHide: [],\n          kendoColOrder: this.defaultColumns,\n          kendoInitColOrder: this.defaultColumns\n        });\n      }\n    });\n  }\n  resetTable() {\n    console.log('Resetting Kendo settings for projects');\n    // Clear all saved settings first\n    this.kendoHide = [];\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.kendoInitColOrder = [];\n    // Clear local storage\n    this.kendoColumnService.clearFromLocalStorage('projects');\n    // Reset to default settings\n    this.resetToDefaultSettings();\n    // Trigger change detection to update the template\n    this.cdr.detectChanges();\n    // Force grid refresh to show all columns\n    if (this.grid) {\n      this.grid.refresh();\n    }\n    // Show success message\n    console.log('Table reset to default settings');\n    //alert('Table reset to default settings - all columns restored');\n  }\n  resetToDefaultSettings() {\n    console.log('Resetting to default settings...');\n    // Reset column visibility - show all columns\n    this.hiddenFields = [];\n    this.gridColumns = [...this.defaultColumns];\n    this.kendoColOrder = [...this.defaultColumns];\n    // Reset sort state to default\n    this.sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    this.page.orderBy = 'lastUpdatedDate';\n    this.page.orderDir = 'desc';\n    // Reset page state\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Reset all filters - clear everything\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.activeFilters = [];\n    // Reset advanced filters\n    this.appliedFilters = {};\n    // Reset search\n    this.searchData = '';\n    // Reset advanced filters visibility\n    this.showAdvancedFilters = false;\n    console.log('Reset completed:', {\n      hiddenFields: this.hiddenFields,\n      gridColumns: this.gridColumns,\n      defaultColumns: this.defaultColumns,\n      sort: this.sort,\n      filter: this.filter,\n      searchData: this.searchData\n    });\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = {\n        logic: 'and',\n        filters: []\n      };\n      // Reset sorting\n      this.grid.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      // Reset column visibility - show all columns\n      this.grid.columns.forEach(column => {\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n    // Trigger change detection\n    this.cdr.detectChanges();\n    // Force grid refresh to apply all changes\n    if (this.grid) {\n      setTimeout(() => {\n        this.grid.refresh();\n        // Also try to reset the grid state completely\n        this.grid.reset();\n      }, 100);\n    }\n    // Reload data with clean state\n    this.loadTable();\n  }\n  // Navigation\n  add() {\n    this.edit(0);\n  }\n  view(projectId) {\n    this.router.navigate(['/projects/view', projectId]);\n  }\n  edit(projectId) {\n    if (projectId == 0) {\n      // Trigger global loader BEFORE opening modal to ensure full-page overlay\n      this.httpUtilService.loadingSubject.next(true);\n      // Open modal for new project\n      const modalRef = this.modalService.open(ProjectPopupComponent, {\n        size: 'lg',\n        centered: true,\n        backdrop: 'static'\n      });\n      modalRef.componentInstance.id = projectId;\n      modalRef.componentInstance.project = null;\n      modalRef.componentInstance.passEntry.subscribe(result => {\n        if (result) {\n          // Refresh the grid after successful add\n          this.loadTable();\n        }\n      });\n    } else {\n      // Navigate to project view for existing projects\n      this.router.navigate(['/projects/view', projectId]);\n    }\n  }\n  delete(projectId) {\n    // if (confirm('Are you sure you want to delete this project?')) {\n    // }\n    this.projectsService.deleteProject({\n      projectId\n    }).subscribe({\n      next: response => {\n        console.log(\"response\", response);\n        if (!response.isFault) {\n          this.customLayoutUtilsService.showSuccess('Project deleted successfully', '');\n          //alert('Project deleted successfully');\n          this.loadTable();\n        }\n      },\n      error: error => {\n        console.error('Delete error:', error);\n        this.customLayoutUtilsService.showError('error deleting project', '');\n        //alert('Error deleting project');\n      }\n    });\n  }\n  // Utility methods\n  getProjectFullName(project) {\n    return `${project.projectFirstName || ''} ${project.projectLastName || ''}`.trim();\n  }\n  getCenterName(project) {\n    return project.medicalCenter?.centerName || '';\n  }\n  formatDate(dateString) {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    const year = date.getFullYear();\n    return `${month}/${day}/${year}`;\n  }\n  getStatusClass(status) {\n    if (!status) return 'badge-light-secondary';\n    const key = status.toLowerCase();\n    if (key === 'current') return 'badge-light-success';\n    if (key === 'completed') return 'badge-light-primary';\n    if (key === 'cancelled & archived') return 'badge-light-dark';\n    if (key === 'closed & archived') return 'badge-light-info';\n    return 'badge-light-secondary';\n  }\n  deletePop(content, projectId, projectName, dataItem) {\n    this.projectName = projectName;\n    this.projectId = projectId;\n    let modalRef = this.modalService.open(ConfirmationDialogComponent, {\n      size: 'nm',\n      centered: true,\n      backdrop: 'static'\n    });\n    if (this.loginUser.roleName === 'Admin') {\n      if (dataItem.isDeletable) {\n        // Open modal for new projec\n        modalRef.componentInstance.showClose = true;\n        modalRef.componentInstance.description = \"Are you sure you want to delete this project - \" + projectName + \"?\";\n        modalRef.componentInstance.cancelButtonText = 'Cancel';\n        modalRef.componentInstance.actionButtonText = 'Yes';\n        modalRef.componentInstance.title = 'Confirm Delete';\n        modalRef.componentInstance.passEntry.subscribe(result => {\n          if (result.success === true) {\n            // Refresh the grid after successful add\n            this.delete(this.projectId);\n          }\n        });\n      } else {\n        modalRef.componentInstance.showClose = false;\n        modalRef.componentInstance.description = \"This project (\" + projectName + \") has permits. Please delete the permits first before deleting the project.\";\n        modalRef.componentInstance.cancelButtonText = 'Cancel';\n        modalRef.componentInstance.actionButtonText = 'Ok';\n        modalRef.componentInstance.title = 'Warning';\n        modalRef.componentInstance.passEntry.subscribe(result => {});\n      }\n    } else if (this.loginUser.roleName === 'Internal PM') {\n      if (dataItem.isDeletable) {\n        modalRef.componentInstance.showClose = true;\n        modalRef.componentInstance.description = \"Are you sure you want to delete this project - \" + projectName + \"?\";\n        modalRef.componentInstance.cancelButtonText = 'Cancel';\n        modalRef.componentInstance.actionButtonText = 'Yes';\n        modalRef.componentInstance.title = 'Confirm Delete';\n        modalRef.componentInstance.passEntry.subscribe(result => {\n          if (result.success === true) {\n            // Refresh the grid after successful add\n            this.delete(this.projectId);\n          }\n        });\n      }\n    }\n  }\n  confirmDelete() {\n    // console.log('Item deleted ✅');\n    this.delete(this.projectId);\n    // your delete logic here\n  }\n  onTabActivated() {\n    // This method is called when the tab is activated\n    // You can add any specific logic here if needed\n    console.log('Projects tab activated');\n  }\n  // Custom dropdown methods\n  toggleExcelDropdown(event) {\n    this.isExcelDropdownOpen = !this.isExcelDropdownOpen;\n    console.log('Excel dropdown toggled:', this.isExcelDropdownOpen);\n    if (this.isExcelDropdownOpen && event) {\n      const button = event.target;\n      const rect = button.getBoundingClientRect();\n      this.dropdownTop = rect.bottom + window.scrollY;\n      this.dropdownLeft = rect.left + window.scrollX;\n      console.log('Dropdown position:', this.dropdownTop, this.dropdownLeft);\n    }\n  }\n  closeExcelDropdown() {\n    this.isExcelDropdownOpen = false;\n    console.log('Excel dropdown closed');\n  }\n  onDocumentClick(event) {\n    const target = event.target;\n    const dropdown = target.closest('.custom-dropdown');\n    if (!dropdown && this.isExcelDropdownOpen) {\n      this.closeExcelDropdown();\n    }\n  }\n  static ɵfac = function ProjectListComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProjectListComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ExceljsService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.HttpUtilsService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.KendoColumnService), i0.ɵɵdirectiveInject(i7.NgbModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i8.AppService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectListComponent,\n    selectors: [[\"app-project-list\"]],\n    viewQuery: function ProjectListComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n      }\n    },\n    hostBindings: function ProjectListComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function ProjectListComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 12,\n    vars: 22,\n    consts: [[\"normalGrid\", \"\"], [\"deleteModal\", \"\"], [\"permitWarningModal\", \"\"], [\"excelDropdown\", \"\"], [\"excelButton\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"selectionChange\", \"filterChange\", \"pageChange\", \"sortChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"total\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\"], [\"kendoGridToolbarTemplate\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"kendoGridNoRecordsTemplate\", \"\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"blur\", \"valueChange\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\"], [1, \"fw-bold\", \"ms-1\"], [\"type\", \"button\", \"title\", \"Add Project\", 1, \"btn\", \"btn-success\", \"btn-sm\", \"toolbar-btn\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"text-white\"], [\"type\", \"button\", \"title\", \"Toggle Grid Expansion\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"toolbar-btn\", 3, \"click\"], [1, \"fas\", \"text-secondary\"], [1, \"custom-dropdown\", \"toolbar-btn\"], [\"type\", \"button\", \"title\", \"Export Excel\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-file-excel\", \"text-success\"], [\"class\", \"custom-dropdown-menu\", 3, \"top\", \"left\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"toolbar-btn\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"text-warning\"], [\"type\", \"button\", \"title\", \"Refresh Grid Data\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"toolbar-btn\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\", \"text-info\"], [1, \"custom-dropdown-menu\"], [\"href\", \"#\", 1, \"custom-dropdown-item\", 3, \"click\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Status\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [1, \"col-md-3\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", 1, \"btn-primary\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [\"kendoButton\", \"\", 1, \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"title\", \"Actions\", 3, \"width\", \"includeInChooser\", \"columnMenu\", \"hidden\", 4, \"ngIf\"], [\"field\", \"projectName\", \"title\", \"Project Name\", 3, \"width\", \"includeInChooser\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"internalProjectNumber\", \"title\", \"Internal Project #\", 3, \"width\", \"includeInChooser\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"projectStartDate\", \"title\", \"Start Date\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"projectEndDate\", \"title\", \"End Date\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"projectLocation\", \"title\", \"Location\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"internalProjectManagerName\", \"title\", \"Manager\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"externalPMNames\", \"title\", \"External PM\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"title\", \"Actions\", 3, \"width\", \"includeInChooser\", \"columnMenu\", \"hidden\"], [\"kendoGridCellTemplate\", \"\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\", 2, \"min-height\", \"32px\"], [\"title\", \"View\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"32px\", \"height\", \"32px\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"text-primary\"], [\"title\", \"Delete\", \"class\", \"btn btn-icon  btn-sm d-flex align-items-center justify-content-center\", \"style\", \"width: 32px; height: 32px;\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Delete\", \"class\", \"btn btn-icon  btn-sm d-flex align-items-center justify-content-center\", \"style\", \"width: 32px; height: 32px;\", 3, \"invisible\", \"disabled\", \"click\", 4, \"ngIf\"], [\"title\", \"Delete\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"32px\", \"height\", \"32px\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"text-danger\"], [\"field\", \"projectName\", \"title\", \"Project Name\", 3, \"width\", \"includeInChooser\", \"hidden\", \"filterable\"], [\"kendoGridFilterMenuTemplate\", \"\"], [1, \"fw-bold\"], [3, \"column\", \"filter\", \"extra\"], [\"field\", \"internalProjectNumber\", \"title\", \"Internal Project #\", 3, \"width\", \"includeInChooser\", \"hidden\", \"filterable\"], [\"field\", \"projectStartDate\", \"title\", \"Start Date\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"projectEndDate\", \"title\", \"End Date\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"projectLocation\", \"title\", \"Location\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"internalProjectManagerName\", \"title\", \"Manager\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"externalPMNames\", \"title\", \"External PM\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\"], [\"class\", \"custom-no-records\", 4, \"ngIf\"], [1, \"custom-no-records\"], [1, \"text-center\"], [1, \"modal-header\", \"bg-danger\", \"text-white\"], [1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [1, \"delete-modal-body\", \"mt-4\", \"text-center\"], [1, \"fs-5\"], [1, \"modal-footer\", \"delete-modal-footer\", \"ms-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"]],\n    template: function ProjectListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵtemplate(0, ProjectListComponent_div_0_Template, 7, 0, \"div\", 5);\n        i0.ɵɵelementStart(1, \"div\", 6)(2, \"kendo-grid\", 7, 0);\n        i0.ɵɵlistener(\"selectionChange\", function ProjectListComponent_Template_kendo_grid_selectionChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSelectionChange($event));\n        })(\"filterChange\", function ProjectListComponent_Template_kendo_grid_filterChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.filterChange($event));\n        })(\"pageChange\", function ProjectListComponent_Template_kendo_grid_pageChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.pageChange($event));\n        })(\"sortChange\", function ProjectListComponent_Template_kendo_grid_sortChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSortChange($event));\n        })(\"columnVisibilityChange\", function ProjectListComponent_Template_kendo_grid_columnVisibilityChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n        });\n        i0.ɵɵtemplate(4, ProjectListComponent_ng_template_4_Template, 23, 12, \"ng-template\", 8)(5, ProjectListComponent_ng_template_5_Template, 1, 1, \"ng-template\", 8)(6, ProjectListComponent_ng_container_6_Template, 10, 9, \"ng-container\", 9)(7, ProjectListComponent_ng_template_7_Template, 1, 1, \"ng-template\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(8, ProjectListComponent_ng_template_8_Template, 12, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(10, ProjectListComponent_ng_template_10_Template, 10, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"data\", ctx.gridData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(17, _c2, i0.ɵɵpureFunction0(16, _c1)))(\"total\", ctx.page.totalElements)(\"sortable\", i0.ɵɵpureFunction0(19, _c3))(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(20, _c4))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.skip)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(21, _c5));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.gridColumns);\n      }\n    },\n    dependencies: [i9.NgForOf, i9.NgIf, i10.NgControlStatus, i10.NgModel, i11.GridComponent, i11.ToolbarTemplateDirective, i11.GridSpacerComponent, i11.ColumnComponent, i11.CellTemplateDirective, i11.NoRecordsTemplateDirective, i11.ContainsFilterOperatorComponent, i11.EqualFilterOperatorComponent, i11.NotEqualFilterOperatorComponent, i11.GreaterOrEqualToFilterOperatorComponent, i11.LessOrEqualToFilterOperatorComponent, i11.StringFilterMenuComponent, i11.FilterMenuTemplateDirective, i11.DateFilterMenuComponent, i12.TextBoxComponent, i13.DropDownListComponent, i14.ButtonComponent, i15.TruncateTextPipe],\n    styles: [\".grid-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  position: relative;\\n}\\n.grid-container[_ngcontent-%COMP%]   .k-loading-mask[_ngcontent-%COMP%], \\n.grid-container[_ngcontent-%COMP%]   .k-loading-overlay[_ngcontent-%COMP%], \\n.grid-container[_ngcontent-%COMP%]   .k-loading[_ngcontent-%COMP%], \\n.grid-container[_ngcontent-%COMP%]   .k-loading-image[_ngcontent-%COMP%], \\n.grid-container[_ngcontent-%COMP%]   .k-loading-text[_ngcontent-%COMP%], \\n.grid-container[_ngcontent-%COMP%]   .k-i-loading[_ngcontent-%COMP%], \\n.grid-container[_ngcontent-%COMP%]   .k-loading-panel[_ngcontent-%COMP%], \\n.grid-container[_ngcontent-%COMP%]   .k-loading-indicator[_ngcontent-%COMP%], \\n.grid-container[_ngcontent-%COMP%]   .k-spinner[_ngcontent-%COMP%], \\n.grid-container[_ngcontent-%COMP%]   .k-spinner-pulse[_ngcontent-%COMP%] {\\n  display: none !important;\\n  visibility: hidden !important;\\n  opacity: 0 !important;\\n}\\n.grid-container.fullscreen-grid[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  z-index: 9999;\\n  background: white;\\n  padding: 20px;\\n  overflow: auto;\\n}\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  min-height: 250px;\\n}\\n\\n.delete-modal-footer[_ngcontent-%COMP%] {\\n  min-height: 10px;\\n}\\n\\n.delete-modal-body[_ngcontent-%COMP%] {\\n  min-height: 52px;\\n}\\n\\n.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  width: 80%;\\n  border: 2px solid #afc7dd;\\n  box-shadow: 0 0 6px rgba(59, 83, 135, 0.5);\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.75rem 1.25rem;\\n  min-width: 120px;\\n  background-color: #4c4e4f;\\n  color: white;\\n  font-weight: 500;\\n  transition: background 0.3s, transform 0.2s;\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  background-color: #4c4e4f;\\n  transform: scale(1.05);\\n}\\n\\n.advanced-filters-panel[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  margin-bottom: 1rem;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n  margin-bottom: 0.5rem;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-radius: 6px;\\n}\\n\\n[_nghost-%COMP%]     .k-grid {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n[_nghost-%COMP%]     .k-grid .k-loading-mask, \\n[_nghost-%COMP%]     .k-grid .k-loading-overlay, \\n[_nghost-%COMP%]     .k-grid .k-loading, \\n[_nghost-%COMP%]     .k-grid .k-loading-image, \\n[_nghost-%COMP%]     .k-grid .k-loading-text, \\n[_nghost-%COMP%]     .k-grid .k-i-loading, \\n[_nghost-%COMP%]     .k-grid .k-loading-panel, \\n[_nghost-%COMP%]     .k-grid .k-loading-indicator, \\n[_nghost-%COMP%]     .k-grid .k-spinner, \\n[_nghost-%COMP%]     .k-grid .k-spinner-pulse {\\n  display: none !important;\\n  visibility: hidden !important;\\n  opacity: 0 !important;\\n}\\n[_nghost-%COMP%]     .k-grid.k-loading .k-loading-mask, \\n[_nghost-%COMP%]     .k-grid.k-loading .k-loading-overlay, \\n[_nghost-%COMP%]     .k-grid.k-loading .k-loading, \\n[_nghost-%COMP%]     .k-grid.k-loading .k-loading-panel, \\n[_nghost-%COMP%]     .k-grid.k-loading .k-loading-indicator, \\n[_nghost-%COMP%]     .k-grid.k-loading .k-spinner, \\n[_nghost-%COMP%]     .k-grid.k-loading .k-spinner-pulse {\\n  display: none !important;\\n  visibility: hidden !important;\\n  opacity: 0 !important;\\n}\\n[_nghost-%COMP%]     .k-grid *[class*=loading], \\n[_nghost-%COMP%]     .k-grid *[class*=spinner], \\n[_nghost-%COMP%]     .k-grid *[class*=k-loading] {\\n  display: none !important;\\n  visibility: hidden !important;\\n  opacity: 0 !important;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header {\\n  background: #f8f9fa;\\n  border-bottom: 2px solid #dee2e6;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header .k-header {\\n  background: #f8f9fa;\\n  border-color: #dee2e6;\\n  font-weight: 600;\\n  color: #495057;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header .k-header:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar {\\n  background: #f8f9fa;\\n  border-bottom: 1px solid #dee2e6;\\n  \\n\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button {\\n  border-radius: 6px;\\n  font-weight: 500;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button.k-primary {\\n  background: #007bff;\\n  border-color: #007bff;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button.k-primary:hover {\\n  background: #0056b3;\\n  border-color: #0056b3;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .btn {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 5px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border-radius: 0.375rem;\\n  transition: all 0.15s ease-in-out;\\n  min-width: 40px;\\n  height: 40px;\\n  border: 1px solid transparent;\\n  background-color: transparent;\\n  \\n\\n  \\n\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .btn:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .btn.btn-icon {\\n  padding: 0.5rem;\\n  min-width: 40px;\\n  width: 40px;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .btn:not(.btn-icon) {\\n  padding: 0.375rem 0.75rem;\\n  gap: 0.5rem;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row:hover {\\n  background: #f8f9fa;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt {\\n  background: #f8f9fa;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager {\\n  background: #f8f9fa;\\n  border-top: 1px solid #dee2e6;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-info {\\n  color: #6c757d;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link {\\n  border-radius: 4px;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link.k-state-selected {\\n  background: #007bff;\\n  color: white;\\n}\\n[_nghost-%COMP%]     .k-dropdownbutton.btn .k-button {\\n  border-radius: 0.375rem !important;\\n  font-weight: 500 !important;\\n  font-size: 0.75rem !important;\\n  padding: 0.25rem 0.5rem !important;\\n  background-color: #6c757d !important;\\n  border-color: #6c757d !important;\\n  color: #fff !important;\\n  margin-right: 0.5rem !important;\\n  transition: all 0.2s ease !important;\\n  height: auto !important;\\n  min-height: 31px !important;\\n}\\n[_nghost-%COMP%]     .k-dropdownbutton.btn .k-button:hover {\\n  background-color: #5a6268 !important;\\n  border-color: #545b62 !important;\\n  transform: translateY(-1px) !important;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;\\n}\\n[_nghost-%COMP%]     .k-dropdownbutton.btn .k-button:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5) !important;\\n}\\n[_nghost-%COMP%]     .k-textbox {\\n  border-radius: 6px;\\n}\\n[_nghost-%COMP%]     .k-textbox:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n[_nghost-%COMP%]     .k-dropdownlist {\\n  border-radius: 6px;\\n}\\n[_nghost-%COMP%]     .k-dropdownlist:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  padding: 0.5em 0.75em;\\n  font-size: 0.75em;\\n  font-weight: 600;\\n  border-radius: 6px;\\n}\\n.badge.badge-light-success[_ngcontent-%COMP%] {\\n  background: #d4edda;\\n  color: #155724;\\n}\\n.badge.badge-light-warning[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  color: #856404;\\n}\\n.badge.badge-light-danger[_ngcontent-%COMP%] {\\n  background: #f8d7da;\\n  color: #721c24;\\n}\\n.badge.badge-light-info[_ngcontent-%COMP%] {\\n  background: #d1ecf1;\\n  color: #0c5460;\\n}\\n.badge.badge-light-secondary[_ngcontent-%COMP%] {\\n  background: #e2e3e5;\\n  color: #383d41;\\n}\\n.badge.badge-light-primary[_ngcontent-%COMP%] {\\n  background: #cce7ff;\\n  color: #004085;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n.btn.btn-success[_ngcontent-%COMP%] {\\n  background: #28a745;\\n  border-color: #28a745;\\n}\\n.btn.btn-success[_ngcontent-%COMP%]:hover {\\n  background: #1e7e34;\\n  border-color: #1e7e34;\\n}\\n.btn.btn-warning[_ngcontent-%COMP%] {\\n  background: #ffc107;\\n  border-color: #ffc107;\\n  color: #212529;\\n}\\n.btn.btn-warning[_ngcontent-%COMP%]:hover {\\n  background: #e0a800;\\n  border-color: #d39e00;\\n}\\n.btn.btn-info[_ngcontent-%COMP%] {\\n  background: #17a2b8;\\n  border-color: #17a2b8;\\n}\\n.btn.btn-info[_ngcontent-%COMP%]:hover {\\n  background: #138496;\\n  border-color: #138496;\\n}\\n.btn.btn-outline-secondary[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  border-color: #6c757d;\\n}\\n.btn.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background: #6c757d;\\n  color: white;\\n}\\n\\n.btn-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  padding: 0;\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n}\\n.btn-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action] {\\n  padding: 4px 8px;\\n  vertical-align: middle;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action] .d-flex {\\n  min-height: 32px;\\n  align-items: center;\\n  justify-content: center;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action] .d-flex .btn-icon {\\n  flex-shrink: 0;\\n  margin: 0 2px;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action] .d-flex .btn-icon.invisible {\\n  visibility: hidden;\\n  pointer-events: none;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-cell[data-field=action] .d-flex .btn-icon.disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n}\\n\\n\\n\\n.dropdown[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   .fas.fa-file-excel[_ngcontent-%COMP%] {\\n  color: #28a745 !important; \\n\\n}\\n\\n.text-muted[_ngcontent-%COMP%]   .fas[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.text-muted[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #495057;\\n  margin-top: 1rem;\\n}\\n.text-muted[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-bottom: 1.5rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%], \\n   .advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n}\\n.grid-container[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.grid-container.fullscreen-grid[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_expandGrid 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_expandGrid {\\n  from {\\n    opacity: 0.8;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n\\n[_nghost-%COMP%]     .dropdown {\\n  z-index: 999999 !important;\\n  position: relative !important;\\n}\\n\\n[_nghost-%COMP%]     .excel-dropdown-menu {\\n  z-index: 999999 !important;\\n  position: absolute !important;\\n}\\n\\n\\n\\n[_nghost-%COMP%]     .k-grid, \\n[_nghost-%COMP%]     .k-grid-header, \\n[_nghost-%COMP%]     .k-grid-header-wrap {\\n  z-index: 1 !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["each", "ProjectPopupComponent", "ConfirmationDialogComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ProjectListComponent_ng_template_4_div_18_Template_a_click_1_listener", "$event", "ɵɵrestoreView", "_r4", "ctx_r2", "ɵɵnextContext", "onExportClick", "value", "closeExcelDropdown", "ɵɵresetView", "preventDefault", "ProjectListComponent_ng_template_4_div_18_Template_a_click_3_listener", "ɵɵstyleProp", "dropdownTop", "dropdownLeft", "ɵɵtwoWayListener", "ProjectListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener", "_r2", "ɵɵtwoWayBindingSet", "searchData", "ProjectListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener", "onSearchKeyDown", "ProjectListComponent_ng_template_4_Template_kendo_textbox_blur_1_listener", "onSearchBlur", "ProjectListComponent_ng_template_4_Template_kendo_textbox_valueChange_1_listener", "onSearchValueChange", "ɵɵelement", "ProjectListComponent_ng_template_4_Template_button_click_8_listener", "add", "ProjectListComponent_ng_template_4_Template_button_click_11_listener", "toggleExpand", "ProjectListComponent_ng_template_4_Template_button_click_15_listener", "toggleExcelDropdown", "ɵɵtemplate", "ProjectListComponent_ng_template_4_div_18_Template", "ProjectListComponent_ng_template_4_Template_button_click_19_listener", "resetTable", "ProjectListComponent_ng_template_4_Template_button_click_21_listener", "refreshGrid", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵproperty", "ɵɵtextInterpolate", "page", "totalElements", "ɵɵclassProp", "isExpanded", "isExcelDropdownOpen", "ProjectListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener", "_r5", "appliedFilters", "status", "ProjectListComponent_ng_template_5_div_0_Template_button_click_7_listener", "applyAdvancedFilters", "ProjectListComponent_ng_template_5_div_0_Template_button_click_10_listener", "clearAllFilters", "advancedFilterOptions", "ProjectListComponent_ng_template_5_div_0_Template", "showAdvancedFilters", "ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_3_Template_a_click_0_listener", "_r8", "dataItem_r7", "dataItem", "deleteModal_r9", "ɵɵreference", "deletePop", "projectId", "projectName", "ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_4_Template_a_click_0_listener", "_r10", "isDeletable", "loginUser", "userId", "created<PERSON>y", "ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_1_listener", "_r6", "edit", "ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_3_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_4_Template", "<PERSON><PERSON><PERSON>", "ProjectListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template", "getHiddenField", "ɵɵpipeBind2", "dataItem_r11", "column_r13", "filter_r12", "ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template", "dataItem_r14", "internalProjectNumber", "column_r16", "filter_r15", "ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template", "ɵɵtextInterpolate1", "formatDate", "dataItem_r17", "projectStartDate", "column_r19", "filter_r18", "ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template", "ɵɵpureFunction0", "_c6", "dataItem_r20", "projectEndDate", "column_r22", "filter_r21", "ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template", "dataItem_r23", "projectLocation", "column_r25", "filter_r24", "ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template", "dataItem_r26", "internalProjectManagerName", "column_r28", "filter_r27", "ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template", "dataItem_r29", "externalPMNames", "column_r31", "filter_r30", "ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template", "dataItem_r32", "lastUpdatedDate", "column_r34", "filter_r33", "ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_9_ng_template_2_Template", "ɵɵelementContainerStart", "ProjectListComponent_ng_container_6_kendo_grid_column_1_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_2_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_3_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_4_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_5_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_6_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_7_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_8_Template", "ProjectListComponent_ng_container_6_kendo_grid_column_9_Template", "column_r35", "ProjectListComponent_ng_template_7_div_0_Template", "loading", "isLoading", "ProjectListComponent_ng_template_8_Template_button_click_3_listener", "modal_r37", "_r36", "$implicit", "dismiss", "ProjectListComponent_ng_template_8_Template_button_click_8_listener", "ProjectListComponent_ng_template_8_Template_button_click_10_listener", "confirmDelete", "close", "ProjectListComponent_ng_template_10_Template_button_click_3_listener", "modal_r39", "_r38", "ProjectListComponent_ng_template_10_Template_button_click_8_listener", "ProjectListComponent", "router", "execeljsservice", "route", "projectsService", "httpUtilService", "customLayoutUtilsService", "kendoColumnService", "modalService", "cdr", "appService", "grid", "serverSideRowData", "gridData", "IsListHasValue", "filter", "logic", "filters", "gridFilter", "activeFilters", "filterOptions", "text", "centers", "kendoHide", "hiddenData", "kendoColOrder", "kendoInitColOrder", "hiddenFields", "gridColumns", "defaultColumns", "normalGrid", "expandedGrid", "customReorderableConfig", "gridColumnConfig", "field", "title", "width", "isFixed", "type", "order", "filterable", "sort", "dir", "size", "pageNumber", "totalPages", "orderBy", "orderDir", "skip", "selectedRows", "isAllSelected", "exportOptions", "constructor", "ngOnInit", "initializeComponent", "loadTable", "ngAfterViewInit", "initializeGrid", "ngOnDestroy", "getLoggedInUser", "console", "log", "initializeColumnVisibility", "setupColumnArrays", "savedConfig", "getFromLocalStorage", "applySavedColumnSettings", "loadColumnSettingsFromServer", "config", "pageName", "userID", "getHideFields", "subscribe", "next", "response", "<PERSON><PERSON><PERSON>", "Data", "hideData", "JSON", "parse", "error", "map", "col", "length", "a", "b", "aOrder", "indexOf", "b<PERSON>rder", "loadTableWithKendoEndpoint", "loadingSubject", "loadingTimeout", "setTimeout", "warn", "resetLoadingStates", "state", "take", "search", "loggedInUserId", "role", "searchTerm", "getProjectsForKendoGrid", "data", "clearTimeout", "responseData", "errors", "handleEmptyResponse", "projectData", "total", "Math", "ceil", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "httpError", "complete", "event", "key", "trim", "onSearchChange", "applySearch", "clearSearch", "testExternalPMSearch", "filterChange", "f", "push", "operator", "center", "clearAdvancedFilters", "onSortChange", "isThirdClick", "undefined", "pageChange", "floor", "updateColumnVisibility", "hiddenColumns", "fieldName", "includes", "onSelectionChange", "selectAll", "gridContainer", "document", "querySelector", "classList", "toggle", "refresh", "selectedOption", "prdItems", "exportExcel", "queryparamsExcel", "pageSize", "sortOrder", "sortField", "getAllProjects", "detectChanges", "listOfItems", "currentDate", "formatMonthDate", "Date", "tableTitle", "headerArray", "percentageColumns", "respResult", "prdItem", "respData", "Array", "fill", "eventDescription", "event_date", "for<PERSON>ach", "i", "adjustedIndex", "colSize", "header", "index", "id", "generateExcel", "message", "saveHead", "settings", "saveToLocalStorage", "LoggedId", "showSuccess", "saveColumnSettingsToServer", "createHideFields", "showError", "saveResetToServer", "deleteConfig", "deleteHideFields", "clearFromLocalStorage", "resetToDefaultSettings", "columns", "column", "hidden", "reset", "view", "navigate", "modalRef", "open", "centered", "backdrop", "componentInstance", "project", "passEntry", "result", "delete", "deleteProject", "getProjectFullName", "projectFirstName", "projectLastName", "getCenterName", "medicalCenter", "centerName", "dateString", "date", "month", "String", "getMonth", "padStart", "day", "getDate", "year", "getFullYear", "getStatusClass", "toLowerCase", "content", "showClose", "description", "cancelButtonText", "actionButtonText", "success", "onTabActivated", "button", "target", "rect", "getBoundingClientRect", "bottom", "window", "scrollY", "left", "scrollX", "onDocumentClick", "dropdown", "closest", "ɵɵdirectiveInject", "i1", "Router", "i2", "ExceljsService", "ActivatedRoute", "i3", "ProjectsService", "i4", "HttpUtilsService", "i5", "CustomLayoutUtilsService", "i6", "KendoColumnService", "i7", "NgbModal", "ChangeDetectorRef", "i8", "AppService", "selectors", "viewQuery", "ProjectListComponent_Query", "rf", "ctx", "ProjectListComponent_click_HostBindingHandler", "ɵɵresolveDocument", "ProjectListComponent_div_0_Template", "ProjectListComponent_Template_kendo_grid_selectionChange_2_listener", "_r1", "ProjectListComponent_Template_kendo_grid_filterChange_2_listener", "ProjectListComponent_Template_kendo_grid_pageChange_2_listener", "ProjectListComponent_Template_kendo_grid_sortChange_2_listener", "ProjectListComponent_Template_kendo_grid_columnVisibilityChange_2_listener", "ProjectListComponent_ng_template_4_Template", "ProjectListComponent_ng_template_5_Template", "ProjectListComponent_ng_container_6_Template", "ProjectListComponent_ng_template_7_Template", "ProjectListComponent_ng_template_8_Template", "ɵɵtemplateRefExtractor", "ProjectListComponent_ng_template_10_Template", "ɵɵpureFunction1", "_c2", "_c1", "_c3", "_c4", "_c5"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-list\\project-list.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-list\\project-list.component.html"], "sourcesContent": ["import { formatDate } from '@angular/common';\r\nimport {\r\n  AfterViewInit,\r\n  ChangeDetectorRef,\r\n  Component,\r\n  HostListener,\r\n  OnDestroy,\r\n  OnInit,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\n\r\nimport saveAs from 'file-saver';\r\nimport { add, each } from 'lodash';\r\n// Removed unused RxJS imports for debounced search\r\n// import {\r\n//   Subject,\r\n//   Subscription,\r\n//   debounceTime,\r\n//   distinctUntilChanged,\r\n//   filter,\r\n// } from 'rxjs';\r\nimport { AppService } from '../../services/app.service';\r\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\r\nimport { HttpUtilsService } from '../../services/http-utils.service';\r\nimport { KendoColumnService } from '../../services/kendo-column.service';\r\nimport {\r\n  CompositeFilterDescriptor,\r\n  SortDescriptor,\r\n} from '@progress/kendo-data-query';\r\nimport { ProjectsService } from '../../services/projects.service';\r\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\r\nimport { ExceljsService } from '../../services/exceljs.service';\r\nimport { ConfirmationDialogComponent } from '../../shared/confirmation-dialog/confirmation-dialog.component';\r\n\r\n@Component({\r\n  selector: 'app-project-list',\r\n  templateUrl: './project-list.component.html',\r\n  styleUrl: './project-list.component.scss',\r\n})\r\nexport class ProjectListComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  @ViewChild('normalGrid') grid: any;\r\n\r\n  // Data\r\n  public serverSideRowData: any[] = [];\r\n  public gridData: any = [];\r\n  public IsListHasValue: boolean = false;\r\n\r\n  public loading: boolean = false;\r\n  public isLoading: boolean = false;\r\n\r\n  loginUser: any = {};\r\n\r\n  // Search\r\n  public searchData: string = '';\r\n  // Removed debounced search - now only triggers on Enter or blur\r\n  // private searchTerms = new Subject<string>();\r\n  // private searchSubscription: Subscription;\r\n\r\n  // Enhanced Filters for Kendo UI\r\n  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\r\n  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\r\n  public activeFilters: Array<{\r\n    field: string;\r\n    operator: string;\r\n    value: any;\r\n  }> = [];\r\n\r\n  public filterOptions: Array<{ text: string; value: string | null }> = [\r\n    { text: 'All', value: null },\r\n    { text: 'Current', value: 'Current' },\r\n    { text: 'Completed', value: 'Completed' },\r\n    { text: 'Cancelled & Archived', value: 'Cancelled & Archived' },\r\n    { text: 'Closed & Archived', value: 'Closed & Archived' },\r\n  ];\r\n\r\n  // Advanced filter options\r\n  public advancedFilterOptions = {\r\n    status: [\r\n      { text: 'All', value: null },\r\n      { text: 'Current', value: 'Current' },\r\n      { text: 'Completed', value: 'Completed' },\r\n      { text: 'Cancelled & Archived', value: 'Cancelled & Archived' },\r\n      { text: 'Closed & Archived', value: 'Closed & Archived' },\r\n    ] as Array<{ text: string; value: string | null }>,\r\n    centers: [] as Array<{ text: string; value: string | null }>,\r\n  };\r\n\r\n  // Filter state\r\n  public showAdvancedFilters = false;\r\n  public appliedFilters: {\r\n    status?: string | null;\r\n    center?: string | null;\r\n  } = {};\r\n\r\n  // Column visibility system\r\n  public kendoHide: any;\r\n  public hiddenData: any = [];\r\n  public kendoColOrder: any = [];\r\n  public kendoInitColOrder: any = [];\r\n  public hiddenFields: any = [];\r\n\r\n  // Column configuration\r\n  public gridColumns: string[] = [];\r\n  public defaultColumns: string[] = [];\r\n  public normalGrid: any;\r\n  public expandedGrid: any;\r\n  public isExpanded = false;\r\n\r\n  // Custom reorderable configuration that prevents fixed column reordering\r\n  public customReorderableConfig = false;\r\n\r\n  // Enhanced Columns with Kendo UI features\r\n  public gridColumnConfig: Array<{\r\n    field: string;\r\n    title: string;\r\n    width: number;\r\n    isFixed: boolean;\r\n    type: string;\r\n    filterable?: boolean;\r\n    order: number;\r\n  }> = [\r\n    {\r\n      field: 'action',\r\n      title: 'Action',\r\n      width: 100,\r\n      isFixed: true,\r\n      type: 'action',\r\n      order: 1,\r\n    },\r\n    {\r\n      field: 'projectName',\r\n      title: 'Project Name',\r\n      width: 200,\r\n      isFixed: true,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 2,\r\n    },\r\n    {\r\n      field: 'internalProjectNumber',\r\n      title: 'Internal Project #',\r\n      width: 120,\r\n      isFixed: true,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 3,\r\n    },\r\n    {\r\n      field: 'projectStartDate',\r\n      title: 'Start Date',\r\n      width: 120,\r\n      isFixed: false,\r\n      type: 'date',\r\n      filterable: true,\r\n      order: 4,\r\n    },\r\n    {\r\n      field: 'projectEndDate',\r\n      title: 'End Date',\r\n      width: 120,\r\n      isFixed: false,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 5,\r\n    },\r\n    {\r\n      field: 'projectLocation',\r\n      title: 'Location',\r\n      width: 150,\r\n      isFixed: false,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 6,\r\n    },\r\n    {\r\n      field: 'internalProjectManagerName',\r\n      title: 'Manager',\r\n      width: 150,\r\n      isFixed: false,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 7,\r\n    },\r\n    {\r\n      field: 'externalPMNames',\r\n      title: 'External PM',\r\n      width: 220,\r\n      type: 'status',\r\n      isFixed: false,\r\n      filterable: true,\r\n      order: 8,\r\n    },\r\n    {\r\n      field: 'lastUpdatedDate',\r\n      title: 'Updated Date',\r\n      width: 120,\r\n      isFixed: false,\r\n      type: 'date',\r\n      filterable: true,\r\n      order: 9,\r\n    },\r\n  ];\r\n\r\n  // State\r\n  public sort: SortDescriptor[] = [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n\r\n  public page: any = {\r\n    size: 15,\r\n    pageNumber: 0,\r\n    totalElements: 0,\r\n    totalPages: 0,\r\n    orderBy: 'lastUpdatedDate',\r\n    orderDir: 'desc',\r\n  };\r\n\r\n  public skip: number = 0;\r\n\r\n  // Selection\r\n  public selectedRows: any[] = [];\r\n  public isAllSelected: boolean = false;\r\n\r\n  // Export options\r\n  public exportOptions = [\r\n    { text: 'All', value: 'all' },\r\n    { text: 'Page Results', value: 'selected' },\r\n    // { text: 'Export Filtered', value: 'filtered' },\r\n  ];\r\n\r\n  // Custom dropdown state\r\n  public isExcelDropdownOpen: boolean = false;\r\n  public dropdownTop: number = 0;\r\n  public dropdownLeft: number = 0;\r\n\r\n  projectName: any;\r\n  projectId: any;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private execeljsservice: ExceljsService,\r\n    private route: ActivatedRoute,\r\n    private projectsService: ProjectsService,\r\n    private httpUtilService: HttpUtilsService,\r\n    private customLayoutUtilsService: CustomLayoutUtilsService,\r\n    private kendoColumnService: KendoColumnService,\r\n    private modalService: NgbModal,\r\n    private cdr: ChangeDetectorRef,\r\n    public appService: AppService\r\n  ) {\r\n    // Removed debounced search subscription - search now only triggers on Enter or blur\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.initializeComponent();\r\n    this.loadTable();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.initializeGrid();\r\n  }\r\n\r\n\r\n\r\n  ngOnDestroy(): void {\r\n    // Removed search subscription cleanup - no longer needed\r\n  }\r\n\r\n  private initializeComponent(): void {\r\n    // Get login user info\r\n    // this.loginUser = this.customLayoutUtils.getLoginUser();\r\n    this.loginUser = this.appService.getLoggedInUser();\r\n    console.log(\"this.loginUser\", this.loginUser)\r\n    // Initialize column visibility system\r\n    this.initializeColumnVisibility();\r\n  }\r\n\r\n  private initializeColumnVisibility(): void {\r\n    // Set up column arrays first\r\n    this.setupColumnArrays();\r\n\r\n    // Try to load from local storage first\r\n    const savedConfig = this.kendoColumnService.getFromLocalStorage('projects', this.loginUser.userId);\r\n\r\n    if (savedConfig) {\r\n      // Load saved settings from local storage\r\n      this.kendoHide = savedConfig.hiddenData || [];\r\n      this.kendoColOrder = savedConfig.kendoColOrder || [...this.defaultColumns];\r\n      this.kendoInitColOrder = [...this.kendoColOrder];\r\n    } else {\r\n      // Initialize with default values\r\n      this.kendoHide = [];\r\n      this.hiddenData = [];\r\n      this.kendoColOrder = [...this.defaultColumns];\r\n      this.kendoInitColOrder = [...this.defaultColumns];\r\n    }\r\n\r\n    // Apply settings\r\n    this.applySavedColumnSettings();\r\n  }\r\n\r\n  private loadColumnSettingsFromServer(): void {\r\n    const config = {\r\n      pageName: 'projects',\r\n      userID: this.loginUser.userId\r\n    };\r\n\r\n    this.kendoColumnService.getHideFields(config).subscribe({\r\n      next: (response) => {\r\n        if (response.isFault === false && response.Data) {\r\n          // Parse the saved settings\r\n          this.kendoHide = response.Data.hideData ? JSON.parse(response.Data.hideData) : [];\r\n          this.kendoColOrder = response.Data.kendoColOrder ? JSON.parse(response.Data.kendoColOrder) : [...this.defaultColumns];\r\n          this.kendoInitColOrder = [...this.kendoColOrder];\r\n\r\n          // Apply the settings\r\n          this.applySavedColumnSettings();\r\n\r\n          console.log('Column settings loaded from server:', {\r\n            kendoHide: this.kendoHide,\r\n            kendoColOrder: this.kendoColOrder\r\n          });\r\n        } else {\r\n          // No saved settings, use defaults\r\n          this.kendoHide = [];\r\n          this.kendoColOrder = [...this.defaultColumns];\r\n          this.kendoInitColOrder = [...this.defaultColumns];\r\n          this.applySavedColumnSettings();\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading column settings:', error);\r\n        // Use defaults on error\r\n        this.kendoHide = [];\r\n        this.kendoColOrder = [...this.defaultColumns];\r\n        this.kendoInitColOrder = [...this.defaultColumns];\r\n        this.applySavedColumnSettings();\r\n      }\r\n    });\r\n  }\r\n\r\n  private setupColumnArrays(): void {\r\n    this.gridColumns = this.gridColumnConfig.map((col) => col.field);\r\n    this.defaultColumns = [...this.gridColumns];\r\n  }\r\n\r\n  private initializeGrid(): void {\r\n    if (this.grid) {\r\n      // Apply saved column settings\r\n      this.applySavedColumnSettings();\r\n    }\r\n  }\r\n\r\n  private applySavedColumnSettings(): void {\r\n    if (this.kendoHide && this.kendoHide.length > 0) {\r\n      this.hiddenFields = this.kendoHide;\r\n    }\r\n\r\n    if (this.kendoColOrder && this.kendoColOrder.length > 0) {\r\n      // Apply column order\r\n      this.gridColumnConfig.sort((a, b) => {\r\n        const aOrder = this.kendoColOrder.indexOf(a.field);\r\n        const bOrder = this.kendoColOrder.indexOf(b.field);\r\n        return aOrder - bOrder;\r\n      });\r\n    }\r\n  }\r\n\r\n  // Load table data\r\n  public loadTable(): void {\r\n    this.loadTableWithKendoEndpoint();\r\n  }\r\n\r\n  // New method to load data using Kendo UI specific endpoint\r\n  loadTableWithKendoEndpoint() {\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n\r\n    // Enable loader\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    // Safety timeout to prevent loader from getting stuck\r\n    const loadingTimeout = setTimeout(() => {\r\n      console.warn('Loading timeout reached, resetting loading states');\r\n      this.resetLoadingStates();\r\n    }, 15000); // 15 seconds timeout - reduced from 30 seconds\r\n\r\n    // Prepare state object for Kendo UI endpoint\r\n    const state = {\r\n      take: this.page.size,\r\n      skip: this.skip,\r\n      sort: this.sort,\r\n      filter: this.filter.filters,\r\n      search: this.searchData,\r\n      loggedInUserId: this.loginUser.userId,\r\n      role:this.loginUser.roleName || 1,\r\n      userId:this.loginUser.userId\r\n    };\r\n\r\n    console.log('Search request state:', {\r\n      searchTerm: this.searchData,\r\n      state: state\r\n    });\r\n\r\n    this.projectsService.getProjectsForKendoGrid(state).subscribe({\r\n      next: (data: {\r\n        isFault?: boolean;\r\n        responseData?: {\r\n          data: any[];\r\n          total: number;\r\n          errors?: string[];\r\n          status?: number;\r\n        };\r\n        data?: any[];\r\n        total?: number;\r\n        errors?: string[];\r\n        status?: number;\r\n      }) => {\r\n        // Clear the safety timeout since we got a response\r\n        clearTimeout(loadingTimeout);\r\n\r\n        console.log('API Response:', data);\r\n\r\n        // Handle the new API response structure\r\n        if (\r\n          data.isFault ||\r\n          (data.responseData &&\r\n            data.responseData.errors &&\r\n            data.responseData.errors.length > 0)\r\n        ) {\r\n          const errors = data.responseData?.errors || data.errors || [];\r\n          console.error('Kendo UI Grid errors:', errors);\r\n\r\n          // Check if this is an authentication error\r\n          if (data.responseData?.status === 401 || data.status === 401) {\r\n            console.warn('Authentication error - token may be expired');\r\n            // Don't handle empty response here, let the interceptor handle auth\r\n            return;\r\n          }\r\n\r\n          this.handleEmptyResponse();\r\n          // Always reset loading states regardless of data content\r\n          this.loading = false;\r\n          this.isLoading = false;\r\n          this.httpUtilService.loadingSubject.next(false);\r\n        } else {\r\n          // Handle both old and new response structures\r\n          const responseData = data.responseData || data;\r\n          const projectData = responseData.data || [];\r\n          const total = responseData.total || 0;\r\n\r\n          this.IsListHasValue = projectData.length !== 0;\r\n          this.serverSideRowData = projectData;\r\n          this.page.totalElements = total;\r\n          this.page.totalPages = Math.ceil(total / this.page.size);\r\n\r\n          // Create a data source with total count for Kendo Grid\r\n          this.gridData = {\r\n            data: projectData,\r\n            total: total\r\n          };\r\n          console.log('this.serverSideRowData ', this.serverSideRowData);\r\n          console.log('this.gridData ', this.gridData);\r\n          console.log('this.IsListHasValue ', this.IsListHasValue);\r\n          console.log('this.page ', this.page);\r\n          this.cdr.markForCheck();\r\n          // Always reset loading states regardless of data content\r\n          this.loading = false;\r\n          this.isLoading = false;\r\n          this.httpUtilService.loadingSubject.next(false);\r\n        }\r\n      },\r\n      error: (error: unknown) => {\r\n        // Clear the safety timeout since we got an error\r\n        clearTimeout(loadingTimeout);\r\n\r\n        console.error('Error loading data with Kendo UI endpoint:', error);\r\n\r\n        // Check if this is an authentication error\r\n        if (error && typeof error === 'object' && 'status' in error) {\r\n          const httpError = error as any;\r\n          if (httpError.status === 401) {\r\n            console.warn('Authentication error - token may be expired');\r\n            // Don't handle empty response here, let the interceptor handle auth\r\n            return;\r\n          }\r\n        }\r\n\r\n        this.handleEmptyResponse();\r\n        this.loading = false;\r\n        this.isLoading = false;\r\n        this.httpUtilService.loadingSubject.next(false);\r\n      },\r\n      complete: () => {\r\n        // Clear the safety timeout\r\n        clearTimeout(loadingTimeout);\r\n\r\n        // Ensure loading states are reset in complete block as well\r\n        this.loading = false;\r\n        this.isLoading = false;\r\n        this.httpUtilService.loadingSubject.next(false);\r\n      },\r\n    });\r\n  }\r\n\r\n  private handleEmptyResponse(): void {\r\n    this.IsListHasValue = false;\r\n    this.serverSideRowData = [];\r\n    this.gridData = [];\r\n    this.page.totalElements = 0;\r\n    this.page.totalPages = 0;\r\n\r\n    // Ensure loading states are reset when handling empty response\r\n    this.loading = false;\r\n    this.isLoading = false;\r\n    this.httpUtilService.loadingSubject.next(false);\r\n  }\r\n\r\n  // Method to manually reset loading states if they get stuck\r\n  private resetLoadingStates(): void {\r\n    this.loading = false;\r\n    this.isLoading = false;\r\n    this.httpUtilService.loadingSubject.next(false);\r\n  }\r\n\r\n  // Public method to manually refresh the grid and reset any stuck loading states\r\n  public refreshGrid(): void {\r\n    console.log('Manually refreshing grid...');\r\n    this.resetLoadingStates();\r\n    this.loadTable();\r\n  }\r\n\r\n\r\n  // Search functionality\r\n  public onSearchKeyDown(event: KeyboardEvent): void {\r\n    if (event.key === 'Enter') {\r\n      console.log('Search triggered by Enter key:', this.searchData);\r\n\r\n      // Set loading state immediately for search\r\n      this.loading = true;\r\n      this.isLoading = true;\r\n      this.httpUtilService.loadingSubject.next(true);\r\n\r\n      this.loadTable();\r\n    }\r\n  }\r\n\r\n  public onSearchBlur(): void {\r\n    console.log('Search blur triggered:', this.searchData);\r\n    // Trigger search when user clicks outside the input\r\n    this.loadTable();\r\n  }\r\n\r\n  public onSearchValueChange(): void {\r\n    console.log('Search value changed:', this.searchData);\r\n    // This method handles value changes, including when clear button is clicked\r\n    // Only trigger search if the value is empty (clear button was clicked)\r\n    if (!this.searchData || this.searchData.trim() === '') {\r\n      this.loadTable();\r\n    }\r\n  }\r\n\r\n  public onSearchChange(): void {\r\n    console.log('Search changed:', this.searchData);\r\n    // This method is no longer used but kept for compatibility\r\n    // Search now only triggers on Enter key or blur event\r\n  }\r\n\r\n\r\n  private applySearch(): void {\r\n    // Set loading state immediately for search\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    this.loadTable();\r\n  }\r\n\r\n  public clearSearch(): void {\r\n    this.searchData = '';\r\n    this.loadTable();\r\n  }\r\n\r\n  // Test method for External PM search\r\n  public testExternalPMSearch(): void {\r\n    console.log('Testing External PM search...');\r\n    this.searchData = 'External PM'; // Test search term\r\n\r\n    // Set loading state immediately for test search\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    this.loadTable();\r\n  }\r\n\r\n  // Filter functionality\r\n  public filterChange(filter: CompositeFilterDescriptor): void {\r\n    this.filter = filter;\r\n\r\n    // Set loading state immediately for filtering\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    this.loadTable();\r\n  }\r\n\r\n  public applyAdvancedFilters(): void {\r\n    // Apply status filter (use new 'status' field)\r\n    if (this.appliedFilters.status) {\r\n      this.filter.filters = this.filter.filters.filter((f) => {\r\n        if ('field' in f) {\r\n          return f.field !== 'status' && f.field !== 'projectStatus';\r\n        }\r\n        return true;\r\n      });\r\n      this.filter.filters.push({\r\n        field: 'status',\r\n        operator: 'eq',\r\n        value: this.appliedFilters.status,\r\n      });\r\n    }\r\n\r\n    // Apply center filter\r\n    if (this.appliedFilters.center) {\r\n      this.filter.filters = this.filter.filters.filter((f) => {\r\n        if ('field' in f) {\r\n          return f.field !== 'centerId';\r\n        }\r\n        return true;\r\n      });\r\n      this.filter.filters.push({\r\n        field: 'centerId',\r\n        operator: 'eq',\r\n        value: this.appliedFilters.center,\r\n      });\r\n    }\r\n\r\n    this.loadTable();\r\n  }\r\n\r\n  public clearAdvancedFilters(): void {\r\n    this.appliedFilters = {};\r\n    this.filter.filters = [];\r\n\r\n    // Set loading state immediately for clearing filters\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    this.loadTable();\r\n  }\r\n\r\n  public clearAllFilters(): void {\r\n    this.appliedFilters = {};\r\n    this.filter.filters = [];\r\n\r\n    // Set loading state immediately for clearing filters\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    this.loadTable();\r\n  }\r\n\r\n  // Sorting functionality\r\n  public onSortChange(sort: SortDescriptor[]): void {\r\n    // Check if this is the 3rd click (dir is undefined)\r\n    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;\r\n\r\n    if (isThirdClick) {\r\n      // 3rd click - clear sort and use default\r\n      this.sort = [];\r\n      this.page.orderBy = 'lastUpdatedDate';\r\n      this.page.orderDir = 'desc';\r\n    } else if (sort.length > 0 && sort[0] && sort[0].dir) {\r\n      // Valid sort with direction\r\n      this.sort = sort;\r\n      this.page.orderBy = sort[0].field || 'lastUpdatedDate';\r\n      this.page.orderDir = sort[0].dir;\r\n    } else {\r\n      // Empty sort array or invalid sort\r\n      this.sort = [];\r\n      this.page.orderBy = 'lastUpdatedDate';\r\n      this.page.orderDir = 'desc';\r\n    }\r\n\r\n    // Reset to first page\r\n    this.skip = 0;\r\n    this.page.pageNumber = 0;\r\n\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    setTimeout(() => this.loadTable(), 0);\r\n  }\r\n\r\n  // Pagination functionality\r\n  public pageChange(event: any): void {\r\n    // Use Kendo's provided values as source of truth\r\n    this.skip = event.skip;\r\n    this.page.size = event.take || this.page.size;\r\n    this.page.pageNumber = Math.floor(this.skip / this.page.size);\r\n\r\n    // Set loading state immediately for pagination\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    this.loadTable();\r\n  }\r\n\r\n\r\n\r\n  public updateColumnVisibility(event: any): void {\r\n    // Handle column visibility changes\r\n    const hiddenColumns = event.hiddenColumns || [];\r\n    this.hiddenFields = hiddenColumns;\r\n  }\r\n\r\n  public getHiddenField(fieldName: string): boolean {\r\n    return this.hiddenFields.includes(fieldName);\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  // Selection functionality\r\n  public onSelectionChange(event: any): void {\r\n    this.selectedRows = event.selectedRows || [];\r\n    this.isAllSelected =\r\n      this.selectedRows.length === this.serverSideRowData.length;\r\n  }\r\n\r\n  public selectAll(): void {\r\n    if (this.isAllSelected) {\r\n      this.selectedRows = [];\r\n      this.isAllSelected = false;\r\n    } else {\r\n      this.selectedRows = [...this.serverSideRowData];\r\n      this.isAllSelected = true;\r\n    }\r\n  }\r\n\r\n  // Grid expansion\r\n  public toggleExpand(): void {\r\n    // Find grid container element and toggle fullscreen class\r\n    const gridContainer = document.querySelector(\r\n      '.grid-container'\r\n    ) as HTMLElement;\r\n    if (gridContainer) {\r\n      gridContainer.classList.toggle('fullscreen-grid');\r\n      this.isExpanded = !this.isExpanded;\r\n      // Refresh grid after resize to ensure proper rendering\r\n      if (this.grid) {\r\n        this.grid.refresh();\r\n      }\r\n    }\r\n  }\r\n\r\n  // Export functionality\r\n  // public onExportClick(event: any): void {\r\n  //   const exportType = event.item.value;\r\n  //   let selectedIds: number[] = [];\r\n\r\n  //   switch (exportType) {\r\n  //     case 'selected':\r\n  //       selectedIds = this.selectedRows.map((row) => row.projectId);\r\n  //       if (selectedIds.length === 0) {\r\n  //         //alert('Please select projects to export');\r\n  //         return;\r\n  //       }\r\n  //       break;\r\n  //     case 'filtered':\r\n  //       // Export filtered data\r\n  //       break;\r\n  //     case 'all':\r\n  //     default:\r\n  //       // Export all data\r\n  //       break;\r\n  //   }\r\n\r\n  //   this.exportProjects(exportType, selectedIds);\r\n  // }\r\n\r\n  // private exportProjects(exportType: string, selectedIds: number[]): void {\r\n  //   this.projectsService.exportProjects(exportType, selectedIds).subscribe({\r\n  //     next: (response: any) => {\r\n  //       if (response.data) {\r\n  //         const blob = new Blob([response.data], {\r\n  //           type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n  //         });\r\n  //         saveAs(\r\n  //           blob,\r\n  //           `projects_${exportType}_${\r\n  //             new Date().toISOString().split('T')[0]\r\n  //           }.xlsx`\r\n  //         );\r\n  //       }\r\n  //     },\r\n  //     error: (error: any) => {\r\n  //       console.error('Export error:', error);\r\n  //       //alert('Error exporting projects data');\r\n  //     },\r\n  //   });\r\n  // }\r\n\r\n  onExportClick(event: any) {\r\n    const selectedOption = event.value; // Get selected option\r\n\r\n    let prdItems: any = [];\r\n    if (selectedOption === 'selected') {\r\n      prdItems = this.serverSideRowData;\r\n\r\n      // declare the title and header data for excel\r\n      // get the data for excel in a array format\r\n      this.exportExcel(prdItems);\r\n    } else if (selectedOption === 'all') {\r\n      const queryparamsExcel = {\r\n        pageSize: this.page.totalElements,\r\n        sortOrder: this.page.orderDir,\r\n        sortField: this.page.orderBy,\r\n        pageNumber: this.page.pageNumber,\r\n        // filter: this.filterConfiguration()\r\n      };\r\n\r\n      // Enable loading indicator\r\n      this.httpUtilService.loadingSubject.next(true);\r\n      // API call\r\n      this.projectsService\r\n        .getAllProjects(queryparamsExcel)\r\n        // .pipe(map((data: any) => data as any))\r\n        .subscribe((data) => {\r\n          // Disable loading indicator\r\n          this.httpUtilService.loadingSubject.next(false);\r\n          if (data.isFault) {\r\n            this.IsListHasValue = false;\r\n            this.cdr.markForCheck();\r\n            return; // Exit early if the response has a fault\r\n          }\r\n\r\n          this.IsListHasValue = true;\r\n          prdItems = data.responseData.data || [];\r\n\r\n          this.cdr.detectChanges(); // Manually trigger UI update\r\n          this.exportExcel(prdItems);\r\n        });\r\n    }\r\n  }\r\n\r\n  exportExcel(listOfItems: any): void {\r\n    // Define local variables for the items and current date\r\n    let prdItems: any = listOfItems;\r\n    let currentDate: Date = this.appService.formatMonthDate(new Date());\r\n\r\n    console.log('prdItems', prdItems);\r\n\r\n    // Check if the data exists and is not empty\r\n    if (prdItems !== undefined && prdItems.length > 0) {\r\n      // Define the title for the Excel file\r\n      const tableTitle = 'Events';\r\n\r\n      // Filter out hidden columns and sort by order\r\n      // const visibleColumns = this.columnJSONFormat\r\n      //   .filter((col: any) => !col.hidden)\r\n      //   .sort((a: any, b: any) => a.order - b.order);\r\n\r\n      // Create header from visible columns\r\n\r\n      const headerArray = [\r\n        'Project Name',\r\n        'Internal Projet #',\r\n        'Start Date',\r\n        'End Date',\r\n        'Location',\r\n        'Manager',\r\n        'External PM',\r\n      ];\r\n      // ...visibleColumns.map((col: any) => col.title),\r\n\r\n      // Define which columns should have currency and percentage formatting\r\n      // const currencyColumns: any = [\r\n      //   'Pending',\r\n      //   'ACAT',\r\n      //   'Annuity',\r\n      //   'AUM',\r\n      //   'Total Assets',\r\n      //   'Event Cost',\r\n      //   'Gross Profit',\r\n      // ].filter((col) => headerArray.includes(col));\r\n\r\n      const percentageColumns: any = [];\r\n\r\n      // Get the data for excel in an array format\r\n      const respResult: any = [];\r\n\r\n      // Prepare the data for export based on visible columns\r\n      each(prdItems, (prdItem: any) => {\r\n        // Create an array with the same length as headerArray\r\n        const respData = Array(headerArray.length).fill(null);\r\n        respData[0] = prdItem.eventDescription;\r\n        respData[1] = this.appService.formatMonthDate(prdItem.event_date);\r\n        // Fill in data for each visible column\r\n        headerArray.forEach((col: any, i: number) => {\r\n          const adjustedIndex = i; // +2 for 'Name' and 'Hot'\r\n          switch (col) {\r\n            case 'Project Name':\r\n              respData[adjustedIndex] = prdItem.projectName;\r\n              break;\r\n            case 'Internal Projet #':\r\n              respData[adjustedIndex] = prdItem.internalProjectNumber;\r\n              break;\r\n            case 'Start Date':\r\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.projectStartDate);\r\n              break;\r\n            case 'End Date':\r\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.projectEndDate);\r\n              break;\r\n            case 'Location':\r\n              respData[adjustedIndex] = prdItem.projectLocation;\r\n              break;\r\n            case 'Manager':\r\n              respData[adjustedIndex] = prdItem.internalProjectManagerName;\r\n              break;\r\n            case 'External PM':\r\n              respData[adjustedIndex] = prdItem.externalPMNames;\r\n              break;\r\n            // case 'kept_appointments':\r\n            //   respData[adjustedIndex] = prdItem.kept_appointments;\r\n            //   break;\r\n            // case 'kept_appt_ratio':\r\n            //   respData[adjustedIndex] = prdItem.kept_appt_ratio;\r\n            //   break;\r\n            // case 'apptKeptNo':\r\n            //   respData[adjustedIndex] = prdItem.apptKeptNo;\r\n            //   break;\r\n            // case 'has_assets':\r\n            //   respData[adjustedIndex] = prdItem.has_assets;\r\n            //   break;\r\n            // case 'prospects_closed':\r\n            //   respData[adjustedIndex] = prdItem.prospects_closed;\r\n            //   break;\r\n            // case 'closing_ratio':\r\n            //   respData[adjustedIndex] = prdItem.closing_ratio;\r\n            //   break;\r\n            // case 'totalPending':\r\n            //   respData[adjustedIndex] = prdItem.totalPending;\r\n            //   break;\r\n            // case 'acatproduction':\r\n            //   respData[adjustedIndex] = prdItem.acatproduction;\r\n            //   break;\r\n            // case 'annuityproduction':\r\n            //   respData[adjustedIndex] = prdItem.annuityproduction;\r\n            //   break;\r\n            // case 'aumproduction':\r\n            //   respData[adjustedIndex] = prdItem.aumproduction;\r\n            //   break;\r\n            // case 'totalAssets':\r\n            //   respData[adjustedIndex] = prdItem.totalAssets;\r\n            //   break;\r\n            // case 'eventCost':\r\n            //   respData[adjustedIndex] = prdItem.eventCost;\r\n            //   break;\r\n            // case 'grossProfit':\r\n            //   respData[adjustedIndex] = prdItem.grossProfit;\r\n            //   break;\r\n            // case 'status':\r\n            //   respData[adjustedIndex] = prdItem.status;\r\n            //   break;\r\n          }\r\n        });\r\n\r\n        respResult.push(respData);\r\n      });\r\n\r\n      // Define column sizes for the Excel file\r\n      const colSize = headerArray.map((header, index) => ({\r\n        id: index + 1,\r\n        width: 20,\r\n      }));\r\n\r\n      // Generate the Excel file using the exceljsService\r\n      this.execeljsservice.generateExcel(\r\n        tableTitle,\r\n        headerArray,\r\n        respResult,\r\n        colSize\r\n        // currencyColumns,\r\n        // percentageColumns\r\n      );\r\n    } else {\r\n      const message = 'There are no records available to export.';\r\n      // this.layoutUtilService.showError(message, '');\r\n    }\r\n  }\r\n  // Column settings management\r\n  public saveHead(): void {\r\n    const settings = {\r\n      kendoHide: this.hiddenFields,\r\n      kendoColOrder: this.kendoColOrder,\r\n      kendoInitColOrder: this.kendoInitColOrder,\r\n    };\r\n\r\n    // Save to local storage only\r\n    this.kendoColumnService.saveToLocalStorage({\r\n      pageName: 'projects',\r\n      userID: this.loginUser.userId,\r\n      hiddenData: settings.kendoHide,\r\n      kendoColOrder: settings.kendoColOrder,\r\n      LoggedId: this.loginUser.userId\r\n    });\r\n\r\n    console.log('Column settings saved locally:', settings);\r\n                            this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');\r\n\r\n    //alert('Column settings saved locally');\r\n  }\r\n\r\n  private saveColumnSettingsToServer(settings: any): void {\r\n    const config = {\r\n      pageName: 'projects',\r\n      userID: this.loginUser.userId,\r\n      hiddenData: settings.kendoHide,\r\n      kendoColOrder: settings.kendoColOrder,\r\n      LoggedId: this.loginUser.userId\r\n    };\r\n\r\n    this.kendoColumnService.createHideFields(config).subscribe({\r\n      next: (response) => {\r\n        if (response.isFault === false) {\r\n          console.log('Column settings saved successfully:', response);\r\n\r\n                            this.customLayoutUtilsService.showSuccess('Column settings saved successfully', '');\r\n          //alert('Column settings saved successfully');\r\n        } else {\r\n          console.error('Failed to save column settings:', response.message);\r\n                            this.customLayoutUtilsService.showError('Column settings failed to save', '');\r\n\r\n          //alert('Failed to save column settings: ' + response.message);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error saving column settings:', error);\r\n                            this.customLayoutUtilsService.showError('Error saving column settings', '');\r\n\r\n        //alert('Error saving column settings. Please try again.');\r\n      }\r\n    });\r\n  }\r\n\r\n  private saveResetToServer(): void {\r\n    // First delete existing settings\r\n    const deleteConfig = {\r\n      pageName: 'projects',\r\n      userID: this.loginUser.userId\r\n    };\r\n\r\n    this.kendoColumnService.deleteHideFields(deleteConfig).subscribe({\r\n      next: (response) => {\r\n        console.log('Existing settings deleted:', response);\r\n        // Then save the reset state (all columns visible)\r\n        this.saveColumnSettingsToServer({\r\n          kendoHide: [],\r\n          kendoColOrder: this.defaultColumns,\r\n          kendoInitColOrder: this.defaultColumns\r\n        });\r\n      },\r\n      error: (error) => {\r\n        console.error('Error deleting existing settings:', error);\r\n        // Still try to save the reset state\r\n        this.saveColumnSettingsToServer({\r\n          kendoHide: [],\r\n          kendoColOrder: this.defaultColumns,\r\n          kendoInitColOrder: this.defaultColumns\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  public resetTable(): void {\r\n    console.log('Resetting Kendo settings for projects');\r\n\r\n    // Clear all saved settings first\r\n    this.kendoHide = [];\r\n    this.hiddenData = [];\r\n    this.kendoColOrder = [];\r\n    this.kendoInitColOrder = [];\r\n\r\n    // Clear local storage\r\n    this.kendoColumnService.clearFromLocalStorage('projects');\r\n\r\n    // Reset to default settings\r\n    this.resetToDefaultSettings();\r\n\r\n    // Trigger change detection to update the template\r\n    this.cdr.detectChanges();\r\n\r\n    // Force grid refresh to show all columns\r\n    if (this.grid) {\r\n      this.grid.refresh();\r\n    }\r\n\r\n    // Show success message\r\n    console.log('Table reset to default settings');\r\n\r\n    //alert('Table reset to default settings - all columns restored');\r\n  }\r\n  private resetToDefaultSettings(): void {\r\n    console.log('Resetting to default settings...');\r\n\r\n    // Reset column visibility - show all columns\r\n    this.hiddenFields = [];\r\n    this.gridColumns = [...this.defaultColumns];\r\n    this.kendoColOrder = [...this.defaultColumns];\r\n\r\n    // Reset sort state to default\r\n    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n    this.page.orderBy = 'lastUpdatedDate';\r\n    this.page.orderDir = 'desc';\r\n\r\n    // Reset page state\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n\r\n    // Reset all filters - clear everything\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.activeFilters = [];\r\n\r\n    // Reset advanced filters\r\n    this.appliedFilters = {};\r\n\r\n    // Reset search\r\n    this.searchData = '';\r\n\r\n    // Reset advanced filters visibility\r\n    this.showAdvancedFilters = false;\r\n\r\n    console.log('Reset completed:', {\r\n      hiddenFields: this.hiddenFields,\r\n      gridColumns: this.gridColumns,\r\n      defaultColumns: this.defaultColumns,\r\n      sort: this.sort,\r\n      filter: this.filter,\r\n      searchData: this.searchData\r\n    });\r\n\r\n    // Reset the Kendo Grid's internal state\r\n    if (this.grid) {\r\n      // Clear all filters\r\n      this.grid.filter = { logic: 'and', filters: [] };\r\n\r\n      // Reset sorting\r\n      this.grid.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n\r\n      // Reset column visibility - show all columns\r\n      this.grid.columns.forEach((column: any) => {\r\n        if (column.field && column.field !== 'action') {\r\n          column.hidden = false;\r\n        }\r\n      });\r\n\r\n      // Reset to first page\r\n      this.grid.skip = 0;\r\n      this.grid.pageSize = this.page.size;\r\n    }\r\n\r\n    // Trigger change detection\r\n    this.cdr.detectChanges();\r\n\r\n    // Force grid refresh to apply all changes\r\n    if (this.grid) {\r\n      setTimeout(() => {\r\n        this.grid.refresh();\r\n        // Also try to reset the grid state completely\r\n        this.grid.reset();\r\n      }, 100);\r\n    }\r\n\r\n    // Reload data with clean state\r\n    this.loadTable();\r\n  }\r\n  // Navigation\r\n  public add(): void {\r\n    this.edit(0);\r\n  }\r\n\r\n  public view(projectId: number): void {\r\n    this.router.navigate(['/projects/view', projectId]);\r\n  }\r\n\r\n  public edit(projectId: number): void {\r\n    if (projectId == 0) {\r\n      // Trigger global loader BEFORE opening modal to ensure full-page overlay\r\n      this.httpUtilService.loadingSubject.next(true);\r\n\r\n      // Open modal for new project\r\n      const modalRef = this.modalService.open(ProjectPopupComponent, {\r\n        size: 'lg',\r\n        centered: true,\r\n        backdrop: 'static'\r\n      });\r\n\r\n      modalRef.componentInstance.id = projectId;\r\n      modalRef.componentInstance.project = null;\r\n\r\n      modalRef.componentInstance.passEntry.subscribe((result: boolean) => {\r\n        if (result) {\r\n          // Refresh the grid after successful add\r\n          this.loadTable();\r\n        }\r\n      });\r\n    } else {\r\n      // Navigate to project view for existing projects\r\n      this.router.navigate(['/projects/view', projectId]);\r\n    }\r\n  }\r\n\r\n  public delete(projectId: number): void {\r\n    // if (confirm('Are you sure you want to delete this project?')) {\r\n\r\n    // }\r\n     this.projectsService.deleteProject({ projectId }).subscribe({\r\n        next: (response: any) => {\r\n\r\n          console.log(\"response\",response)\r\n          if (!response.isFault) {\r\n                            this.customLayoutUtilsService.showSuccess('Project deleted successfully', '');\r\n\r\n            //alert('Project deleted successfully');\r\n            this.loadTable();\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Delete error:', error);\r\n                            this.customLayoutUtilsService.showError('error deleting project', '');\r\n\r\n          //alert('Error deleting project');\r\n        },\r\n      });\r\n  }\r\n\r\n  // Utility methods\r\n  public getProjectFullName(project: any): string {\r\n    return `${project.projectFirstName || ''} ${\r\n      project.projectLastName || ''\r\n    }`.trim();\r\n  }\r\n\r\n  public getCenterName(project: any): string {\r\n    return project.medicalCenter?.centerName || '';\r\n  }\r\n\r\n  public formatDate(dateString: string): string {\r\n    if (!dateString) return '';\r\n    const date = new Date(dateString);\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    const year = date.getFullYear();\r\n    return `${month}/${day}/${year}`;\r\n  }\r\n\r\n  public getStatusClass(status: string): string {\r\n    if (!status) return 'badge-light-secondary';\r\n    const key = status.toLowerCase();\r\n    if (key === 'current') return 'badge-light-success';\r\n    if (key === 'completed') return 'badge-light-primary';\r\n    if (key === 'cancelled & archived') return 'badge-light-dark';\r\n    if (key === 'closed & archived') return 'badge-light-info';\r\n    return 'badge-light-secondary';\r\n  }\r\n  deletePop(content: any, projectId: any, projectName: any, dataItem: any) {\r\n    this.projectName = projectName;\r\n    this.projectId = projectId;\r\n    let modalRef = this.modalService.open(ConfirmationDialogComponent, {\r\n        size: 'nm',\r\n        centered: true,\r\n        backdrop: 'static'\r\n      });\r\n    if (this.loginUser.roleName === 'Admin') {\r\n      if (dataItem.isDeletable) {\r\n        // Open modal for new projec\r\n        modalRef.componentInstance.showClose = true;\r\n        modalRef.componentInstance.description = \"Are you sure you want to delete this project - \" + projectName + \"?\";\r\n        modalRef.componentInstance.cancelButtonText = 'Cancel';\r\n        modalRef.componentInstance.actionButtonText = 'Yes';\r\n        modalRef.componentInstance.title = 'Confirm Delete';\r\n        modalRef.componentInstance.passEntry.subscribe((result: any) => {\r\n          if (result.success === true) {\r\n            // Refresh the grid after successful add\r\n            this.delete(this.projectId);\r\n          }\r\n        });\r\n      } else {\r\n        modalRef.componentInstance.showClose = false;\r\n        modalRef.componentInstance.description = \"This project (\" + projectName + \") has permits. Please delete the permits first before deleting the project.\";\r\n        modalRef.componentInstance.cancelButtonText = 'Cancel';\r\n        modalRef.componentInstance.actionButtonText = 'Ok';\r\n        modalRef.componentInstance.title = 'Warning';\r\n        modalRef.componentInstance.passEntry.subscribe((result: any) => {\r\n        });\r\n      }\r\n    } else if (this.loginUser.roleName === 'Internal PM') {\r\n      if (dataItem.isDeletable) {\r\n        modalRef.componentInstance.showClose = true;\r\n        modalRef.componentInstance.description = \"Are you sure you want to delete this project - \" + projectName + \"?\";\r\n        modalRef.componentInstance.cancelButtonText = 'Cancel';\r\n        modalRef.componentInstance.actionButtonText = 'Yes';\r\n        modalRef.componentInstance.title = 'Confirm Delete';\r\n        modalRef.componentInstance.passEntry.subscribe((result: any) => {\r\n          if (result.success === true) {\r\n            // Refresh the grid after successful add\r\n            this.delete(this.projectId);\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n\r\n  }\r\n\r\n  confirmDelete() {\r\n    // console.log('Item deleted ✅');\r\n    this.delete(this.projectId);\r\n    // your delete logic here\r\n  }\r\n\r\n  onTabActivated() {\r\n    // This method is called when the tab is activated\r\n    // You can add any specific logic here if needed\r\n    console.log('Projects tab activated');\r\n  }\r\n\r\n  // Custom dropdown methods\r\n  toggleExcelDropdown(event?: Event): void {\r\n    this.isExcelDropdownOpen = !this.isExcelDropdownOpen;\r\n    console.log('Excel dropdown toggled:', this.isExcelDropdownOpen);\r\n\r\n    if (this.isExcelDropdownOpen && event) {\r\n      const button = event.target as HTMLElement;\r\n      const rect = button.getBoundingClientRect();\r\n      this.dropdownTop = rect.bottom + window.scrollY;\r\n      this.dropdownLeft = rect.left + window.scrollX;\r\n      console.log('Dropdown position:', this.dropdownTop, this.dropdownLeft);\r\n    }\r\n  }\r\n\r\n  closeExcelDropdown(): void {\r\n    this.isExcelDropdownOpen = false;\r\n    console.log('Excel dropdown closed');\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event): void {\r\n    const target = event.target as HTMLElement;\r\n    const dropdown = target.closest('.custom-dropdown');\r\n    if (!dropdown && this.isExcelDropdownOpen) {\r\n      this.closeExcelDropdown();\r\n    }\r\n  }\r\n}\r\n", "<!-- Full Screen Loading Overlay -->\r\n<div *ngIf=\"loading || isLoading\" class=\"fullscreen-loading-overlay\">\r\n  <div class=\"loading-content\">\r\n    <div class=\"custom-colored-spinner\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <div class=\"mt-4 fs-5\">Loading...</div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"grid-container\">\r\n  <kendo-grid\r\n    #normalGrid\r\n    [data]=\"gridData\"\r\n    [pageSize]=\"page.size\"\r\n    [sort]=\"sort\"\r\n    [pageable]=\"{\r\n      pageSizes: [15, 20, 50, 100],\r\n      previousNext: true,\r\n      info: true,\r\n      type: 'numeric',\r\n      buttonCount: 5\r\n    }\"\r\n    [total]=\"page.totalElements\"\r\n    [sortable]=\"{ allowUnsort: true, mode: 'single' }\"\r\n    [groupable]=\"false\"\r\n    [selectable]=\"{ checkboxOnly: true, mode: 'multiple' }\"\r\n    (selectionChange)=\"onSelectionChange($event)\"\r\n    [reorderable]=\"true\"\r\n    style=\"width: auto; overflow-x: auto\"\r\n    [resizable]=\"false\"\r\n    [height]=\"720\"\r\n    [skip]=\"skip\"\r\n    [filter]=\"filter\"\r\n    [columnMenu]=\"{ filter: true }\"\r\n    (filterChange)=\"filterChange($event)\"\r\n    (pageChange)=\"pageChange($event)\"\r\n    (sortChange)=\"onSortChange($event)\"\r\n    (columnVisibilityChange)=\"updateColumnVisibility($event)\"\r\n  >\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <!-- Search Section -->\r\n      <div class=\"d-flex align-items-center me-3 search-section\">\r\n        <kendo-textbox\r\n          [style.width.px]=\"500\"\r\n          placeholder=\"Search...\"\r\n          [(ngModel)]=\"searchData\"\r\n          [clearButton]=\"true\"\r\n          (keydown)=\"onSearchKeyDown($event)\"\r\n          (blur)=\"onSearchBlur()\"\r\n          (valueChange)=\"onSearchValueChange()\"\r\n        ></kendo-textbox>\r\n      </div>\r\n\r\n      <kendo-grid-spacer></kendo-grid-spacer>\r\n\r\n      <!-- Total Count - Repositioned to the right -->\r\n      <div class=\"d-flex align-items-center me-3\">\r\n        <span class=\"text-muted\">Total: </span>\r\n        <span class=\"fw-bold ms-1\">{{ page.totalElements || 0 }}</span>\r\n      </div>\r\n\r\n      <!-- Action Buttons -->\r\n      <button type=\"button\" class=\"btn btn-success btn-sm toolbar-btn\" (click)=\"add()\" title=\"Add Project\">\r\n        <i class=\"fas fa-plus text-white\"></i>Add\r\n      </button>\r\n\r\n      <button\r\n      type=\"button\"\r\n      class=\"btn btn-icon btn-sm toolbar-btn\"\r\n      (click)=\"toggleExpand()\"\r\n      title=\"Toggle Grid Expansion\"\r\n    >\r\n      <i\r\n        class=\"fas text-secondary\"\r\n        [class.fa-expand]=\"!isExpanded\"\r\n        [class.fa-compress]=\"isExpanded\"\r\n      ></i>\r\n    </button>\r\n\r\n     <!-- Excel Export Dropdown -->\r\n     <div class=\"custom-dropdown toolbar-btn\" [class.show]=\"isExcelDropdownOpen\" #excelDropdown>\r\n       <button class=\"btn btn-icon btn-sm\" type=\"button\" (click)=\"toggleExcelDropdown($event)\" title=\"Export Excel\" #excelButton>\r\n         <i class=\"fas fa-file-excel text-success\"></i>\r\n       </button>\r\n       <div class=\"custom-dropdown-menu\" *ngIf=\"isExcelDropdownOpen\" [style.top.px]=\"dropdownTop\" [style.left.px]=\"dropdownLeft\">\r\n         <a class=\"custom-dropdown-item\" href=\"#\" (click)=\"onExportClick({value: 'all'}); closeExcelDropdown(); $event.preventDefault()\">All</a>\r\n         <a class=\"custom-dropdown-item\" href=\"#\" (click)=\"onExportClick({value: 'selected'}); closeExcelDropdown(); $event.preventDefault()\">Page Results</a>\r\n       </div>\r\n     </div>\r\n\r\n\r\n      <!-- Save Column Settings Button -->\r\n      <!-- <button\r\n        type=\"button\"\r\n        class=\"btn btn-icon btn-sm me-2\"\r\n        (click)=\"saveHead()\"\r\n        title=\"Save Column Settings\"\r\n      >\r\n        <i class=\"fas fa-save text-success\"></i>\r\n      </button> -->\r\n\r\n      <!-- Reset Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-icon btn-sm toolbar-btn\"\r\n        (click)=\"resetTable()\"\r\n        title=\"Reset to Default\"\r\n      >\r\n        <i class=\"fas fa-undo text-warning\"></i>\r\n      </button>\r\n\r\n      <!-- Refresh Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-icon btn-sm toolbar-btn\"\r\n        (click)=\"refreshGrid()\"\r\n        title=\"Refresh Grid Data\"\r\n      >\r\n        <i class=\"fas fa-sync-alt text-info\"></i>\r\n      </button>\r\n    </ng-template>\r\n\r\n    <!-- Advanced Filters Panel -->\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <div\r\n        *ngIf=\"showAdvancedFilters\"\r\n        class=\"advanced-filters-panel p-3 bg-light border-bottom\"\r\n      >\r\n        <div class=\"row\">\r\n          <div class=\"col-md-3\">\r\n            <label class=\"form-label\">Status</label>\r\n\r\n            <kendo-dropdownlist\r\n              [data]=\"advancedFilterOptions.status\"\r\n              [(ngModel)]=\"appliedFilters.status\"\r\n              textField=\"text\"\r\n              valueField=\"value\"\r\n              placeholder=\"Select Status\"\r\n            >\r\n            </kendo-dropdownlist>\r\n          </div>\r\n          <div class=\"col-md-3 d-flex align-items-end\">\r\n            <button\r\n              kendoButton\r\n              (click)=\"applyAdvancedFilters()\"\r\n              class=\"btn-primary me-2\"\r\n            >\r\n              <i class=\"fas fa-check\"></i> Apply Filters\r\n            </button>\r\n            <button\r\n              kendoButton\r\n              (click)=\"clearAllFilters()\"\r\n              class=\"btn-secondary\"\r\n            >\r\n              <i class=\"fas fa-times\"></i> Clear\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n    <ng-container *ngFor=\"let column of gridColumns\">\r\n      <!-- Action Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'action'\"\r\n        title=\"Actions\"\r\n        [width]=\"90\"\r\n        [includeInChooser]=\"false\"\r\n        [columnMenu]=\"false\"\r\n        [hidden]=\"getHiddenField('action')\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          <div class=\"d-flex align-items-center justify-content-center gap-1\" style=\"min-height: 32px;\">\r\n            <!-- <a\r\n              title=\"View\"\r\n              class=\"btn btn-icon btn-sm\"\r\n              (click)=\"view(dataItem.patientId)\"\r\n            >\r\n              <span\r\n                [inlineSVG]=\"'./assets/media/icons/duotune/general/gen019.svg'\"\r\n                class=\"svg-icon svg-icon-3 svg-icon-primary\"\r\n              >\r\n              </span>\r\n            </a> -->\r\n            <!-- View icon - always on the left -->\r\n            <a\r\n              title=\"View\"\r\n              class=\"btn btn-icon btn-sm d-flex align-items-center justify-content-center\"\r\n              style=\"width: 32px; height: 32px;\"\r\n              (click)=\"edit(dataItem.projectId)\"\r\n            >\r\n              <i class=\"fas fa-edit text-primary\"></i>\r\n            </a>\r\n            <!-- Delete icon - always on the right, with consistent spacing -->\r\n            <a *ngIf=\"loginUser.roleName ==='Admin'\" title=\"Delete\"\r\n              class=\"btn btn-icon  btn-sm d-flex align-items-center justify-content-center\" style=\"width: 32px; height: 32px;\"\r\n              (click)=\"deletePop(deleteModal, dataItem.projectId, dataItem.projectName,dataItem)\">\r\n               <i class=\"fas fa-trash text-danger\"></i>\r\n              <!-- [class.invisible]=\"!dataItem.isDeletable\"\r\n                          [class.disabled]=\"!dataItem.isDeletable\"\r\n\r\n                          (click)=\"dataItem.isDeletable && deletePop(deleteModal, dataItem.projectId, dataItem.projectName)\" -->\r\n              <!-- <span [inlineSVG]=\"'./assets/media/icons/duotune/general/gen027.svg'\" class=\"svg-icon svg-icon-3 svg-icon-danger\">\r\n              </span> -->\r\n            </a>\r\n\r\n            <!-- Delete icon - always on the right, with consistent spacing -->\r\n            <a *ngIf=\"loginUser.roleName ==='Internal PM'\" title=\"Delete\"\r\n              class=\"btn btn-icon  btn-sm d-flex align-items-center justify-content-center\" style=\"width: 32px; height: 32px;\"\r\n              [class.invisible]=\"!dataItem.isDeletable && loginUser.userId === dataItem.createdBy\" [class.disabled]=\"!dataItem.isDeletable && loginUser.userId === dataItem.createdBy\"\r\n              (click)=\"deletePop(deleteModal, dataItem.projectId, dataItem.projectName,dataItem)\">\r\n                <i class=\"fas fa-trash text-danger\"></i>\r\n              <!-- <span [inlineSVG]=\"'./assets/media/icons/duotune/general/gen027.svg'\" class=\"svg-icon svg-icon-3 svg-icon-danger\">\r\n              </span> -->\r\n            </a>\r\n          </div>\r\n\r\n          <!-- <div class=\"d-flex gap-1\">\r\n          <button class=\"btn btn-sm btn-light-primary\" (click)=\"view(dataItem.patientId)\" title=\"View\">\r\n            <i class=\"fas fa-eye\"></i>\r\n          </button>\r\n          <button class=\"btn btn-sm btn-light-warning\" (click)=\"edit(dataItem.patientId)\" title=\"Edit\">\r\n            <i class=\"fas fa-edit\"></i>\r\n          </button>\r\n          <button class=\"btn btn-sm btn-light-danger\" (click)=\"delete(dataItem.patientId)\" title=\"Delete\">\r\n            <i class=\"fas fa-trash\"></i>\r\n          </button>\r\n        </div> -->\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n      <!-- Project Name Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'projectName'\"\r\n        field=\"projectName\"\r\n        title=\"Project Name\"\r\n        [width]=\"200\"\r\n        [includeInChooser]=\"false\"\r\n        [hidden]=\"getHiddenField('projectName')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n           <span class=\"fw-bold\">{{ dataItem.projectName | truncateText:45 }}</span>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n      <!-- Internal Project Number Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'internalProjectNumber'\"\r\n        field=\"internalProjectNumber\"\r\n        title=\"Internal Project #\"\r\n        [width]=\"150\"\r\n        [includeInChooser]=\"false\"\r\n        [hidden]=\"getHiddenField('internalProjectNumber')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n           <span class=\"fw-bold\">{{ dataItem.internalProjectNumber | truncateText:45 }}</span>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Project Start Date Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'projectStartDate'\"\r\n        field=\"projectStartDate\"\r\n        title=\"Start Date\"\r\n        [width]=\"120\"\r\n\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('projectStartDate')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          {{ formatDate(dataItem.projectStartDate) }}\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"true\"\r\n          >\r\n            <kendo-filter-gte-operator></kendo-filter-gte-operator>\r\n            <kendo-filter-lte-operator></kendo-filter-lte-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n      <!-- Project End Date Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'projectEndDate'\"\r\n        field=\"projectEndDate\"\r\n        title=\"End Date\"\r\n        [width]=\"120\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('projectEndDate')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n         {{ formatDate(dataItem.projectEndDate) }}\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"true\"\r\n          >\r\n            <kendo-filter-gte-operator></kendo-filter-gte-operator>\r\n            <kendo-filter-lte-operator></kendo-filter-lte-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Project Location Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'projectLocation'\"\r\n        field=\"projectLocation\"\r\n        title=\"Location\"\r\n        [width]=\"180\"\r\n\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('projectLocation')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          {{ dataItem.projectLocation | truncateText:45 }}\r\n          <!-- <div>\r\n            <span class=\"fw-bolder\">\r\n\r\n            </span>\r\n          </div> -->\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Internal Project Manager Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'internalProjectManagerName'\"\r\n        field=\"internalProjectManagerName\"\r\n        title=\"Manager\"\r\n        [width]=\"180\"\r\n\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('internalProjectManagerName')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          {{ dataItem.internalProjectManagerName | truncateText:45 }}\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- External PM Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'externalPMNames'\"\r\n        field=\"externalPMNames\"\r\n        title=\"External PM\"\r\n        [width]=\"220\"\r\n\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('externalPMNames')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n       {{ dataItem.externalPMNames | truncateText:45 }}\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Last Updated Date Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'lastUpdatedDate'\"\r\n        field=\"lastUpdatedDate\"\r\n        title=\"Updated Date\"\r\n        [width]=\"120\"\r\n\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('lastUpdatedDate')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n           {{ formatDate(dataItem.lastUpdatedDate) }}\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"true\"\r\n          >\r\n            <kendo-filter-gte-operator></kendo-filter-gte-operator>\r\n            <kendo-filter-lte-operator></kendo-filter-lte-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n    </ng-container>\r\n\r\n    <!-- No Data Template -->\r\n    <ng-template kendoGridNoRecordsTemplate>\r\n      <div class=\"custom-no-records\" *ngIf=\"!loading && !isLoading\">\r\n        <div class=\"text-center\">\r\n          <p class=\"text-muted\">No data found</p>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n  </kendo-grid>\r\n</div>\r\n\r\n<!-- Delete Confirmation Modal -->\r\n<ng-template #deleteModal let-modal>\r\n  <div class=\"modal-header bg-danger text-white\">\r\n    <h5 class=\"modal-title\">Confirm Delete</h5>\r\n    <button\r\n      type=\"button\"\r\n      class=\"btn-close\"\r\n      aria-label=\"Close\"\r\n      (click)=\"modal.dismiss()\"\r\n    ></button>\r\n  </div>\r\n\r\n  <div class=\"delete-modal-body mt-4 text-center\">\r\n    <p class=\"fs-5\">\r\n      Are you sure you want to delete this project? - {{ this.projectName }}\r\n    </p>\r\n  </div>\r\n\r\n  <div class=\"modal-footer delete-modal-footer ms-2\">\r\n    <button type=\"button\" class=\"btn btn-danger\" (click)=\"modal.dismiss()\">\r\n      Cancel\r\n    </button>\r\n    <button\r\n      type=\"button\"\r\n      class=\"btn btn-primary \"\r\n      (click)=\"confirmDelete(); modal.close()\"\r\n    >\r\n      Delete\r\n    </button>\r\n  </div>\r\n</ng-template>\r\n\r\n<!-- Delete Confirmation Modal -->\r\n<ng-template #permitWarningModal let-modal>\r\n  <div class=\"modal-header bg-danger text-white\">\r\n    <h5 class=\"modal-title\">Warning</h5>\r\n    <button\r\n      type=\"button\"\r\n      class=\"btn-close\"\r\n      aria-label=\"Close\"\r\n      (click)=\"modal.dismiss()\"\r\n    ></button>\r\n  </div>\r\n\r\n  <div class=\"delete-modal-body mt-4 text-center\">\r\n    <p class=\"fs-5\">\r\n      This project ({{this.projectName }}) has permits. Please delete the permits first before deleting the project.\r\n    </p>\r\n  </div>\r\n\r\n  <div class=\"modal-footer delete-modal-footer ms-2\">\r\n\r\n    <button\r\n      type=\"button\"\r\n      class=\"btn btn-primary \"\r\n      (click)=\"modal.close()\"\r\n    >\r\n      Ok\r\n    </button>\r\n  </div>\r\n</ng-template>\r\n"], "mappings": "AAcA,SAAcA,IAAI,QAAQ,QAAQ;AAkBlC,SAASC,qBAAqB,QAAQ,0CAA0C;AAEhF,SAASC,2BAA2B,QAAQ,gEAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC9BtGC,EAHN,CAAAC,cAAA,cAAqE,cACtC,cACuB,eAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAErCF,EAFqC,CAAAG,YAAA,EAAM,EACnC,EACF;;;;;;IA8EGH,EADF,CAAAC,cAAA,cAA0H,YACQ;IAAvFD,EAAA,CAAAI,UAAA,mBAAAC,sEAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAAE,aAAA,CAAc;QAAAC,KAAA,EAAQ;MAAK,CAAC,CAAC;MAAEH,MAAA,CAAAI,kBAAA,EAAoB;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAER,MAAA,CAAAS,cAAA,EAAuB;IAAA,EAAC;IAACf,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvIH,EAAA,CAAAC,cAAA,YAAqI;IAA5FD,EAAA,CAAAI,UAAA,mBAAAY,sEAAAV,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAAE,aAAA,CAAc;QAAAC,KAAA,EAAQ;MAAU,CAAC,CAAC;MAAEH,MAAA,CAAAI,kBAAA,EAAoB;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAER,MAAA,CAAAS,cAAA,EAAuB;IAAA,EAAC;IAACf,EAAA,CAAAE,MAAA,mBAAY;IACnJF,EADmJ,CAAAG,YAAA,EAAI,EACjJ;;;;IAHqFH,EAA7B,CAAAiB,WAAA,QAAAR,MAAA,CAAAS,WAAA,OAA4B,SAAAT,MAAA,CAAAU,YAAA,OAA+B;;;;;;IA1CxHnB,EADF,CAAAC,cAAA,cAA2D,wBASxD;IALCD,EAAA,CAAAoB,gBAAA,2BAAAC,mFAAAf,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAAe,UAAA,EAAAlB,MAAA,MAAAG,MAAA,CAAAe,UAAA,GAAAlB,MAAA;MAAA,OAAAN,EAAA,CAAAc,WAAA,CAAAR,MAAA;IAAA,EAAwB;IAIxBN,EAFA,CAAAI,UAAA,qBAAAqB,6EAAAnB,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAAWL,MAAA,CAAAiB,eAAA,CAAApB,MAAA,CAAuB;IAAA,EAAC,kBAAAqB,0EAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAC3BL,MAAA,CAAAmB,YAAA,EAAc;IAAA,EAAC,yBAAAC,iFAAA;MAAA7B,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CACRL,MAAA,CAAAqB,mBAAA,EAAqB;IAAA,EAAC;IAEzC9B,EADG,CAAAG,YAAA,EAAgB,EACb;IAENH,EAAA,CAAA+B,SAAA,wBAAuC;IAIrC/B,EADF,CAAAC,cAAA,cAA4C,eACjB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAGNH,EAAA,CAAAC,cAAA,iBAAqG;IAApCD,EAAA,CAAAI,UAAA,mBAAA4B,oEAAA;MAAAhC,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAAwB,GAAA,EAAK;IAAA,EAAC;IAC9EjC,EAAA,CAAA+B,SAAA,YAAsC;IAAA/B,EAAA,CAAAE,MAAA,YACxC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAKD;IAFCD,EAAA,CAAAI,UAAA,mBAAA8B,qEAAA;MAAAlC,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAA0B,YAAA,EAAc;IAAA,EAAC;IAGxBnC,EAAA,CAAA+B,SAAA,aAIK;IACP/B,EAAA,CAAAG,YAAA,EAAS;IAINH,EADF,CAAAC,cAAA,kBAA2F,qBACiC;IAAxED,EAAA,CAAAI,UAAA,mBAAAgC,qEAAA9B,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAA4B,mBAAA,CAAA/B,MAAA,CAA2B;IAAA,EAAC;IACrFN,EAAA,CAAA+B,SAAA,aAA8C;IAChD/B,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAsC,UAAA,KAAAC,kDAAA,kBAA0H;IAI5HvC,EAAA,CAAAG,YAAA,EAAM;IAcLH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAI,UAAA,mBAAAoC,qEAAA;MAAAxC,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAAgC,UAAA,EAAY;IAAA,EAAC;IAGtBzC,EAAA,CAAA+B,SAAA,aAAwC;IAC1C/B,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAI,UAAA,mBAAAsC,qEAAA;MAAA1C,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAAkC,WAAA,EAAa;IAAA,EAAC;IAGvB3C,EAAA,CAAA+B,SAAA,aAAyC;IAC3C/B,EAAA,CAAAG,YAAA,EAAS;;;;IA5ELH,EAAA,CAAA4C,SAAA,EAAsB;IAAtB5C,EAAA,CAAAiB,WAAA,oBAAsB;IAEtBjB,EAAA,CAAA6C,gBAAA,YAAApC,MAAA,CAAAe,UAAA,CAAwB;IACxBxB,EAAA,CAAA8C,UAAA,qBAAoB;IAYK9C,EAAA,CAAA4C,SAAA,GAA6B;IAA7B5C,EAAA,CAAA+C,iBAAA,CAAAtC,MAAA,CAAAuC,IAAA,CAAAC,aAAA,MAA6B;IAgBxDjD,EAAA,CAAA4C,SAAA,GAA+B;IAC/B5C,EADA,CAAAkD,WAAA,eAAAzC,MAAA,CAAA0C,UAAA,CAA+B,gBAAA1C,MAAA,CAAA0C,UAAA,CACC;IAKMnD,EAAA,CAAA4C,SAAA,EAAkC;IAAlC5C,EAAA,CAAAkD,WAAA,SAAAzC,MAAA,CAAA2C,mBAAA,CAAkC;IAItCpD,EAAA,CAAA4C,SAAA,GAAyB;IAAzB5C,EAAA,CAAA8C,UAAA,SAAArC,MAAA,CAAA2C,mBAAA,CAAyB;;;;;;IA8CvDpD,EANN,CAAAC,cAAA,cAGC,cACkB,cACO,gBACM;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAExCH,EAAA,CAAAC,cAAA,6BAMC;IAJCD,EAAA,CAAAoB,gBAAA,2BAAAiC,8FAAA/C,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAA8C,cAAA,CAAAC,MAAA,EAAAlD,MAAA,MAAAG,MAAA,CAAA8C,cAAA,CAAAC,MAAA,GAAAlD,MAAA;MAAA,OAAAN,EAAA,CAAAc,WAAA,CAAAR,MAAA;IAAA,EAAmC;IAMvCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,cAA6C,iBAK1C;IAFCD,EAAA,CAAAI,UAAA,mBAAAqD,0EAAA;MAAAzD,EAAA,CAAAO,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAAiD,oBAAA,EAAsB;IAAA,EAAC;IAGhC1D,EAAA,CAAA+B,SAAA,YAA4B;IAAC/B,EAAA,CAAAE,MAAA,sBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAI,UAAA,mBAAAuD,2EAAA;MAAA3D,EAAA,CAAAO,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAAmD,eAAA,EAAiB;IAAA,EAAC;IAG3B5D,EAAA,CAAA+B,SAAA,aAA4B;IAAC/B,EAAA,CAAAE,MAAA,eAC/B;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IAzBEH,EAAA,CAAA4C,SAAA,GAAqC;IAArC5C,EAAA,CAAA8C,UAAA,SAAArC,MAAA,CAAAoD,qBAAA,CAAAL,MAAA,CAAqC;IACrCxD,EAAA,CAAA6C,gBAAA,YAAApC,MAAA,CAAA8C,cAAA,CAAAC,MAAA,CAAmC;;;;;IAV3CxD,EAAA,CAAAsC,UAAA,IAAAwB,iDAAA,mBAGC;;;;IAFE9D,EAAA,CAAA8C,UAAA,SAAArC,MAAA,CAAAsD,mBAAA,CAAyB;;;;;;IAoEtB/D,EAAA,CAAAC,cAAA,YAEsF;IAApFD,EAAA,CAAAI,UAAA,mBAAA4D,sGAAA;MAAAhE,EAAA,CAAAO,aAAA,CAAA0D,GAAA;MAAA,MAAAC,WAAA,GAAAlE,EAAA,CAAAU,aAAA,GAAAyD,QAAA;MAAA,MAAA1D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,MAAA0D,cAAA,GAAApE,EAAA,CAAAqE,WAAA;MAAA,OAAArE,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAA6D,SAAA,CAAAF,cAAA,EAAAF,WAAA,CAAAK,SAAA,EAAAL,WAAA,CAAAM,WAAA,EAAAN,WAAA,CAAyE;IAAA,EAAC;IAClFlE,EAAA,CAAA+B,SAAA,YAAwC;IAO3C/B,EAAA,CAAAG,YAAA,EAAI;;;;;;IAGJH,EAAA,CAAAC,cAAA,YAGsF;IAApFD,EAAA,CAAAI,UAAA,mBAAAqE,sGAAA;MAAAzE,EAAA,CAAAO,aAAA,CAAAmE,IAAA;MAAA,MAAAR,WAAA,GAAAlE,EAAA,CAAAU,aAAA,GAAAyD,QAAA;MAAA,MAAA1D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,MAAA0D,cAAA,GAAApE,EAAA,CAAAqE,WAAA;MAAA,OAAArE,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAA6D,SAAA,CAAAF,cAAA,EAAAF,WAAA,CAAAK,SAAA,EAAAL,WAAA,CAAAM,WAAA,EAAAN,WAAA,CAAyE;IAAA,EAAC;IACjFlE,EAAA,CAAA+B,SAAA,YAAwC;IAG5C/B,EAAA,CAAAG,YAAA,EAAI;;;;;IALmFH,EAArF,CAAAkD,WAAA,eAAAgB,WAAA,CAAAS,WAAA,IAAAlE,MAAA,CAAAmE,SAAA,CAAAC,MAAA,KAAAX,WAAA,CAAAY,SAAA,CAAoF,cAAAZ,WAAA,CAAAS,WAAA,IAAAlE,MAAA,CAAAmE,SAAA,CAAAC,MAAA,KAAAX,WAAA,CAAAY,SAAA,CAAoF;;;;;;IAxB1K9E,EAbF,CAAAC,cAAA,cAA8F,YAkB3F;IADCD,EAAA,CAAAI,UAAA,mBAAA2E,kGAAA;MAAA,MAAAb,WAAA,GAAAlE,EAAA,CAAAO,aAAA,CAAAyE,GAAA,EAAAb,QAAA;MAAA,MAAA1D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAAwE,IAAA,CAAAf,WAAA,CAAAK,SAAA,CAAwB;IAAA,EAAC;IAElCvE,EAAA,CAAA+B,SAAA,YAAwC;IAC1C/B,EAAA,CAAAG,YAAA,EAAI;IAeJH,EAbA,CAAAsC,UAAA,IAAA4C,kFAAA,gBAEsF,IAAAC,kFAAA,gBAcA;IAKxFnF,EAAA,CAAAG,YAAA,EAAM;;;;IArBAH,EAAA,CAAA4C,SAAA,GAAmC;IAAnC5C,EAAA,CAAA8C,UAAA,SAAArC,MAAA,CAAAmE,SAAA,CAAAQ,QAAA,aAAmC;IAanCpF,EAAA,CAAA4C,SAAA,EAAyC;IAAzC5C,EAAA,CAAA8C,UAAA,SAAArC,MAAA,CAAAmE,SAAA,CAAAQ,QAAA,mBAAyC;;;;;IA5CnDpF,EAAA,CAAAC,cAAA,4BAOC;IACCD,EAAA,CAAAsC,UAAA,IAAA+C,8EAAA,0BAA2D;IA0D7DrF,EAAA,CAAAG,YAAA,EAAoB;;;;IA5DlBH,EAHA,CAAA8C,UAAA,aAAY,2BACc,qBACN,WAAArC,MAAA,CAAA6E,cAAA,WACe;;;;;IAwEhCtF,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,GAA4C;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAnDH,EAAA,CAAA4C,SAAA,EAA4C;IAA5C5C,EAAA,CAAA+C,iBAAA,CAAA/C,EAAA,CAAAuF,WAAA,OAAAC,YAAA,CAAAhB,WAAA,MAA4C;;;;;IAGnExE,EAAA,CAAAC,cAAA,wCAIC;IACCD,EAAA,CAAA+B,SAAA,qCAAiE;IAKnE/B,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAA8C,UAAA,WAAA2C,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAhBrB1F,EAAA,CAAAC,cAAA,4BAQC;IAICD,EAHA,CAAAsC,UAAA,IAAAqD,8EAAA,0BAAgD,IAAAC,8EAAA,0BAGwB;IAa1E5F,EAAA,CAAAG,YAAA,EAAoB;;;;IAlBlBH,EAHA,CAAA8C,UAAA,cAAa,2BACa,WAAArC,MAAA,CAAA6E,cAAA,gBACc,oBACrB;;;;;IA8BhBtF,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAsD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA7DH,EAAA,CAAA4C,SAAA,EAAsD;IAAtD5C,EAAA,CAAA+C,iBAAA,CAAA/C,EAAA,CAAAuF,WAAA,OAAAM,YAAA,CAAAC,qBAAA,MAAsD;;;;;IAG7E9F,EAAA,CAAAC,cAAA,wCAIC;IACCD,EAAA,CAAA+B,SAAA,qCAAiE;IAKnE/B,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAA8C,UAAA,WAAAiD,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAhBrBhG,EAAA,CAAAC,cAAA,4BAQC;IAICD,EAHA,CAAAsC,UAAA,IAAA2D,8EAAA,0BAAgD,IAAAC,8EAAA,0BAGwB;IAa1ElG,EAAA,CAAAG,YAAA,EAAoB;;;;IAlBlBH,EAHA,CAAA8C,UAAA,cAAa,2BACa,WAAArC,MAAA,CAAA6E,cAAA,0BACwB,oBAC/B;;;;;IAgCjBtF,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAmG,kBAAA,MAAA1F,MAAA,CAAA2F,UAAA,CAAAC,YAAA,CAAAC,gBAAA,OACF;;;;;IAEEtG,EAAA,CAAAC,cAAA,sCAIC;IAICD,EAHA,CAAA+B,SAAA,gCAAuD,gCACA,+BACF,gCACE;IACzD/B,EAAA,CAAAG,YAAA,EAA8B;;;;;IAN5BH,EAFA,CAAA8C,UAAA,WAAAyD,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAjBpBxG,EAAA,CAAAC,cAAA,4BASC;IAICD,EAHA,CAAAsC,UAAA,IAAAmE,8EAAA,0BAAgD,IAAAC,8EAAA,0BAGwB;IAY1E1G,EAAA,CAAAG,YAAA,EAAoB;;;;IAjBlBH,EAJA,CAAA8C,UAAA,cAAa,gBAAA9C,EAAA,CAAA2G,eAAA,IAAAC,GAAA,EAE0D,WAAAnG,MAAA,CAAA6E,cAAA,qBAC1B,oBAC1B;;;;;IA6BlBtF,EAAA,CAAAE,MAAA,GACD;;;;;IADCF,EAAA,CAAAmG,kBAAA,MAAA1F,MAAA,CAAA2F,UAAA,CAAAS,YAAA,CAAAC,cAAA,OACD;;;;;IAEE9G,EAAA,CAAAC,cAAA,sCAIC;IAICD,EAHA,CAAA+B,SAAA,gCAAuD,gCACA,+BACF,gCACE;IACzD/B,EAAA,CAAAG,YAAA,EAA8B;;;;;IAN5BH,EAFA,CAAA8C,UAAA,WAAAiE,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAhBpBhH,EAAA,CAAAC,cAAA,4BAQC;IAICD,EAHA,CAAAsC,UAAA,IAAA2E,8EAAA,0BAAgD,IAAAC,8EAAA,0BAGwB;IAY1ElH,EAAA,CAAAG,YAAA,EAAoB;;;;IAjBlBH,EAHA,CAAA8C,UAAA,cAAa,gBAAA9C,EAAA,CAAA2G,eAAA,IAAAC,GAAA,EAC0D,WAAAnG,MAAA,CAAA6E,cAAA,mBAC5B,oBACxB;;;;;IA+BjBtF,EAAA,CAAAE,MAAA,GACA;;;;;IADAF,EAAA,CAAAmG,kBAAA,MAAAnG,EAAA,CAAAuF,WAAA,OAAA4B,YAAA,CAAAC,eAAA,WACA;;;;;IAOApH,EAAA,CAAAC,cAAA,wCAIC;IACCD,EAAA,CAAA+B,SAAA,qCAAiE;IAKnE/B,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAA8C,UAAA,WAAAuE,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAtBrBtH,EAAA,CAAAC,cAAA,4BASC;IASCD,EARA,CAAAsC,UAAA,IAAAiF,8EAAA,0BAAgD,IAAAC,8EAAA,0BAQwB;IAa1ExH,EAAA,CAAAG,YAAA,EAAoB;;;;IAvBlBH,EAJA,CAAA8C,UAAA,cAAa,gBAAA9C,EAAA,CAAA2G,eAAA,IAAAC,GAAA,EAE0D,WAAAnG,MAAA,CAAA6E,cAAA,oBAC3B,oBACzB;;;;;IAqCjBtF,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAmG,kBAAA,MAAAnG,EAAA,CAAAuF,WAAA,OAAAkC,YAAA,CAAAC,0BAAA,WACF;;;;;IAEE1H,EAAA,CAAAC,cAAA,wCAIC;IACCD,EAAA,CAAA+B,SAAA,qCAAiE;IAKnE/B,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAA8C,UAAA,WAAA6E,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAjBrB5H,EAAA,CAAAC,cAAA,4BASC;IAICD,EAHA,CAAAsC,UAAA,IAAAuF,8EAAA,0BAAgD,IAAAC,8EAAA,0BAGwB;IAa1E9H,EAAA,CAAAG,YAAA,EAAoB;;;;IAlBlBH,EAJA,CAAA8C,UAAA,cAAa,gBAAA9C,EAAA,CAAA2G,eAAA,IAAAC,GAAA,EAE0D,WAAAnG,MAAA,CAAA6E,cAAA,+BAChB,oBACpC;;;;;IAgCpBtF,EAAA,CAAAE,MAAA,GACC;;;;;IADDF,EAAA,CAAAmG,kBAAA,MAAAnG,EAAA,CAAAuF,WAAA,OAAAwC,YAAA,CAAAC,eAAA,WACC;;;;;IAEEhI,EAAA,CAAAC,cAAA,wCAIC;IACCD,EAAA,CAAA+B,SAAA,qCAAiE;IAKnE/B,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAA8C,UAAA,WAAAmF,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAjBrBlI,EAAA,CAAAC,cAAA,4BASC;IAICD,EAHA,CAAAsC,UAAA,IAAA6F,8EAAA,0BAAgD,IAAAC,8EAAA,0BAGwB;IAa1EpI,EAAA,CAAAG,YAAA,EAAoB;;;;IAlBlBH,EAJA,CAAA8C,UAAA,cAAa,gBAAA9C,EAAA,CAAA2G,eAAA,IAAAC,GAAA,EAE0D,WAAAnG,MAAA,CAAA6E,cAAA,oBAC3B,oBACzB;;;;;IAgChBtF,EAAA,CAAAE,MAAA,GACH;;;;;IADGF,EAAA,CAAAmG,kBAAA,MAAA1F,MAAA,CAAA2F,UAAA,CAAAiC,YAAA,CAAAC,eAAA,OACH;;;;;IAEEtI,EAAA,CAAAC,cAAA,sCAIC;IAICD,EAHA,CAAA+B,SAAA,gCAAuD,gCACA,+BACF,gCACE;IACzD/B,EAAA,CAAAG,YAAA,EAA8B;;;;;IAN5BH,EAFA,CAAA8C,UAAA,WAAAyF,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAjBpBxI,EAAA,CAAAC,cAAA,4BASC;IAICD,EAHA,CAAAsC,UAAA,IAAAmG,8EAAA,0BAAgD,IAAAC,8EAAA,0BAGwB;IAY1E1I,EAAA,CAAAG,YAAA,EAAoB;;;;IAjBlBH,EAJA,CAAA8C,UAAA,cAAa,gBAAA9C,EAAA,CAAA2G,eAAA,IAAAC,GAAA,EAE0D,WAAAnG,MAAA,CAAA6E,cAAA,oBAC3B,oBACzB;;;;;IAvRvBtF,EAAA,CAAA2I,uBAAA,GAAiD;IA+Q/C3I,EA7QA,CAAAsC,UAAA,IAAAsG,gEAAA,gCAOC,IAAAC,gEAAA,gCAqEA,IAAAC,gEAAA,gCA2BA,IAAAC,gEAAA,gCA6BA,IAAAC,gEAAA,gCA0BA,IAAAC,gEAAA,gCA4BA,IAAAC,gEAAA,gCAkCA,IAAAC,gEAAA,gCA6BA,IAAAC,gEAAA,gCA6BA;;;;;IArREpJ,EAAA,CAAA4C,SAAA,EAAyB;IAAzB5C,EAAA,CAAA8C,UAAA,SAAAuG,UAAA,cAAyB;IAoEzBrJ,EAAA,CAAA4C,SAAA,EAA8B;IAA9B5C,EAAA,CAAA8C,UAAA,SAAAuG,UAAA,mBAA8B;IA2B9BrJ,EAAA,CAAA4C,SAAA,EAAwC;IAAxC5C,EAAA,CAAA8C,UAAA,SAAAuG,UAAA,6BAAwC;IA4BxCrJ,EAAA,CAAA4C,SAAA,EAAmC;IAAnC5C,EAAA,CAAA8C,UAAA,SAAAuG,UAAA,wBAAmC;IA2BnCrJ,EAAA,CAAA4C,SAAA,EAAiC;IAAjC5C,EAAA,CAAA8C,UAAA,SAAAuG,UAAA,sBAAiC;IA2BjCrJ,EAAA,CAAA4C,SAAA,EAAkC;IAAlC5C,EAAA,CAAA8C,UAAA,SAAAuG,UAAA,uBAAkC;IAkClCrJ,EAAA,CAAA4C,SAAA,EAA6C;IAA7C5C,EAAA,CAAA8C,UAAA,SAAAuG,UAAA,kCAA6C;IA6B7CrJ,EAAA,CAAA4C,SAAA,EAAkC;IAAlC5C,EAAA,CAAA8C,UAAA,SAAAuG,UAAA,uBAAkC;IA6BlCrJ,EAAA,CAAA4C,SAAA,EAAkC;IAAlC5C,EAAA,CAAA8C,UAAA,SAAAuG,UAAA,uBAAkC;;;;;IA+BjCrJ,EAFJ,CAAAC,cAAA,cAA8D,cACnC,YACD;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAEvCF,EAFuC,CAAAG,YAAA,EAAI,EACnC,EACF;;;;;IAJNH,EAAA,CAAAsC,UAAA,IAAAgH,iDAAA,kBAA8D;;;;IAA9BtJ,EAAA,CAAA8C,UAAA,UAAArC,MAAA,CAAA8I,OAAA,KAAA9I,MAAA,CAAA+I,SAAA,CAA4B;;;;;;IAY9DxJ,EADF,CAAAC,cAAA,cAA+C,aACrB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAI,UAAA,mBAAAqJ,oEAAA;MAAA,MAAAC,SAAA,GAAA1J,EAAA,CAAAO,aAAA,CAAAoJ,IAAA,EAAAC,SAAA;MAAA,OAAA5J,EAAA,CAAAc,WAAA,CAAS4I,SAAA,CAAAG,OAAA,EAAe;IAAA,EAAC;IAE7B7J,EADG,CAAAG,YAAA,EAAS,EACN;IAGJH,EADF,CAAAC,cAAA,cAAgD,YAC9B;IACdD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;IAGJH,EADF,CAAAC,cAAA,cAAmD,iBACsB;IAA1BD,EAAA,CAAAI,UAAA,mBAAA0J,oEAAA;MAAA,MAAAJ,SAAA,GAAA1J,EAAA,CAAAO,aAAA,CAAAoJ,IAAA,EAAAC,SAAA;MAAA,OAAA5J,EAAA,CAAAc,WAAA,CAAS4I,SAAA,CAAAG,OAAA,EAAe;IAAA,EAAC;IACpE7J,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAI,UAAA,mBAAA2J,qEAAA;MAAA,MAAAL,SAAA,GAAA1J,EAAA,CAAAO,aAAA,CAAAoJ,IAAA,EAAAC,SAAA;MAAA,MAAAnJ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAAuJ,aAAA,EAAe;MAAA,OAAAhK,EAAA,CAAAc,WAAA,CAAE4I,SAAA,CAAAO,KAAA,EAAa;IAAA,EAAC;IAExCjK,EAAA,CAAAE,MAAA,gBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAfFH,EAAA,CAAA4C,SAAA,GACF;IADE5C,EAAA,CAAAmG,kBAAA,sDAAA1F,MAAA,CAAA+D,WAAA,MACF;;;;;;IAoBAxE,EADF,CAAAC,cAAA,cAA+C,aACrB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAI,UAAA,mBAAA8J,qEAAA;MAAA,MAAAC,SAAA,GAAAnK,EAAA,CAAAO,aAAA,CAAA6J,IAAA,EAAAR,SAAA;MAAA,OAAA5J,EAAA,CAAAc,WAAA,CAASqJ,SAAA,CAAAN,OAAA,EAAe;IAAA,EAAC;IAE7B7J,EADG,CAAAG,YAAA,EAAS,EACN;IAGJH,EADF,CAAAC,cAAA,cAAgD,YAC9B;IACdD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;IAIJH,EAFF,CAAAC,cAAA,cAAmD,iBAMhD;IADCD,EAAA,CAAAI,UAAA,mBAAAiK,qEAAA;MAAA,MAAAF,SAAA,GAAAnK,EAAA,CAAAO,aAAA,CAAA6J,IAAA,EAAAR,SAAA;MAAA,OAAA5J,EAAA,CAAAc,WAAA,CAASqJ,SAAA,CAAAF,KAAA,EAAa;IAAA,EAAC;IAEvBjK,EAAA,CAAAE,MAAA,WACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAbFH,EAAA,CAAA4C,SAAA,GACF;IADE5C,EAAA,CAAAmG,kBAAA,oBAAA1F,MAAA,CAAA+D,WAAA,iFACF;;;AD7dJ,OAAM,MAAO8F,oBAAoB;EAsMrBC,MAAA;EACAC,eAAA;EACAC,KAAA;EACAC,eAAA;EACAC,eAAA;EACAC,wBAAA;EACAC,kBAAA;EACAC,YAAA;EACAC,GAAA;EACDC,UAAA;EA9MgBC,IAAI;EAE7B;EACOC,iBAAiB,GAAU,EAAE;EAC7BC,QAAQ,GAAQ,EAAE;EAClBC,cAAc,GAAY,KAAK;EAE/B7B,OAAO,GAAY,KAAK;EACxBC,SAAS,GAAY,KAAK;EAEjC5E,SAAS,GAAQ,EAAE;EAEnB;EACOpD,UAAU,GAAW,EAAE;EAC9B;EACA;EACA;EAEA;EACO6J,MAAM,GAA8B;IAAEC,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACjEC,UAAU,GAA8B;IAAEF,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACrEE,aAAa,GAIf,EAAE;EAEAC,aAAa,GAAkD,CACpE;IAAEC,IAAI,EAAE,KAAK;IAAE/K,KAAK,EAAE;EAAI,CAAE,EAC5B;IAAE+K,IAAI,EAAE,SAAS;IAAE/K,KAAK,EAAE;EAAS,CAAE,EACrC;IAAE+K,IAAI,EAAE,WAAW;IAAE/K,KAAK,EAAE;EAAW,CAAE,EACzC;IAAE+K,IAAI,EAAE,sBAAsB;IAAE/K,KAAK,EAAE;EAAsB,CAAE,EAC/D;IAAE+K,IAAI,EAAE,mBAAmB;IAAE/K,KAAK,EAAE;EAAmB,CAAE,CAC1D;EAED;EACOiD,qBAAqB,GAAG;IAC7BL,MAAM,EAAE,CACN;MAAEmI,IAAI,EAAE,KAAK;MAAE/K,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAE+K,IAAI,EAAE,SAAS;MAAE/K,KAAK,EAAE;IAAS,CAAE,EACrC;MAAE+K,IAAI,EAAE,WAAW;MAAE/K,KAAK,EAAE;IAAW,CAAE,EACzC;MAAE+K,IAAI,EAAE,sBAAsB;MAAE/K,KAAK,EAAE;IAAsB,CAAE,EAC/D;MAAE+K,IAAI,EAAE,mBAAmB;MAAE/K,KAAK,EAAE;IAAmB,CAAE,CACT;IAClDgL,OAAO,EAAE;GACV;EAED;EACO7H,mBAAmB,GAAG,KAAK;EAC3BR,cAAc,GAGjB,EAAE;EAEN;EACOsI,SAAS;EACTC,UAAU,GAAQ,EAAE;EACpBC,aAAa,GAAQ,EAAE;EACvBC,iBAAiB,GAAQ,EAAE;EAC3BC,YAAY,GAAQ,EAAE;EAE7B;EACOC,WAAW,GAAa,EAAE;EAC1BC,cAAc,GAAa,EAAE;EAC7BC,UAAU;EACVC,YAAY;EACZlJ,UAAU,GAAG,KAAK;EAEzB;EACOmJ,uBAAuB,GAAG,KAAK;EAEtC;EACOC,gBAAgB,GAQlB,CACH;IACEC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,uBAAuB;IAC9BC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,4BAA4B;IACnCC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,GAAG;IACVE,IAAI,EAAE,QAAQ;IACdD,OAAO,EAAE,KAAK;IACdG,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,CACF;EAED;EACOE,IAAI,GAAqB,CAAC;IAAEP,KAAK,EAAE,iBAAiB;IAAEQ,GAAG,EAAE;EAAM,CAAE,CAAC;EAEpEhK,IAAI,GAAQ;IACjBiK,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,CAAC;IACbjK,aAAa,EAAE,CAAC;IAChBkK,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,iBAAiB;IAC1BC,QAAQ,EAAE;GACX;EAEMC,IAAI,GAAW,CAAC;EAEvB;EACOC,YAAY,GAAU,EAAE;EACxBC,aAAa,GAAY,KAAK;EAErC;EACOC,aAAa,GAAG,CACrB;IAAE9B,IAAI,EAAE,KAAK;IAAE/K,KAAK,EAAE;EAAK,CAAE,EAC7B;IAAE+K,IAAI,EAAE,cAAc;IAAE/K,KAAK,EAAE;EAAU;EACzC;EAAA,CACD;EAED;EACOwC,mBAAmB,GAAY,KAAK;EACpClC,WAAW,GAAW,CAAC;EACvBC,YAAY,GAAW,CAAC;EAE/BqD,WAAW;EACXD,SAAS;EAETmJ,YACUnD,MAAc,EACdC,eAA+B,EAC/BC,KAAqB,EACrBC,eAAgC,EAChCC,eAAiC,EACjCC,wBAAkD,EAClDC,kBAAsC,EACtCC,YAAsB,EACtBC,GAAsB,EACvBC,UAAsB;IATrB,KAAAT,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IACJ,KAAAC,UAAU,GAAVA,UAAU;IAEjB;EACF;EAEA2C,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,cAAc,EAAE;EACvB;EAIAC,WAAWA,CAAA;IACT;EAAA;EAGMJ,mBAAmBA,CAAA;IACzB;IACA;IACA,IAAI,CAAChJ,SAAS,GAAG,IAAI,CAACoG,UAAU,CAACiD,eAAe,EAAE;IAClDC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACvJ,SAAS,CAAC;IAC7C;IACA,IAAI,CAACwJ,0BAA0B,EAAE;EACnC;EAEQA,0BAA0BA,CAAA;IAChC;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,MAAMC,WAAW,GAAG,IAAI,CAACzD,kBAAkB,CAAC0D,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC3J,SAAS,CAACC,MAAM,CAAC;IAElG,IAAIyJ,WAAW,EAAE;MACf;MACA,IAAI,CAACzC,SAAS,GAAGyC,WAAW,CAACxC,UAAU,IAAI,EAAE;MAC7C,IAAI,CAACC,aAAa,GAAGuC,WAAW,CAACvC,aAAa,IAAI,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;MAC1E,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,aAAa,CAAC;IAClD,CAAC,MAAM;MACL;MACA,IAAI,CAACF,SAAS,GAAG,EAAE;MACnB,IAAI,CAACC,UAAU,GAAG,EAAE;MACpB,IAAI,CAACC,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;MAC7C,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACG,cAAc,CAAC;IACnD;IAEA;IACA,IAAI,CAACqC,wBAAwB,EAAE;EACjC;EAEQC,4BAA4BA,CAAA;IAClC,MAAMC,MAAM,GAAG;MACbC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,IAAI,CAAChK,SAAS,CAACC;KACxB;IAED,IAAI,CAACgG,kBAAkB,CAACgE,aAAa,CAACH,MAAM,CAAC,CAACI,SAAS,CAAC;MACtDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,KAAK,KAAK,IAAID,QAAQ,CAACE,IAAI,EAAE;UAC/C;UACA,IAAI,CAACrD,SAAS,GAAGmD,QAAQ,CAACE,IAAI,CAACC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAACE,IAAI,CAACC,QAAQ,CAAC,GAAG,EAAE;UACjF,IAAI,CAACpD,aAAa,GAAGiD,QAAQ,CAACE,IAAI,CAACnD,aAAa,GAAGqD,IAAI,CAACC,KAAK,CAACL,QAAQ,CAACE,IAAI,CAACnD,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;UACrH,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,aAAa,CAAC;UAEhD;UACA,IAAI,CAACyC,wBAAwB,EAAE;UAE/BN,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;YACjDtC,SAAS,EAAE,IAAI,CAACA,SAAS;YACzBE,aAAa,EAAE,IAAI,CAACA;WACrB,CAAC;QACJ,CAAC,MAAM;UACL;UACA,IAAI,CAACF,SAAS,GAAG,EAAE;UACnB,IAAI,CAACE,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;UAC7C,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACG,cAAc,CAAC;UACjD,IAAI,CAACqC,wBAAwB,EAAE;QACjC;MACF,CAAC;MACDc,KAAK,EAAGA,KAAK,IAAI;QACfpB,OAAO,CAACoB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD;QACA,IAAI,CAACzD,SAAS,GAAG,EAAE;QACnB,IAAI,CAACE,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;QAC7C,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACG,cAAc,CAAC;QACjD,IAAI,CAACqC,wBAAwB,EAAE;MACjC;KACD,CAAC;EACJ;EAEQH,iBAAiBA,CAAA;IACvB,IAAI,CAACnC,WAAW,GAAG,IAAI,CAACK,gBAAgB,CAACgD,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAAChD,KAAK,CAAC;IAChE,IAAI,CAACL,cAAc,GAAG,CAAC,GAAG,IAAI,CAACD,WAAW,CAAC;EAC7C;EAEQ6B,cAAcA,CAAA;IACpB,IAAI,IAAI,CAAC9C,IAAI,EAAE;MACb;MACA,IAAI,CAACuD,wBAAwB,EAAE;IACjC;EACF;EAEQA,wBAAwBA,CAAA;IAC9B,IAAI,IAAI,CAAC3C,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC4D,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAACxD,YAAY,GAAG,IAAI,CAACJ,SAAS;IACpC;IAEA,IAAI,IAAI,CAACE,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC0D,MAAM,GAAG,CAAC,EAAE;MACvD;MACA,IAAI,CAAClD,gBAAgB,CAACQ,IAAI,CAAC,CAAC2C,CAAC,EAAEC,CAAC,KAAI;QAClC,MAAMC,MAAM,GAAG,IAAI,CAAC7D,aAAa,CAAC8D,OAAO,CAACH,CAAC,CAAClD,KAAK,CAAC;QAClD,MAAMsD,MAAM,GAAG,IAAI,CAAC/D,aAAa,CAAC8D,OAAO,CAACF,CAAC,CAACnD,KAAK,CAAC;QAClD,OAAOoD,MAAM,GAAGE,MAAM;MACxB,CAAC,CAAC;IACJ;EACF;EAEA;EACOjC,SAASA,CAAA;IACd,IAAI,CAACkC,0BAA0B,EAAE;EACnC;EAEA;EACAA,0BAA0BA,CAAA;IACxB,IAAI,CAACxG,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,MAAMkB,cAAc,GAAGC,UAAU,CAAC,MAAK;MACrChC,OAAO,CAACiC,IAAI,CAAC,mDAAmD,CAAC;MACjE,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAEX;IACA,MAAMC,KAAK,GAAG;MACZC,IAAI,EAAE,IAAI,CAACtN,IAAI,CAACiK,IAAI;MACpBK,IAAI,EAAE,IAAI,CAACA,IAAI;MACfP,IAAI,EAAE,IAAI,CAACA,IAAI;MACf1B,MAAM,EAAE,IAAI,CAACA,MAAM,CAACE,OAAO;MAC3BgF,MAAM,EAAE,IAAI,CAAC/O,UAAU;MACvBgP,cAAc,EAAE,IAAI,CAAC5L,SAAS,CAACC,MAAM;MACrC4L,IAAI,EAAC,IAAI,CAAC7L,SAAS,CAACQ,QAAQ,IAAI,CAAC;MACjCP,MAAM,EAAC,IAAI,CAACD,SAAS,CAACC;KACvB;IAEDqJ,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;MACnCuC,UAAU,EAAE,IAAI,CAAClP,UAAU;MAC3B6O,KAAK,EAAEA;KACR,CAAC;IAEF,IAAI,CAAC3F,eAAe,CAACiG,uBAAuB,CAACN,KAAK,CAAC,CAACvB,SAAS,CAAC;MAC5DC,IAAI,EAAG6B,IAYN,IAAI;QACH;QACAC,YAAY,CAACZ,cAAc,CAAC;QAE5B/B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEyC,IAAI,CAAC;QAElC;QACA,IACEA,IAAI,CAAC3B,OAAO,IACX2B,IAAI,CAACE,YAAY,IAChBF,IAAI,CAACE,YAAY,CAACC,MAAM,IACxBH,IAAI,CAACE,YAAY,CAACC,MAAM,CAACtB,MAAM,GAAG,CAAE,EACtC;UACA,MAAMsB,MAAM,GAAGH,IAAI,CAACE,YAAY,EAAEC,MAAM,IAAIH,IAAI,CAACG,MAAM,IAAI,EAAE;UAC7D7C,OAAO,CAACoB,KAAK,CAAC,uBAAuB,EAAEyB,MAAM,CAAC;UAE9C;UACA,IAAIH,IAAI,CAACE,YAAY,EAAEtN,MAAM,KAAK,GAAG,IAAIoN,IAAI,CAACpN,MAAM,KAAK,GAAG,EAAE;YAC5D0K,OAAO,CAACiC,IAAI,CAAC,6CAA6C,CAAC;YAC3D;YACA;UACF;UAEA,IAAI,CAACa,mBAAmB,EAAE;UAC1B;UACA,IAAI,CAACzH,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;UACtB,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,KAAK,CAAC;QACjD,CAAC,MAAM;UACL;UACA,MAAM+B,YAAY,GAAGF,IAAI,CAACE,YAAY,IAAIF,IAAI;UAC9C,MAAMK,WAAW,GAAGH,YAAY,CAACF,IAAI,IAAI,EAAE;UAC3C,MAAMM,KAAK,GAAGJ,YAAY,CAACI,KAAK,IAAI,CAAC;UAErC,IAAI,CAAC9F,cAAc,GAAG6F,WAAW,CAACxB,MAAM,KAAK,CAAC;UAC9C,IAAI,CAACvE,iBAAiB,GAAG+F,WAAW;UACpC,IAAI,CAACjO,IAAI,CAACC,aAAa,GAAGiO,KAAK;UAC/B,IAAI,CAAClO,IAAI,CAACmK,UAAU,GAAGgE,IAAI,CAACC,IAAI,CAACF,KAAK,GAAG,IAAI,CAAClO,IAAI,CAACiK,IAAI,CAAC;UAExD;UACA,IAAI,CAAC9B,QAAQ,GAAG;YACdyF,IAAI,EAAEK,WAAW;YACjBC,KAAK,EAAEA;WACR;UACDhD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACjD,iBAAiB,CAAC;UAC9DgD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAChD,QAAQ,CAAC;UAC5C+C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC/C,cAAc,CAAC;UACxD8C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACnL,IAAI,CAAC;UACpC,IAAI,CAAC+H,GAAG,CAACsG,YAAY,EAAE;UACvB;UACA,IAAI,CAAC9H,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;UACtB,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,KAAK,CAAC;QACjD;MACF,CAAC;MACDO,KAAK,EAAGA,KAAc,IAAI;QACxB;QACAuB,YAAY,CAACZ,cAAc,CAAC;QAE5B/B,OAAO,CAACoB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAElE;QACA,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAIA,KAAK,EAAE;UAC3D,MAAMgC,SAAS,GAAGhC,KAAY;UAC9B,IAAIgC,SAAS,CAAC9N,MAAM,KAAK,GAAG,EAAE;YAC5B0K,OAAO,CAACiC,IAAI,CAAC,6CAA6C,CAAC;YAC3D;YACA;UACF;QACF;QAEA,IAAI,CAACa,mBAAmB,EAAE;QAC1B,IAAI,CAACzH,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACDwC,QAAQ,EAAEA,CAAA,KAAK;QACb;QACAV,YAAY,CAACZ,cAAc,CAAC;QAE5B;QACA,IAAI,CAAC1G,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,KAAK,CAAC;MACjD;KACD,CAAC;EACJ;EAEQiC,mBAAmBA,CAAA;IACzB,IAAI,CAAC5F,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACF,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACnI,IAAI,CAACC,aAAa,GAAG,CAAC;IAC3B,IAAI,CAACD,IAAI,CAACmK,UAAU,GAAG,CAAC;IAExB;IACA,IAAI,CAAC5D,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,KAAK,CAAC;EACjD;EAEA;EACQqB,kBAAkBA,CAAA;IACxB,IAAI,CAAC7G,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,KAAK,CAAC;EACjD;EAEA;EACOpM,WAAWA,CAAA;IAChBuL,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1C,IAAI,CAACiC,kBAAkB,EAAE;IACzB,IAAI,CAACvC,SAAS,EAAE;EAClB;EAGA;EACOnM,eAAeA,CAAC8P,KAAoB;IACzC,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MACzBvD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAAC3M,UAAU,CAAC;MAE9D;MACA,IAAI,CAAC+H,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,IAAI,CAAC;MAE9C,IAAI,CAAClB,SAAS,EAAE;IAClB;EACF;EAEOjM,YAAYA,CAAA;IACjBsM,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC3M,UAAU,CAAC;IACtD;IACA,IAAI,CAACqM,SAAS,EAAE;EAClB;EAEO/L,mBAAmBA,CAAA;IACxBoM,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC3M,UAAU,CAAC;IACrD;IACA;IACA,IAAI,CAAC,IAAI,CAACA,UAAU,IAAI,IAAI,CAACA,UAAU,CAACkQ,IAAI,EAAE,KAAK,EAAE,EAAE;MACrD,IAAI,CAAC7D,SAAS,EAAE;IAClB;EACF;EAEO8D,cAAcA,CAAA;IACnBzD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC3M,UAAU,CAAC;IAC/C;IACA;EACF;EAGQoQ,WAAWA,CAAA;IACjB;IACA,IAAI,CAACrI,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,IAAI,CAAC;IAE9C,IAAI,CAAClB,SAAS,EAAE;EAClB;EAEOgE,WAAWA,CAAA;IAChB,IAAI,CAACrQ,UAAU,GAAG,EAAE;IACpB,IAAI,CAACqM,SAAS,EAAE;EAClB;EAEA;EACOiE,oBAAoBA,CAAA;IACzB5D,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,IAAI,CAAC3M,UAAU,GAAG,aAAa,CAAC,CAAC;IAEjC;IACA,IAAI,CAAC+H,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,IAAI,CAAC;IAE9C,IAAI,CAAClB,SAAS,EAAE;EAClB;EAEA;EACOkE,YAAYA,CAAC1G,MAAiC;IACnD,IAAI,CAACA,MAAM,GAAGA,MAAM;IAEpB;IACA,IAAI,CAAC9B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,IAAI,CAAC;IAE9C,IAAI,CAAClB,SAAS,EAAE;EAClB;EAEOnK,oBAAoBA,CAAA;IACzB;IACA,IAAI,IAAI,CAACH,cAAc,CAACC,MAAM,EAAE;MAC9B,IAAI,CAAC6H,MAAM,CAACE,OAAO,GAAG,IAAI,CAACF,MAAM,CAACE,OAAO,CAACF,MAAM,CAAE2G,CAAC,IAAI;QACrD,IAAI,OAAO,IAAIA,CAAC,EAAE;UAChB,OAAOA,CAAC,CAACxF,KAAK,KAAK,QAAQ,IAAIwF,CAAC,CAACxF,KAAK,KAAK,eAAe;QAC5D;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAI,CAACnB,MAAM,CAACE,OAAO,CAAC0G,IAAI,CAAC;QACvBzF,KAAK,EAAE,QAAQ;QACf0F,QAAQ,EAAE,IAAI;QACdtR,KAAK,EAAE,IAAI,CAAC2C,cAAc,CAACC;OAC5B,CAAC;IACJ;IAEA;IACA,IAAI,IAAI,CAACD,cAAc,CAAC4O,MAAM,EAAE;MAC9B,IAAI,CAAC9G,MAAM,CAACE,OAAO,GAAG,IAAI,CAACF,MAAM,CAACE,OAAO,CAACF,MAAM,CAAE2G,CAAC,IAAI;QACrD,IAAI,OAAO,IAAIA,CAAC,EAAE;UAChB,OAAOA,CAAC,CAACxF,KAAK,KAAK,UAAU;QAC/B;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAI,CAACnB,MAAM,CAACE,OAAO,CAAC0G,IAAI,CAAC;QACvBzF,KAAK,EAAE,UAAU;QACjB0F,QAAQ,EAAE,IAAI;QACdtR,KAAK,EAAE,IAAI,CAAC2C,cAAc,CAAC4O;OAC5B,CAAC;IACJ;IAEA,IAAI,CAACtE,SAAS,EAAE;EAClB;EAEOuE,oBAAoBA,CAAA;IACzB,IAAI,CAAC7O,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC8H,MAAM,CAACE,OAAO,GAAG,EAAE;IAExB;IACA,IAAI,CAAChC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,IAAI,CAAC;IAE9C,IAAI,CAAClB,SAAS,EAAE;EAClB;EAEOjK,eAAeA,CAAA;IACpB,IAAI,CAACL,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC8H,MAAM,CAACE,OAAO,GAAG,EAAE;IAExB;IACA,IAAI,CAAChC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,IAAI,CAAC;IAE9C,IAAI,CAAClB,SAAS,EAAE;EAClB;EAEA;EACOwE,YAAYA,CAACtF,IAAsB;IACxC;IACA,MAAMuF,YAAY,GAAGvF,IAAI,CAAC0C,MAAM,GAAG,CAAC,IAAI1C,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,KAAKuF,SAAS;IAE5E,IAAID,YAAY,EAAE;MAChB;MACA,IAAI,CAACvF,IAAI,GAAG,EAAE;MACd,IAAI,CAAC/J,IAAI,CAACoK,OAAO,GAAG,iBAAiB;MACrC,IAAI,CAACpK,IAAI,CAACqK,QAAQ,GAAG,MAAM;IAC7B,CAAC,MAAM,IAAIN,IAAI,CAAC0C,MAAM,GAAG,CAAC,IAAI1C,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,EAAE;MACpD;MACA,IAAI,CAACD,IAAI,GAAGA,IAAI;MAChB,IAAI,CAAC/J,IAAI,CAACoK,OAAO,GAAGL,IAAI,CAAC,CAAC,CAAC,CAACP,KAAK,IAAI,iBAAiB;MACtD,IAAI,CAACxJ,IAAI,CAACqK,QAAQ,GAAGN,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG;IAClC,CAAC,MAAM;MACL;MACA,IAAI,CAACD,IAAI,GAAG,EAAE;MACd,IAAI,CAAC/J,IAAI,CAACoK,OAAO,GAAG,iBAAiB;MACrC,IAAI,CAACpK,IAAI,CAACqK,QAAQ,GAAG,MAAM;IAC7B;IAEA;IACA,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACtK,IAAI,CAACkK,UAAU,GAAG,CAAC;IAExB,IAAI,CAAC3D,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,IAAI,CAAC;IAE9CmB,UAAU,CAAC,MAAM,IAAI,CAACrC,SAAS,EAAE,EAAE,CAAC,CAAC;EACvC;EAEA;EACO2E,UAAUA,CAAChB,KAAU;IAC1B;IACA,IAAI,CAAClE,IAAI,GAAGkE,KAAK,CAAClE,IAAI;IACtB,IAAI,CAACtK,IAAI,CAACiK,IAAI,GAAGuE,KAAK,CAAClB,IAAI,IAAI,IAAI,CAACtN,IAAI,CAACiK,IAAI;IAC7C,IAAI,CAACjK,IAAI,CAACkK,UAAU,GAAGiE,IAAI,CAACsB,KAAK,CAAC,IAAI,CAACnF,IAAI,GAAG,IAAI,CAACtK,IAAI,CAACiK,IAAI,CAAC;IAE7D;IACA,IAAI,CAAC1D,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACmB,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,IAAI,CAAC;IAE9C,IAAI,CAAClB,SAAS,EAAE;EAClB;EAIO6E,sBAAsBA,CAAClB,KAAU;IACtC;IACA,MAAMmB,aAAa,GAAGnB,KAAK,CAACmB,aAAa,IAAI,EAAE;IAC/C,IAAI,CAAC1G,YAAY,GAAG0G,aAAa;EACnC;EAEOrN,cAAcA,CAACsN,SAAiB;IACrC,OAAO,IAAI,CAAC3G,YAAY,CAAC4G,QAAQ,CAACD,SAAS,CAAC;EAC9C;EAUA;EACOE,iBAAiBA,CAACtB,KAAU;IACjC,IAAI,CAACjE,YAAY,GAAGiE,KAAK,CAACjE,YAAY,IAAI,EAAE;IAC5C,IAAI,CAACC,aAAa,GAChB,IAAI,CAACD,YAAY,CAACkC,MAAM,KAAK,IAAI,CAACvE,iBAAiB,CAACuE,MAAM;EAC9D;EAEOsD,SAASA,CAAA;IACd,IAAI,IAAI,CAACvF,aAAa,EAAE;MACtB,IAAI,CAACD,YAAY,GAAG,EAAE;MACtB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC5B,CAAC,MAAM;MACL,IAAI,CAACD,YAAY,GAAG,CAAC,GAAG,IAAI,CAACrC,iBAAiB,CAAC;MAC/C,IAAI,CAACsC,aAAa,GAAG,IAAI;IAC3B;EACF;EAEA;EACOrL,YAAYA,CAAA;IACjB;IACA,MAAM6Q,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAC1C,iBAAiB,CACH;IAChB,IAAIF,aAAa,EAAE;MACjBA,aAAa,CAACG,SAAS,CAACC,MAAM,CAAC,iBAAiB,CAAC;MACjD,IAAI,CAACjQ,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;MAClC;MACA,IAAI,IAAI,CAAC8H,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACoI,OAAO,EAAE;MACrB;IACF;EACF;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA1S,aAAaA,CAAC6Q,KAAU;IACtB,MAAM8B,cAAc,GAAG9B,KAAK,CAAC5Q,KAAK,CAAC,CAAC;IAEpC,IAAI2S,QAAQ,GAAQ,EAAE;IACtB,IAAID,cAAc,KAAK,UAAU,EAAE;MACjCC,QAAQ,GAAG,IAAI,CAACrI,iBAAiB;MAEjC;MACA;MACA,IAAI,CAACsI,WAAW,CAACD,QAAQ,CAAC;IAC5B,CAAC,MAAM,IAAID,cAAc,KAAK,KAAK,EAAE;MACnC,MAAMG,gBAAgB,GAAG;QACvBC,QAAQ,EAAE,IAAI,CAAC1Q,IAAI,CAACC,aAAa;QACjC0Q,SAAS,EAAE,IAAI,CAAC3Q,IAAI,CAACqK,QAAQ;QAC7BuG,SAAS,EAAE,IAAI,CAAC5Q,IAAI,CAACoK,OAAO;QAC5BF,UAAU,EAAE,IAAI,CAAClK,IAAI,CAACkK;QACtB;OACD;MAED;MACA,IAAI,CAACvC,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,IAAI,CAAC;MAC9C;MACA,IAAI,CAACrE,eAAe,CACjBmJ,cAAc,CAACJ,gBAAgB;MAChC;MAAA,CACC3E,SAAS,CAAE8B,IAAI,IAAI;QAClB;QACA,IAAI,CAACjG,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI6B,IAAI,CAAC3B,OAAO,EAAE;UAChB,IAAI,CAAC7D,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACL,GAAG,CAACsG,YAAY,EAAE;UACvB,OAAO,CAAC;QACV;QAEA,IAAI,CAACjG,cAAc,GAAG,IAAI;QAC1BmI,QAAQ,GAAG3C,IAAI,CAACE,YAAY,CAACF,IAAI,IAAI,EAAE;QAEvC,IAAI,CAAC7F,GAAG,CAAC+I,aAAa,EAAE,CAAC,CAAC;QAC1B,IAAI,CAACN,WAAW,CAACD,QAAQ,CAAC;MAC5B,CAAC,CAAC;IACN;EACF;EAEAC,WAAWA,CAACO,WAAgB;IAC1B;IACA,IAAIR,QAAQ,GAAQQ,WAAW;IAC/B,IAAIC,WAAW,GAAS,IAAI,CAAChJ,UAAU,CAACiJ,eAAe,CAAC,IAAIC,IAAI,EAAE,CAAC;IAEnEhG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEoF,QAAQ,CAAC;IAEjC;IACA,IAAIA,QAAQ,KAAKhB,SAAS,IAAIgB,QAAQ,CAAC9D,MAAM,GAAG,CAAC,EAAE;MACjD;MACA,MAAM0E,UAAU,GAAG,QAAQ;MAE3B;MACA;MACA;MACA;MAEA;MAEA,MAAMC,WAAW,GAAG,CAClB,cAAc,EACd,mBAAmB,EACnB,YAAY,EACZ,UAAU,EACV,UAAU,EACV,SAAS,EACT,aAAa,CACd;MACD;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA,MAAMC,iBAAiB,GAAQ,EAAE;MAEjC;MACA,MAAMC,UAAU,GAAQ,EAAE;MAE1B;MACAzU,IAAI,CAAC0T,QAAQ,EAAGgB,OAAY,IAAI;QAC9B;QACA,MAAMC,QAAQ,GAAGC,KAAK,CAACL,WAAW,CAAC3E,MAAM,CAAC,CAACiF,IAAI,CAAC,IAAI,CAAC;QACrDF,QAAQ,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACI,gBAAgB;QACtCH,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAACxJ,UAAU,CAACiJ,eAAe,CAACM,OAAO,CAACK,UAAU,CAAC;QACjE;QACAR,WAAW,CAACS,OAAO,CAAC,CAACrF,GAAQ,EAAEsF,CAAS,KAAI;UAC1C,MAAMC,aAAa,GAAGD,CAAC,CAAC,CAAC;UACzB,QAAQtF,GAAG;YACT,KAAK,cAAc;cACjBgF,QAAQ,CAACO,aAAa,CAAC,GAAGR,OAAO,CAAC/P,WAAW;cAC7C;YACF,KAAK,mBAAmB;cACtBgQ,QAAQ,CAACO,aAAa,CAAC,GAAGR,OAAO,CAACzO,qBAAqB;cACvD;YACF,KAAK,YAAY;cACf0O,QAAQ,CAACO,aAAa,CAAC,GAAG,IAAI,CAAC/J,UAAU,CAAC5E,UAAU,CAACmO,OAAO,CAACjO,gBAAgB,CAAC;cAC9E;YACF,KAAK,UAAU;cACbkO,QAAQ,CAACO,aAAa,CAAC,GAAG,IAAI,CAAC/J,UAAU,CAAC5E,UAAU,CAACmO,OAAO,CAACzN,cAAc,CAAC;cAC5E;YACF,KAAK,UAAU;cACb0N,QAAQ,CAACO,aAAa,CAAC,GAAGR,OAAO,CAACnN,eAAe;cACjD;YACF,KAAK,SAAS;cACZoN,QAAQ,CAACO,aAAa,CAAC,GAAGR,OAAO,CAAC7M,0BAA0B;cAC5D;YACF,KAAK,aAAa;cAChB8M,QAAQ,CAACO,aAAa,CAAC,GAAGR,OAAO,CAACvM,eAAe;cACjD;YACF;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACF;QACF,CAAC,CAAC;QAEFsM,UAAU,CAACrC,IAAI,CAACuC,QAAQ,CAAC;MAC3B,CAAC,CAAC;MAEF;MACA,MAAMQ,OAAO,GAAGZ,WAAW,CAAC7E,GAAG,CAAC,CAAC0F,MAAM,EAAEC,KAAK,MAAM;QAClDC,EAAE,EAAED,KAAK,GAAG,CAAC;QACbxI,KAAK,EAAE;OACR,CAAC,CAAC;MAEH;MACA,IAAI,CAAClC,eAAe,CAAC4K,aAAa,CAChCjB,UAAU,EACVC,WAAW,EACXE,UAAU,EACVU;MACA;MACA;OACD;IACH,CAAC,MAAM;MACL,MAAMK,OAAO,GAAG,2CAA2C;MAC3D;IACF;EACF;EACA;EACOC,QAAQA,CAAA;IACb,MAAMC,QAAQ,GAAG;MACf1J,SAAS,EAAE,IAAI,CAACI,YAAY;MAC5BF,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,iBAAiB,EAAE,IAAI,CAACA;KACzB;IAED;IACA,IAAI,CAACnB,kBAAkB,CAAC2K,kBAAkB,CAAC;MACzC7G,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,IAAI,CAAChK,SAAS,CAACC,MAAM;MAC7BiH,UAAU,EAAEyJ,QAAQ,CAAC1J,SAAS;MAC9BE,aAAa,EAAEwJ,QAAQ,CAACxJ,aAAa;MACrC0J,QAAQ,EAAE,IAAI,CAAC7Q,SAAS,CAACC;KAC1B,CAAC;IAEFqJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEoH,QAAQ,CAAC;IAC/B,IAAI,CAAC3K,wBAAwB,CAAC8K,WAAW,CAAC,+BAA+B,EAAE,EAAE,CAAC;IAEtG;EACF;EAEQC,0BAA0BA,CAACJ,QAAa;IAC9C,MAAM7G,MAAM,GAAG;MACbC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,IAAI,CAAChK,SAAS,CAACC,MAAM;MAC7BiH,UAAU,EAAEyJ,QAAQ,CAAC1J,SAAS;MAC9BE,aAAa,EAAEwJ,QAAQ,CAACxJ,aAAa;MACrC0J,QAAQ,EAAE,IAAI,CAAC7Q,SAAS,CAACC;KAC1B;IAED,IAAI,CAACgG,kBAAkB,CAAC+K,gBAAgB,CAAClH,MAAM,CAAC,CAACI,SAAS,CAAC;MACzDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,KAAK,KAAK,EAAE;UAC9Bf,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEa,QAAQ,CAAC;UAE1C,IAAI,CAACpE,wBAAwB,CAAC8K,WAAW,CAAC,oCAAoC,EAAE,EAAE,CAAC;UACrG;QACF,CAAC,MAAM;UACLxH,OAAO,CAACoB,KAAK,CAAC,iCAAiC,EAAEN,QAAQ,CAACqG,OAAO,CAAC;UAChD,IAAI,CAACzK,wBAAwB,CAACiL,SAAS,CAAC,gCAAgC,EAAE,EAAE,CAAC;UAE/F;QACF;MACF,CAAC;MACDvG,KAAK,EAAGA,KAAK,IAAI;QACfpB,OAAO,CAACoB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACjC,IAAI,CAAC1E,wBAAwB,CAACiL,SAAS,CAAC,8BAA8B,EAAE,EAAE,CAAC;QAE/F;MACF;KACD,CAAC;EACJ;EAEQC,iBAAiBA,CAAA;IACvB;IACA,MAAMC,YAAY,GAAG;MACnBpH,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,IAAI,CAAChK,SAAS,CAACC;KACxB;IAED,IAAI,CAACgG,kBAAkB,CAACmL,gBAAgB,CAACD,YAAY,CAAC,CAACjH,SAAS,CAAC;MAC/DC,IAAI,EAAGC,QAAQ,IAAI;QACjBd,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEa,QAAQ,CAAC;QACnD;QACA,IAAI,CAAC2G,0BAA0B,CAAC;UAC9B9J,SAAS,EAAE,EAAE;UACbE,aAAa,EAAE,IAAI,CAACI,cAAc;UAClCH,iBAAiB,EAAE,IAAI,CAACG;SACzB,CAAC;MACJ,CAAC;MACDmD,KAAK,EAAGA,KAAK,IAAI;QACfpB,OAAO,CAACoB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD;QACA,IAAI,CAACqG,0BAA0B,CAAC;UAC9B9J,SAAS,EAAE,EAAE;UACbE,aAAa,EAAE,IAAI,CAACI,cAAc;UAClCH,iBAAiB,EAAE,IAAI,CAACG;SACzB,CAAC;MACJ;KACD,CAAC;EACJ;EAEO1J,UAAUA,CAAA;IACfyL,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IAEpD;IACA,IAAI,CAACtC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAE3B;IACA,IAAI,CAACnB,kBAAkB,CAACoL,qBAAqB,CAAC,UAAU,CAAC;IAEzD;IACA,IAAI,CAACC,sBAAsB,EAAE;IAE7B;IACA,IAAI,CAACnL,GAAG,CAAC+I,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAAC7I,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAACoI,OAAO,EAAE;IACrB;IAEA;IACAnF,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C;EACF;EACQ+H,sBAAsBA,CAAA;IAC5BhI,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAE/C;IACA,IAAI,CAAClC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAC3C,IAAI,CAACJ,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;IAE7C;IACA,IAAI,CAACY,IAAI,GAAG,CAAC;MAAEP,KAAK,EAAE,iBAAiB;MAAEQ,GAAG,EAAE;IAAM,CAAE,CAAC;IACvD,IAAI,CAAChK,IAAI,CAACoK,OAAO,GAAG,iBAAiB;IACrC,IAAI,CAACpK,IAAI,CAACqK,QAAQ,GAAG,MAAM;IAE3B;IACA,IAAI,CAACrK,IAAI,CAACkK,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IAEb;IACA,IAAI,CAACjC,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACE,aAAa,GAAG,EAAE;IAEvB;IACA,IAAI,CAAClI,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAAC/B,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAACuC,mBAAmB,GAAG,KAAK;IAEhCmK,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;MAC9BlC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCY,IAAI,EAAE,IAAI,CAACA,IAAI;MACf1B,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB7J,UAAU,EAAE,IAAI,CAACA;KAClB,CAAC;IAEF;IACA,IAAI,IAAI,CAACyJ,IAAI,EAAE;MACb;MACA,IAAI,CAACA,IAAI,CAACI,MAAM,GAAG;QAAEC,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAE,CAAE;MAEhD;MACA,IAAI,CAACN,IAAI,CAAC8B,IAAI,GAAG,CAAC;QAAEP,KAAK,EAAE,iBAAiB;QAAEQ,GAAG,EAAE;MAAM,CAAE,CAAC;MAE5D;MACA,IAAI,CAAC/B,IAAI,CAACkL,OAAO,CAACtB,OAAO,CAAEuB,MAAW,IAAI;QACxC,IAAIA,MAAM,CAAC5J,KAAK,IAAI4J,MAAM,CAAC5J,KAAK,KAAK,QAAQ,EAAE;UAC7C4J,MAAM,CAACC,MAAM,GAAG,KAAK;QACvB;MACF,CAAC,CAAC;MAEF;MACA,IAAI,CAACpL,IAAI,CAACqC,IAAI,GAAG,CAAC;MAClB,IAAI,CAACrC,IAAI,CAACyI,QAAQ,GAAG,IAAI,CAAC1Q,IAAI,CAACiK,IAAI;IACrC;IAEA;IACA,IAAI,CAAClC,GAAG,CAAC+I,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAAC7I,IAAI,EAAE;MACbiF,UAAU,CAAC,MAAK;QACd,IAAI,CAACjF,IAAI,CAACoI,OAAO,EAAE;QACnB;QACA,IAAI,CAACpI,IAAI,CAACqL,KAAK,EAAE;MACnB,CAAC,EAAE,GAAG,CAAC;IACT;IAEA;IACA,IAAI,CAACzI,SAAS,EAAE;EAClB;EACA;EACO5L,GAAGA,CAAA;IACR,IAAI,CAACgD,IAAI,CAAC,CAAC,CAAC;EACd;EAEOsR,IAAIA,CAAChS,SAAiB;IAC3B,IAAI,CAACgG,MAAM,CAACiM,QAAQ,CAAC,CAAC,gBAAgB,EAAEjS,SAAS,CAAC,CAAC;EACrD;EAEOU,IAAIA,CAACV,SAAiB;IAC3B,IAAIA,SAAS,IAAI,CAAC,EAAE;MAClB;MACA,IAAI,CAACoG,eAAe,CAACqF,cAAc,CAACjB,IAAI,CAAC,IAAI,CAAC;MAE9C;MACA,MAAM0H,QAAQ,GAAG,IAAI,CAAC3L,YAAY,CAAC4L,IAAI,CAAC5W,qBAAqB,EAAE;QAC7DmN,IAAI,EAAE,IAAI;QACV0J,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;OACX,CAAC;MAEFH,QAAQ,CAACI,iBAAiB,CAAC1B,EAAE,GAAG5Q,SAAS;MACzCkS,QAAQ,CAACI,iBAAiB,CAACC,OAAO,GAAG,IAAI;MAEzCL,QAAQ,CAACI,iBAAiB,CAACE,SAAS,CAACjI,SAAS,CAAEkI,MAAe,IAAI;QACjE,IAAIA,MAAM,EAAE;UACV;UACA,IAAI,CAACnJ,SAAS,EAAE;QAClB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,CAACtD,MAAM,CAACiM,QAAQ,CAAC,CAAC,gBAAgB,EAAEjS,SAAS,CAAC,CAAC;IACrD;EACF;EAEO0S,MAAMA,CAAC1S,SAAiB;IAC7B;IAEA;IACC,IAAI,CAACmG,eAAe,CAACwM,aAAa,CAAC;MAAE3S;IAAS,CAAE,CAAC,CAACuK,SAAS,CAAC;MACzDC,IAAI,EAAGC,QAAa,IAAI;QAEtBd,OAAO,CAACC,GAAG,CAAC,UAAU,EAACa,QAAQ,CAAC;QAChC,IAAI,CAACA,QAAQ,CAACC,OAAO,EAAE;UACL,IAAI,CAACrE,wBAAwB,CAAC8K,WAAW,CAAC,8BAA8B,EAAE,EAAE,CAAC;UAE7F;UACA,IAAI,CAAC7H,SAAS,EAAE;QAClB;MACF,CAAC;MACDyB,KAAK,EAAGA,KAAU,IAAI;QACpBpB,OAAO,CAACoB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACnB,IAAI,CAAC1E,wBAAwB,CAACiL,SAAS,CAAC,wBAAwB,EAAE,EAAE,CAAC;QAEvF;MACF;KACD,CAAC;EACN;EAEA;EACOsB,kBAAkBA,CAACL,OAAY;IACpC,OAAO,GAAGA,OAAO,CAACM,gBAAgB,IAAI,EAAE,IACtCN,OAAO,CAACO,eAAe,IAAI,EAC7B,EAAE,CAAC3F,IAAI,EAAE;EACX;EAEO4F,aAAaA,CAACR,OAAY;IAC/B,OAAOA,OAAO,CAACS,aAAa,EAAEC,UAAU,IAAI,EAAE;EAChD;EAEOpR,UAAUA,CAACqR,UAAkB;IAClC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B,MAAMC,IAAI,GAAG,IAAIxD,IAAI,CAACuD,UAAU,CAAC;IACjC,MAAME,KAAK,GAAGC,MAAM,CAACF,IAAI,CAACG,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACF,IAAI,CAACM,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,MAAMG,IAAI,GAAGP,IAAI,CAACQ,WAAW,EAAE;IAC/B,OAAO,GAAGP,KAAK,IAAII,GAAG,IAAIE,IAAI,EAAE;EAClC;EAEOE,cAAcA,CAAC3U,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,uBAAuB;IAC3C,MAAMiO,GAAG,GAAGjO,MAAM,CAAC4U,WAAW,EAAE;IAChC,IAAI3G,GAAG,KAAK,SAAS,EAAE,OAAO,qBAAqB;IACnD,IAAIA,GAAG,KAAK,WAAW,EAAE,OAAO,qBAAqB;IACrD,IAAIA,GAAG,KAAK,sBAAsB,EAAE,OAAO,kBAAkB;IAC7D,IAAIA,GAAG,KAAK,mBAAmB,EAAE,OAAO,kBAAkB;IAC1D,OAAO,uBAAuB;EAChC;EACAnN,SAASA,CAAC+T,OAAY,EAAE9T,SAAc,EAAEC,WAAgB,EAAEL,QAAa;IACrE,IAAI,CAACK,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAIkS,QAAQ,GAAG,IAAI,CAAC3L,YAAY,CAAC4L,IAAI,CAAC3W,2BAA2B,EAAE;MAC/DkN,IAAI,EAAE,IAAI;MACV0J,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;KACX,CAAC;IACJ,IAAI,IAAI,CAAChS,SAAS,CAACQ,QAAQ,KAAK,OAAO,EAAE;MACvC,IAAIjB,QAAQ,CAACQ,WAAW,EAAE;QACxB;QACA8R,QAAQ,CAACI,iBAAiB,CAACyB,SAAS,GAAG,IAAI;QAC3C7B,QAAQ,CAACI,iBAAiB,CAAC0B,WAAW,GAAG,iDAAiD,GAAG/T,WAAW,GAAG,GAAG;QAC9GiS,QAAQ,CAACI,iBAAiB,CAAC2B,gBAAgB,GAAG,QAAQ;QACtD/B,QAAQ,CAACI,iBAAiB,CAAC4B,gBAAgB,GAAG,KAAK;QACnDhC,QAAQ,CAACI,iBAAiB,CAACpK,KAAK,GAAG,gBAAgB;QACnDgK,QAAQ,CAACI,iBAAiB,CAACE,SAAS,CAACjI,SAAS,CAAEkI,MAAW,IAAI;UAC7D,IAAIA,MAAM,CAAC0B,OAAO,KAAK,IAAI,EAAE;YAC3B;YACA,IAAI,CAACzB,MAAM,CAAC,IAAI,CAAC1S,SAAS,CAAC;UAC7B;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLkS,QAAQ,CAACI,iBAAiB,CAACyB,SAAS,GAAG,KAAK;QAC5C7B,QAAQ,CAACI,iBAAiB,CAAC0B,WAAW,GAAG,gBAAgB,GAAG/T,WAAW,GAAG,6EAA6E;QACvJiS,QAAQ,CAACI,iBAAiB,CAAC2B,gBAAgB,GAAG,QAAQ;QACtD/B,QAAQ,CAACI,iBAAiB,CAAC4B,gBAAgB,GAAG,IAAI;QAClDhC,QAAQ,CAACI,iBAAiB,CAACpK,KAAK,GAAG,SAAS;QAC5CgK,QAAQ,CAACI,iBAAiB,CAACE,SAAS,CAACjI,SAAS,CAAEkI,MAAW,IAAI,CAC/D,CAAC,CAAC;MACJ;IACF,CAAC,MAAM,IAAI,IAAI,CAACpS,SAAS,CAACQ,QAAQ,KAAK,aAAa,EAAE;MACpD,IAAIjB,QAAQ,CAACQ,WAAW,EAAE;QACxB8R,QAAQ,CAACI,iBAAiB,CAACyB,SAAS,GAAG,IAAI;QAC3C7B,QAAQ,CAACI,iBAAiB,CAAC0B,WAAW,GAAG,iDAAiD,GAAG/T,WAAW,GAAG,GAAG;QAC9GiS,QAAQ,CAACI,iBAAiB,CAAC2B,gBAAgB,GAAG,QAAQ;QACtD/B,QAAQ,CAACI,iBAAiB,CAAC4B,gBAAgB,GAAG,KAAK;QACnDhC,QAAQ,CAACI,iBAAiB,CAACpK,KAAK,GAAG,gBAAgB;QACnDgK,QAAQ,CAACI,iBAAiB,CAACE,SAAS,CAACjI,SAAS,CAAEkI,MAAW,IAAI;UAC7D,IAAIA,MAAM,CAAC0B,OAAO,KAAK,IAAI,EAAE;YAC3B;YACA,IAAI,CAACzB,MAAM,CAAC,IAAI,CAAC1S,SAAS,CAAC;UAC7B;QACF,CAAC,CAAC;MACJ;IACF;EAGF;EAEAyF,aAAaA,CAAA;IACX;IACA,IAAI,CAACiN,MAAM,CAAC,IAAI,CAAC1S,SAAS,CAAC;IAC3B;EACF;EAEAoU,cAAcA,CAAA;IACZ;IACA;IACAzK,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;EACvC;EAEA;EACA9L,mBAAmBA,CAACmP,KAAa;IAC/B,IAAI,CAACpO,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;IACpD8K,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC/K,mBAAmB,CAAC;IAEhE,IAAI,IAAI,CAACA,mBAAmB,IAAIoO,KAAK,EAAE;MACrC,MAAMoH,MAAM,GAAGpH,KAAK,CAACqH,MAAqB;MAC1C,MAAMC,IAAI,GAAGF,MAAM,CAACG,qBAAqB,EAAE;MAC3C,IAAI,CAAC7X,WAAW,GAAG4X,IAAI,CAACE,MAAM,GAAGC,MAAM,CAACC,OAAO;MAC/C,IAAI,CAAC/X,YAAY,GAAG2X,IAAI,CAACK,IAAI,GAAGF,MAAM,CAACG,OAAO;MAC9ClL,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACjN,WAAW,EAAE,IAAI,CAACC,YAAY,CAAC;IACxE;EACF;EAEAN,kBAAkBA,CAAA;IAChB,IAAI,CAACuC,mBAAmB,GAAG,KAAK;IAChC8K,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;EACtC;EAGAkL,eAAeA,CAAC7H,KAAY;IAC1B,MAAMqH,MAAM,GAAGrH,KAAK,CAACqH,MAAqB;IAC1C,MAAMS,QAAQ,GAAGT,MAAM,CAACU,OAAO,CAAC,kBAAkB,CAAC;IACnD,IAAI,CAACD,QAAQ,IAAI,IAAI,CAAClW,mBAAmB,EAAE;MACzC,IAAI,CAACvC,kBAAkB,EAAE;IAC3B;EACF;;qCA5yCWyJ,oBAAoB,EAAAtK,EAAA,CAAAwZ,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA1Z,EAAA,CAAAwZ,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5Z,EAAA,CAAAwZ,iBAAA,CAAAC,EAAA,CAAAI,cAAA,GAAA7Z,EAAA,CAAAwZ,iBAAA,CAAAM,EAAA,CAAAC,eAAA,GAAA/Z,EAAA,CAAAwZ,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAja,EAAA,CAAAwZ,iBAAA,CAAAU,EAAA,CAAAC,wBAAA,GAAAna,EAAA,CAAAwZ,iBAAA,CAAAY,EAAA,CAAAC,kBAAA,GAAAra,EAAA,CAAAwZ,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAva,EAAA,CAAAwZ,iBAAA,CAAAxZ,EAAA,CAAAwa,iBAAA,GAAAxa,EAAA,CAAAwZ,iBAAA,CAAAiB,EAAA,CAAAC,UAAA;EAAA;;UAApBpQ,oBAAoB;IAAAqQ,SAAA;IAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;QAApB9a,EAAA,CAAAI,UAAA,mBAAA4a,8CAAA1a,MAAA;UAAA,OAAAya,GAAA,CAAA1B,eAAA,CAAA/Y,MAAA,CAAuB;QAAA,UAAAN,EAAA,CAAAib,iBAAA,CAAH;;;;;;;;;QCxCjCjb,EAAA,CAAAsC,UAAA,IAAA4Y,mCAAA,iBAAqE;QAUnElb,EADF,CAAAC,cAAA,aAA4B,uBA6BzB;QADCD,EAXA,CAAAI,UAAA,6BAAA+a,oEAAA7a,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA6a,GAAA;UAAA,OAAApb,EAAA,CAAAc,WAAA,CAAmBia,GAAA,CAAAjI,iBAAA,CAAAxS,MAAA,CAAyB;QAAA,EAAC,0BAAA+a,iEAAA/a,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA6a,GAAA;UAAA,OAAApb,EAAA,CAAAc,WAAA,CAQ7Bia,GAAA,CAAAhJ,YAAA,CAAAzR,MAAA,CAAoB;QAAA,EAAC,wBAAAgb,+DAAAhb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA6a,GAAA;UAAA,OAAApb,EAAA,CAAAc,WAAA,CACvBia,GAAA,CAAAvI,UAAA,CAAAlS,MAAA,CAAkB;QAAA,EAAC,wBAAAib,+DAAAjb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA6a,GAAA;UAAA,OAAApb,EAAA,CAAAc,WAAA,CACnBia,GAAA,CAAA1I,YAAA,CAAA/R,MAAA,CAAoB;QAAA,EAAC,oCAAAkb,2EAAAlb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA6a,GAAA;UAAA,OAAApb,EAAA,CAAAc,WAAA,CACTia,GAAA,CAAArI,sBAAA,CAAApS,MAAA,CAA8B;QAAA,EAAC;QAuazDN,EAraA,CAAAsC,UAAA,IAAAmZ,2CAAA,2BAAsC,IAAAC,2CAAA,yBAoFA,IAAAC,4CAAA,2BAqCW,IAAAC,2CAAA,0BA4ST;QAQ5C5b,EADE,CAAAG,YAAA,EAAa,EACT;QAmCNH,EAhCA,CAAAsC,UAAA,IAAAuZ,2CAAA,iCAAA7b,EAAA,CAAA8b,sBAAA,CAAoC,KAAAC,4CAAA,iCAAA/b,EAAA,CAAA8b,sBAAA,CAgCO;;;QAvfrC9b,EAAA,CAAA8C,UAAA,SAAAiY,GAAA,CAAAxR,OAAA,IAAAwR,GAAA,CAAAvR,SAAA,CAA0B;QAY5BxJ,EAAA,CAAA4C,SAAA,GAAiB;QAqBjB5C,EArBA,CAAA8C,UAAA,SAAAiY,GAAA,CAAA5P,QAAA,CAAiB,aAAA4P,GAAA,CAAA/X,IAAA,CAAAiK,IAAA,CACK,SAAA8N,GAAA,CAAAhO,IAAA,CACT,aAAA/M,EAAA,CAAAgc,eAAA,KAAAC,GAAA,EAAAjc,EAAA,CAAA2G,eAAA,KAAAuV,GAAA,GAOX,UAAAnB,GAAA,CAAA/X,IAAA,CAAAC,aAAA,CAC0B,aAAAjD,EAAA,CAAA2G,eAAA,KAAAwV,GAAA,EACsB,oBAC/B,eAAAnc,EAAA,CAAA2G,eAAA,KAAAyV,GAAA,EACoC,qBAEnC,oBAED,eACL,SAAArB,GAAA,CAAAzN,IAAA,CACD,WAAAyN,GAAA,CAAA1P,MAAA,CACI,eAAArL,EAAA,CAAA2G,eAAA,KAAA0V,GAAA,EACc;QA+HErc,EAAA,CAAA4C,SAAA,GAAc;QAAd5C,EAAA,CAAA8C,UAAA,YAAAiY,GAAA,CAAA7O,WAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}