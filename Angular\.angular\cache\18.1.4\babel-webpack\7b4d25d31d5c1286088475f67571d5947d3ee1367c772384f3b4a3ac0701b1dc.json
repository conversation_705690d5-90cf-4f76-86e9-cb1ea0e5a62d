{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.iterator = exports.getSymbolIterator = void 0;\nfunction getSymbolIterator() {\n  if (typeof Symbol !== 'function' || !Symbol.iterator) {\n    return '@@iterator';\n  }\n  return Symbol.iterator;\n}\nexports.getSymbolIterator = getSymbolIterator;\nexports.iterator = getSymbolIterator();", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "iterator", "getSymbolIterator", "Symbol"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/symbol/iterator.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.iterator = exports.getSymbolIterator = void 0;\nfunction getSymbolIterator() {\n    if (typeof Symbol !== 'function' || !Symbol.iterator) {\n        return '@@iterator';\n    }\n    return Symbol.iterator;\n}\nexports.getSymbolIterator = getSymbolIterator;\nexports.iterator = getSymbolIterator();\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,QAAQ,GAAGF,OAAO,CAACG,iBAAiB,GAAG,KAAK,CAAC;AACrD,SAASA,iBAAiBA,CAAA,EAAG;EACzB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,CAACA,MAAM,CAACF,QAAQ,EAAE;IAClD,OAAO,YAAY;EACvB;EACA,OAAOE,MAAM,CAACF,QAAQ;AAC1B;AACAF,OAAO,CAACG,iBAAiB,GAAGA,iBAAiB;AAC7CH,OAAO,CAACE,QAAQ,GAAGC,iBAAiB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}