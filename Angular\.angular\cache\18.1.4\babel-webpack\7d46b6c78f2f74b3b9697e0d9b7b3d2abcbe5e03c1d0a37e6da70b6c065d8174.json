{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.switchScan = void 0;\nvar switchMap_1 = require(\"./switchMap\");\nvar lift_1 = require(\"../util/lift\");\nfunction switchScan(accumulator, seed) {\n  return lift_1.operate(function (source, subscriber) {\n    var state = seed;\n    switchMap_1.switchMap(function (value, index) {\n      return accumulator(state, value, index);\n    }, function (_, innerValue) {\n      return state = innerValue, innerValue;\n    })(source).subscribe(subscriber);\n    return function () {\n      state = null;\n    };\n  });\n}\nexports.switchScan = switchScan;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "switchScan", "switchMap_1", "require", "lift_1", "accumulator", "seed", "operate", "source", "subscriber", "state", "switchMap", "index", "_", "innerValue", "subscribe"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/switchScan.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.switchScan = void 0;\nvar switchMap_1 = require(\"./switchMap\");\nvar lift_1 = require(\"../util/lift\");\nfunction switchScan(accumulator, seed) {\n    return lift_1.operate(function (source, subscriber) {\n        var state = seed;\n        switchMap_1.switchMap(function (value, index) { return accumulator(state, value, index); }, function (_, innerValue) { return ((state = innerValue), innerValue); })(source).subscribe(subscriber);\n        return function () {\n            state = null;\n        };\n    });\n}\nexports.switchScan = switchScan;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAIC,WAAW,GAAGC,OAAO,CAAC,aAAa,CAAC;AACxC,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,SAASF,UAAUA,CAACI,WAAW,EAAEC,IAAI,EAAE;EACnC,OAAOF,MAAM,CAACG,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,KAAK,GAAGJ,IAAI;IAChBJ,WAAW,CAACS,SAAS,CAAC,UAAUX,KAAK,EAAEY,KAAK,EAAE;MAAE,OAAOP,WAAW,CAACK,KAAK,EAAEV,KAAK,EAAEY,KAAK,CAAC;IAAE,CAAC,EAAE,UAAUC,CAAC,EAAEC,UAAU,EAAE;MAAE,OAASJ,KAAK,GAAGI,UAAU,EAAGA,UAAU;IAAG,CAAC,CAAC,CAACN,MAAM,CAAC,CAACO,SAAS,CAACN,UAAU,CAAC;IAClM,OAAO,YAAY;MACfC,KAAK,GAAG,IAAI;IAChB,CAAC;EACL,CAAC,CAAC;AACN;AACAX,OAAO,CAACE,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}