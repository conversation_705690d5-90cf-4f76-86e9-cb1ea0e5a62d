{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.mergeScan = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar mergeInternals_1 = require(\"./mergeInternals\");\nfunction mergeScan(accumulator, seed, concurrent) {\n  if (concurrent === void 0) {\n    concurrent = Infinity;\n  }\n  return lift_1.operate(function (source, subscriber) {\n    var state = seed;\n    return mergeInternals_1.mergeInternals(source, subscriber, function (value, index) {\n      return accumulator(state, value, index);\n    }, concurrent, function (value) {\n      state = value;\n    }, false, undefined, function () {\n      return state = null;\n    });\n  });\n}\nexports.mergeScan = mergeScan;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "mergeScan", "lift_1", "require", "mergeInternals_1", "accumulator", "seed", "concurrent", "Infinity", "operate", "source", "subscriber", "state", "mergeInternals", "index", "undefined"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/mergeScan.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeScan = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar mergeInternals_1 = require(\"./mergeInternals\");\nfunction mergeScan(accumulator, seed, concurrent) {\n    if (concurrent === void 0) { concurrent = Infinity; }\n    return lift_1.operate(function (source, subscriber) {\n        var state = seed;\n        return mergeInternals_1.mergeInternals(source, subscriber, function (value, index) { return accumulator(state, value, index); }, concurrent, function (value) {\n            state = value;\n        }, false, undefined, function () { return (state = null); });\n    });\n}\nexports.mergeScan = mergeScan;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,gBAAgB,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AAClD,SAASF,SAASA,CAACI,WAAW,EAAEC,IAAI,EAAEC,UAAU,EAAE;EAC9C,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAGC,QAAQ;EAAE;EACpD,OAAON,MAAM,CAACO,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,KAAK,GAAGN,IAAI;IAChB,OAAOF,gBAAgB,CAACS,cAAc,CAACH,MAAM,EAAEC,UAAU,EAAE,UAAUX,KAAK,EAAEc,KAAK,EAAE;MAAE,OAAOT,WAAW,CAACO,KAAK,EAAEZ,KAAK,EAAEc,KAAK,CAAC;IAAE,CAAC,EAAEP,UAAU,EAAE,UAAUP,KAAK,EAAE;MAC1JY,KAAK,GAAGZ,KAAK;IACjB,CAAC,EAAE,KAAK,EAAEe,SAAS,EAAE,YAAY;MAAE,OAAQH,KAAK,GAAG,IAAI;IAAG,CAAC,CAAC;EAChE,CAAC,CAAC;AACN;AACAb,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}