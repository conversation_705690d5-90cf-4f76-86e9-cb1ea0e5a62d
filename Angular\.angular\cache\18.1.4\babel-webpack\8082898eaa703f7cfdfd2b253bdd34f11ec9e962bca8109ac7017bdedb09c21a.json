{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.animationFrame = exports.animationFrameScheduler = void 0;\nvar AnimationFrameAction_1 = require(\"./AnimationFrameAction\");\nvar AnimationFrameScheduler_1 = require(\"./AnimationFrameScheduler\");\nexports.animationFrameScheduler = new AnimationFrameScheduler_1.AnimationFrameScheduler(AnimationFrameAction_1.AnimationFrameAction);\nexports.animationFrame = exports.animationFrameScheduler;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "animationFrame", "animationFrameScheduler", "AnimationFrameAction_1", "require", "AnimationFrameScheduler_1", "AnimationFrameScheduler", "AnimationFrameAction"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/scheduler/animationFrame.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.animationFrame = exports.animationFrameScheduler = void 0;\nvar AnimationFrameAction_1 = require(\"./AnimationFrameAction\");\nvar AnimationFrameScheduler_1 = require(\"./AnimationFrameScheduler\");\nexports.animationFrameScheduler = new AnimationFrameScheduler_1.AnimationFrameScheduler(AnimationFrameAction_1.AnimationFrameAction);\nexports.animationFrame = exports.animationFrameScheduler;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,uBAAuB,GAAG,KAAK,CAAC;AACjE,IAAIC,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAC9D,IAAIC,yBAAyB,GAAGD,OAAO,CAAC,2BAA2B,CAAC;AACpEL,OAAO,CAACG,uBAAuB,GAAG,IAAIG,yBAAyB,CAACC,uBAAuB,CAACH,sBAAsB,CAACI,oBAAoB,CAAC;AACpIR,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}