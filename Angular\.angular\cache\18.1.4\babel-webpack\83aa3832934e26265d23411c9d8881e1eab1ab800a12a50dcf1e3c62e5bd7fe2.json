{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.delay = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar delayWhen_1 = require(\"./delayWhen\");\nvar timer_1 = require(\"../observable/timer\");\nfunction delay(due, scheduler) {\n  if (scheduler === void 0) {\n    scheduler = async_1.asyncScheduler;\n  }\n  var duration = timer_1.timer(due, scheduler);\n  return delayWhen_1.delayWhen(function () {\n    return duration;\n  });\n}\nexports.delay = delay;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "delay", "async_1", "require", "delayWhen_1", "timer_1", "due", "scheduler", "asyncScheduler", "duration", "timer", "<PERSON><PERSON>hen"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/delay.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.delay = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar delayWhen_1 = require(\"./delayWhen\");\nvar timer_1 = require(\"../observable/timer\");\nfunction delay(due, scheduler) {\n    if (scheduler === void 0) { scheduler = async_1.asyncScheduler; }\n    var duration = timer_1.timer(due, scheduler);\n    return delayWhen_1.delayWhen(function () { return duration; });\n}\nexports.delay = delay;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,KAAK,GAAG,KAAK,CAAC;AACtB,IAAIC,OAAO,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAC3C,IAAIC,WAAW,GAAGD,OAAO,CAAC,aAAa,CAAC;AACxC,IAAIE,OAAO,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AAC5C,SAASF,KAAKA,CAACK,GAAG,EAAEC,SAAS,EAAE;EAC3B,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IAAEA,SAAS,GAAGL,OAAO,CAACM,cAAc;EAAE;EAChE,IAAIC,QAAQ,GAAGJ,OAAO,CAACK,KAAK,CAACJ,GAAG,EAAEC,SAAS,CAAC;EAC5C,OAAOH,WAAW,CAACO,SAAS,CAAC,YAAY;IAAE,OAAOF,QAAQ;EAAE,CAAC,CAAC;AAClE;AACAV,OAAO,CAACE,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}