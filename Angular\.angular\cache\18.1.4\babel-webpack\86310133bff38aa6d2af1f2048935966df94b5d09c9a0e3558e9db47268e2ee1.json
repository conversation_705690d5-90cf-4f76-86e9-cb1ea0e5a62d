{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.switchMapTo = void 0;\nvar switchMap_1 = require(\"./switchMap\");\nvar isFunction_1 = require(\"../util/isFunction\");\nfunction switchMapTo(innerObservable, resultSelector) {\n  return isFunction_1.isFunction(resultSelector) ? switchMap_1.switchMap(function () {\n    return innerObservable;\n  }, resultSelector) : switchMap_1.switchMap(function () {\n    return innerObservable;\n  });\n}\nexports.switchMapTo = switchMapTo;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "switchMapTo", "switchMap_1", "require", "isFunction_1", "innerObservable", "resultSelector", "isFunction", "switchMap"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/switchMapTo.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.switchMapTo = void 0;\nvar switchMap_1 = require(\"./switchMap\");\nvar isFunction_1 = require(\"../util/isFunction\");\nfunction switchMapTo(innerObservable, resultSelector) {\n    return isFunction_1.isFunction(resultSelector) ? switchMap_1.switchMap(function () { return innerObservable; }, resultSelector) : switchMap_1.switchMap(function () { return innerObservable; });\n}\nexports.switchMapTo = switchMapTo;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,WAAW,GAAG,KAAK,CAAC;AAC5B,IAAIC,WAAW,GAAGC,OAAO,CAAC,aAAa,CAAC;AACxC,IAAIC,YAAY,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AAChD,SAASF,WAAWA,CAACI,eAAe,EAAEC,cAAc,EAAE;EAClD,OAAOF,YAAY,CAACG,UAAU,CAACD,cAAc,CAAC,GAAGJ,WAAW,CAACM,SAAS,CAAC,YAAY;IAAE,OAAOH,eAAe;EAAE,CAAC,EAAEC,cAAc,CAAC,GAAGJ,WAAW,CAACM,SAAS,CAAC,YAAY;IAAE,OAAOH,eAAe;EAAE,CAAC,CAAC;AACpM;AACAN,OAAO,CAACE,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}