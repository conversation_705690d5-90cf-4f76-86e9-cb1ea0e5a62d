{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.concatMap = void 0;\nvar mergeMap_1 = require(\"./mergeMap\");\nvar isFunction_1 = require(\"../util/isFunction\");\nfunction concatMap(project, resultSelector) {\n  return isFunction_1.isFunction(resultSelector) ? mergeMap_1.mergeMap(project, resultSelector, 1) : mergeMap_1.mergeMap(project, 1);\n}\nexports.concatMap = concatMap;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "concatMap", "mergeMap_1", "require", "isFunction_1", "project", "resultSelector", "isFunction", "mergeMap"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/concatMap.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.concatMap = void 0;\nvar mergeMap_1 = require(\"./mergeMap\");\nvar isFunction_1 = require(\"../util/isFunction\");\nfunction concatMap(project, resultSelector) {\n    return isFunction_1.isFunction(resultSelector) ? mergeMap_1.mergeMap(project, resultSelector, 1) : mergeMap_1.mergeMap(project, 1);\n}\nexports.concatMap = concatMap;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,UAAU,GAAGC,OAAO,CAAC,YAAY,CAAC;AACtC,IAAIC,YAAY,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AAChD,SAASF,SAASA,CAACI,OAAO,EAAEC,cAAc,EAAE;EACxC,OAAOF,YAAY,CAACG,UAAU,CAACD,cAAc,CAAC,GAAGJ,UAAU,CAACM,QAAQ,CAACH,OAAO,EAAEC,cAAc,EAAE,CAAC,CAAC,GAAGJ,UAAU,CAACM,QAAQ,CAACH,OAAO,EAAE,CAAC,CAAC;AACtI;AACAN,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}