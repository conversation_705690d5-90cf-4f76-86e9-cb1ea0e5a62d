{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.publishLast = void 0;\nvar AsyncSubject_1 = require(\"../AsyncSubject\");\nvar ConnectableObservable_1 = require(\"../observable/ConnectableObservable\");\nfunction publishLast() {\n  return function (source) {\n    var subject = new AsyncSubject_1.AsyncSubject();\n    return new ConnectableObservable_1.ConnectableObservable(source, function () {\n      return subject;\n    });\n  };\n}\nexports.publishLast = publishLast;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "publishLast", "AsyncSubject_1", "require", "ConnectableObservable_1", "source", "subject", "AsyncSubject", "ConnectableObservable"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/publishLast.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.publishLast = void 0;\nvar AsyncSubject_1 = require(\"../AsyncSubject\");\nvar ConnectableObservable_1 = require(\"../observable/ConnectableObservable\");\nfunction publishLast() {\n    return function (source) {\n        var subject = new AsyncSubject_1.AsyncSubject();\n        return new ConnectableObservable_1.ConnectableObservable(source, function () { return subject; });\n    };\n}\nexports.publishLast = publishLast;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,WAAW,GAAG,KAAK,CAAC;AAC5B,IAAIC,cAAc,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAC/C,IAAIC,uBAAuB,GAAGD,OAAO,CAAC,qCAAqC,CAAC;AAC5E,SAASF,WAAWA,CAAA,EAAG;EACnB,OAAO,UAAUI,MAAM,EAAE;IACrB,IAAIC,OAAO,GAAG,IAAIJ,cAAc,CAACK,YAAY,CAAC,CAAC;IAC/C,OAAO,IAAIH,uBAAuB,CAACI,qBAAqB,CAACH,MAAM,EAAE,YAAY;MAAE,OAAOC,OAAO;IAAE,CAAC,CAAC;EACrG,CAAC;AACL;AACAP,OAAO,CAACE,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}