{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createFind = exports.find = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction find(predicate, thisArg) {\n  return lift_1.operate(createFind(predicate, thisArg, 'value'));\n}\nexports.find = find;\nfunction createFind(predicate, thisArg, emit) {\n  var findIndex = emit === 'index';\n  return function (source, subscriber) {\n    var index = 0;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var i = index++;\n      if (predicate.call(thisArg, value, i, source)) {\n        subscriber.next(findIndex ? i : value);\n        subscriber.complete();\n      }\n    }, function () {\n      subscriber.next(findIndex ? -1 : undefined);\n      subscriber.complete();\n    }));\n  };\n}\nexports.createFind = createFind;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "createFind", "find", "lift_1", "require", "OperatorSubscriber_1", "predicate", "thisArg", "operate", "emit", "findIndex", "source", "subscriber", "index", "subscribe", "createOperatorSubscriber", "i", "call", "next", "complete", "undefined"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/find.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createFind = exports.find = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction find(predicate, thisArg) {\n    return lift_1.operate(createFind(predicate, thisArg, 'value'));\n}\nexports.find = find;\nfunction createFind(predicate, thisArg, emit) {\n    var findIndex = emit === 'index';\n    return function (source, subscriber) {\n        var index = 0;\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            var i = index++;\n            if (predicate.call(thisArg, value, i, source)) {\n                subscriber.next(findIndex ? i : value);\n                subscriber.complete();\n            }\n        }, function () {\n            subscriber.next(findIndex ? -1 : undefined);\n            subscriber.complete();\n        }));\n    };\n}\nexports.createFind = createFind;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,UAAU,GAAGF,OAAO,CAACG,IAAI,GAAG,KAAK,CAAC;AAC1C,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,IAAIA,CAACI,SAAS,EAAEC,OAAO,EAAE;EAC9B,OAAOJ,MAAM,CAACK,OAAO,CAACP,UAAU,CAACK,SAAS,EAAEC,OAAO,EAAE,OAAO,CAAC,CAAC;AAClE;AACAR,OAAO,CAACG,IAAI,GAAGA,IAAI;AACnB,SAASD,UAAUA,CAACK,SAAS,EAAEC,OAAO,EAAEE,IAAI,EAAE;EAC1C,IAAIC,SAAS,GAAGD,IAAI,KAAK,OAAO;EAChC,OAAO,UAAUE,MAAM,EAAEC,UAAU,EAAE;IACjC,IAAIC,KAAK,GAAG,CAAC;IACbF,MAAM,CAACG,SAAS,CAACT,oBAAoB,CAACU,wBAAwB,CAACH,UAAU,EAAE,UAAUZ,KAAK,EAAE;MACxF,IAAIgB,CAAC,GAAGH,KAAK,EAAE;MACf,IAAIP,SAAS,CAACW,IAAI,CAACV,OAAO,EAAEP,KAAK,EAAEgB,CAAC,EAAEL,MAAM,CAAC,EAAE;QAC3CC,UAAU,CAACM,IAAI,CAACR,SAAS,GAAGM,CAAC,GAAGhB,KAAK,CAAC;QACtCY,UAAU,CAACO,QAAQ,CAAC,CAAC;MACzB;IACJ,CAAC,EAAE,YAAY;MACXP,UAAU,CAACM,IAAI,CAACR,SAAS,GAAG,CAAC,CAAC,GAAGU,SAAS,CAAC;MAC3CR,UAAU,CAACO,QAAQ,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;EACP,CAAC;AACL;AACApB,OAAO,CAACE,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}