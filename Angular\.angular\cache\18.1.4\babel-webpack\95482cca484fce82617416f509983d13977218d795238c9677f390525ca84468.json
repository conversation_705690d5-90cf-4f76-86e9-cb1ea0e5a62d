{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.schedulePromise = void 0;\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar observeOn_1 = require(\"../operators/observeOn\");\nvar subscribeOn_1 = require(\"../operators/subscribeOn\");\nfunction schedulePromise(input, scheduler) {\n  return innerFrom_1.innerFrom(input).pipe(subscribeOn_1.subscribeOn(scheduler), observeOn_1.observeOn(scheduler));\n}\nexports.schedulePromise = schedulePromise;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "schedulePromise", "innerFrom_1", "require", "observeOn_1", "subscribeOn_1", "input", "scheduler", "innerFrom", "pipe", "subscribeOn", "observeOn"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/scheduled/schedulePromise.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.schedulePromise = void 0;\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar observeOn_1 = require(\"../operators/observeOn\");\nvar subscribeOn_1 = require(\"../operators/subscribeOn\");\nfunction schedulePromise(input, scheduler) {\n    return innerFrom_1.innerFrom(input).pipe(subscribeOn_1.subscribeOn(scheduler), observeOn_1.observeOn(scheduler));\n}\nexports.schedulePromise = schedulePromise;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,eAAe,GAAG,KAAK,CAAC;AAChC,IAAIC,WAAW,GAAGC,OAAO,CAAC,yBAAyB,CAAC;AACpD,IAAIC,WAAW,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AACnD,IAAIE,aAAa,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AACvD,SAASF,eAAeA,CAACK,KAAK,EAAEC,SAAS,EAAE;EACvC,OAAOL,WAAW,CAACM,SAAS,CAACF,KAAK,CAAC,CAACG,IAAI,CAACJ,aAAa,CAACK,WAAW,CAACH,SAAS,CAAC,EAAEH,WAAW,CAACO,SAAS,CAACJ,SAAS,CAAC,CAAC;AACpH;AACAR,OAAO,CAACE,eAAe,GAAGA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}