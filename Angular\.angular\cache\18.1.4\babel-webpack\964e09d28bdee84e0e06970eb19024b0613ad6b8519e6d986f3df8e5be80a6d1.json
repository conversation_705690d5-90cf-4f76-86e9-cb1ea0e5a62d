{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.debounce = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar noop_1 = require(\"../util/noop\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction debounce(durationSelector) {\n  return lift_1.operate(function (source, subscriber) {\n    var hasValue = false;\n    var lastValue = null;\n    var durationSubscriber = null;\n    var emit = function () {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      durationSubscriber = null;\n      if (hasValue) {\n        hasValue = false;\n        var value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    };\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      hasValue = true;\n      lastValue = value;\n      durationSubscriber = OperatorSubscriber_1.createOperatorSubscriber(subscriber, emit, noop_1.noop);\n      innerFrom_1.innerFrom(durationSelector(value)).subscribe(durationSubscriber);\n    }, function () {\n      emit();\n      subscriber.complete();\n    }, undefined, function () {\n      lastValue = durationSubscriber = null;\n    }));\n  });\n}\nexports.debounce = debounce;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "debounce", "lift_1", "require", "noop_1", "OperatorSubscriber_1", "innerFrom_1", "durationSelector", "operate", "source", "subscriber", "hasValue", "lastValue", "durationSubscriber", "emit", "unsubscribe", "next", "subscribe", "createOperatorSubscriber", "noop", "innerFrom", "complete", "undefined"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/debounce.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.debounce = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar noop_1 = require(\"../util/noop\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction debounce(durationSelector) {\n    return lift_1.operate(function (source, subscriber) {\n        var hasValue = false;\n        var lastValue = null;\n        var durationSubscriber = null;\n        var emit = function () {\n            durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n            durationSubscriber = null;\n            if (hasValue) {\n                hasValue = false;\n                var value = lastValue;\n                lastValue = null;\n                subscriber.next(value);\n            }\n        };\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n            hasValue = true;\n            lastValue = value;\n            durationSubscriber = OperatorSubscriber_1.createOperatorSubscriber(subscriber, emit, noop_1.noop);\n            innerFrom_1.innerFrom(durationSelector(value)).subscribe(durationSubscriber);\n        }, function () {\n            emit();\n            subscriber.complete();\n        }, undefined, function () {\n            lastValue = durationSubscriber = null;\n        }));\n    });\n}\nexports.debounce = debounce;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,QAAQ,GAAG,KAAK,CAAC;AACzB,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIG,WAAW,GAAGH,OAAO,CAAC,yBAAyB,CAAC;AACpD,SAASF,QAAQA,CAACM,gBAAgB,EAAE;EAChC,OAAOL,MAAM,CAACM,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,kBAAkB,GAAG,IAAI;IAC7B,IAAIC,IAAI,GAAG,SAAAA,CAAA,EAAY;MACnBD,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACE,WAAW,CAAC,CAAC;MACxGF,kBAAkB,GAAG,IAAI;MACzB,IAAIF,QAAQ,EAAE;QACVA,QAAQ,GAAG,KAAK;QAChB,IAAIX,KAAK,GAAGY,SAAS;QACrBA,SAAS,GAAG,IAAI;QAChBF,UAAU,CAACM,IAAI,CAAChB,KAAK,CAAC;MAC1B;IACJ,CAAC;IACDS,MAAM,CAACQ,SAAS,CAACZ,oBAAoB,CAACa,wBAAwB,CAACR,UAAU,EAAE,UAAUV,KAAK,EAAE;MACxFa,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACE,WAAW,CAAC,CAAC;MACxGJ,QAAQ,GAAG,IAAI;MACfC,SAAS,GAAGZ,KAAK;MACjBa,kBAAkB,GAAGR,oBAAoB,CAACa,wBAAwB,CAACR,UAAU,EAAEI,IAAI,EAAEV,MAAM,CAACe,IAAI,CAAC;MACjGb,WAAW,CAACc,SAAS,CAACb,gBAAgB,CAACP,KAAK,CAAC,CAAC,CAACiB,SAAS,CAACJ,kBAAkB,CAAC;IAChF,CAAC,EAAE,YAAY;MACXC,IAAI,CAAC,CAAC;MACNJ,UAAU,CAACW,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAEC,SAAS,EAAE,YAAY;MACtBV,SAAS,GAAGC,kBAAkB,GAAG,IAAI;IACzC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACAd,OAAO,CAACE,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}