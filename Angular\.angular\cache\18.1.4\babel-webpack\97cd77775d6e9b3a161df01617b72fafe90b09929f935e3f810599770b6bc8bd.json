{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.scheduleIterable = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar iterator_1 = require(\"../symbol/iterator\");\nvar isFunction_1 = require(\"../util/isFunction\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nfunction scheduleIterable(input, scheduler) {\n  return new Observable_1.Observable(function (subscriber) {\n    var iterator;\n    executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n      iterator = input[iterator_1.iterator]();\n      executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n        var _a;\n        var value;\n        var done;\n        try {\n          _a = iterator.next(), value = _a.value, done = _a.done;\n        } catch (err) {\n          subscriber.error(err);\n          return;\n        }\n        if (done) {\n          subscriber.complete();\n        } else {\n          subscriber.next(value);\n        }\n      }, 0, true);\n    });\n    return function () {\n      return isFunction_1.isFunction(iterator === null || iterator === void 0 ? void 0 : iterator.return) && iterator.return();\n    };\n  });\n}\nexports.scheduleIterable = scheduleIterable;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "scheduleIterable", "Observable_1", "require", "iterator_1", "isFunction_1", "executeSchedule_1", "input", "scheduler", "Observable", "subscriber", "iterator", "executeSchedule", "_a", "done", "next", "err", "error", "complete", "isFunction", "return"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/scheduled/scheduleIterable.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.scheduleIterable = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar iterator_1 = require(\"../symbol/iterator\");\nvar isFunction_1 = require(\"../util/isFunction\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nfunction scheduleIterable(input, scheduler) {\n    return new Observable_1.Observable(function (subscriber) {\n        var iterator;\n        executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n            iterator = input[iterator_1.iterator]();\n            executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n                var _a;\n                var value;\n                var done;\n                try {\n                    (_a = iterator.next(), value = _a.value, done = _a.done);\n                }\n                catch (err) {\n                    subscriber.error(err);\n                    return;\n                }\n                if (done) {\n                    subscriber.complete();\n                }\n                else {\n                    subscriber.next(value);\n                }\n            }, 0, true);\n        });\n        return function () { return isFunction_1.isFunction(iterator === null || iterator === void 0 ? void 0 : iterator.return) && iterator.return(); };\n    });\n}\nexports.scheduleIterable = scheduleIterable;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,gBAAgB,GAAG,KAAK,CAAC;AACjC,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIC,UAAU,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AAC9C,IAAIE,YAAY,GAAGF,OAAO,CAAC,oBAAoB,CAAC;AAChD,IAAIG,iBAAiB,GAAGH,OAAO,CAAC,yBAAyB,CAAC;AAC1D,SAASF,gBAAgBA,CAACM,KAAK,EAAEC,SAAS,EAAE;EACxC,OAAO,IAAIN,YAAY,CAACO,UAAU,CAAC,UAAUC,UAAU,EAAE;IACrD,IAAIC,QAAQ;IACZL,iBAAiB,CAACM,eAAe,CAACF,UAAU,EAAEF,SAAS,EAAE,YAAY;MACjEG,QAAQ,GAAGJ,KAAK,CAACH,UAAU,CAACO,QAAQ,CAAC,CAAC,CAAC;MACvCL,iBAAiB,CAACM,eAAe,CAACF,UAAU,EAAEF,SAAS,EAAE,YAAY;QACjE,IAAIK,EAAE;QACN,IAAIb,KAAK;QACT,IAAIc,IAAI;QACR,IAAI;UACCD,EAAE,GAAGF,QAAQ,CAACI,IAAI,CAAC,CAAC,EAAEf,KAAK,GAAGa,EAAE,CAACb,KAAK,EAAEc,IAAI,GAAGD,EAAE,CAACC,IAAI;QAC3D,CAAC,CACD,OAAOE,GAAG,EAAE;UACRN,UAAU,CAACO,KAAK,CAACD,GAAG,CAAC;UACrB;QACJ;QACA,IAAIF,IAAI,EAAE;UACNJ,UAAU,CAACQ,QAAQ,CAAC,CAAC;QACzB,CAAC,MACI;UACDR,UAAU,CAACK,IAAI,CAACf,KAAK,CAAC;QAC1B;MACJ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACf,CAAC,CAAC;IACF,OAAO,YAAY;MAAE,OAAOK,YAAY,CAACc,UAAU,CAACR,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACS,MAAM,CAAC,IAAIT,QAAQ,CAACS,MAAM,CAAC,CAAC;IAAE,CAAC;EACpJ,CAAC,CAAC;AACN;AACArB,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}