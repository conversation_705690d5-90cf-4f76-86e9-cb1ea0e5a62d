{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createErrorClass = void 0;\nfunction createErrorClass(createImpl) {\n  var _super = function (instance) {\n    Error.call(instance);\n    instance.stack = new Error().stack;\n  };\n  var ctorFunc = createImpl(_super);\n  ctorFunc.prototype = Object.create(Error.prototype);\n  ctorFunc.prototype.constructor = ctorFunc;\n  return ctorFunc;\n}\nexports.createErrorClass = createErrorClass;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "createErrorClass", "createImpl", "_super", "instance", "Error", "call", "stack", "ctorFunc", "prototype", "create", "constructor"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/createErrorClass.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createErrorClass = void 0;\nfunction createErrorClass(createImpl) {\n    var _super = function (instance) {\n        Error.call(instance);\n        instance.stack = new Error().stack;\n    };\n    var ctorFunc = createImpl(_super);\n    ctorFunc.prototype = Object.create(Error.prototype);\n    ctorFunc.prototype.constructor = ctorFunc;\n    return ctorFunc;\n}\nexports.createErrorClass = createErrorClass;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,gBAAgB,GAAG,KAAK,CAAC;AACjC,SAASA,gBAAgBA,CAACC,UAAU,EAAE;EAClC,IAAIC,MAAM,GAAG,SAAAA,CAAUC,QAAQ,EAAE;IAC7BC,KAAK,CAACC,IAAI,CAACF,QAAQ,CAAC;IACpBA,QAAQ,CAACG,KAAK,GAAG,IAAIF,KAAK,CAAC,CAAC,CAACE,KAAK;EACtC,CAAC;EACD,IAAIC,QAAQ,GAAGN,UAAU,CAACC,MAAM,CAAC;EACjCK,QAAQ,CAACC,SAAS,GAAGZ,MAAM,CAACa,MAAM,CAACL,KAAK,CAACI,SAAS,CAAC;EACnDD,QAAQ,CAACC,SAAS,CAACE,WAAW,GAAGH,QAAQ;EACzC,OAAOA,QAAQ;AACnB;AACAT,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}