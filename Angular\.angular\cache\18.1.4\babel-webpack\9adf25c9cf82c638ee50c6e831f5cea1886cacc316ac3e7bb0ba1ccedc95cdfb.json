{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.elementAt = void 0;\nvar ArgumentOutOfRangeError_1 = require(\"../util/ArgumentOutOfRangeError\");\nvar filter_1 = require(\"./filter\");\nvar throwIfEmpty_1 = require(\"./throwIfEmpty\");\nvar defaultIfEmpty_1 = require(\"./defaultIfEmpty\");\nvar take_1 = require(\"./take\");\nfunction elementAt(index, defaultValue) {\n  if (index < 0) {\n    throw new ArgumentOutOfRangeError_1.ArgumentOutOfRangeError();\n  }\n  var hasDefaultValue = arguments.length >= 2;\n  return function (source) {\n    return source.pipe(filter_1.filter(function (v, i) {\n      return i === index;\n    }), take_1.take(1), hasDefaultValue ? defaultIfEmpty_1.defaultIfEmpty(defaultValue) : throwIfEmpty_1.throwIfEmpty(function () {\n      return new ArgumentOutOfRangeError_1.ArgumentOutOfRangeError();\n    }));\n  };\n}\nexports.elementAt = elementAt;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "elementAt", "ArgumentOutOfRangeError_1", "require", "filter_1", "throwIfEmpty_1", "defaultIfEmpty_1", "take_1", "index", "defaultValue", "ArgumentOutOfRangeError", "hasDefaultValue", "arguments", "length", "source", "pipe", "filter", "v", "i", "take", "defaultIfEmpty", "throwIfEmpty"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/elementAt.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.elementAt = void 0;\nvar ArgumentOutOfRangeError_1 = require(\"../util/ArgumentOutOfRangeError\");\nvar filter_1 = require(\"./filter\");\nvar throwIfEmpty_1 = require(\"./throwIfEmpty\");\nvar defaultIfEmpty_1 = require(\"./defaultIfEmpty\");\nvar take_1 = require(\"./take\");\nfunction elementAt(index, defaultValue) {\n    if (index < 0) {\n        throw new ArgumentOutOfRangeError_1.ArgumentOutOfRangeError();\n    }\n    var hasDefaultValue = arguments.length >= 2;\n    return function (source) {\n        return source.pipe(filter_1.filter(function (v, i) { return i === index; }), take_1.take(1), hasDefaultValue ? defaultIfEmpty_1.defaultIfEmpty(defaultValue) : throwIfEmpty_1.throwIfEmpty(function () { return new ArgumentOutOfRangeError_1.ArgumentOutOfRangeError(); }));\n    };\n}\nexports.elementAt = elementAt;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,yBAAyB,GAAGC,OAAO,CAAC,iCAAiC,CAAC;AAC1E,IAAIC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;AAClC,IAAIE,cAAc,GAAGF,OAAO,CAAC,gBAAgB,CAAC;AAC9C,IAAIG,gBAAgB,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AAClD,IAAII,MAAM,GAAGJ,OAAO,CAAC,QAAQ,CAAC;AAC9B,SAASF,SAASA,CAACO,KAAK,EAAEC,YAAY,EAAE;EACpC,IAAID,KAAK,GAAG,CAAC,EAAE;IACX,MAAM,IAAIN,yBAAyB,CAACQ,uBAAuB,CAAC,CAAC;EACjE;EACA,IAAIC,eAAe,GAAGC,SAAS,CAACC,MAAM,IAAI,CAAC;EAC3C,OAAO,UAAUC,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACC,IAAI,CAACX,QAAQ,CAACY,MAAM,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MAAE,OAAOA,CAAC,KAAKV,KAAK;IAAE,CAAC,CAAC,EAAED,MAAM,CAACY,IAAI,CAAC,CAAC,CAAC,EAAER,eAAe,GAAGL,gBAAgB,CAACc,cAAc,CAACX,YAAY,CAAC,GAAGJ,cAAc,CAACgB,YAAY,CAAC,YAAY;MAAE,OAAO,IAAInB,yBAAyB,CAACQ,uBAAuB,CAAC,CAAC;IAAE,CAAC,CAAC,CAAC;EAChR,CAAC;AACL;AACAX,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}