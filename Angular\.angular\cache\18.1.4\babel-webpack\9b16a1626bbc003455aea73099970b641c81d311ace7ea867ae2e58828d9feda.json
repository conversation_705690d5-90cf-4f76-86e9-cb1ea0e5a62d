{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@angular/common\";\nfunction ConfirmationDialogComponent_a_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 10);\n    i0.ɵɵlistener(\"click\", function ConfirmationDialogComponent_a_7_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.modal.dismiss());\n    });\n    i0.ɵɵelement(1, \"i\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConfirmationDialogComponent_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ConfirmationDialogComponent_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCancelClick());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.cancelButtonText);\n  }\n}\nexport class ConfirmationDialogComponent {\n  modal;\n  showClose = true;\n  description = '';\n  actionButtonText = '';\n  cancelButtonText = '';\n  title = '';\n  selectedTab;\n  passEntry = new EventEmitter();\n  constructor(modal) {\n    this.modal = modal;\n  }\n  ngOnInit() {}\n  onYesClick() {\n    this.passEntry.emit({\n      success: true,\n      selectedTab: this.selectedTab\n    });\n    // this.passEntry.emit(true)\n    this.modal.close();\n  }\n  onCancelClick() {\n    this.passEntry.emit({\n      success: false,\n      selectedTab: this.selectedTab\n    });\n    // this.passEntry.emit(false)\n    this.modal.close();\n  }\n  static ɵfac = function ConfirmationDialogComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ConfirmationDialogComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ConfirmationDialogComponent,\n    selectors: [[\"app-confirmation-dialog\"]],\n    inputs: {\n      showClose: \"showClose\",\n      description: \"description\",\n      actionButtonText: \"actionButtonText\",\n      cancelButtonText: \"cancelButtonText\",\n      title: \"title\",\n      selectedTab: \"selectedTab\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 18,\n    vars: 5,\n    consts: [[1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"example-modal-sizes-title-lg\", 1, \"modal-title\", \"h5\", \"fs-3\"], [1, \"float-right\"], [\"class\", \"btn btn-icon  btn-sm pl-08\", 3, \"click\", 4, \"ngIf\"], [1, \"modal-body\"], [1, \"col-lg-12\", \"form-label\"], [1, \"modal-footer\"], [\"type\", \"button\", \"class\", \"btn btn-danger btn-sm btn-elevate \", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-elevate\", \"btn-sm\", \"mr-2\", 3, \"click\"], [1, \"btn\", \"btn-icon\", \"btn-sm\", \"pl-08\", 3, \"click\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 2, \"margin-left\", \"50%\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", 3, \"click\"]],\n    template: function ConfirmationDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementContainerStart(3)(4);\n        i0.ɵɵtext(5);\n        i0.ɵɵelementContainerEnd()();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 3);\n        i0.ɵɵtemplate(7, ConfirmationDialogComponent_a_7_Template, 2, 0, \"a\", 4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 5);\n        i0.ɵɵelementContainerStart(9);\n        i0.ɵɵelementStart(10, \"div\", 6);\n        i0.ɵɵtext(11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"div\", 7);\n        i0.ɵɵelementContainerStart(13);\n        i0.ɵɵtemplate(14, ConfirmationDialogComponent_button_14_Template, 2, 1, \"button\", 8);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵtext(15, \"\\u00A0 \");\n        i0.ɵɵelementStart(16, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function ConfirmationDialogComponent_Template_button_click_16_listener() {\n          return ctx.onYesClick();\n        });\n        i0.ɵɵtext(17);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.title);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.showClose === true);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.description, \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.showClose === true);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", ctx.actionButtonText, \"\");\n      }\n    },\n    dependencies: [i2.NgIf],\n    styles: [\".modal-footer[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1.25rem !important;\\n}\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  padding: 1.75rem 1.75rem !important;\\n  min-height: 75px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9zaGFyZWQvY29uZmlybWF0aW9uLWRpYWxvZy9jb25maXJtYXRpb24tZGlhbG9nLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ00sbUNBQUE7QUFDTjs7QUFDQTtFQUNNLG1DQUFBO0VBQ0EsZ0JBQUE7QUFFTiIsInNvdXJjZXNDb250ZW50IjpbIi5tb2RhbC1mb290ZXJ7XHJcbiAgICAgIHBhZGRpbmc6IDAuNzVyZW0gMS4yNXJlbSAhaW1wb3J0YW50O1xyXG59XHJcbi5tb2RhbC1ib2R5e1xyXG4gICAgICBwYWRkaW5nOiAxLjc1cmVtIDEuNzVyZW0gIWltcG9ydGFudDtcclxuICAgICAgbWluLWhlaWdodDogNzVweDtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵlistener", "ConfirmationDialogComponent_a_7_Template_a_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "modal", "dismiss", "ɵɵelement", "ɵɵelementEnd", "ConfirmationDialogComponent_button_14_Template_button_click_0_listener", "_r3", "onCancelClick", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "cancelButtonText", "ConfirmationDialogComponent", "showClose", "description", "actionButtonText", "title", "selectedTab", "passEntry", "constructor", "ngOnInit", "onYesClick", "emit", "success", "close", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ConfirmationDialogComponent_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵtemplate", "ConfirmationDialogComponent_a_7_Template", "ConfirmationDialogComponent_button_14_Template", "ConfirmationDialogComponent_Template_button_click_16_listener", "ɵɵproperty", "ɵɵtextInterpolate1"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\shared\\confirmation-dialog\\confirmation-dialog.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\shared\\confirmation-dialog\\confirmation-dialog.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\n\r\n@Component({\r\n  selector: 'app-confirmation-dialog',\r\n  templateUrl: './confirmation-dialog.component.html',\r\n  styleUrls: ['./confirmation-dialog.component.scss']\r\n})\r\nexport class ConfirmationDialogComponent implements OnInit {\r\n\r\n  @Input() showClose:boolean = true;\r\n  @Input() description:string ='';\r\n  @Input() actionButtonText:string ='';\r\n  @Input() cancelButtonText:string='';\r\n  @Input() title:string = '';\r\n  @Input() selectedTab: string;\r\n\r\n  @Output() passEntry: EventEmitter<any> = new EventEmitter();\r\n  constructor(public modal: NgbActiveModal,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  onYesClick(): void {\r\n    this.passEntry.emit({ success: true, selectedTab: this.selectedTab });\r\n    // this.passEntry.emit(true)\r\n    this.modal.close();\r\n\t}\r\n\r\n  onCancelClick(): void {\r\n    this.passEntry.emit({ success: false, selectedTab: this.selectedTab });\r\n    // this.passEntry.emit(false)\r\n    this.modal.close();\r\n\t}\r\n\r\n\r\n}\r\n", "<div class=\"modal-content\">\r\n  <div class=\"modal-header\">\r\n    <div class=\"modal-title h5 fs-3\" id=\"example-modal-sizes-title-lg\">\r\n      <ng-container>\r\n        <ng-container>{{title}}</ng-container>\r\n      </ng-container>\r\n    </div>\r\n    <div class=\"float-right\">\r\n     <a class=\"btn btn-icon  btn-sm pl-08\"  *ngIf=\"showClose === true\" (click)=\"modal.dismiss()\">\r\n        <i class=\"fa-solid fs-2 fa-xmark text-white\" style=\"    margin-left: 50%;\"></i>\r\n    </a>\r\n    </div>\r\n  </div>\r\n  <div class=\"modal-body \" >\r\n    <ng-container>\r\n      <div class=\"col-lg-12 form-label\">\r\n        {{description}}\r\n      </div>\r\n    </ng-container>\r\n  </div>\r\n  <div class=\"modal-footer \">\r\n    <ng-container>\r\n       <button type=\"button\" *ngIf=\"showClose === true\" class=\"btn btn-danger btn-sm btn-elevate \" (click)=\"onCancelClick()\">{{cancelButtonText}}</button>\r\n    </ng-container>&nbsp;\r\n    <button type=\"button\" class=\"btn btn-primary btn-elevate btn-sm mr-2\" (click)=\"onYesClick()\">\r\n      {{actionButtonText}}</button>\r\n\r\n\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;;;;;;;ICQzEC,EAAA,CAAAC,cAAA,YAA4F;IAA1BD,EAAA,CAAAE,UAAA,mBAAAC,4DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,KAAA,CAAAC,OAAA,EAAe;IAAA,EAAC;IACxFV,EAAA,CAAAW,SAAA,YAA+E;IACnFX,EAAA,CAAAY,YAAA,EAAI;;;;;;IAYDZ,EAAA,CAAAC,cAAA,iBAAsH;IAA1BD,EAAA,CAAAE,UAAA,mBAAAW,uEAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAS,aAAA,EAAe;IAAA,EAAC;IAACf,EAAA,CAAAgB,MAAA,GAAoB;IAAAhB,EAAA,CAAAY,YAAA,EAAS;;;;IAA7BZ,EAAA,CAAAiB,SAAA,EAAoB;IAApBjB,EAAA,CAAAkB,iBAAA,CAAAZ,MAAA,CAAAa,gBAAA,CAAoB;;;ADdjJ,OAAM,MAAOC,2BAA2B;EAUnBX,KAAA;EARVY,SAAS,GAAW,IAAI;EACxBC,WAAW,GAAS,EAAE;EACtBC,gBAAgB,GAAS,EAAE;EAC3BJ,gBAAgB,GAAQ,EAAE;EAC1BK,KAAK,GAAU,EAAE;EACjBC,WAAW;EAEVC,SAAS,GAAsB,IAAI3B,YAAY,EAAE;EAC3D4B,YAAmBlB,KAAqB;IAArB,KAAAA,KAAK,GAALA,KAAK;EACpB;EAEJmB,QAAQA,CAAA,GACR;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACH,SAAS,CAACI,IAAI,CAAC;MAAEC,OAAO,EAAE,IAAI;MAAEN,WAAW,EAAE,IAAI,CAACA;IAAW,CAAE,CAAC;IACrE;IACA,IAAI,CAAChB,KAAK,CAACuB,KAAK,EAAE;EACrB;EAECjB,aAAaA,CAAA;IACX,IAAI,CAACW,SAAS,CAACI,IAAI,CAAC;MAAEC,OAAO,EAAE,KAAK;MAAEN,WAAW,EAAE,IAAI,CAACA;IAAW,CAAE,CAAC;IACtE;IACA,IAAI,CAAChB,KAAK,CAACuB,KAAK,EAAE;EACrB;;qCA1BYZ,2BAA2B,EAAApB,EAAA,CAAAiC,iBAAA,CAAAC,EAAA,CAAAC,cAAA;EAAA;;UAA3Bf,2BAA2B;IAAAgB,SAAA;IAAAC,MAAA;MAAAhB,SAAA;MAAAC,WAAA;MAAAC,gBAAA;MAAAJ,gBAAA;MAAAK,KAAA;MAAAC,WAAA;IAAA;IAAAa,OAAA;MAAAZ,SAAA;IAAA;IAAAa,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCNpC5C,EAFJ,CAAAC,cAAA,aAA2B,aACC,aAC2C;QAE/DD,EADF,CAAA8C,uBAAA,GAAc,GACE;QAAA9C,EAAA,CAAAgB,MAAA,GAAS;;QAE3BhB,EAAA,CAAAY,YAAA,EAAM;QACNZ,EAAA,CAAAC,cAAA,aAAyB;QACxBD,EAAA,CAAA+C,UAAA,IAAAC,wCAAA,eAA4F;QAI/FhD,EADE,CAAAY,YAAA,EAAM,EACF;QACNZ,EAAA,CAAAC,cAAA,aAA0B;QACxBD,EAAA,CAAA8C,uBAAA,GAAc;QACZ9C,EAAA,CAAAC,cAAA,cAAkC;QAChCD,EAAA,CAAAgB,MAAA,IACF;QAAAhB,EAAA,CAAAY,YAAA,EAAM;;QAEVZ,EAAA,CAAAY,YAAA,EAAM;QACNZ,EAAA,CAAAC,cAAA,cAA2B;QACzBD,EAAA,CAAA8C,uBAAA,IAAc;QACX9C,EAAA,CAAA+C,UAAA,KAAAE,8CAAA,oBAAsH;;QAC1GjD,EAAA,CAAAgB,MAAA,eACf;QAAAhB,EAAA,CAAAC,cAAA,iBAA6F;QAAvBD,EAAA,CAAAE,UAAA,mBAAAgD,8DAAA;UAAA,OAASL,GAAA,CAAAhB,UAAA,EAAY;QAAA,EAAC;QAC1F7B,EAAA,CAAAgB,MAAA,IAAoB;QAI1BhB,EAJ0B,CAAAY,YAAA,EAAS,EAG3B,EACF;;;QAzBgBZ,EAAA,CAAAiB,SAAA,GAAS;QAATjB,EAAA,CAAAkB,iBAAA,CAAA2B,GAAA,CAAArB,KAAA,CAAS;QAIcxB,EAAA,CAAAiB,SAAA,GAAwB;QAAxBjB,EAAA,CAAAmD,UAAA,SAAAN,GAAA,CAAAxB,SAAA,UAAwB;QAQ7DrB,EAAA,CAAAiB,SAAA,GACF;QADEjB,EAAA,CAAAoD,kBAAA,MAAAP,GAAA,CAAAvB,WAAA,MACF;QAKwBtB,EAAA,CAAAiB,SAAA,GAAwB;QAAxBjB,EAAA,CAAAmD,UAAA,SAAAN,GAAA,CAAAxB,SAAA,UAAwB;QAGhDrB,EAAA,CAAAiB,SAAA,GAAoB;QAApBjB,EAAA,CAAAoD,kBAAA,MAAAP,GAAA,CAAAtB,gBAAA,KAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}