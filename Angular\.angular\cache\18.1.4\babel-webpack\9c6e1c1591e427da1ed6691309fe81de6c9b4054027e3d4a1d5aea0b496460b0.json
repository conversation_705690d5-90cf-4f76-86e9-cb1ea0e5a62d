{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.scheduleAsyncIterable = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nfunction scheduleAsyncIterable(input, scheduler) {\n  if (!input) {\n    throw new Error('Iterable cannot be null');\n  }\n  return new Observable_1.Observable(function (subscriber) {\n    executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n      var iterator = input[Symbol.asyncIterator]();\n      executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n        iterator.next().then(function (result) {\n          if (result.done) {\n            subscriber.complete();\n          } else {\n            subscriber.next(result.value);\n          }\n        });\n      }, 0, true);\n    });\n  });\n}\nexports.scheduleAsyncIterable = scheduleAsyncIterable;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "scheduleAsyncIterable", "Observable_1", "require", "executeSchedule_1", "input", "scheduler", "Error", "Observable", "subscriber", "executeSchedule", "iterator", "Symbol", "asyncIterator", "next", "then", "result", "done", "complete"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/scheduled/scheduleAsyncIterable.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.scheduleAsyncIterable = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nfunction scheduleAsyncIterable(input, scheduler) {\n    if (!input) {\n        throw new Error('Iterable cannot be null');\n    }\n    return new Observable_1.Observable(function (subscriber) {\n        executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n            var iterator = input[Symbol.asyncIterator]();\n            executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n                iterator.next().then(function (result) {\n                    if (result.done) {\n                        subscriber.complete();\n                    }\n                    else {\n                        subscriber.next(result.value);\n                    }\n                });\n            }, 0, true);\n        });\n    });\n}\nexports.scheduleAsyncIterable = scheduleAsyncIterable;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,qBAAqB,GAAG,KAAK,CAAC;AACtC,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIC,iBAAiB,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AAC1D,SAASF,qBAAqBA,CAACI,KAAK,EAAEC,SAAS,EAAE;EAC7C,IAAI,CAACD,KAAK,EAAE;IACR,MAAM,IAAIE,KAAK,CAAC,yBAAyB,CAAC;EAC9C;EACA,OAAO,IAAIL,YAAY,CAACM,UAAU,CAAC,UAAUC,UAAU,EAAE;IACrDL,iBAAiB,CAACM,eAAe,CAACD,UAAU,EAAEH,SAAS,EAAE,YAAY;MACjE,IAAIK,QAAQ,GAAGN,KAAK,CAACO,MAAM,CAACC,aAAa,CAAC,CAAC,CAAC;MAC5CT,iBAAiB,CAACM,eAAe,CAACD,UAAU,EAAEH,SAAS,EAAE,YAAY;QACjEK,QAAQ,CAACG,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,UAAUC,MAAM,EAAE;UACnC,IAAIA,MAAM,CAACC,IAAI,EAAE;YACbR,UAAU,CAACS,QAAQ,CAAC,CAAC;UACzB,CAAC,MACI;YACDT,UAAU,CAACK,IAAI,CAACE,MAAM,CAAChB,KAAK,CAAC;UACjC;QACJ,CAAC,CAAC;MACN,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACf,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACAD,OAAO,CAACE,qBAAqB,GAAGA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}