{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.skipUntil = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar noop_1 = require(\"../util/noop\");\nfunction skipUntil(notifier) {\n  return lift_1.operate(function (source, subscriber) {\n    var taking = false;\n    var skipSubscriber = OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n      skipSubscriber === null || skipSubscriber === void 0 ? void 0 : skipSubscriber.unsubscribe();\n      taking = true;\n    }, noop_1.noop);\n    innerFrom_1.innerFrom(notifier).subscribe(skipSubscriber);\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      return taking && subscriber.next(value);\n    }));\n  });\n}\nexports.skipUntil = skipUntil;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "<PERSON><PERSON><PERSON><PERSON>", "lift_1", "require", "OperatorSubscriber_1", "innerFrom_1", "noop_1", "notifier", "operate", "source", "subscriber", "taking", "skipSubscriber", "createOperatorSubscriber", "unsubscribe", "noop", "innerFrom", "subscribe", "next"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/skipUntil.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.skipUntil = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar noop_1 = require(\"../util/noop\");\nfunction skipUntil(notifier) {\n    return lift_1.operate(function (source, subscriber) {\n        var taking = false;\n        var skipSubscriber = OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n            skipSubscriber === null || skipSubscriber === void 0 ? void 0 : skipSubscriber.unsubscribe();\n            taking = true;\n        }, noop_1.noop);\n        innerFrom_1.innerFrom(notifier).subscribe(skipSubscriber);\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) { return taking && subscriber.next(value); }));\n    });\n}\nexports.skipUntil = skipUntil;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIE,WAAW,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AACpD,IAAIG,MAAM,GAAGH,OAAO,CAAC,cAAc,CAAC;AACpC,SAASF,SAASA,CAACM,QAAQ,EAAE;EACzB,OAAOL,MAAM,CAACM,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,MAAM,GAAG,KAAK;IAClB,IAAIC,cAAc,GAAGR,oBAAoB,CAACS,wBAAwB,CAACH,UAAU,EAAE,YAAY;MACvFE,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACE,WAAW,CAAC,CAAC;MAC5FH,MAAM,GAAG,IAAI;IACjB,CAAC,EAAEL,MAAM,CAACS,IAAI,CAAC;IACfV,WAAW,CAACW,SAAS,CAACT,QAAQ,CAAC,CAACU,SAAS,CAACL,cAAc,CAAC;IACzDH,MAAM,CAACQ,SAAS,CAACb,oBAAoB,CAACS,wBAAwB,CAACH,UAAU,EAAE,UAAUV,KAAK,EAAE;MAAE,OAAOW,MAAM,IAAID,UAAU,CAACQ,IAAI,CAAClB,KAAK,CAAC;IAAE,CAAC,CAAC,CAAC;EAC9I,CAAC,CAAC;AACN;AACAD,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}