{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.scheduled = void 0;\nvar scheduleObservable_1 = require(\"./scheduleObservable\");\nvar schedulePromise_1 = require(\"./schedulePromise\");\nvar scheduleArray_1 = require(\"./scheduleArray\");\nvar scheduleIterable_1 = require(\"./scheduleIterable\");\nvar scheduleAsyncIterable_1 = require(\"./scheduleAsyncIterable\");\nvar isInteropObservable_1 = require(\"../util/isInteropObservable\");\nvar isPromise_1 = require(\"../util/isPromise\");\nvar isArrayLike_1 = require(\"../util/isArrayLike\");\nvar isIterable_1 = require(\"../util/isIterable\");\nvar isAsyncIterable_1 = require(\"../util/isAsyncIterable\");\nvar throwUnobservableError_1 = require(\"../util/throwUnobservableError\");\nvar isReadableStreamLike_1 = require(\"../util/isReadableStreamLike\");\nvar scheduleReadableStreamLike_1 = require(\"./scheduleReadableStreamLike\");\nfunction scheduled(input, scheduler) {\n  if (input != null) {\n    if (isInteropObservable_1.isInteropObservable(input)) {\n      return scheduleObservable_1.scheduleObservable(input, scheduler);\n    }\n    if (isArrayLike_1.isArrayLike(input)) {\n      return scheduleArray_1.scheduleArray(input, scheduler);\n    }\n    if (isPromise_1.isPromise(input)) {\n      return schedulePromise_1.schedulePromise(input, scheduler);\n    }\n    if (isAsyncIterable_1.isAsyncIterable(input)) {\n      return scheduleAsyncIterable_1.scheduleAsyncIterable(input, scheduler);\n    }\n    if (isIterable_1.isIterable(input)) {\n      return scheduleIterable_1.scheduleIterable(input, scheduler);\n    }\n    if (isReadableStreamLike_1.isReadableStreamLike(input)) {\n      return scheduleReadableStreamLike_1.scheduleReadableStreamLike(input, scheduler);\n    }\n  }\n  throw throwUnobservableError_1.createInvalidObservableTypeError(input);\n}\nexports.scheduled = scheduled;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "scheduled", "scheduleObservable_1", "require", "schedulePromise_1", "scheduleArray_1", "scheduleIterable_1", "scheduleAsyncIterable_1", "isInteropObservable_1", "isPromise_1", "isArrayLike_1", "isIterable_1", "isAsyncIterable_1", "throwUnobservableError_1", "isReadableStreamLike_1", "scheduleReadableStreamLike_1", "input", "scheduler", "isInteropObservable", "scheduleObservable", "isArrayLike", "scheduleArray", "isPromise", "schedulePromise", "isAsyncIterable", "scheduleAsyncIterable", "isIterable", "scheduleIterable", "isReadableStreamLike", "scheduleReadableStreamLike", "createInvalidObservableTypeError"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/scheduled/scheduled.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.scheduled = void 0;\nvar scheduleObservable_1 = require(\"./scheduleObservable\");\nvar schedulePromise_1 = require(\"./schedulePromise\");\nvar scheduleArray_1 = require(\"./scheduleArray\");\nvar scheduleIterable_1 = require(\"./scheduleIterable\");\nvar scheduleAsyncIterable_1 = require(\"./scheduleAsyncIterable\");\nvar isInteropObservable_1 = require(\"../util/isInteropObservable\");\nvar isPromise_1 = require(\"../util/isPromise\");\nvar isArrayLike_1 = require(\"../util/isArrayLike\");\nvar isIterable_1 = require(\"../util/isIterable\");\nvar isAsyncIterable_1 = require(\"../util/isAsyncIterable\");\nvar throwUnobservableError_1 = require(\"../util/throwUnobservableError\");\nvar isReadableStreamLike_1 = require(\"../util/isReadableStreamLike\");\nvar scheduleReadableStreamLike_1 = require(\"./scheduleReadableStreamLike\");\nfunction scheduled(input, scheduler) {\n    if (input != null) {\n        if (isInteropObservable_1.isInteropObservable(input)) {\n            return scheduleObservable_1.scheduleObservable(input, scheduler);\n        }\n        if (isArrayLike_1.isArrayLike(input)) {\n            return scheduleArray_1.scheduleArray(input, scheduler);\n        }\n        if (isPromise_1.isPromise(input)) {\n            return schedulePromise_1.schedulePromise(input, scheduler);\n        }\n        if (isAsyncIterable_1.isAsyncIterable(input)) {\n            return scheduleAsyncIterable_1.scheduleAsyncIterable(input, scheduler);\n        }\n        if (isIterable_1.isIterable(input)) {\n            return scheduleIterable_1.scheduleIterable(input, scheduler);\n        }\n        if (isReadableStreamLike_1.isReadableStreamLike(input)) {\n            return scheduleReadableStreamLike_1.scheduleReadableStreamLike(input, scheduler);\n        }\n    }\n    throw throwUnobservableError_1.createInvalidObservableTypeError(input);\n}\nexports.scheduled = scheduled;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,oBAAoB,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIC,iBAAiB,GAAGD,OAAO,CAAC,mBAAmB,CAAC;AACpD,IAAIE,eAAe,GAAGF,OAAO,CAAC,iBAAiB,CAAC;AAChD,IAAIG,kBAAkB,GAAGH,OAAO,CAAC,oBAAoB,CAAC;AACtD,IAAII,uBAAuB,GAAGJ,OAAO,CAAC,yBAAyB,CAAC;AAChE,IAAIK,qBAAqB,GAAGL,OAAO,CAAC,6BAA6B,CAAC;AAClE,IAAIM,WAAW,GAAGN,OAAO,CAAC,mBAAmB,CAAC;AAC9C,IAAIO,aAAa,GAAGP,OAAO,CAAC,qBAAqB,CAAC;AAClD,IAAIQ,YAAY,GAAGR,OAAO,CAAC,oBAAoB,CAAC;AAChD,IAAIS,iBAAiB,GAAGT,OAAO,CAAC,yBAAyB,CAAC;AAC1D,IAAIU,wBAAwB,GAAGV,OAAO,CAAC,gCAAgC,CAAC;AACxE,IAAIW,sBAAsB,GAAGX,OAAO,CAAC,8BAA8B,CAAC;AACpE,IAAIY,4BAA4B,GAAGZ,OAAO,CAAC,8BAA8B,CAAC;AAC1E,SAASF,SAASA,CAACe,KAAK,EAAEC,SAAS,EAAE;EACjC,IAAID,KAAK,IAAI,IAAI,EAAE;IACf,IAAIR,qBAAqB,CAACU,mBAAmB,CAACF,KAAK,CAAC,EAAE;MAClD,OAAOd,oBAAoB,CAACiB,kBAAkB,CAACH,KAAK,EAAEC,SAAS,CAAC;IACpE;IACA,IAAIP,aAAa,CAACU,WAAW,CAACJ,KAAK,CAAC,EAAE;MAClC,OAAOX,eAAe,CAACgB,aAAa,CAACL,KAAK,EAAEC,SAAS,CAAC;IAC1D;IACA,IAAIR,WAAW,CAACa,SAAS,CAACN,KAAK,CAAC,EAAE;MAC9B,OAAOZ,iBAAiB,CAACmB,eAAe,CAACP,KAAK,EAAEC,SAAS,CAAC;IAC9D;IACA,IAAIL,iBAAiB,CAACY,eAAe,CAACR,KAAK,CAAC,EAAE;MAC1C,OAAOT,uBAAuB,CAACkB,qBAAqB,CAACT,KAAK,EAAEC,SAAS,CAAC;IAC1E;IACA,IAAIN,YAAY,CAACe,UAAU,CAACV,KAAK,CAAC,EAAE;MAChC,OAAOV,kBAAkB,CAACqB,gBAAgB,CAACX,KAAK,EAAEC,SAAS,CAAC;IAChE;IACA,IAAIH,sBAAsB,CAACc,oBAAoB,CAACZ,KAAK,CAAC,EAAE;MACpD,OAAOD,4BAA4B,CAACc,0BAA0B,CAACb,KAAK,EAAEC,SAAS,CAAC;IACpF;EACJ;EACA,MAAMJ,wBAAwB,CAACiB,gCAAgC,CAACd,KAAK,CAAC;AAC1E;AACAjB,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}