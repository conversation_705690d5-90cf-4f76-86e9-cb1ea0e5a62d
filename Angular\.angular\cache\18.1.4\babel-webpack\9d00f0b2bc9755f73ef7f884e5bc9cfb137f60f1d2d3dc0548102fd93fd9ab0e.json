{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isFunction = void 0;\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\nexports.isFunction = isFunction;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "isFunction"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/isFunction.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isFunction = void 0;\nfunction isFunction(value) {\n    return typeof value === 'function';\n}\nexports.isFunction = isFunction;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,UAAU,GAAG,KAAK,CAAC;AAC3B,SAASA,UAAUA,CAACD,KAAK,EAAE;EACvB,OAAO,OAAOA,KAAK,KAAK,UAAU;AACtC;AACAD,OAAO,CAACE,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}