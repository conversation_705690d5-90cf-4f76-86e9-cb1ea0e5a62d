{"ast": null, "code": "\"use strict\";\n\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.windowCount = void 0;\nvar Subject_1 = require(\"../Subject\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction windowCount(windowSize, startWindowEvery) {\n  if (startWindowEvery === void 0) {\n    startWindowEvery = 0;\n  }\n  var startEvery = startWindowEvery > 0 ? startWindowEvery : windowSize;\n  return lift_1.operate(function (source, subscriber) {\n    var windows = [new Subject_1.Subject()];\n    var starts = [];\n    var count = 0;\n    subscriber.next(windows[0].asObservable());\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var e_1, _a;\n      try {\n        for (var windows_1 = __values(windows), windows_1_1 = windows_1.next(); !windows_1_1.done; windows_1_1 = windows_1.next()) {\n          var window_1 = windows_1_1.value;\n          window_1.next(value);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (windows_1_1 && !windows_1_1.done && (_a = windows_1.return)) _a.call(windows_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      var c = count - windowSize + 1;\n      if (c >= 0 && c % startEvery === 0) {\n        windows.shift().complete();\n      }\n      if (++count % startEvery === 0) {\n        var window_2 = new Subject_1.Subject();\n        windows.push(window_2);\n        subscriber.next(window_2.asObservable());\n      }\n    }, function () {\n      while (windows.length > 0) {\n        windows.shift().complete();\n      }\n      subscriber.complete();\n    }, function (err) {\n      while (windows.length > 0) {\n        windows.shift().error(err);\n      }\n      subscriber.error(err);\n    }, function () {\n      starts = null;\n      windows = null;\n    }));\n  });\n}\nexports.windowCount = windowCount;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "Object", "defineProperty", "exports", "windowCount", "Subject_1", "require", "lift_1", "OperatorSubscriber_1", "windowSize", "startWindowEvery", "startEvery", "operate", "source", "subscriber", "windows", "Subject", "starts", "count", "asObservable", "subscribe", "createOperatorSubscriber", "e_1", "_a", "windows_1", "windows_1_1", "window_1", "e_1_1", "error", "return", "c", "shift", "complete", "window_2", "push", "err"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/windowCount.js"], "sourcesContent": ["\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.windowCount = void 0;\nvar Subject_1 = require(\"../Subject\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction windowCount(windowSize, startWindowEvery) {\n    if (startWindowEvery === void 0) { startWindowEvery = 0; }\n    var startEvery = startWindowEvery > 0 ? startWindowEvery : windowSize;\n    return lift_1.operate(function (source, subscriber) {\n        var windows = [new Subject_1.Subject()];\n        var starts = [];\n        var count = 0;\n        subscriber.next(windows[0].asObservable());\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            var e_1, _a;\n            try {\n                for (var windows_1 = __values(windows), windows_1_1 = windows_1.next(); !windows_1_1.done; windows_1_1 = windows_1.next()) {\n                    var window_1 = windows_1_1.value;\n                    window_1.next(value);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (windows_1_1 && !windows_1_1.done && (_a = windows_1.return)) _a.call(windows_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            var c = count - windowSize + 1;\n            if (c >= 0 && c % startEvery === 0) {\n                windows.shift().complete();\n            }\n            if (++count % startEvery === 0) {\n                var window_2 = new Subject_1.Subject();\n                windows.push(window_2);\n                subscriber.next(window_2.asObservable());\n            }\n        }, function () {\n            while (windows.length > 0) {\n                windows.shift().complete();\n            }\n            subscriber.complete();\n        }, function (err) {\n            while (windows.length > 0) {\n                windows.shift().error(err);\n            }\n            subscriber.error(err);\n        }, function () {\n            starts = null;\n            windows = null;\n        }));\n    });\n}\nexports.windowCount = windowCount;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACDW,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEL,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DK,OAAO,CAACC,WAAW,GAAG,KAAK,CAAC;AAC5B,IAAIC,SAAS,GAAGC,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,WAAWA,CAACK,UAAU,EAAEC,gBAAgB,EAAE;EAC/C,IAAIA,gBAAgB,KAAK,KAAK,CAAC,EAAE;IAAEA,gBAAgB,GAAG,CAAC;EAAE;EACzD,IAAIC,UAAU,GAAGD,gBAAgB,GAAG,CAAC,GAAGA,gBAAgB,GAAGD,UAAU;EACrE,OAAOF,MAAM,CAACK,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,OAAO,GAAG,CAAC,IAAIV,SAAS,CAACW,OAAO,CAAC,CAAC,CAAC;IACvC,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,KAAK,GAAG,CAAC;IACbJ,UAAU,CAACjB,IAAI,CAACkB,OAAO,CAAC,CAAC,CAAC,CAACI,YAAY,CAAC,CAAC,CAAC;IAC1CN,MAAM,CAACO,SAAS,CAACZ,oBAAoB,CAACa,wBAAwB,CAACP,UAAU,EAAE,UAAUhB,KAAK,EAAE;MACxF,IAAIwB,GAAG,EAAEC,EAAE;MACX,IAAI;QACA,KAAK,IAAIC,SAAS,GAAGpC,QAAQ,CAAC2B,OAAO,CAAC,EAAEU,WAAW,GAAGD,SAAS,CAAC3B,IAAI,CAAC,CAAC,EAAE,CAAC4B,WAAW,CAAC1B,IAAI,EAAE0B,WAAW,GAAGD,SAAS,CAAC3B,IAAI,CAAC,CAAC,EAAE;UACvH,IAAI6B,QAAQ,GAAGD,WAAW,CAAC3B,KAAK;UAChC4B,QAAQ,CAAC7B,IAAI,CAACC,KAAK,CAAC;QACxB;MACJ,CAAC,CACD,OAAO6B,KAAK,EAAE;QAAEL,GAAG,GAAG;UAAEM,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIF,WAAW,IAAI,CAACA,WAAW,CAAC1B,IAAI,KAAKwB,EAAE,GAAGC,SAAS,CAACK,MAAM,CAAC,EAAEN,EAAE,CAAC5B,IAAI,CAAC6B,SAAS,CAAC;QACvF,CAAC,SACO;UAAE,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAACM,KAAK;QAAE;MACxC;MACA,IAAIE,CAAC,GAAGZ,KAAK,GAAGT,UAAU,GAAG,CAAC;MAC9B,IAAIqB,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGnB,UAAU,KAAK,CAAC,EAAE;QAChCI,OAAO,CAACgB,KAAK,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MAC9B;MACA,IAAI,EAAEd,KAAK,GAAGP,UAAU,KAAK,CAAC,EAAE;QAC5B,IAAIsB,QAAQ,GAAG,IAAI5B,SAAS,CAACW,OAAO,CAAC,CAAC;QACtCD,OAAO,CAACmB,IAAI,CAACD,QAAQ,CAAC;QACtBnB,UAAU,CAACjB,IAAI,CAACoC,QAAQ,CAACd,YAAY,CAAC,CAAC,CAAC;MAC5C;IACJ,CAAC,EAAE,YAAY;MACX,OAAOJ,OAAO,CAACnB,MAAM,GAAG,CAAC,EAAE;QACvBmB,OAAO,CAACgB,KAAK,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MAC9B;MACAlB,UAAU,CAACkB,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAE,UAAUG,GAAG,EAAE;MACd,OAAOpB,OAAO,CAACnB,MAAM,GAAG,CAAC,EAAE;QACvBmB,OAAO,CAACgB,KAAK,CAAC,CAAC,CAACH,KAAK,CAACO,GAAG,CAAC;MAC9B;MACArB,UAAU,CAACc,KAAK,CAACO,GAAG,CAAC;IACzB,CAAC,EAAE,YAAY;MACXlB,MAAM,GAAG,IAAI;MACbF,OAAO,GAAG,IAAI;IAClB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACAZ,OAAO,CAACC,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}