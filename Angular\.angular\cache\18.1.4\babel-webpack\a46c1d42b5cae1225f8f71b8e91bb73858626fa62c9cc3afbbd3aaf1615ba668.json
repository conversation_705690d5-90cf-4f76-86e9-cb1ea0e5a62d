{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.switchAll = void 0;\nvar switchMap_1 = require(\"./switchMap\");\nvar identity_1 = require(\"../util/identity\");\nfunction switchAll() {\n  return switchMap_1.switchMap(identity_1.identity);\n}\nexports.switchAll = switchAll;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "switchAll", "switchMap_1", "require", "identity_1", "switchMap", "identity"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/switchAll.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.switchAll = void 0;\nvar switchMap_1 = require(\"./switchMap\");\nvar identity_1 = require(\"../util/identity\");\nfunction switchAll() {\n    return switchMap_1.switchMap(identity_1.identity);\n}\nexports.switchAll = switchAll;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,WAAW,GAAGC,OAAO,CAAC,aAAa,CAAC;AACxC,IAAIC,UAAU,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AAC5C,SAASF,SAASA,CAAA,EAAG;EACjB,OAAOC,WAAW,CAACG,SAAS,CAACD,UAAU,CAACE,QAAQ,CAAC;AACrD;AACAP,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}