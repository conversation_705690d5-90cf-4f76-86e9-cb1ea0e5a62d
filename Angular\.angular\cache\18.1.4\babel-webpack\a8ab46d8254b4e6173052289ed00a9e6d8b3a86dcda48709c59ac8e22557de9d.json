{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.single = void 0;\nvar EmptyError_1 = require(\"../util/EmptyError\");\nvar SequenceError_1 = require(\"../util/SequenceError\");\nvar NotFoundError_1 = require(\"../util/NotFoundError\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction single(predicate) {\n  return lift_1.operate(function (source, subscriber) {\n    var hasValue = false;\n    var singleValue;\n    var seenValue = false;\n    var index = 0;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      seenValue = true;\n      if (!predicate || predicate(value, index++, source)) {\n        hasValue && subscriber.error(new SequenceError_1.SequenceError('Too many matching values'));\n        hasValue = true;\n        singleValue = value;\n      }\n    }, function () {\n      if (hasValue) {\n        subscriber.next(singleValue);\n        subscriber.complete();\n      } else {\n        subscriber.error(seenValue ? new NotFoundError_1.NotFoundError('No matching values') : new EmptyError_1.EmptyError());\n      }\n    }));\n  });\n}\nexports.single = single;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "single", "EmptyError_1", "require", "SequenceError_1", "NotFoundError_1", "lift_1", "OperatorSubscriber_1", "predicate", "operate", "source", "subscriber", "hasValue", "singleValue", "seenValue", "index", "subscribe", "createOperatorSubscriber", "error", "SequenceError", "next", "complete", "NotFoundError", "EmptyError"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/single.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.single = void 0;\nvar EmptyError_1 = require(\"../util/EmptyError\");\nvar SequenceError_1 = require(\"../util/SequenceError\");\nvar NotFoundError_1 = require(\"../util/NotFoundError\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction single(predicate) {\n    return lift_1.operate(function (source, subscriber) {\n        var hasValue = false;\n        var singleValue;\n        var seenValue = false;\n        var index = 0;\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            seenValue = true;\n            if (!predicate || predicate(value, index++, source)) {\n                hasValue && subscriber.error(new SequenceError_1.SequenceError('Too many matching values'));\n                hasValue = true;\n                singleValue = value;\n            }\n        }, function () {\n            if (hasValue) {\n                subscriber.next(singleValue);\n                subscriber.complete();\n            }\n            else {\n                subscriber.error(seenValue ? new NotFoundError_1.NotFoundError('No matching values') : new EmptyError_1.EmptyError());\n            }\n        }));\n    });\n}\nexports.single = single;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,MAAM,GAAG,KAAK,CAAC;AACvB,IAAIC,YAAY,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAChD,IAAIC,eAAe,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AACtD,IAAIE,eAAe,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AACtD,IAAIG,MAAM,GAAGH,OAAO,CAAC,cAAc,CAAC;AACpC,IAAII,oBAAoB,GAAGJ,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,MAAMA,CAACO,SAAS,EAAE;EACvB,OAAOF,MAAM,CAACG,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,WAAW;IACf,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,KAAK,GAAG,CAAC;IACbL,MAAM,CAACM,SAAS,CAACT,oBAAoB,CAACU,wBAAwB,CAACN,UAAU,EAAE,UAAUX,KAAK,EAAE;MACxFc,SAAS,GAAG,IAAI;MAChB,IAAI,CAACN,SAAS,IAAIA,SAAS,CAACR,KAAK,EAAEe,KAAK,EAAE,EAAEL,MAAM,CAAC,EAAE;QACjDE,QAAQ,IAAID,UAAU,CAACO,KAAK,CAAC,IAAId,eAAe,CAACe,aAAa,CAAC,0BAA0B,CAAC,CAAC;QAC3FP,QAAQ,GAAG,IAAI;QACfC,WAAW,GAAGb,KAAK;MACvB;IACJ,CAAC,EAAE,YAAY;MACX,IAAIY,QAAQ,EAAE;QACVD,UAAU,CAACS,IAAI,CAACP,WAAW,CAAC;QAC5BF,UAAU,CAACU,QAAQ,CAAC,CAAC;MACzB,CAAC,MACI;QACDV,UAAU,CAACO,KAAK,CAACJ,SAAS,GAAG,IAAIT,eAAe,CAACiB,aAAa,CAAC,oBAAoB,CAAC,GAAG,IAAIpB,YAAY,CAACqB,UAAU,CAAC,CAAC,CAAC;MACzH;IACJ,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACAxB,OAAO,CAACE,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}