{"ast": null, "code": "import _asyncToGenerator from \"D:/permittracker/Angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"../../services/permits.service\";\nimport * as i4 from \"../../services/custom-layout.utils.service\";\nimport * as i5 from \"../../services/http-utils.service\";\nimport * as i6 from \"@angular/common\";\nfunction AddEditInternalReviewDetailComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Review Detail - \", ctx_r1.reviewCategory || \"\", \"\");\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Add Review Detail - \", ctx_r1.reviewCategory || \"\", \"\");\n  }\n}\nfunction AddEditInternalReviewDetailComponent_form_27_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_form_27_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_form_27_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_form_27_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_form_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 17);\n    i0.ɵɵlistener(\"ngSubmit\", function AddEditInternalReviewDetailComponent_form_27_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 18)(2, \"div\", 19)(3, \"label\", 20);\n    i0.ɵɵtext(4, \"Sheet Number \");\n    i0.ɵɵelementStart(5, \"span\", 21);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"input\", 22);\n    i0.ɵɵtemplate(8, AddEditInternalReviewDetailComponent_form_27_div_8_Template, 2, 0, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 19)(10, \"label\", 20);\n    i0.ɵɵtext(11, \"Code Ref \");\n    i0.ɵɵelementStart(12, \"span\", 21);\n    i0.ɵɵtext(13, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(14, \"input\", 24);\n    i0.ɵɵtemplate(15, AddEditInternalReviewDetailComponent_form_27_div_15_Template, 2, 0, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 25)(17, \"div\", 26)(18, \"label\", 20);\n    i0.ɵɵtext(19, \"Code Description \");\n    i0.ɵɵelementStart(20, \"span\", 21);\n    i0.ɵɵtext(21, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(22, \"textarea\", 27);\n    i0.ɵɵtemplate(23, AddEditInternalReviewDetailComponent_form_27_div_23_Template, 2, 0, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 25)(25, \"div\", 26)(26, \"label\", 20);\n    i0.ɵɵtext(27, \"Reasoning \");\n    i0.ɵɵelementStart(28, \"span\", 21);\n    i0.ɵɵtext(29, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(30, \"textarea\", 28);\n    i0.ɵɵtemplate(31, AddEditInternalReviewDetailComponent_form_27_div_31_Template, 2, 0, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 25)(33, \"div\", 26)(34, \"label\", 20);\n    i0.ɵɵtext(35, \"Non Compliance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"textarea\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 25)(38, \"div\", 26)(39, \"label\", 20);\n    i0.ɵɵtext(40, \"Actionable Step\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(41, \"textarea\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 31)(43, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_form_27_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCancel());\n    });\n    i0.ɵɵtext(44, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"button\", 33);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.detailForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"sheetNumber\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"sheetNumber\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"codeRef\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"codeRef\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"codeDescription\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"codeDescription\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"reasoning\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"reasoning\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.isEdit ? \"Update\" : \"Create\");\n  }\n}\nexport class AddEditInternalReviewDetailComponent {\n  fb;\n  modal;\n  permitsService;\n  customLayoutUtilsService;\n  httpUtilsService;\n  permitId = null;\n  permitNumber = '';\n  reviewCategory = '';\n  internalCommentsId = null;\n  detailData = null; // For edit mode\n  loggedInUserId = 'user'; // Should be passed from parent\n  detailForm;\n  isEdit = false;\n  isLoading = false;\n  // Tabs removed; single-view form\n  formSubmitted = false;\n  showForm = false;\n  templateUrlPath = '/assets/excel/claimstempate.xlsx';\n  importedRows = [];\n  isImporting = false;\n  constructor(fb, modal, permitsService, customLayoutUtilsService, httpUtilsService) {\n    this.fb = fb;\n    this.modal = modal;\n    this.permitsService = permitsService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.httpUtilsService = httpUtilsService;\n  }\n  ngOnInit() {\n    this.isEdit = !!this.detailData;\n    this.detailForm = this.fb.group({\n      sheetNumber: [this.detailData?.sheetNumber || '', Validators.required],\n      codeRef: [this.detailData?.codeRef || '', Validators.required],\n      codeDescription: [this.detailData?.codeDescription || '', Validators.required],\n      reasoning: [this.detailData?.reasoning || '', Validators.required],\n      nonCompliance: [this.detailData?.nonCompliance || ''],\n      actionableStep: [this.detailData?.actionableStep || ''],\n      aeResponse: [this.detailData?.aeResponse || ''],\n      commentResponsedBy: [this.detailData?.commentResponsedBy || '']\n    });\n  }\n  // Tab navigation removed\n  shouldShowValidationError(fieldName) {\n    // Only show validation errors when form has been submitted\n    if (!this.formSubmitted) {\n      return false;\n    }\n    const field = this.detailForm.get(fieldName);\n    return !!(field && field.invalid);\n  }\n  onSubmit() {\n    this.formSubmitted = true;\n    if (this.detailForm.valid && this.permitId) {\n      this.isLoading = true;\n      // Enable common loader\n      this.httpUtilsService.loadingSubject.next(true);\n      const formData = {\n        ...this.detailForm.value,\n        permitId: this.permitId,\n        permitNumber: this.permitNumber,\n        reviewCategory: this.reviewCategory,\n        internalCommentsId: this.internalCommentsId || this.detailData?.internalCommentsId,\n        loggedInUserId: this.loggedInUserId\n      };\n      if (this.isEdit && this.detailData?.internalReviewCommentsId) {\n        formData.internalReviewCommentsId = this.detailData.internalReviewCommentsId;\n        this.permitsService.updateInternalPlanReviewDetail(formData).subscribe({\n          next: res => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to update review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess(res.responseData?.message || 'Review detail updated successfully!', '');\n              this.modal.close('updated');\n            }\n          },\n          error: err => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error updating review detail', '');\n            console.error(err);\n          }\n        });\n      } else {\n        this.permitsService.addInternalPlanReviewDetail(formData).subscribe({\n          next: res => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess('Review detail created successfully!', '');\n              this.modal.close('created');\n            }\n          },\n          error: err => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error creating review detail', '');\n            console.error(err);\n          }\n        });\n      }\n    } else {\n      this.detailForm.markAllAsTouched();\n      if (!this.permitId) {\n        this.customLayoutUtilsService.showError('Permit Id is required', '');\n      }\n    }\n  }\n  onCancel() {\n    this.modal.dismiss('cancelled');\n  }\n  onBack() {\n    this.modal.dismiss('back');\n  }\n  triggerFileImport(fileInput) {\n    if (this.isLoading) {\n      return;\n    }\n    fileInput.click();\n  }\n  onFileSelected(event) {\n    var _this = this;\n    const input = event.target;\n    const file = input?.files && input.files.length ? input.files[0] : null;\n    if (!file) {\n      return;\n    }\n    this.isImporting = true;\n    // Enable common loader\n    this.httpUtilsService.loadingSubject.next(true);\n    const reader = new FileReader();\n    reader.onload = /*#__PURE__*/_asyncToGenerator(function* () {\n      try {\n        const XLSX = yield import('xlsx');\n        const data = new Uint8Array(reader.result);\n        const workbook = XLSX.read(data, {\n          type: 'array'\n        });\n        const firstSheetName = workbook.SheetNames[0];\n        const worksheet = workbook.Sheets[firstSheetName];\n        const json = XLSX.utils.sheet_to_json(worksheet, {\n          defval: ''\n        });\n        _this.importedRows = _this.mapExcelRows(json);\n        if (!_this.importedRows.length) {\n          _this.customLayoutUtilsService.showError('No rows found in the uploaded Excel.', '');\n        } else {\n          _this.customLayoutUtilsService.showSuccess(`Imported ${_this.importedRows.length} row(s).`, '');\n        }\n      } catch (err) {\n        console.error('Error parsing Excel', err);\n        _this.importedRows = [];\n        _this.customLayoutUtilsService.showError('Failed to parse Excel file. Please check the template.', '');\n      } finally {\n        _this.isImporting = false;\n        // Disable common loader\n        _this.httpUtilsService.loadingSubject.next(false);\n        // Reset file input so the same file can be selected again if needed\n        input.value = '';\n      }\n    });\n    reader.readAsArrayBuffer(file);\n  }\n  normalizeHeader(header) {\n    return (header || '').toString().trim().toLowerCase().replace(/\\s+/g, '');\n  }\n  mapExcelRows(jsonRows) {\n    if (!jsonRows || !jsonRows.length) {\n      return [];\n    }\n    const headerMap = {};\n    // Build header map from first row's keys\n    const firstRow = jsonRows[0];\n    Object.keys(firstRow).forEach(key => {\n      const norm = this.normalizeHeader(key);\n      headerMap[norm] = key;\n    });\n    const pick = (row, keyCandidates) => {\n      for (const candidate of keyCandidates) {\n        const norm = this.normalizeHeader(candidate);\n        const actual = headerMap[norm];\n        if (actual && row.hasOwnProperty(actual)) {\n          return (row[actual] ?? '').toString();\n        }\n      }\n      return '';\n    };\n    // Expected columns with common variants\n    const rows = jsonRows.map(row => {\n      return {\n        sheetNumber: pick(row, ['Sheet Number', 'SheetNumber', 'Sheet#', 'Sheet No']),\n        codeRef: pick(row, ['Code Ref', 'Code Reference', 'CodeRef']),\n        codeDescription: pick(row, ['Code Description', 'Description']),\n        reasoning: pick(row, ['Reasoning', 'Reason']),\n        nonCompliance: pick(row, ['Non Compliance', 'Non-Compliance', 'NonCompliance']),\n        actionableStep: pick(row, ['Actionable Step', 'ActionableStep', 'Action Step']),\n        aeResponse: pick(row, ['A/E Response', 'AE Response', 'AEResponse']),\n        commentResponsedBy: pick(row, ['Comment Responded By', 'Comment Responsed By', 'Responded By'])\n      };\n    });\n    // Filter out completely empty rows\n    return rows.filter(r => Object.values(r).some(v => (v || '').toString().trim() !== ''));\n  }\n  deleteImportedRow(index) {\n    if (index < 0 || index >= this.importedRows.length) {\n      return;\n    }\n    this.importedRows.splice(index, 1);\n  }\n  saveAllImported() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.permitId) {\n        _this2.customLayoutUtilsService.showError('Permit Id is required to save imported rows', '');\n        return;\n      }\n      if (!_this2.reviewCategory) {\n        _this2.customLayoutUtilsService.showError('Review Category is required to save imported rows', '');\n        return;\n      }\n      if (!_this2.importedRows.length) {\n        _this2.customLayoutUtilsService.showError('No imported rows to save', '');\n        return;\n      }\n      _this2.isLoading = true;\n      // Enable common loader\n      _this2.httpUtilsService.loadingSubject.next(true);\n      try {\n        const requests = _this2.importedRows.map(r => {\n          const payload = {\n            sheetNumber: r.sheetNumber || '',\n            codeRef: r.codeRef || '',\n            codeDescription: r.codeDescription || '',\n            reasoning: r.reasoning || '',\n            nonCompliance: r.nonCompliance || '',\n            actionableStep: r.actionableStep || '',\n            aeResponse: r.aeResponse || '',\n            commentResponsedBy: r.commentResponsedBy || '',\n            permitId: _this2.permitId,\n            permitNumber: _this2.permitNumber,\n            reviewCategory: _this2.reviewCategory,\n            internalCommentsId: _this2.internalCommentsId,\n            loggedInUserId: _this2.loggedInUserId\n          };\n          return _this2.permitsService.addInternalPlanReviewDetail(payload);\n        });\n        // Execute all in parallel\n        yield new Promise((resolve, reject) => {\n          const {\n            forkJoin\n          } = require('rxjs');\n          forkJoin(requests).subscribe({\n            next: resolve,\n            error: reject\n          });\n        });\n        _this2.customLayoutUtilsService.showSuccess('All imported rows saved successfully!', '');\n        _this2.importedRows = [];\n        // Optionally close modal or refresh parent via close value\n        _this2.modal.close('bulk-created');\n      } catch (err) {\n        console.error('Error saving imported rows', err);\n        _this2.customLayoutUtilsService.showError('Failed to save one or more imported rows', '');\n      } finally {\n        _this2.isLoading = false;\n        // Disable common loader\n        _this2.httpUtilsService.loadingSubject.next(false);\n      }\n    })();\n  }\n  get isFormValid() {\n    return this.detailForm.valid;\n  }\n  get isDetailsValid() {\n    if (!this.detailForm) {\n      return false;\n    }\n    const controls = this.detailForm.controls;\n    return !!controls.sheetNumber?.valid && !!controls.codeRef?.valid && !!controls.codeDescription?.valid && !!controls.reasoning?.valid;\n  }\n  static ɵfac = function AddEditInternalReviewDetailComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AddEditInternalReviewDetailComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.NgbActiveModal), i0.ɵɵdirectiveInject(i3.PermitsService), i0.ɵɵdirectiveInject(i4.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i5.HttpUtilsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddEditInternalReviewDetailComponent,\n    selectors: [[\"app-add-edit-internal-review-detail\"]],\n    inputs: {\n      permitId: \"permitId\",\n      permitNumber: \"permitNumber\",\n      reviewCategory: \"reviewCategory\",\n      internalCommentsId: \"internalCommentsId\",\n      detailData: \"detailData\",\n      loggedInUserId: \"loggedInUserId\"\n    },\n    decls: 28,\n    vars: 5,\n    consts: [[\"fileInput\", \"\"], [1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", \"btn-sm\", 3, \"click\"], [1, \"d-flex\", \"align-items-center\"], [\"download\", \"\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"href\"], [\"type\", \"file\", \"accept\", \".xlsx,.xls\", 1, \"d-none\", 3, \"change\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"me-2\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [\"role\", \"alert\", 1, \"alert\", \"alert-info\", \"mt-4\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"novalidate\", \"\", 3, \"ngSubmit\", \"formGroup\"], [1, \"row\", \"mt-3\"], [1, \"col-xl-6\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"text-danger\"], [\"type\", \"text\", \"formControlName\", \"sheetNumber\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"codeRef\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [1, \"row\", \"mt-4\"], [1, \"col-xl-12\"], [\"formControlName\", \"codeDescription\", \"rows\", \"3\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"reasoning\", \"rows\", \"3\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"nonCompliance\", \"rows\", \"3\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"actionableStep\", \"rows\", \"3\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [1, \"modal-footer\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"me-2\", \"mr-2\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"disabled\"], [1, \"invalid-feedback\"]],\n    template: function AddEditInternalReviewDetailComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵtemplate(4, AddEditInternalReviewDetailComponent_div_4_Template, 2, 1, \"div\", 4)(5, AddEditInternalReviewDetailComponent_div_5_Template, 2, 1, \"div\", 4);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 5)(7, \"i\", 6);\n        i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_Template_i_click_7_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onCancel());\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8)(10, \"div\")(11, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_Template_button_click_11_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onBack());\n        });\n        i0.ɵɵtext(12, \"Back\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"div\", 10)(14, \"a\", 11);\n        i0.ɵɵtext(15, \"Download Template\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"input\", 12, 0);\n        i0.ɵɵlistener(\"change\", function AddEditInternalReviewDetailComponent_Template_input_change_16_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onFileSelected($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"button\", 13);\n        i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_Template_button_click_18_listener() {\n          i0.ɵɵrestoreView(_r1);\n          const fileInput_r3 = i0.ɵɵreference(17);\n          return i0.ɵɵresetView(ctx.triggerFileImport(fileInput_r3));\n        });\n        i0.ɵɵtext(19, \"Import Excel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"button\", 14);\n        i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_Template_button_click_20_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.showForm = true);\n        });\n        i0.ɵɵtext(21, \"Add Review Detail\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(22, \"div\", 15);\n        i0.ɵɵtext(23, \" You can upload review details by downloading the template, filling it, and importing the Excel. Or click \");\n        i0.ɵɵelementStart(24, \"strong\");\n        i0.ɵɵtext(25, \"Add Review Detail\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(26, \" to enter details manually. \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(27, AddEditInternalReviewDetailComponent_form_27_Template, 47, 22, \"form\", 16);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.isEdit);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isEdit);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"href\", ctx.templateUrlPath, i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngIf\", ctx.showForm);\n      }\n    },\n    dependencies: [i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "reviewCategory", "ɵɵlistener", "AddEditInternalReviewDetailComponent_form_27_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ɵɵelement", "ɵɵtemplate", "AddEditInternalReviewDetailComponent_form_27_div_8_Template", "AddEditInternalReviewDetailComponent_form_27_div_15_Template", "AddEditInternalReviewDetailComponent_form_27_div_23_Template", "AddEditInternalReviewDetailComponent_form_27_div_31_Template", "AddEditInternalReviewDetailComponent_form_27_Template_button_click_43_listener", "onCancel", "ɵɵproperty", "detailForm", "ɵɵclassProp", "shouldShowValidationError", "isLoading", "ɵɵtextInterpolate", "isEdit", "AddEditInternalReviewDetailComponent", "fb", "modal", "permitsService", "customLayoutUtilsService", "httpUtilsService", "permitId", "permitNumber", "internalCommentsId", "detailData", "loggedInUserId", "formSubmitted", "showForm", "templateUrlPath", "importedRows", "isImporting", "constructor", "ngOnInit", "group", "sheetNumber", "required", "codeRef", "codeDescription", "reasoning", "nonCompliance", "actionableStep", "aeResponse", "commentResponsedBy", "fieldName", "field", "get", "invalid", "valid", "loadingSubject", "next", "formData", "value", "internalReviewCommentsId", "updateInternalPlanReviewDetail", "subscribe", "res", "<PERSON><PERSON><PERSON>", "showError", "faultMessage", "showSuccess", "responseData", "message", "close", "error", "err", "console", "addInternalPlanReviewDetail", "mark<PERSON>llAsTouched", "dismiss", "onBack", "triggerFileImport", "fileInput", "click", "onFileSelected", "event", "_this", "input", "target", "file", "files", "length", "reader", "FileReader", "onload", "_asyncToGenerator", "XLSX", "data", "Uint8Array", "result", "workbook", "read", "type", "firstSheetName", "SheetNames", "worksheet", "Sheets", "json", "utils", "sheet_to_json", "defval", "mapExcelRows", "readAsA<PERSON>y<PERSON><PERSON>er", "normalizeHeader", "header", "toString", "trim", "toLowerCase", "replace", "jsonRows", "headerMap", "firstRow", "Object", "keys", "for<PERSON>ach", "key", "norm", "pick", "row", "keyCandidates", "candidate", "actual", "hasOwnProperty", "rows", "map", "filter", "r", "values", "some", "v", "deleteImportedRow", "index", "splice", "saveAllImported", "_this2", "requests", "payload", "Promise", "resolve", "reject", "fork<PERSON><PERSON>n", "require", "isFormValid", "isDetailsValid", "controls", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "NgbActiveModal", "i3", "PermitsService", "i4", "CustomLayoutUtilsService", "i5", "HttpUtilsService", "selectors", "inputs", "decls", "vars", "consts", "template", "AddEditInternalReviewDetailComponent_Template", "rf", "ctx", "ɵɵelementContainerStart", "AddEditInternalReviewDetailComponent_div_4_Template", "AddEditInternalReviewDetailComponent_div_5_Template", "AddEditInternalReviewDetailComponent_Template_i_click_7_listener", "_r1", "AddEditInternalReviewDetailComponent_Template_button_click_11_listener", "AddEditInternalReviewDetailComponent_Template_input_change_16_listener", "$event", "AddEditInternalReviewDetailComponent_Template_button_click_18_listener", "fileInput_r3", "ɵɵreference", "AddEditInternalReviewDetailComponent_Template_button_click_20_listener", "AddEditInternalReviewDetailComponent_form_27_Template", "ɵɵsanitizeUrl"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\add-edit-internal-review-detail\\add-edit-internal-review-detail.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\add-edit-internal-review-detail\\add-edit-internal-review-detail.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\nimport { PermitsService } from '../../services/permits.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { HttpUtilsService } from '../../services/http-utils.service';\n\n@Component({\n  selector: 'app-add-edit-internal-review-detail',\n  templateUrl: './add-edit-internal-review-detail.component.html',\n  styleUrls: ['./add-edit-internal-review-detail.component.scss']\n})\nexport class AddEditInternalReviewDetailComponent implements OnInit {\n  @Input() permitId: number | null = null;\n  @Input() permitNumber: string = '';\n  @Input() reviewCategory: string = '';\n  @Input() internalCommentsId: number | null = null;\n  @Input() detailData: any = null; // For edit mode\n  @Input() loggedInUserId: string = 'user'; // Should be passed from parent\n\n  detailForm!: FormGroup;\n  isEdit: boolean = false;\n  isLoading: boolean = false;\n  // Tabs removed; single-view form\n  formSubmitted: boolean = false;\n  showForm: boolean = false;\n  templateUrlPath: string = '/assets/excel/claimstempate.xlsx';\n  importedRows: Array<any> = [];\n  isImporting: boolean = false;\n\n  constructor(\n    private fb: FormBuilder,\n    public modal: NgbActiveModal,\n    private permitsService: PermitsService,\n    private customLayoutUtilsService: CustomLayoutUtilsService,\n    private httpUtilsService: HttpUtilsService\n  ) {}\n\n  ngOnInit(): void {\n    this.isEdit = !!this.detailData;\n    this.detailForm = this.fb.group({\n      sheetNumber: [this.detailData?.sheetNumber || '', Validators.required],\n      codeRef: [this.detailData?.codeRef || '', Validators.required],\n      codeDescription: [this.detailData?.codeDescription || '', Validators.required],\n      reasoning: [this.detailData?.reasoning || '', Validators.required],\n      nonCompliance: [this.detailData?.nonCompliance || ''],\n      actionableStep: [this.detailData?.actionableStep || ''],\n      aeResponse: [this.detailData?.aeResponse || ''],\n      commentResponsedBy: [this.detailData?.commentResponsedBy || ''],\n    });\n  }\n\n  // Tab navigation removed\n\n  shouldShowValidationError(fieldName: string): boolean {\n    // Only show validation errors when form has been submitted\n    if (!this.formSubmitted) {\n      return false;\n    }\n    \n    const field = this.detailForm.get(fieldName);\n    return !!(field && field.invalid);\n  }\n\n  onSubmit(): void {\n    this.formSubmitted = true;\n    if (this.detailForm.valid && this.permitId) {\n      this.isLoading = true;\n      // Enable common loader\n      this.httpUtilsService.loadingSubject.next(true);\n      \n      const formData: any = {\n        ...this.detailForm.value,\n        permitId: this.permitId,\n        permitNumber: this.permitNumber,\n        reviewCategory: this.reviewCategory,\n        internalCommentsId: this.internalCommentsId || this.detailData?.internalCommentsId,\n        loggedInUserId: this.loggedInUserId\n      };\n      if (this.isEdit && this.detailData?.internalReviewCommentsId) {\n        formData.internalReviewCommentsId = this.detailData.internalReviewCommentsId;\n        this.permitsService.updateInternalPlanReviewDetail(formData).subscribe({\n          next: (res: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to update review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess(res.responseData?.message || 'Review detail updated successfully!', '');\n              this.modal.close('updated');\n            }\n          },\n          error: (err: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error updating review detail', '');\n            console.error(err);\n          }\n        });\n      } else {\n        this.permitsService.addInternalPlanReviewDetail(formData).subscribe({\n          next: (res: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess('Review detail created successfully!', '');\n              this.modal.close('created');\n            }\n          },\n          error: (err: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error creating review detail', '');\n            console.error(err);\n          }\n        });\n      }\n    } else {\n      this.detailForm.markAllAsTouched();\n      if (!this.permitId) {\n        this.customLayoutUtilsService.showError('Permit Id is required', '');\n      }\n    }\n  }\n\n  onCancel(): void {\n    this.modal.dismiss('cancelled');\n  }\n\n  onBack(): void {\n    this.modal.dismiss('back');\n  }\n\n  triggerFileImport(fileInput: HTMLInputElement): void {\n    if (this.isLoading) { return; }\n    fileInput.click();\n  }\n\n  onFileSelected(event: Event): void {\n    const input = event.target as HTMLInputElement;\n    const file = input?.files && input.files.length ? input.files[0] : null;\n    if (!file) { return; }\n    this.isImporting = true;\n    // Enable common loader\n    this.httpUtilsService.loadingSubject.next(true);\n    const reader = new FileReader();\n    reader.onload = async () => {\n      try {\n        const XLSX: any = await import('xlsx');\n        const data = new Uint8Array(reader.result as ArrayBuffer);\n        const workbook = XLSX.read(data, { type: 'array' });\n        const firstSheetName = workbook.SheetNames[0];\n        const worksheet = workbook.Sheets[firstSheetName];\n        const json: any[] = XLSX.utils.sheet_to_json(worksheet, { defval: '' });\n        this.importedRows = this.mapExcelRows(json);\n        if (!this.importedRows.length) {\n          this.customLayoutUtilsService.showError('No rows found in the uploaded Excel.', '');\n        } else {\n          this.customLayoutUtilsService.showSuccess(`Imported ${this.importedRows.length} row(s).`, '');\n        }\n      } catch (err) {\n        console.error('Error parsing Excel', err);\n        this.importedRows = [];\n        this.customLayoutUtilsService.showError('Failed to parse Excel file. Please check the template.', '');\n      } finally {\n        this.isImporting = false;\n        // Disable common loader\n        this.httpUtilsService.loadingSubject.next(false);\n        // Reset file input so the same file can be selected again if needed\n        input.value = '';\n      }\n    };\n    reader.readAsArrayBuffer(file);\n  }\n\n  private normalizeHeader(header: string): string {\n    return (header || '').toString().trim().toLowerCase().replace(/\\s+/g, '');\n  }\n\n  private mapExcelRows(jsonRows: any[]): Array<any> {\n    if (!jsonRows || !jsonRows.length) { return []; }\n    const headerMap: any = {};\n    // Build header map from first row's keys\n    const firstRow = jsonRows[0];\n    Object.keys(firstRow).forEach((key) => {\n      const norm = this.normalizeHeader(key);\n      headerMap[norm] = key;\n    });\n\n    const pick = (row: any, keyCandidates: string[]): string => {\n      for (const candidate of keyCandidates) {\n        const norm = this.normalizeHeader(candidate);\n        const actual = headerMap[norm];\n        if (actual && row.hasOwnProperty(actual)) {\n          return (row[actual] ?? '').toString();\n        }\n      }\n      return '';\n    };\n\n    // Expected columns with common variants\n    const rows = jsonRows.map((row) => {\n      return {\n        sheetNumber: pick(row, ['Sheet Number', 'SheetNumber', 'Sheet#', 'Sheet No']),\n        codeRef: pick(row, ['Code Ref', 'Code Reference', 'CodeRef']),\n        codeDescription: pick(row, ['Code Description', 'Description']),\n        reasoning: pick(row, ['Reasoning', 'Reason']),\n        nonCompliance: pick(row, ['Non Compliance', 'Non-Compliance', 'NonCompliance']),\n        actionableStep: pick(row, ['Actionable Step', 'ActionableStep', 'Action Step']),\n        aeResponse: pick(row, ['A/E Response', 'AE Response', 'AEResponse']),\n        commentResponsedBy: pick(row, ['Comment Responded By', 'Comment Responsed By', 'Responded By'])\n      };\n    });\n    // Filter out completely empty rows\n    return rows.filter((r) => Object.values(r).some((v) => (v || '').toString().trim() !== ''));\n  }\n\n  deleteImportedRow(index: number): void {\n    if (index < 0 || index >= this.importedRows.length) { return; }\n    this.importedRows.splice(index, 1);\n  }\n\n  async saveAllImported(): Promise<void> {\n    if (!this.permitId) {\n      this.customLayoutUtilsService.showError('Permit Id is required to save imported rows', '');\n      return;\n    }\n    if (!this.reviewCategory) {\n      this.customLayoutUtilsService.showError('Review Category is required to save imported rows', '');\n      return;\n    }\n    if (!this.importedRows.length) {\n      this.customLayoutUtilsService.showError('No imported rows to save', '');\n      return;\n    }\n    this.isLoading = true;\n    // Enable common loader\n    this.httpUtilsService.loadingSubject.next(true);\n    try {\n      const requests = this.importedRows.map((r) => {\n        const payload: any = {\n          sheetNumber: r.sheetNumber || '',\n          codeRef: r.codeRef || '',\n          codeDescription: r.codeDescription || '',\n          reasoning: r.reasoning || '',\n          nonCompliance: r.nonCompliance || '',\n          actionableStep: r.actionableStep || '',\n          aeResponse: r.aeResponse || '',\n          commentResponsedBy: r.commentResponsedBy || '',\n          permitId: this.permitId,\n          permitNumber: this.permitNumber,\n          reviewCategory: this.reviewCategory,\n          internalCommentsId: this.internalCommentsId,\n          loggedInUserId: this.loggedInUserId\n        };\n        return this.permitsService.addInternalPlanReviewDetail(payload);\n      });\n      // Execute all in parallel\n      await new Promise((resolve, reject) => {\n        const { forkJoin } = require('rxjs');\n        forkJoin(requests).subscribe({ next: resolve, error: reject });\n      });\n      this.customLayoutUtilsService.showSuccess('All imported rows saved successfully!', '');\n      this.importedRows = [];\n      // Optionally close modal or refresh parent via close value\n      this.modal.close('bulk-created');\n    } catch (err) {\n      console.error('Error saving imported rows', err);\n      this.customLayoutUtilsService.showError('Failed to save one or more imported rows', '');\n    } finally {\n      this.isLoading = false;\n      // Disable common loader\n      this.httpUtilsService.loadingSubject.next(false);\n    }\n  }\n\n  get isFormValid(): boolean {\n    return this.detailForm.valid;\n  }\n\n  get isDetailsValid(): boolean {\n    if (!this.detailForm) { return false; }\n    const controls = this.detailForm.controls as any;\n    return (\n      !!controls.sheetNumber?.valid &&\n      !!controls.codeRef?.valid &&\n      !!controls.codeDescription?.valid &&\n      !!controls.reasoning?.valid\n    );\n  }\n}\n", "<div class=\"modal-content h-auto\">\n  <!-- Header -->\n  <div class=\"modal-header bg-light-primary\">\n    <div class=\"modal-title h5 fs-3\">\n      <ng-container>\n        <div *ngIf=\"isEdit\">Edit Review Detail - {{ reviewCategory || '' }}</div>\n        <div *ngIf=\"!isEdit\">Add Review Detail - {{ reviewCategory || '' }}</div>\n      </ng-container>\n    </div>\n    <div class=\"float-right\">\n      <i class=\"fa-solid fs-2 fa-xmark text-white\" (click)=\"onCancel()\"></i>\n    </div>\n  </div>\n\n  <!-- Body -->\n  <div class=\"modal-body\">\n    <div class=\"d-flex justify-content-between align-items-center\">\n      <div>\n        <button type=\"button\" class=\"btn btn-light btn-sm\" (click)=\"onBack()\">Back</button>\n      </div>\n      <div class=\"d-flex align-items-center\">\n        <a [href]=\"templateUrlPath\" download class=\"btn btn-outline-secondary btn-sm me-2\">Download Template</a>\n        <input #fileInput type=\"file\" class=\"d-none\" accept=\".xlsx,.xls\" (change)=\"onFileSelected($event)\" />\n        <button type=\"button\" class=\"btn btn-outline-primary btn-sm me-2\" (click)=\"triggerFileImport(fileInput)\" [disabled]=\"isLoading\">Import Excel</button>\n        <button type=\"button\" class=\"btn btn-primary btn-sm\" (click)=\"showForm = true\">Add Review Detail</button>\n      </div>\n    </div>\n\n    <div class=\"alert alert-info mt-4\" role=\"alert\">\n      You can upload review details by downloading the template, filling it, and importing the Excel. Or click <strong>Add Review Detail</strong> to enter details manually.\n    </div>\n\n    <!-- Form: single view (Review Details + Actionable Step) -->\n    <form *ngIf=\"showForm\" [formGroup]=\"detailForm\" (ngSubmit)=\"onSubmit()\" novalidate>\n      <div class=\"row mt-3\">\n        <div class=\"col-xl-6\">\n          <label class=\"fw-bold form-label mb-2\">Sheet Number <span class=\"text-danger\">*</span></label>\n          <input type=\"text\" formControlName=\"sheetNumber\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('sheetNumber')\" placeholder=\"Type here\" [disabled]=\"isLoading\" />\n          <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('sheetNumber')\">Required Field</div>\n        </div>\n        <div class=\"col-xl-6\">\n          <label class=\"fw-bold form-label mb-2\">Code Ref <span class=\"text-danger\">*</span></label>\n          <input type=\"text\" formControlName=\"codeRef\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('codeRef')\" placeholder=\"Type here\" [disabled]=\"isLoading\" />\n          <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('codeRef')\">Required Field</div>\n        </div>\n      </div>\n      <div class=\"row mt-4\">\n        <div class=\"col-xl-12\">\n          <label class=\"fw-bold form-label mb-2\">Code Description <span class=\"text-danger\">*</span></label>\n          <textarea formControlName=\"codeDescription\" rows=\"3\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('codeDescription')\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n          <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('codeDescription')\">Required Field</div>\n        </div>\n      </div>\n      <div class=\"row mt-4\">\n        <div class=\"col-xl-12\">\n          <label class=\"fw-bold form-label mb-2\">Reasoning <span class=\"text-danger\">*</span></label>\n          <textarea formControlName=\"reasoning\" rows=\"3\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('reasoning')\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n          <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('reasoning')\">Required Field</div>\n        </div>\n      </div>\n      <div class=\"row mt-4\">\n        <div class=\"col-xl-12\">\n          <label class=\"fw-bold form-label mb-2\">Non Compliance</label>\n          <textarea formControlName=\"nonCompliance\" rows=\"3\" class=\"form-control form-control-sm\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n        </div>\n      </div>\n      <div class=\"row mt-4\">\n        <div class=\"col-xl-12\">\n          <label class=\"fw-bold form-label mb-2\">Actionable Step</label>\n          <textarea formControlName=\"actionableStep\" rows=\"3\" class=\"form-control form-control-sm\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n        </div>\n      </div>\n\n      <div class=\"modal-footer justify-content-end\">\n        <button type=\"button\" class=\"btn btn-danger btn-sm btn-elevate me-2 mr-2\" (click)=\"onCancel()\" [disabled]=\"isLoading\">Cancel</button>\n        <button type=\"submit\" class=\"btn btn-primary btn-sm\" [disabled]=\"isLoading\">{{ isEdit ? 'Update' : 'Create' }}</button>\n      </div>\n    </form>\n  </div>\n</div>\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;ICI3DC,EAAA,CAAAC,cAAA,UAAoB;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAArDH,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAK,kBAAA,0BAAAC,MAAA,CAAAC,cAAA,WAA+C;;;;;IACnEP,EAAA,CAAAC,cAAA,UAAqB;IAAAD,EAAA,CAAAE,MAAA,GAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAApDH,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAK,kBAAA,yBAAAC,MAAA,CAAAC,cAAA,WAA8C;;;;;IAgCjEP,EAAA,CAAAC,cAAA,cAA+E;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKnGH,EAAA,CAAAC,cAAA,cAA2E;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAO/FH,EAAA,CAAAC,cAAA,cAAmF;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAOvGH,EAAA,CAAAC,cAAA,cAA6E;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAxBvGH,EAAA,CAAAC,cAAA,eAAmF;IAAnCD,EAAA,CAAAQ,UAAA,sBAAAC,+EAAA;MAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAYP,MAAA,CAAAQ,QAAA,EAAU;IAAA,EAAC;IAGjEd,EAFJ,CAAAC,cAAA,cAAsB,cACE,gBACmB;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC9FH,EAAA,CAAAe,SAAA,gBAAqM;IACrMf,EAAA,CAAAgB,UAAA,IAAAC,2DAAA,kBAA+E;IACjFjB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC1FH,EAAA,CAAAe,SAAA,iBAA6L;IAC7Lf,EAAA,CAAAgB,UAAA,KAAAE,4DAAA,kBAA2E;IAE/ElB,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAClGH,EAAA,CAAAe,SAAA,oBAAsN;IACtNf,EAAA,CAAAgB,UAAA,KAAAG,4DAAA,kBAAmF;IAEvFnB,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC3FH,EAAA,CAAAe,SAAA,oBAA0M;IAC1Mf,EAAA,CAAAgB,UAAA,KAAAI,4DAAA,kBAA6E;IAEjFpB,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7DH,EAAA,CAAAe,SAAA,oBAAkJ;IAEtJf,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAe,SAAA,oBAAmJ;IAEvJf,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAA8C,kBAC0E;IAA5CD,EAAA,CAAAQ,UAAA,mBAAAa,+EAAA;MAAArB,EAAA,CAAAU,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAgB,QAAA,EAAU;IAAA,EAAC;IAAwBtB,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrIH,EAAA,CAAAC,cAAA,kBAA4E;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAElHF,EAFkH,CAAAG,YAAA,EAAS,EACnH,EACD;;;;IA5CgBH,EAAA,CAAAuB,UAAA,cAAAjB,MAAA,CAAAkB,UAAA,CAAwB;IAI6CxB,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAyB,WAAA,eAAAnB,MAAA,CAAAoB,yBAAA,gBAA6D;IAAyB1B,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAqB,SAAA,CAAsB;IACnK3B,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAoB,yBAAA,gBAA8C;IAIK1B,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAyB,WAAA,eAAAnB,MAAA,CAAAoB,yBAAA,YAAyD;IAAyB1B,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAqB,SAAA,CAAsB;IAC3J3B,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAoB,yBAAA,YAA0C;IAMiB1B,EAAA,CAAAI,SAAA,GAAiE;IAAjEJ,EAAA,CAAAyB,WAAA,eAAAnB,MAAA,CAAAoB,yBAAA,oBAAiE;IAAyB1B,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAqB,SAAA,CAAsB;IAC3K3B,EAAA,CAAAI,SAAA,EAAkD;IAAlDJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAoB,yBAAA,oBAAkD;IAMG1B,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAyB,WAAA,eAAAnB,MAAA,CAAAoB,yBAAA,cAA2D;IAAyB1B,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAqB,SAAA,CAAsB;IAC/J3B,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAoB,yBAAA,cAA4C;IAMqC1B,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAqB,SAAA,CAAsB;IAMrB3B,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAqB,SAAA,CAAsB;IAK1C3B,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAqB,SAAA,CAAsB;IAChE3B,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAqB,SAAA,CAAsB;IAAC3B,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAA4B,iBAAA,CAAAtB,MAAA,CAAAuB,MAAA,uBAAkC;;;AD/DtH,OAAM,MAAOC,oCAAoC;EAmBrCC,EAAA;EACDC,KAAA;EACCC,cAAA;EACAC,wBAAA;EACAC,gBAAA;EAtBDC,QAAQ,GAAkB,IAAI;EAC9BC,YAAY,GAAW,EAAE;EACzB9B,cAAc,GAAW,EAAE;EAC3B+B,kBAAkB,GAAkB,IAAI;EACxCC,UAAU,GAAQ,IAAI,CAAC,CAAC;EACxBC,cAAc,GAAW,MAAM,CAAC,CAAC;EAE1ChB,UAAU;EACVK,MAAM,GAAY,KAAK;EACvBF,SAAS,GAAY,KAAK;EAC1B;EACAc,aAAa,GAAY,KAAK;EAC9BC,QAAQ,GAAY,KAAK;EACzBC,eAAe,GAAW,kCAAkC;EAC5DC,YAAY,GAAe,EAAE;EAC7BC,WAAW,GAAY,KAAK;EAE5BC,YACUf,EAAe,EAChBC,KAAqB,EACpBC,cAA8B,EAC9BC,wBAAkD,EAClDC,gBAAkC;IAJlC,KAAAJ,EAAE,GAAFA,EAAE;IACH,KAAAC,KAAK,GAALA,KAAK;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,gBAAgB,GAAhBA,gBAAgB;EACvB;EAEHY,QAAQA,CAAA;IACN,IAAI,CAAClB,MAAM,GAAG,CAAC,CAAC,IAAI,CAACU,UAAU;IAC/B,IAAI,CAACf,UAAU,GAAG,IAAI,CAACO,EAAE,CAACiB,KAAK,CAAC;MAC9BC,WAAW,EAAE,CAAC,IAAI,CAACV,UAAU,EAAEU,WAAW,IAAI,EAAE,EAAElD,UAAU,CAACmD,QAAQ,CAAC;MACtEC,OAAO,EAAE,CAAC,IAAI,CAACZ,UAAU,EAAEY,OAAO,IAAI,EAAE,EAAEpD,UAAU,CAACmD,QAAQ,CAAC;MAC9DE,eAAe,EAAE,CAAC,IAAI,CAACb,UAAU,EAAEa,eAAe,IAAI,EAAE,EAAErD,UAAU,CAACmD,QAAQ,CAAC;MAC9EG,SAAS,EAAE,CAAC,IAAI,CAACd,UAAU,EAAEc,SAAS,IAAI,EAAE,EAAEtD,UAAU,CAACmD,QAAQ,CAAC;MAClEI,aAAa,EAAE,CAAC,IAAI,CAACf,UAAU,EAAEe,aAAa,IAAI,EAAE,CAAC;MACrDC,cAAc,EAAE,CAAC,IAAI,CAAChB,UAAU,EAAEgB,cAAc,IAAI,EAAE,CAAC;MACvDC,UAAU,EAAE,CAAC,IAAI,CAACjB,UAAU,EAAEiB,UAAU,IAAI,EAAE,CAAC;MAC/CC,kBAAkB,EAAE,CAAC,IAAI,CAAClB,UAAU,EAAEkB,kBAAkB,IAAI,EAAE;KAC/D,CAAC;EACJ;EAEA;EAEA/B,yBAAyBA,CAACgC,SAAiB;IACzC;IACA,IAAI,CAAC,IAAI,CAACjB,aAAa,EAAE;MACvB,OAAO,KAAK;IACd;IAEA,MAAMkB,KAAK,GAAG,IAAI,CAACnC,UAAU,CAACoC,GAAG,CAACF,SAAS,CAAC;IAC5C,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAACE,OAAO,CAAC;EACnC;EAEA/C,QAAQA,CAAA;IACN,IAAI,CAAC2B,aAAa,GAAG,IAAI;IACzB,IAAI,IAAI,CAACjB,UAAU,CAACsC,KAAK,IAAI,IAAI,CAAC1B,QAAQ,EAAE;MAC1C,IAAI,CAACT,SAAS,GAAG,IAAI;MACrB;MACA,IAAI,CAACQ,gBAAgB,CAAC4B,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;MAE/C,MAAMC,QAAQ,GAAQ;QACpB,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;QACxB9B,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBC,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/B9B,cAAc,EAAE,IAAI,CAACA,cAAc;QACnC+B,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,IAAI,IAAI,CAACC,UAAU,EAAED,kBAAkB;QAClFE,cAAc,EAAE,IAAI,CAACA;OACtB;MACD,IAAI,IAAI,CAACX,MAAM,IAAI,IAAI,CAACU,UAAU,EAAE4B,wBAAwB,EAAE;QAC5DF,QAAQ,CAACE,wBAAwB,GAAG,IAAI,CAAC5B,UAAU,CAAC4B,wBAAwB;QAC5E,IAAI,CAAClC,cAAc,CAACmC,8BAA8B,CAACH,QAAQ,CAAC,CAACI,SAAS,CAAC;UACrEL,IAAI,EAAGM,GAAQ,IAAI;YACjB,IAAI,CAAC3C,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAACQ,gBAAgB,CAAC4B,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAIM,GAAG,EAAEC,OAAO,EAAE;cAChB,IAAI,CAACrC,wBAAwB,CAACsC,SAAS,CAACF,GAAG,CAACG,YAAY,IAAI,gCAAgC,EAAE,EAAE,CAAC;YACnG,CAAC,MAAM;cACL,IAAI,CAACvC,wBAAwB,CAACwC,WAAW,CAACJ,GAAG,CAACK,YAAY,EAAEC,OAAO,IAAI,qCAAqC,EAAE,EAAE,CAAC;cACjH,IAAI,CAAC5C,KAAK,CAAC6C,KAAK,CAAC,SAAS,CAAC;YAC7B;UACF,CAAC;UACDC,KAAK,EAAGC,GAAQ,IAAI;YAClB,IAAI,CAACpD,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAACQ,gBAAgB,CAAC4B,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAI,CAAC9B,wBAAwB,CAACsC,SAAS,CAAC,8BAA8B,EAAE,EAAE,CAAC;YAC3EQ,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;UACpB;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAAC9C,cAAc,CAACgD,2BAA2B,CAAChB,QAAQ,CAAC,CAACI,SAAS,CAAC;UAClEL,IAAI,EAAGM,GAAQ,IAAI;YACjB,IAAI,CAAC3C,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAACQ,gBAAgB,CAAC4B,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAIM,GAAG,EAAEC,OAAO,EAAE;cAChB,IAAI,CAACrC,wBAAwB,CAACsC,SAAS,CAACF,GAAG,CAACG,YAAY,IAAI,gCAAgC,EAAE,EAAE,CAAC;YACnG,CAAC,MAAM;cACL,IAAI,CAACvC,wBAAwB,CAACwC,WAAW,CAAC,qCAAqC,EAAE,EAAE,CAAC;cACpF,IAAI,CAAC1C,KAAK,CAAC6C,KAAK,CAAC,SAAS,CAAC;YAC7B;UACF,CAAC;UACDC,KAAK,EAAGC,GAAQ,IAAI;YAClB,IAAI,CAACpD,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAACQ,gBAAgB,CAAC4B,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAI,CAAC9B,wBAAwB,CAACsC,SAAS,CAAC,8BAA8B,EAAE,EAAE,CAAC;YAC3EQ,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;UACpB;SACD,CAAC;MACJ;IACF,CAAC,MAAM;MACL,IAAI,CAACvD,UAAU,CAAC0D,gBAAgB,EAAE;MAClC,IAAI,CAAC,IAAI,CAAC9C,QAAQ,EAAE;QAClB,IAAI,CAACF,wBAAwB,CAACsC,SAAS,CAAC,uBAAuB,EAAE,EAAE,CAAC;MACtE;IACF;EACF;EAEAlD,QAAQA,CAAA;IACN,IAAI,CAACU,KAAK,CAACmD,OAAO,CAAC,WAAW,CAAC;EACjC;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACpD,KAAK,CAACmD,OAAO,CAAC,MAAM,CAAC;EAC5B;EAEAE,iBAAiBA,CAACC,SAA2B;IAC3C,IAAI,IAAI,CAAC3D,SAAS,EAAE;MAAE;IAAQ;IAC9B2D,SAAS,CAACC,KAAK,EAAE;EACnB;EAEAC,cAAcA,CAACC,KAAY;IAAA,IAAAC,KAAA;IACzB,MAAMC,KAAK,GAAGF,KAAK,CAACG,MAA0B;IAC9C,MAAMC,IAAI,GAAGF,KAAK,EAAEG,KAAK,IAAIH,KAAK,CAACG,KAAK,CAACC,MAAM,GAAGJ,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;IACvE,IAAI,CAACD,IAAI,EAAE;MAAE;IAAQ;IACrB,IAAI,CAAChD,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACV,gBAAgB,CAAC4B,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC/C,MAAMgC,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,gBAAAC,iBAAA,CAAG,aAAW;MACzB,IAAI;QACF,MAAMC,IAAI,SAAc,MAAM,CAAC,MAAM,CAAC;QACtC,MAAMC,IAAI,GAAG,IAAIC,UAAU,CAACN,MAAM,CAACO,MAAqB,CAAC;QACzD,MAAMC,QAAQ,GAAGJ,IAAI,CAACK,IAAI,CAACJ,IAAI,EAAE;UAAEK,IAAI,EAAE;QAAO,CAAE,CAAC;QACnD,MAAMC,cAAc,GAAGH,QAAQ,CAACI,UAAU,CAAC,CAAC,CAAC;QAC7C,MAAMC,SAAS,GAAGL,QAAQ,CAACM,MAAM,CAACH,cAAc,CAAC;QACjD,MAAMI,IAAI,GAAUX,IAAI,CAACY,KAAK,CAACC,aAAa,CAACJ,SAAS,EAAE;UAAEK,MAAM,EAAE;QAAE,CAAE,CAAC;QACvExB,KAAI,CAAC9C,YAAY,GAAG8C,KAAI,CAACyB,YAAY,CAACJ,IAAI,CAAC;QAC3C,IAAI,CAACrB,KAAI,CAAC9C,YAAY,CAACmD,MAAM,EAAE;UAC7BL,KAAI,CAACxD,wBAAwB,CAACsC,SAAS,CAAC,sCAAsC,EAAE,EAAE,CAAC;QACrF,CAAC,MAAM;UACLkB,KAAI,CAACxD,wBAAwB,CAACwC,WAAW,CAAC,YAAYgB,KAAI,CAAC9C,YAAY,CAACmD,MAAM,UAAU,EAAE,EAAE,CAAC;QAC/F;MACF,CAAC,CAAC,OAAOhB,GAAG,EAAE;QACZC,OAAO,CAACF,KAAK,CAAC,qBAAqB,EAAEC,GAAG,CAAC;QACzCW,KAAI,CAAC9C,YAAY,GAAG,EAAE;QACtB8C,KAAI,CAACxD,wBAAwB,CAACsC,SAAS,CAAC,wDAAwD,EAAE,EAAE,CAAC;MACvG,CAAC,SAAS;QACRkB,KAAI,CAAC7C,WAAW,GAAG,KAAK;QACxB;QACA6C,KAAI,CAACvD,gBAAgB,CAAC4B,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAChD;QACA2B,KAAK,CAACzB,KAAK,GAAG,EAAE;MAClB;IACF,CAAC;IACD8B,MAAM,CAACoB,iBAAiB,CAACvB,IAAI,CAAC;EAChC;EAEQwB,eAAeA,CAACC,MAAc;IACpC,OAAO,CAACA,MAAM,IAAI,EAAE,EAAEC,QAAQ,EAAE,CAACC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;EAC3E;EAEQP,YAAYA,CAACQ,QAAe;IAClC,IAAI,CAACA,QAAQ,IAAI,CAACA,QAAQ,CAAC5B,MAAM,EAAE;MAAE,OAAO,EAAE;IAAE;IAChD,MAAM6B,SAAS,GAAQ,EAAE;IACzB;IACA,MAAMC,QAAQ,GAAGF,QAAQ,CAAC,CAAC,CAAC;IAC5BG,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,OAAO,CAAEC,GAAG,IAAI;MACpC,MAAMC,IAAI,GAAG,IAAI,CAACb,eAAe,CAACY,GAAG,CAAC;MACtCL,SAAS,CAACM,IAAI,CAAC,GAAGD,GAAG;IACvB,CAAC,CAAC;IAEF,MAAME,IAAI,GAAGA,CAACC,GAAQ,EAAEC,aAAuB,KAAY;MACzD,KAAK,MAAMC,SAAS,IAAID,aAAa,EAAE;QACrC,MAAMH,IAAI,GAAG,IAAI,CAACb,eAAe,CAACiB,SAAS,CAAC;QAC5C,MAAMC,MAAM,GAAGX,SAAS,CAACM,IAAI,CAAC;QAC9B,IAAIK,MAAM,IAAIH,GAAG,CAACI,cAAc,CAACD,MAAM,CAAC,EAAE;UACxC,OAAO,CAACH,GAAG,CAACG,MAAM,CAAC,IAAI,EAAE,EAAEhB,QAAQ,EAAE;QACvC;MACF;MACA,OAAO,EAAE;IACX,CAAC;IAED;IACA,MAAMkB,IAAI,GAAGd,QAAQ,CAACe,GAAG,CAAEN,GAAG,IAAI;MAChC,OAAO;QACLnF,WAAW,EAAEkF,IAAI,CAACC,GAAG,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QAC7EjF,OAAO,EAAEgF,IAAI,CAACC,GAAG,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAC7DhF,eAAe,EAAE+E,IAAI,CAACC,GAAG,EAAE,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;QAC/D/E,SAAS,EAAE8E,IAAI,CAACC,GAAG,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAC7C9E,aAAa,EAAE6E,IAAI,CAACC,GAAG,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;QAC/E7E,cAAc,EAAE4E,IAAI,CAACC,GAAG,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;QAC/E5E,UAAU,EAAE2E,IAAI,CAACC,GAAG,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;QACpE3E,kBAAkB,EAAE0E,IAAI,CAACC,GAAG,EAAE,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,cAAc,CAAC;OAC/F;IACH,CAAC,CAAC;IACF;IACA,OAAOK,IAAI,CAACE,MAAM,CAAEC,CAAC,IAAKd,MAAM,CAACe,MAAM,CAACD,CAAC,CAAC,CAACE,IAAI,CAAEC,CAAC,IAAK,CAACA,CAAC,IAAI,EAAE,EAAExB,QAAQ,EAAE,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;EAC7F;EAEAwB,iBAAiBA,CAACC,KAAa;IAC7B,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACrG,YAAY,CAACmD,MAAM,EAAE;MAAE;IAAQ;IAC9D,IAAI,CAACnD,YAAY,CAACsG,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;EACpC;EAEME,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAjD,iBAAA;MACnB,IAAI,CAACiD,MAAI,CAAChH,QAAQ,EAAE;QAClBgH,MAAI,CAAClH,wBAAwB,CAACsC,SAAS,CAAC,6CAA6C,EAAE,EAAE,CAAC;QAC1F;MACF;MACA,IAAI,CAAC4E,MAAI,CAAC7I,cAAc,EAAE;QACxB6I,MAAI,CAAClH,wBAAwB,CAACsC,SAAS,CAAC,mDAAmD,EAAE,EAAE,CAAC;QAChG;MACF;MACA,IAAI,CAAC4E,MAAI,CAACxG,YAAY,CAACmD,MAAM,EAAE;QAC7BqD,MAAI,CAAClH,wBAAwB,CAACsC,SAAS,CAAC,0BAA0B,EAAE,EAAE,CAAC;QACvE;MACF;MACA4E,MAAI,CAACzH,SAAS,GAAG,IAAI;MACrB;MACAyH,MAAI,CAACjH,gBAAgB,CAAC4B,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;MAC/C,IAAI;QACF,MAAMqF,QAAQ,GAAGD,MAAI,CAACxG,YAAY,CAAC8F,GAAG,CAAEE,CAAC,IAAI;UAC3C,MAAMU,OAAO,GAAQ;YACnBrG,WAAW,EAAE2F,CAAC,CAAC3F,WAAW,IAAI,EAAE;YAChCE,OAAO,EAAEyF,CAAC,CAACzF,OAAO,IAAI,EAAE;YACxBC,eAAe,EAAEwF,CAAC,CAACxF,eAAe,IAAI,EAAE;YACxCC,SAAS,EAAEuF,CAAC,CAACvF,SAAS,IAAI,EAAE;YAC5BC,aAAa,EAAEsF,CAAC,CAACtF,aAAa,IAAI,EAAE;YACpCC,cAAc,EAAEqF,CAAC,CAACrF,cAAc,IAAI,EAAE;YACtCC,UAAU,EAAEoF,CAAC,CAACpF,UAAU,IAAI,EAAE;YAC9BC,kBAAkB,EAAEmF,CAAC,CAACnF,kBAAkB,IAAI,EAAE;YAC9CrB,QAAQ,EAAEgH,MAAI,CAAChH,QAAQ;YACvBC,YAAY,EAAE+G,MAAI,CAAC/G,YAAY;YAC/B9B,cAAc,EAAE6I,MAAI,CAAC7I,cAAc;YACnC+B,kBAAkB,EAAE8G,MAAI,CAAC9G,kBAAkB;YAC3CE,cAAc,EAAE4G,MAAI,CAAC5G;WACtB;UACD,OAAO4G,MAAI,CAACnH,cAAc,CAACgD,2BAA2B,CAACqE,OAAO,CAAC;QACjE,CAAC,CAAC;QACF;QACA,MAAM,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;UACpC,MAAM;YAAEC;UAAQ,CAAE,GAAGC,OAAO,CAAC,MAAM,CAAC;UACpCD,QAAQ,CAACL,QAAQ,CAAC,CAAChF,SAAS,CAAC;YAAEL,IAAI,EAAEwF,OAAO;YAAE1E,KAAK,EAAE2E;UAAM,CAAE,CAAC;QAChE,CAAC,CAAC;QACFL,MAAI,CAAClH,wBAAwB,CAACwC,WAAW,CAAC,uCAAuC,EAAE,EAAE,CAAC;QACtF0E,MAAI,CAACxG,YAAY,GAAG,EAAE;QACtB;QACAwG,MAAI,CAACpH,KAAK,CAAC6C,KAAK,CAAC,cAAc,CAAC;MAClC,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZC,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEC,GAAG,CAAC;QAChDqE,MAAI,CAAClH,wBAAwB,CAACsC,SAAS,CAAC,0CAA0C,EAAE,EAAE,CAAC;MACzF,CAAC,SAAS;QACR4E,MAAI,CAACzH,SAAS,GAAG,KAAK;QACtB;QACAyH,MAAI,CAACjH,gBAAgB,CAAC4B,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAClD;IAAC;EACH;EAEA,IAAI4F,WAAWA,CAAA;IACb,OAAO,IAAI,CAACpI,UAAU,CAACsC,KAAK;EAC9B;EAEA,IAAI+F,cAAcA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACrI,UAAU,EAAE;MAAE,OAAO,KAAK;IAAE;IACtC,MAAMsI,QAAQ,GAAG,IAAI,CAACtI,UAAU,CAACsI,QAAe;IAChD,OACE,CAAC,CAACA,QAAQ,CAAC7G,WAAW,EAAEa,KAAK,IAC7B,CAAC,CAACgG,QAAQ,CAAC3G,OAAO,EAAEW,KAAK,IACzB,CAAC,CAACgG,QAAQ,CAAC1G,eAAe,EAAEU,KAAK,IACjC,CAAC,CAACgG,QAAQ,CAACzG,SAAS,EAAES,KAAK;EAE/B;;qCA3RWhC,oCAAoC,EAAA9B,EAAA,CAAA+J,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjK,EAAA,CAAA+J,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAnK,EAAA,CAAA+J,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAArK,EAAA,CAAA+J,iBAAA,CAAAO,EAAA,CAAAC,wBAAA,GAAAvK,EAAA,CAAA+J,iBAAA,CAAAS,EAAA,CAAAC,gBAAA;EAAA;;UAApC3I,oCAAoC;IAAA4I,SAAA;IAAAC,MAAA;MAAAvI,QAAA;MAAAC,YAAA;MAAA9B,cAAA;MAAA+B,kBAAA;MAAAC,UAAA;MAAAC,cAAA;IAAA;IAAAoI,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCT7CjL,EAHJ,CAAAC,cAAA,aAAkC,aAEW,aACR;QAC/BD,EAAA,CAAAmL,uBAAA,GAAc;QAEZnL,EADA,CAAAgB,UAAA,IAAAoK,mDAAA,iBAAoB,IAAAC,mDAAA,iBACC;;QAEzBrL,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAAyB,WAC2C;QAArBD,EAAA,CAAAQ,UAAA,mBAAA8K,iEAAA;UAAAtL,EAAA,CAAAU,aAAA,CAAA6K,GAAA;UAAA,OAAAvL,EAAA,CAAAa,WAAA,CAASqK,GAAA,CAAA5J,QAAA,EAAU;QAAA,EAAC;QAErEtB,EAFsE,CAAAG,YAAA,EAAI,EAClE,EACF;QAMAH,EAHN,CAAAC,cAAA,aAAwB,aACyC,WACxD,iBACmE;QAAnBD,EAAA,CAAAQ,UAAA,mBAAAgL,uEAAA;UAAAxL,EAAA,CAAAU,aAAA,CAAA6K,GAAA;UAAA,OAAAvL,EAAA,CAAAa,WAAA,CAASqK,GAAA,CAAA9F,MAAA,EAAQ;QAAA,EAAC;QAACpF,EAAA,CAAAE,MAAA,YAAI;QAC5EF,EAD4E,CAAAG,YAAA,EAAS,EAC/E;QAEJH,EADF,CAAAC,cAAA,eAAuC,aAC8C;QAAAD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACxGH,EAAA,CAAAC,cAAA,oBAAqG;QAApCD,EAAA,CAAAQ,UAAA,oBAAAiL,uEAAAC,MAAA;UAAA1L,EAAA,CAAAU,aAAA,CAAA6K,GAAA;UAAA,OAAAvL,EAAA,CAAAa,WAAA,CAAUqK,GAAA,CAAA1F,cAAA,CAAAkG,MAAA,CAAsB;QAAA,EAAC;QAAlG1L,EAAA,CAAAG,YAAA,EAAqG;QACrGH,EAAA,CAAAC,cAAA,kBAAgI;QAA9DD,EAAA,CAAAQ,UAAA,mBAAAmL,uEAAA;UAAA3L,EAAA,CAAAU,aAAA,CAAA6K,GAAA;UAAA,MAAAK,YAAA,GAAA5L,EAAA,CAAA6L,WAAA;UAAA,OAAA7L,EAAA,CAAAa,WAAA,CAASqK,GAAA,CAAA7F,iBAAA,CAAAuG,YAAA,CAA4B;QAAA,EAAC;QAAwB5L,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACrJH,EAAA,CAAAC,cAAA,kBAA+E;QAA1BD,EAAA,CAAAQ,UAAA,mBAAAsL,uEAAA;UAAA9L,EAAA,CAAAU,aAAA,CAAA6K,GAAA;UAAA,OAAAvL,EAAA,CAAAa,WAAA,CAAAqK,GAAA,CAAAxI,QAAA,GAAoB,IAAI;QAAA,EAAC;QAAC1C,EAAA,CAAAE,MAAA,yBAAiB;QAEpGF,EAFoG,CAAAG,YAAA,EAAS,EACrG,EACF;QAENH,EAAA,CAAAC,cAAA,eAAgD;QAC9CD,EAAA,CAAAE,MAAA,kHAAyG;QAAAF,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAE,MAAA,oCAC9I;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAgB,UAAA,KAAA+K,qDAAA,qBAAmF;QA8CvF/L,EADE,CAAAG,YAAA,EAAM,EACF;;;QA1EQH,EAAA,CAAAI,SAAA,GAAY;QAAZJ,EAAA,CAAAuB,UAAA,SAAA2J,GAAA,CAAArJ,MAAA,CAAY;QACZ7B,EAAA,CAAAI,SAAA,EAAa;QAAbJ,EAAA,CAAAuB,UAAA,UAAA2J,GAAA,CAAArJ,MAAA,CAAa;QAehB7B,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAuB,UAAA,SAAA2J,GAAA,CAAAvI,eAAA,EAAA3C,EAAA,CAAAgM,aAAA,CAAwB;QAE8EhM,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAuB,UAAA,aAAA2J,GAAA,CAAAvJ,SAAA,CAAsB;QAU5H3B,EAAA,CAAAI,SAAA,GAAc;QAAdJ,EAAA,CAAAuB,UAAA,SAAA2J,GAAA,CAAAxI,QAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}