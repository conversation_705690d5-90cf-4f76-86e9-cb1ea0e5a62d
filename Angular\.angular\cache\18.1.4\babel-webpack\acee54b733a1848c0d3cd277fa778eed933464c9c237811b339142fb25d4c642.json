{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.takeUntil = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar noop_1 = require(\"../util/noop\");\nfunction takeUntil(notifier) {\n  return lift_1.operate(function (source, subscriber) {\n    innerFrom_1.innerFrom(notifier).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n      return subscriber.complete();\n    }, noop_1.noop));\n    !subscriber.closed && source.subscribe(subscriber);\n  });\n}\nexports.takeUntil = takeUntil;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "takeUntil", "lift_1", "require", "OperatorSubscriber_1", "innerFrom_1", "noop_1", "notifier", "operate", "source", "subscriber", "innerFrom", "subscribe", "createOperatorSubscriber", "complete", "noop", "closed"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/takeUntil.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.takeUntil = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar noop_1 = require(\"../util/noop\");\nfunction takeUntil(notifier) {\n    return lift_1.operate(function (source, subscriber) {\n        innerFrom_1.innerFrom(notifier).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () { return subscriber.complete(); }, noop_1.noop));\n        !subscriber.closed && source.subscribe(subscriber);\n    });\n}\nexports.takeUntil = takeUntil;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIE,WAAW,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AACpD,IAAIG,MAAM,GAAGH,OAAO,CAAC,cAAc,CAAC;AACpC,SAASF,SAASA,CAACM,QAAQ,EAAE;EACzB,OAAOL,MAAM,CAACM,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChDL,WAAW,CAACM,SAAS,CAACJ,QAAQ,CAAC,CAACK,SAAS,CAACR,oBAAoB,CAACS,wBAAwB,CAACH,UAAU,EAAE,YAAY;MAAE,OAAOA,UAAU,CAACI,QAAQ,CAAC,CAAC;IAAE,CAAC,EAAER,MAAM,CAACS,IAAI,CAAC,CAAC;IAChK,CAACL,UAAU,CAACM,MAAM,IAAIP,MAAM,CAACG,SAAS,CAACF,UAAU,CAAC;EACtD,CAAC,CAAC;AACN;AACAX,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}