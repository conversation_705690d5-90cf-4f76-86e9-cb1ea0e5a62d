{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.repeat = void 0;\nvar empty_1 = require(\"../observable/empty\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar timer_1 = require(\"../observable/timer\");\nfunction repeat(countOrConfig) {\n  var _a;\n  var count = Infinity;\n  var delay;\n  if (countOrConfig != null) {\n    if (typeof countOrConfig === 'object') {\n      _a = countOrConfig.count, count = _a === void 0 ? Infinity : _a, delay = countOrConfig.delay;\n    } else {\n      count = countOrConfig;\n    }\n  }\n  return count <= 0 ? function () {\n    return empty_1.EMPTY;\n  } : lift_1.operate(function (source, subscriber) {\n    var soFar = 0;\n    var sourceSub;\n    var resubscribe = function () {\n      sourceSub === null || sourceSub === void 0 ? void 0 : sourceSub.unsubscribe();\n      sourceSub = null;\n      if (delay != null) {\n        var notifier = typeof delay === 'number' ? timer_1.timer(delay) : innerFrom_1.innerFrom(delay(soFar));\n        var notifierSubscriber_1 = OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n          notifierSubscriber_1.unsubscribe();\n          subscribeToSource();\n        });\n        notifier.subscribe(notifierSubscriber_1);\n      } else {\n        subscribeToSource();\n      }\n    };\n    var subscribeToSource = function () {\n      var syncUnsub = false;\n      sourceSub = source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, undefined, function () {\n        if (++soFar < count) {\n          if (sourceSub) {\n            resubscribe();\n          } else {\n            syncUnsub = true;\n          }\n        } else {\n          subscriber.complete();\n        }\n      }));\n      if (syncUnsub) {\n        resubscribe();\n      }\n    };\n    subscribeToSource();\n  });\n}\nexports.repeat = repeat;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "repeat", "empty_1", "require", "lift_1", "OperatorSubscriber_1", "innerFrom_1", "timer_1", "countOrConfig", "_a", "count", "Infinity", "delay", "EMPTY", "operate", "source", "subscriber", "soFar", "sourceSub", "resubscribe", "unsubscribe", "notifier", "timer", "innerFrom", "notifierSubscriber_1", "createOperatorSubscriber", "subscribeToSource", "subscribe", "syncUnsub", "undefined", "complete"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/repeat.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.repeat = void 0;\nvar empty_1 = require(\"../observable/empty\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar timer_1 = require(\"../observable/timer\");\nfunction repeat(countOrConfig) {\n    var _a;\n    var count = Infinity;\n    var delay;\n    if (countOrConfig != null) {\n        if (typeof countOrConfig === 'object') {\n            (_a = countOrConfig.count, count = _a === void 0 ? Infinity : _a, delay = countOrConfig.delay);\n        }\n        else {\n            count = countOrConfig;\n        }\n    }\n    return count <= 0\n        ? function () { return empty_1.EMPTY; }\n        : lift_1.operate(function (source, subscriber) {\n            var soFar = 0;\n            var sourceSub;\n            var resubscribe = function () {\n                sourceSub === null || sourceSub === void 0 ? void 0 : sourceSub.unsubscribe();\n                sourceSub = null;\n                if (delay != null) {\n                    var notifier = typeof delay === 'number' ? timer_1.timer(delay) : innerFrom_1.innerFrom(delay(soFar));\n                    var notifierSubscriber_1 = OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n                        notifierSubscriber_1.unsubscribe();\n                        subscribeToSource();\n                    });\n                    notifier.subscribe(notifierSubscriber_1);\n                }\n                else {\n                    subscribeToSource();\n                }\n            };\n            var subscribeToSource = function () {\n                var syncUnsub = false;\n                sourceSub = source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, undefined, function () {\n                    if (++soFar < count) {\n                        if (sourceSub) {\n                            resubscribe();\n                        }\n                        else {\n                            syncUnsub = true;\n                        }\n                    }\n                    else {\n                        subscriber.complete();\n                    }\n                }));\n                if (syncUnsub) {\n                    resubscribe();\n                }\n            };\n            subscribeToSource();\n        });\n}\nexports.repeat = repeat;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,MAAM,GAAG,KAAK,CAAC;AACvB,IAAIC,OAAO,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC5C,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIG,WAAW,GAAGH,OAAO,CAAC,yBAAyB,CAAC;AACpD,IAAII,OAAO,GAAGJ,OAAO,CAAC,qBAAqB,CAAC;AAC5C,SAASF,MAAMA,CAACO,aAAa,EAAE;EAC3B,IAAIC,EAAE;EACN,IAAIC,KAAK,GAAGC,QAAQ;EACpB,IAAIC,KAAK;EACT,IAAIJ,aAAa,IAAI,IAAI,EAAE;IACvB,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MAClCC,EAAE,GAAGD,aAAa,CAACE,KAAK,EAAEA,KAAK,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAGE,QAAQ,GAAGF,EAAE,EAAEG,KAAK,GAAGJ,aAAa,CAACI,KAAK;IACjG,CAAC,MACI;MACDF,KAAK,GAAGF,aAAa;IACzB;EACJ;EACA,OAAOE,KAAK,IAAI,CAAC,GACX,YAAY;IAAE,OAAOR,OAAO,CAACW,KAAK;EAAE,CAAC,GACrCT,MAAM,CAACU,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAC3C,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,SAAS;IACb,IAAIC,WAAW,GAAG,SAAAA,CAAA,EAAY;MAC1BD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACE,WAAW,CAAC,CAAC;MAC7EF,SAAS,GAAG,IAAI;MAChB,IAAIN,KAAK,IAAI,IAAI,EAAE;QACf,IAAIS,QAAQ,GAAG,OAAOT,KAAK,KAAK,QAAQ,GAAGL,OAAO,CAACe,KAAK,CAACV,KAAK,CAAC,GAAGN,WAAW,CAACiB,SAAS,CAACX,KAAK,CAACK,KAAK,CAAC,CAAC;QACrG,IAAIO,oBAAoB,GAAGnB,oBAAoB,CAACoB,wBAAwB,CAACT,UAAU,EAAE,YAAY;UAC7FQ,oBAAoB,CAACJ,WAAW,CAAC,CAAC;UAClCM,iBAAiB,CAAC,CAAC;QACvB,CAAC,CAAC;QACFL,QAAQ,CAACM,SAAS,CAACH,oBAAoB,CAAC;MAC5C,CAAC,MACI;QACDE,iBAAiB,CAAC,CAAC;MACvB;IACJ,CAAC;IACD,IAAIA,iBAAiB,GAAG,SAAAA,CAAA,EAAY;MAChC,IAAIE,SAAS,GAAG,KAAK;MACrBV,SAAS,GAAGH,MAAM,CAACY,SAAS,CAACtB,oBAAoB,CAACoB,wBAAwB,CAACT,UAAU,EAAEa,SAAS,EAAE,YAAY;QAC1G,IAAI,EAAEZ,KAAK,GAAGP,KAAK,EAAE;UACjB,IAAIQ,SAAS,EAAE;YACXC,WAAW,CAAC,CAAC;UACjB,CAAC,MACI;YACDS,SAAS,GAAG,IAAI;UACpB;QACJ,CAAC,MACI;UACDZ,UAAU,CAACc,QAAQ,CAAC,CAAC;QACzB;MACJ,CAAC,CAAC,CAAC;MACH,IAAIF,SAAS,EAAE;QACXT,WAAW,CAAC,CAAC;MACjB;IACJ,CAAC;IACDO,iBAAiB,CAAC,CAAC;EACvB,CAAC,CAAC;AACV;AACA3B,OAAO,CAACE,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}