{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.range = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar empty_1 = require(\"./empty\");\nfunction range(start, count, scheduler) {\n  if (count == null) {\n    count = start;\n    start = 0;\n  }\n  if (count <= 0) {\n    return empty_1.EMPTY;\n  }\n  var end = count + start;\n  return new Observable_1.Observable(scheduler ? function (subscriber) {\n    var n = start;\n    return scheduler.schedule(function () {\n      if (n < end) {\n        subscriber.next(n++);\n        this.schedule();\n      } else {\n        subscriber.complete();\n      }\n    });\n  } : function (subscriber) {\n    var n = start;\n    while (n < end && !subscriber.closed) {\n      subscriber.next(n++);\n    }\n    subscriber.complete();\n  });\n}\nexports.range = range;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "range", "Observable_1", "require", "empty_1", "start", "count", "scheduler", "EMPTY", "end", "Observable", "subscriber", "n", "schedule", "next", "complete", "closed"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/range.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.range = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar empty_1 = require(\"./empty\");\nfunction range(start, count, scheduler) {\n    if (count == null) {\n        count = start;\n        start = 0;\n    }\n    if (count <= 0) {\n        return empty_1.EMPTY;\n    }\n    var end = count + start;\n    return new Observable_1.Observable(scheduler\n        ?\n            function (subscriber) {\n                var n = start;\n                return scheduler.schedule(function () {\n                    if (n < end) {\n                        subscriber.next(n++);\n                        this.schedule();\n                    }\n                    else {\n                        subscriber.complete();\n                    }\n                });\n            }\n        :\n            function (subscriber) {\n                var n = start;\n                while (n < end && !subscriber.closed) {\n                    subscriber.next(n++);\n                }\n                subscriber.complete();\n            });\n}\nexports.range = range;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,KAAK,GAAG,KAAK,CAAC;AACtB,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIC,OAAO,GAAGD,OAAO,CAAC,SAAS,CAAC;AAChC,SAASF,KAAKA,CAACI,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EACpC,IAAID,KAAK,IAAI,IAAI,EAAE;IACfA,KAAK,GAAGD,KAAK;IACbA,KAAK,GAAG,CAAC;EACb;EACA,IAAIC,KAAK,IAAI,CAAC,EAAE;IACZ,OAAOF,OAAO,CAACI,KAAK;EACxB;EACA,IAAIC,GAAG,GAAGH,KAAK,GAAGD,KAAK;EACvB,OAAO,IAAIH,YAAY,CAACQ,UAAU,CAACH,SAAS,GAEpC,UAAUI,UAAU,EAAE;IAClB,IAAIC,CAAC,GAAGP,KAAK;IACb,OAAOE,SAAS,CAACM,QAAQ,CAAC,YAAY;MAClC,IAAID,CAAC,GAAGH,GAAG,EAAE;QACTE,UAAU,CAACG,IAAI,CAACF,CAAC,EAAE,CAAC;QACpB,IAAI,CAACC,QAAQ,CAAC,CAAC;MACnB,CAAC,MACI;QACDF,UAAU,CAACI,QAAQ,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;EACN,CAAC,GAED,UAAUJ,UAAU,EAAE;IAClB,IAAIC,CAAC,GAAGP,KAAK;IACb,OAAOO,CAAC,GAAGH,GAAG,IAAI,CAACE,UAAU,CAACK,MAAM,EAAE;MAClCL,UAAU,CAACG,IAAI,CAACF,CAAC,EAAE,CAAC;IACxB;IACAD,UAAU,CAACI,QAAQ,CAAC,CAAC;EACzB,CAAC,CAAC;AACd;AACAhB,OAAO,CAACE,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}