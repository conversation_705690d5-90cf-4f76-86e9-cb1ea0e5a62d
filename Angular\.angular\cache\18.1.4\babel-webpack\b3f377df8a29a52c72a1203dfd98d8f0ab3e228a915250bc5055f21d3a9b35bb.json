{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.skipWhile = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction skipWhile(predicate) {\n  return lift_1.operate(function (source, subscriber) {\n    var taking = false;\n    var index = 0;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      return (taking || (taking = !predicate(value, index++))) && subscriber.next(value);\n    }));\n  });\n}\nexports.skipWhile = skipWhile;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "<PERSON><PERSON><PERSON><PERSON>", "lift_1", "require", "OperatorSubscriber_1", "predicate", "operate", "source", "subscriber", "taking", "index", "subscribe", "createOperatorSubscriber", "next"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/skipWhile.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.skipWhile = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction skipWhile(predicate) {\n    return lift_1.operate(function (source, subscriber) {\n        var taking = false;\n        var index = 0;\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) { return (taking || (taking = !predicate(value, index++))) && subscriber.next(value); }));\n    });\n}\nexports.skipWhile = skipWhile;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,SAASA,CAACI,SAAS,EAAE;EAC1B,OAAOH,MAAM,CAACI,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,MAAM,GAAG,KAAK;IAClB,IAAIC,KAAK,GAAG,CAAC;IACbH,MAAM,CAACI,SAAS,CAACP,oBAAoB,CAACQ,wBAAwB,CAACJ,UAAU,EAAE,UAAUR,KAAK,EAAE;MAAE,OAAO,CAACS,MAAM,KAAKA,MAAM,GAAG,CAACJ,SAAS,CAACL,KAAK,EAAEU,KAAK,EAAE,CAAC,CAAC,KAAKF,UAAU,CAACK,IAAI,CAACb,KAAK,CAAC;IAAE,CAAC,CAAC,CAAC;EACzL,CAAC,CAAC;AACN;AACAD,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}