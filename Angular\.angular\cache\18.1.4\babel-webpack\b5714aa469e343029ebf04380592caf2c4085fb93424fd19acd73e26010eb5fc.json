{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ObjectUnsubscribedError = void 0;\nvar createErrorClass_1 = require(\"./createErrorClass\");\nexports.ObjectUnsubscribedError = createErrorClass_1.createErrorClass(function (_super) {\n  return function ObjectUnsubscribedErrorImpl() {\n    _super(this);\n    this.name = 'ObjectUnsubscribedError';\n    this.message = 'object unsubscribed';\n  };\n});", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "ObjectUnsubscribedError", "createErrorClass_1", "require", "createErrorClass", "_super", "ObjectUnsubscribedErrorImpl", "name", "message"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/ObjectUnsubscribedError.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ObjectUnsubscribedError = void 0;\nvar createErrorClass_1 = require(\"./createErrorClass\");\nexports.ObjectUnsubscribedError = createErrorClass_1.createErrorClass(function (_super) {\n    return function ObjectUnsubscribedErrorImpl() {\n        _super(this);\n        this.name = 'ObjectUnsubscribedError';\n        this.message = 'object unsubscribed';\n    };\n});\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,uBAAuB,GAAG,KAAK,CAAC;AACxC,IAAIC,kBAAkB,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACtDJ,OAAO,CAACE,uBAAuB,GAAGC,kBAAkB,CAACE,gBAAgB,CAAC,UAAUC,MAAM,EAAE;EACpF,OAAO,SAASC,2BAA2BA,CAAA,EAAG;IAC1CD,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACE,IAAI,GAAG,yBAAyB;IACrC,IAAI,CAACC,OAAO,GAAG,qBAAqB;EACxC,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}