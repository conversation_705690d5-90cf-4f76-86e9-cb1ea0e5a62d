{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.take = void 0;\nvar empty_1 = require(\"../observable/empty\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction take(count) {\n  return count <= 0 ? function () {\n    return empty_1.EMPTY;\n  } : lift_1.operate(function (source, subscriber) {\n    var seen = 0;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      if (++seen <= count) {\n        subscriber.next(value);\n        if (count <= seen) {\n          subscriber.complete();\n        }\n      }\n    }));\n  });\n}\nexports.take = take;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "take", "empty_1", "require", "lift_1", "OperatorSubscriber_1", "count", "EMPTY", "operate", "source", "subscriber", "seen", "subscribe", "createOperatorSubscriber", "next", "complete"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/take.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.take = void 0;\nvar empty_1 = require(\"../observable/empty\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction take(count) {\n    return count <= 0\n        ?\n            function () { return empty_1.EMPTY; }\n        : lift_1.operate(function (source, subscriber) {\n            var seen = 0;\n            source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n                if (++seen <= count) {\n                    subscriber.next(value);\n                    if (count <= seen) {\n                        subscriber.complete();\n                    }\n                }\n            }));\n        });\n}\nexports.take = take;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,IAAI,GAAG,KAAK,CAAC;AACrB,IAAIC,OAAO,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC5C,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,IAAIA,CAACK,KAAK,EAAE;EACjB,OAAOA,KAAK,IAAI,CAAC,GAET,YAAY;IAAE,OAAOJ,OAAO,CAACK,KAAK;EAAE,CAAC,GACvCH,MAAM,CAACI,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAC3C,IAAIC,IAAI,GAAG,CAAC;IACZF,MAAM,CAACG,SAAS,CAACP,oBAAoB,CAACQ,wBAAwB,CAACH,UAAU,EAAE,UAAUV,KAAK,EAAE;MACxF,IAAI,EAAEW,IAAI,IAAIL,KAAK,EAAE;QACjBI,UAAU,CAACI,IAAI,CAACd,KAAK,CAAC;QACtB,IAAIM,KAAK,IAAIK,IAAI,EAAE;UACfD,UAAU,CAACK,QAAQ,CAAC,CAAC;QACzB;MACJ;IACJ,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACV;AACAhB,OAAO,CAACE,IAAI,GAAGA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}