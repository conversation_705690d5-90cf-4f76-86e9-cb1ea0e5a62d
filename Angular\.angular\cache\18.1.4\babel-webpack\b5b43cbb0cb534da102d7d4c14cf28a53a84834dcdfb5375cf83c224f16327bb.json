{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ArgumentOutOfRangeError = void 0;\nvar createErrorClass_1 = require(\"./createErrorClass\");\nexports.ArgumentOutOfRangeError = createErrorClass_1.createErrorClass(function (_super) {\n  return function ArgumentOutOfRangeErrorImpl() {\n    _super(this);\n    this.name = 'ArgumentOutOfRangeError';\n    this.message = 'argument out of range';\n  };\n});", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "ArgumentOutOfRangeError", "createErrorClass_1", "require", "createErrorClass", "_super", "ArgumentOutOfRangeErrorImpl", "name", "message"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/ArgumentOutOfRangeError.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ArgumentOutOfRangeError = void 0;\nvar createErrorClass_1 = require(\"./createErrorClass\");\nexports.ArgumentOutOfRangeError = createErrorClass_1.createErrorClass(function (_super) {\n    return function ArgumentOutOfRangeErrorImpl() {\n        _super(this);\n        this.name = 'ArgumentOutOfRangeError';\n        this.message = 'argument out of range';\n    };\n});\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,uBAAuB,GAAG,KAAK,CAAC;AACxC,IAAIC,kBAAkB,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACtDJ,OAAO,CAACE,uBAAuB,GAAGC,kBAAkB,CAACE,gBAAgB,CAAC,UAAUC,MAAM,EAAE;EACpF,OAAO,SAASC,2BAA2BA,CAAA,EAAG;IAC1CD,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACE,IAAI,GAAG,yBAAyB;IACrC,IAAI,CAACC,OAAO,GAAG,uBAAuB;EAC1C,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}