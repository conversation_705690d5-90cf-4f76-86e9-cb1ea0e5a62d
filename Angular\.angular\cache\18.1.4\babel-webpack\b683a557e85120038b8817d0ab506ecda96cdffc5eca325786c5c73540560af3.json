{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.observeNotification = exports.Notification = exports.NotificationKind = void 0;\nvar empty_1 = require(\"./observable/empty\");\nvar of_1 = require(\"./observable/of\");\nvar throwError_1 = require(\"./observable/throwError\");\nvar isFunction_1 = require(\"./util/isFunction\");\nvar NotificationKind;\n(function (NotificationKind) {\n  NotificationKind[\"NEXT\"] = \"N\";\n  NotificationKind[\"ERROR\"] = \"E\";\n  NotificationKind[\"COMPLETE\"] = \"C\";\n})(NotificationKind = exports.NotificationKind || (exports.NotificationKind = {}));\nvar Notification = function () {\n  function Notification(kind, value, error) {\n    this.kind = kind;\n    this.value = value;\n    this.error = error;\n    this.hasValue = kind === 'N';\n  }\n  Notification.prototype.observe = function (observer) {\n    return observeNotification(this, observer);\n  };\n  Notification.prototype.do = function (nextHand<PERSON>, errorHandler, completeHandler) {\n    var _a = this,\n      kind = _a.kind,\n      value = _a.value,\n      error = _a.error;\n    return kind === 'N' ? nextHandler === null || nextHandler === void 0 ? void 0 : nextHandler(value) : kind === 'E' ? errorHandler === null || errorHandler === void 0 ? void 0 : errorHandler(error) : completeHandler === null || completeHandler === void 0 ? void 0 : completeHandler();\n  };\n  Notification.prototype.accept = function (nextOrObserver, error, complete) {\n    var _a;\n    return isFunction_1.isFunction((_a = nextOrObserver) === null || _a === void 0 ? void 0 : _a.next) ? this.observe(nextOrObserver) : this.do(nextOrObserver, error, complete);\n  };\n  Notification.prototype.toObservable = function () {\n    var _a = this,\n      kind = _a.kind,\n      value = _a.value,\n      error = _a.error;\n    var result = kind === 'N' ? of_1.of(value) : kind === 'E' ? throwError_1.throwError(function () {\n      return error;\n    }) : kind === 'C' ? empty_1.EMPTY : 0;\n    if (!result) {\n      throw new TypeError(\"Unexpected notification kind \" + kind);\n    }\n    return result;\n  };\n  Notification.createNext = function (value) {\n    return new Notification('N', value);\n  };\n  Notification.createError = function (err) {\n    return new Notification('E', undefined, err);\n  };\n  Notification.createComplete = function () {\n    return Notification.completeNotification;\n  };\n  Notification.completeNotification = new Notification('C');\n  return Notification;\n}();\nexports.Notification = Notification;\nfunction observeNotification(notification, observer) {\n  var _a, _b, _c;\n  var _d = notification,\n    kind = _d.kind,\n    value = _d.value,\n    error = _d.error;\n  if (typeof kind !== 'string') {\n    throw new TypeError('Invalid notification, missing \"kind\"');\n  }\n  kind === 'N' ? (_a = observer.next) === null || _a === void 0 ? void 0 : _a.call(observer, value) : kind === 'E' ? (_b = observer.error) === null || _b === void 0 ? void 0 : _b.call(observer, error) : (_c = observer.complete) === null || _c === void 0 ? void 0 : _c.call(observer);\n}\nexports.observeNotification = observeNotification;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "observeNotification", "Notification", "NotificationKind", "empty_1", "require", "of_1", "throwError_1", "isFunction_1", "kind", "error", "hasValue", "prototype", "observe", "observer", "do", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "completeHandler", "_a", "accept", "nextOrObserver", "complete", "isFunction", "next", "toObservable", "result", "of", "throwError", "EMPTY", "TypeError", "createNext", "createError", "err", "undefined", "createComplete", "completeNotification", "notification", "_b", "_c", "_d", "call"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/Notification.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.observeNotification = exports.Notification = exports.NotificationKind = void 0;\nvar empty_1 = require(\"./observable/empty\");\nvar of_1 = require(\"./observable/of\");\nvar throwError_1 = require(\"./observable/throwError\");\nvar isFunction_1 = require(\"./util/isFunction\");\nvar NotificationKind;\n(function (NotificationKind) {\n    NotificationKind[\"NEXT\"] = \"N\";\n    NotificationKind[\"ERROR\"] = \"E\";\n    NotificationKind[\"COMPLETE\"] = \"C\";\n})(NotificationKind = exports.NotificationKind || (exports.NotificationKind = {}));\nvar Notification = (function () {\n    function Notification(kind, value, error) {\n        this.kind = kind;\n        this.value = value;\n        this.error = error;\n        this.hasValue = kind === 'N';\n    }\n    Notification.prototype.observe = function (observer) {\n        return observeNotification(this, observer);\n    };\n    Notification.prototype.do = function (nextHand<PERSON>, errorHand<PERSON>, completeHandler) {\n        var _a = this, kind = _a.kind, value = _a.value, error = _a.error;\n        return kind === 'N' ? nextHandler === null || nextHandler === void 0 ? void 0 : nextHandler(value) : kind === 'E' ? errorHandler === null || errorHandler === void 0 ? void 0 : errorHandler(error) : completeHandler === null || completeHandler === void 0 ? void 0 : completeHandler();\n    };\n    Notification.prototype.accept = function (nextOrObserver, error, complete) {\n        var _a;\n        return isFunction_1.isFunction((_a = nextOrObserver) === null || _a === void 0 ? void 0 : _a.next)\n            ? this.observe(nextOrObserver)\n            : this.do(nextOrObserver, error, complete);\n    };\n    Notification.prototype.toObservable = function () {\n        var _a = this, kind = _a.kind, value = _a.value, error = _a.error;\n        var result = kind === 'N'\n            ?\n                of_1.of(value)\n            :\n                kind === 'E'\n                    ?\n                        throwError_1.throwError(function () { return error; })\n                    :\n                        kind === 'C'\n                            ?\n                                empty_1.EMPTY\n                            :\n                                0;\n        if (!result) {\n            throw new TypeError(\"Unexpected notification kind \" + kind);\n        }\n        return result;\n    };\n    Notification.createNext = function (value) {\n        return new Notification('N', value);\n    };\n    Notification.createError = function (err) {\n        return new Notification('E', undefined, err);\n    };\n    Notification.createComplete = function () {\n        return Notification.completeNotification;\n    };\n    Notification.completeNotification = new Notification('C');\n    return Notification;\n}());\nexports.Notification = Notification;\nfunction observeNotification(notification, observer) {\n    var _a, _b, _c;\n    var _d = notification, kind = _d.kind, value = _d.value, error = _d.error;\n    if (typeof kind !== 'string') {\n        throw new TypeError('Invalid notification, missing \"kind\"');\n    }\n    kind === 'N' ? (_a = observer.next) === null || _a === void 0 ? void 0 : _a.call(observer, value) : kind === 'E' ? (_b = observer.error) === null || _b === void 0 ? void 0 : _b.call(observer, error) : (_c = observer.complete) === null || _c === void 0 ? void 0 : _c.call(observer);\n}\nexports.observeNotification = observeNotification;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,mBAAmB,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,gBAAgB,GAAG,KAAK,CAAC;AACtF,IAAIC,OAAO,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAC3C,IAAIC,IAAI,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AACrC,IAAIE,YAAY,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AACrD,IAAIG,YAAY,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AAC/C,IAAIF,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAAC,MAAM,CAAC,GAAG,GAAG;EAC9BA,gBAAgB,CAAC,OAAO,CAAC,GAAG,GAAG;EAC/BA,gBAAgB,CAAC,UAAU,CAAC,GAAG,GAAG;AACtC,CAAC,EAAEA,gBAAgB,GAAGJ,OAAO,CAACI,gBAAgB,KAAKJ,OAAO,CAACI,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAClF,IAAID,YAAY,GAAI,YAAY;EAC5B,SAASA,YAAYA,CAACO,IAAI,EAAET,KAAK,EAAEU,KAAK,EAAE;IACtC,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACT,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACU,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAGF,IAAI,KAAK,GAAG;EAChC;EACAP,YAAY,CAACU,SAAS,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;IACjD,OAAOb,mBAAmB,CAAC,IAAI,EAAEa,QAAQ,CAAC;EAC9C,CAAC;EACDZ,YAAY,CAACU,SAAS,CAACG,EAAE,GAAG,UAAUC,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAE;IAC9E,IAAIC,EAAE,GAAG,IAAI;MAAEV,IAAI,GAAGU,EAAE,CAACV,IAAI;MAAET,KAAK,GAAGmB,EAAE,CAACnB,KAAK;MAAEU,KAAK,GAAGS,EAAE,CAACT,KAAK;IACjE,OAAOD,IAAI,KAAK,GAAG,GAAGO,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAChB,KAAK,CAAC,GAAGS,IAAI,KAAK,GAAG,GAAGQ,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACP,KAAK,CAAC,GAAGQ,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC,CAAC;EAC7R,CAAC;EACDhB,YAAY,CAACU,SAAS,CAACQ,MAAM,GAAG,UAAUC,cAAc,EAAEX,KAAK,EAAEY,QAAQ,EAAE;IACvE,IAAIH,EAAE;IACN,OAAOX,YAAY,CAACe,UAAU,CAAC,CAACJ,EAAE,GAAGE,cAAc,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,IAAI,CAAC,GAC5F,IAAI,CAACX,OAAO,CAACQ,cAAc,CAAC,GAC5B,IAAI,CAACN,EAAE,CAACM,cAAc,EAAEX,KAAK,EAAEY,QAAQ,CAAC;EAClD,CAAC;EACDpB,YAAY,CAACU,SAAS,CAACa,YAAY,GAAG,YAAY;IAC9C,IAAIN,EAAE,GAAG,IAAI;MAAEV,IAAI,GAAGU,EAAE,CAACV,IAAI;MAAET,KAAK,GAAGmB,EAAE,CAACnB,KAAK;MAAEU,KAAK,GAAGS,EAAE,CAACT,KAAK;IACjE,IAAIgB,MAAM,GAAGjB,IAAI,KAAK,GAAG,GAEjBH,IAAI,CAACqB,EAAE,CAAC3B,KAAK,CAAC,GAEdS,IAAI,KAAK,GAAG,GAEJF,YAAY,CAACqB,UAAU,CAAC,YAAY;MAAE,OAAOlB,KAAK;IAAE,CAAC,CAAC,GAEtDD,IAAI,KAAK,GAAG,GAEJL,OAAO,CAACyB,KAAK,GAEb,CAAC;IACzB,IAAI,CAACH,MAAM,EAAE;MACT,MAAM,IAAII,SAAS,CAAC,+BAA+B,GAAGrB,IAAI,CAAC;IAC/D;IACA,OAAOiB,MAAM;EACjB,CAAC;EACDxB,YAAY,CAAC6B,UAAU,GAAG,UAAU/B,KAAK,EAAE;IACvC,OAAO,IAAIE,YAAY,CAAC,GAAG,EAAEF,KAAK,CAAC;EACvC,CAAC;EACDE,YAAY,CAAC8B,WAAW,GAAG,UAAUC,GAAG,EAAE;IACtC,OAAO,IAAI/B,YAAY,CAAC,GAAG,EAAEgC,SAAS,EAAED,GAAG,CAAC;EAChD,CAAC;EACD/B,YAAY,CAACiC,cAAc,GAAG,YAAY;IACtC,OAAOjC,YAAY,CAACkC,oBAAoB;EAC5C,CAAC;EACDlC,YAAY,CAACkC,oBAAoB,GAAG,IAAIlC,YAAY,CAAC,GAAG,CAAC;EACzD,OAAOA,YAAY;AACvB,CAAC,CAAC,CAAE;AACJH,OAAO,CAACG,YAAY,GAAGA,YAAY;AACnC,SAASD,mBAAmBA,CAACoC,YAAY,EAAEvB,QAAQ,EAAE;EACjD,IAAIK,EAAE,EAAEmB,EAAE,EAAEC,EAAE;EACd,IAAIC,EAAE,GAAGH,YAAY;IAAE5B,IAAI,GAAG+B,EAAE,CAAC/B,IAAI;IAAET,KAAK,GAAGwC,EAAE,CAACxC,KAAK;IAAEU,KAAK,GAAG8B,EAAE,CAAC9B,KAAK;EACzE,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;IAC1B,MAAM,IAAIqB,SAAS,CAAC,sCAAsC,CAAC;EAC/D;EACArB,IAAI,KAAK,GAAG,GAAG,CAACU,EAAE,GAAGL,QAAQ,CAACU,IAAI,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,IAAI,CAAC3B,QAAQ,EAAEd,KAAK,CAAC,GAAGS,IAAI,KAAK,GAAG,GAAG,CAAC6B,EAAE,GAAGxB,QAAQ,CAACJ,KAAK,MAAM,IAAI,IAAI4B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAAC3B,QAAQ,EAAEJ,KAAK,CAAC,GAAG,CAAC6B,EAAE,GAAGzB,QAAQ,CAACQ,QAAQ,MAAM,IAAI,IAAIiB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAAC3B,QAAQ,CAAC;AAC5R;AACAf,OAAO,CAACE,mBAAmB,GAAGA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}