{"ast": null, "code": "\"use strict\";\n\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.bufferCount = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar arrRemove_1 = require(\"../util/arrRemove\");\nfunction bufferCount(bufferSize, startBufferEvery) {\n  if (startBufferEvery === void 0) {\n    startBufferEvery = null;\n  }\n  startBufferEvery = startBufferEvery !== null && startBufferEvery !== void 0 ? startBufferEvery : bufferSize;\n  return lift_1.operate(function (source, subscriber) {\n    var buffers = [];\n    var count = 0;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var e_1, _a, e_2, _b;\n      var toEmit = null;\n      if (count++ % startBufferEvery === 0) {\n        buffers.push([]);\n      }\n      try {\n        for (var buffers_1 = __values(buffers), buffers_1_1 = buffers_1.next(); !buffers_1_1.done; buffers_1_1 = buffers_1.next()) {\n          var buffer = buffers_1_1.value;\n          buffer.push(value);\n          if (bufferSize <= buffer.length) {\n            toEmit = toEmit !== null && toEmit !== void 0 ? toEmit : [];\n            toEmit.push(buffer);\n          }\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (buffers_1_1 && !buffers_1_1.done && (_a = buffers_1.return)) _a.call(buffers_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      if (toEmit) {\n        try {\n          for (var toEmit_1 = __values(toEmit), toEmit_1_1 = toEmit_1.next(); !toEmit_1_1.done; toEmit_1_1 = toEmit_1.next()) {\n            var buffer = toEmit_1_1.value;\n            arrRemove_1.arrRemove(buffers, buffer);\n            subscriber.next(buffer);\n          }\n        } catch (e_2_1) {\n          e_2 = {\n            error: e_2_1\n          };\n        } finally {\n          try {\n            if (toEmit_1_1 && !toEmit_1_1.done && (_b = toEmit_1.return)) _b.call(toEmit_1);\n          } finally {\n            if (e_2) throw e_2.error;\n          }\n        }\n      }\n    }, function () {\n      var e_3, _a;\n      try {\n        for (var buffers_2 = __values(buffers), buffers_2_1 = buffers_2.next(); !buffers_2_1.done; buffers_2_1 = buffers_2.next()) {\n          var buffer = buffers_2_1.value;\n          subscriber.next(buffer);\n        }\n      } catch (e_3_1) {\n        e_3 = {\n          error: e_3_1\n        };\n      } finally {\n        try {\n          if (buffers_2_1 && !buffers_2_1.done && (_a = buffers_2.return)) _a.call(buffers_2);\n        } finally {\n          if (e_3) throw e_3.error;\n        }\n      }\n      subscriber.complete();\n    }, undefined, function () {\n      buffers = null;\n    }));\n  });\n}\nexports.bufferCount = bufferCount;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "Object", "defineProperty", "exports", "bufferCount", "lift_1", "require", "OperatorSubscriber_1", "arrRemove_1", "bufferSize", "startBufferEvery", "operate", "source", "subscriber", "buffers", "count", "subscribe", "createOperatorSubscriber", "e_1", "_a", "e_2", "_b", "toEmit", "push", "buffers_1", "buffers_1_1", "buffer", "e_1_1", "error", "return", "toEmit_1", "toEmit_1_1", "arr<PERSON><PERSON><PERSON>", "e_2_1", "e_3", "buffers_2", "buffers_2_1", "e_3_1", "complete", "undefined"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/bufferCount.js"], "sourcesContent": ["\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bufferCount = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar arrRemove_1 = require(\"../util/arrRemove\");\nfunction bufferCount(bufferSize, startBufferEvery) {\n    if (startBufferEvery === void 0) { startBufferEvery = null; }\n    startBufferEvery = startBufferEvery !== null && startBufferEvery !== void 0 ? startBufferEvery : bufferSize;\n    return lift_1.operate(function (source, subscriber) {\n        var buffers = [];\n        var count = 0;\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            var e_1, _a, e_2, _b;\n            var toEmit = null;\n            if (count++ % startBufferEvery === 0) {\n                buffers.push([]);\n            }\n            try {\n                for (var buffers_1 = __values(buffers), buffers_1_1 = buffers_1.next(); !buffers_1_1.done; buffers_1_1 = buffers_1.next()) {\n                    var buffer = buffers_1_1.value;\n                    buffer.push(value);\n                    if (bufferSize <= buffer.length) {\n                        toEmit = toEmit !== null && toEmit !== void 0 ? toEmit : [];\n                        toEmit.push(buffer);\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (buffers_1_1 && !buffers_1_1.done && (_a = buffers_1.return)) _a.call(buffers_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            if (toEmit) {\n                try {\n                    for (var toEmit_1 = __values(toEmit), toEmit_1_1 = toEmit_1.next(); !toEmit_1_1.done; toEmit_1_1 = toEmit_1.next()) {\n                        var buffer = toEmit_1_1.value;\n                        arrRemove_1.arrRemove(buffers, buffer);\n                        subscriber.next(buffer);\n                    }\n                }\n                catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                finally {\n                    try {\n                        if (toEmit_1_1 && !toEmit_1_1.done && (_b = toEmit_1.return)) _b.call(toEmit_1);\n                    }\n                    finally { if (e_2) throw e_2.error; }\n                }\n            }\n        }, function () {\n            var e_3, _a;\n            try {\n                for (var buffers_2 = __values(buffers), buffers_2_1 = buffers_2.next(); !buffers_2_1.done; buffers_2_1 = buffers_2.next()) {\n                    var buffer = buffers_2_1.value;\n                    subscriber.next(buffer);\n                }\n            }\n            catch (e_3_1) { e_3 = { error: e_3_1 }; }\n            finally {\n                try {\n                    if (buffers_2_1 && !buffers_2_1.done && (_a = buffers_2.return)) _a.call(buffers_2);\n                }\n                finally { if (e_3) throw e_3.error; }\n            }\n            subscriber.complete();\n        }, undefined, function () {\n            buffers = null;\n        }));\n    });\n}\nexports.bufferCount = bufferCount;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACDW,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEL,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DK,OAAO,CAACC,WAAW,GAAG,KAAK,CAAC;AAC5B,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIE,WAAW,GAAGF,OAAO,CAAC,mBAAmB,CAAC;AAC9C,SAASF,WAAWA,CAACK,UAAU,EAAEC,gBAAgB,EAAE;EAC/C,IAAIA,gBAAgB,KAAK,KAAK,CAAC,EAAE;IAAEA,gBAAgB,GAAG,IAAI;EAAE;EAC5DA,gBAAgB,GAAGA,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGD,UAAU;EAC3G,OAAOJ,MAAM,CAACM,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,KAAK,GAAG,CAAC;IACbH,MAAM,CAACI,SAAS,CAACT,oBAAoB,CAACU,wBAAwB,CAACJ,UAAU,EAAE,UAAUf,KAAK,EAAE;MACxF,IAAIoB,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE;MACpB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIP,KAAK,EAAE,GAAGL,gBAAgB,KAAK,CAAC,EAAE;QAClCI,OAAO,CAACS,IAAI,CAAC,EAAE,CAAC;MACpB;MACA,IAAI;QACA,KAAK,IAAIC,SAAS,GAAGpC,QAAQ,CAAC0B,OAAO,CAAC,EAAEW,WAAW,GAAGD,SAAS,CAAC3B,IAAI,CAAC,CAAC,EAAE,CAAC4B,WAAW,CAAC1B,IAAI,EAAE0B,WAAW,GAAGD,SAAS,CAAC3B,IAAI,CAAC,CAAC,EAAE;UACvH,IAAI6B,MAAM,GAAGD,WAAW,CAAC3B,KAAK;UAC9B4B,MAAM,CAACH,IAAI,CAACzB,KAAK,CAAC;UAClB,IAAIW,UAAU,IAAIiB,MAAM,CAAC9B,MAAM,EAAE;YAC7B0B,MAAM,GAAGA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,EAAE;YAC3DA,MAAM,CAACC,IAAI,CAACG,MAAM,CAAC;UACvB;QACJ;MACJ,CAAC,CACD,OAAOC,KAAK,EAAE;QAAET,GAAG,GAAG;UAAEU,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIF,WAAW,IAAI,CAACA,WAAW,CAAC1B,IAAI,KAAKoB,EAAE,GAAGK,SAAS,CAACK,MAAM,CAAC,EAAEV,EAAE,CAACxB,IAAI,CAAC6B,SAAS,CAAC;QACvF,CAAC,SACO;UAAE,IAAIN,GAAG,EAAE,MAAMA,GAAG,CAACU,KAAK;QAAE;MACxC;MACA,IAAIN,MAAM,EAAE;QACR,IAAI;UACA,KAAK,IAAIQ,QAAQ,GAAG1C,QAAQ,CAACkC,MAAM,CAAC,EAAES,UAAU,GAAGD,QAAQ,CAACjC,IAAI,CAAC,CAAC,EAAE,CAACkC,UAAU,CAAChC,IAAI,EAAEgC,UAAU,GAAGD,QAAQ,CAACjC,IAAI,CAAC,CAAC,EAAE;YAChH,IAAI6B,MAAM,GAAGK,UAAU,CAACjC,KAAK;YAC7BU,WAAW,CAACwB,SAAS,CAAClB,OAAO,EAAEY,MAAM,CAAC;YACtCb,UAAU,CAAChB,IAAI,CAAC6B,MAAM,CAAC;UAC3B;QACJ,CAAC,CACD,OAAOO,KAAK,EAAE;UAAEb,GAAG,GAAG;YAAEQ,KAAK,EAAEK;UAAM,CAAC;QAAE,CAAC,SACjC;UACJ,IAAI;YACA,IAAIF,UAAU,IAAI,CAACA,UAAU,CAAChC,IAAI,KAAKsB,EAAE,GAAGS,QAAQ,CAACD,MAAM,CAAC,EAAER,EAAE,CAAC1B,IAAI,CAACmC,QAAQ,CAAC;UACnF,CAAC,SACO;YAAE,IAAIV,GAAG,EAAE,MAAMA,GAAG,CAACQ,KAAK;UAAE;QACxC;MACJ;IACJ,CAAC,EAAE,YAAY;MACX,IAAIM,GAAG,EAAEf,EAAE;MACX,IAAI;QACA,KAAK,IAAIgB,SAAS,GAAG/C,QAAQ,CAAC0B,OAAO,CAAC,EAAEsB,WAAW,GAAGD,SAAS,CAACtC,IAAI,CAAC,CAAC,EAAE,CAACuC,WAAW,CAACrC,IAAI,EAAEqC,WAAW,GAAGD,SAAS,CAACtC,IAAI,CAAC,CAAC,EAAE;UACvH,IAAI6B,MAAM,GAAGU,WAAW,CAACtC,KAAK;UAC9Be,UAAU,CAAChB,IAAI,CAAC6B,MAAM,CAAC;QAC3B;MACJ,CAAC,CACD,OAAOW,KAAK,EAAE;QAAEH,GAAG,GAAG;UAAEN,KAAK,EAAES;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAID,WAAW,IAAI,CAACA,WAAW,CAACrC,IAAI,KAAKoB,EAAE,GAAGgB,SAAS,CAACN,MAAM,CAAC,EAAEV,EAAE,CAACxB,IAAI,CAACwC,SAAS,CAAC;QACvF,CAAC,SACO;UAAE,IAAID,GAAG,EAAE,MAAMA,GAAG,CAACN,KAAK;QAAE;MACxC;MACAf,UAAU,CAACyB,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAEC,SAAS,EAAE,YAAY;MACtBzB,OAAO,GAAG,IAAI;IAClB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACAX,OAAO,CAACC,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}