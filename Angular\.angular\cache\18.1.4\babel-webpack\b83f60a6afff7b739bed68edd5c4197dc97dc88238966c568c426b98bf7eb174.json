{"ast": null, "code": "\"use strict\";\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.fromEvent = void 0;\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar Observable_1 = require(\"../Observable\");\nvar mergeMap_1 = require(\"../operators/mergeMap\");\nvar isArrayLike_1 = require(\"../util/isArrayLike\");\nvar isFunction_1 = require(\"../util/isFunction\");\nvar mapOneOrManyArgs_1 = require(\"../util/mapOneOrManyArgs\");\nvar nodeEventEmitterMethods = ['addListener', 'removeListener'];\nvar eventTargetMethods = ['addEventListener', 'removeEventListener'];\nvar jqueryMethods = ['on', 'off'];\nfunction fromEvent(target, eventName, options, resultSelector) {\n  if (isFunction_1.isFunction(options)) {\n    resultSelector = options;\n    options = undefined;\n  }\n  if (resultSelector) {\n    return fromEvent(target, eventName, options).pipe(mapOneOrManyArgs_1.mapOneOrManyArgs(resultSelector));\n  }\n  var _a = __read(isEventTarget(target) ? eventTargetMethods.map(function (methodName) {\n      return function (handler) {\n        return target[methodName](eventName, handler, options);\n      };\n    }) : isNodeStyleEventEmitter(target) ? nodeEventEmitterMethods.map(toCommonHandlerRegistry(target, eventName)) : isJQueryStyleEventEmitter(target) ? jqueryMethods.map(toCommonHandlerRegistry(target, eventName)) : [], 2),\n    add = _a[0],\n    remove = _a[1];\n  if (!add) {\n    if (isArrayLike_1.isArrayLike(target)) {\n      return mergeMap_1.mergeMap(function (subTarget) {\n        return fromEvent(subTarget, eventName, options);\n      })(innerFrom_1.innerFrom(target));\n    }\n  }\n  if (!add) {\n    throw new TypeError('Invalid event target');\n  }\n  return new Observable_1.Observable(function (subscriber) {\n    var handler = function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return subscriber.next(1 < args.length ? args : args[0]);\n    };\n    add(handler);\n    return function () {\n      return remove(handler);\n    };\n  });\n}\nexports.fromEvent = fromEvent;\nfunction toCommonHandlerRegistry(target, eventName) {\n  return function (methodName) {\n    return function (handler) {\n      return target[methodName](eventName, handler);\n    };\n  };\n}\nfunction isNodeStyleEventEmitter(target) {\n  return isFunction_1.isFunction(target.addListener) && isFunction_1.isFunction(target.removeListener);\n}\nfunction isJQueryStyleEventEmitter(target) {\n  return isFunction_1.isFunction(target.on) && isFunction_1.isFunction(target.off);\n}\nfunction isEventTarget(target) {\n  return isFunction_1.isFunction(target.addEventListener) && isFunction_1.isFunction(target.removeEventListener);\n}", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "Object", "defineProperty", "exports", "fromEvent", "innerFrom_1", "require", "Observable_1", "mergeMap_1", "isArrayLike_1", "isFunction_1", "mapOneOrManyArgs_1", "nodeEventEmitterMethods", "eventTargetMethods", "jqueryMethods", "target", "eventName", "options", "resultSelector", "isFunction", "undefined", "pipe", "mapOneOrManyArgs", "_a", "isEventTarget", "map", "methodName", "handler", "isNodeStyleEventEmitter", "toCommonHandlerRegistry", "isJQueryStyleEventEmitter", "add", "remove", "isArrayLike", "mergeMap", "subTarget", "innerFrom", "TypeError", "Observable", "subscriber", "args", "_i", "arguments", "length", "addListener", "removeListener", "on", "off", "addEventListener", "removeEventListener"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/fromEvent.js"], "sourcesContent": ["\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.fromEvent = void 0;\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar Observable_1 = require(\"../Observable\");\nvar mergeMap_1 = require(\"../operators/mergeMap\");\nvar isArrayLike_1 = require(\"../util/isArrayLike\");\nvar isFunction_1 = require(\"../util/isFunction\");\nvar mapOneOrManyArgs_1 = require(\"../util/mapOneOrManyArgs\");\nvar nodeEventEmitterMethods = ['addListener', 'removeListener'];\nvar eventTargetMethods = ['addEventListener', 'removeEventListener'];\nvar jqueryMethods = ['on', 'off'];\nfunction fromEvent(target, eventName, options, resultSelector) {\n    if (isFunction_1.isFunction(options)) {\n        resultSelector = options;\n        options = undefined;\n    }\n    if (resultSelector) {\n        return fromEvent(target, eventName, options).pipe(mapOneOrManyArgs_1.mapOneOrManyArgs(resultSelector));\n    }\n    var _a = __read(isEventTarget(target)\n        ? eventTargetMethods.map(function (methodName) { return function (handler) { return target[methodName](eventName, handler, options); }; })\n        :\n            isNodeStyleEventEmitter(target)\n                ? nodeEventEmitterMethods.map(toCommonHandlerRegistry(target, eventName))\n                : isJQueryStyleEventEmitter(target)\n                    ? jqueryMethods.map(toCommonHandlerRegistry(target, eventName))\n                    : [], 2), add = _a[0], remove = _a[1];\n    if (!add) {\n        if (isArrayLike_1.isArrayLike(target)) {\n            return mergeMap_1.mergeMap(function (subTarget) { return fromEvent(subTarget, eventName, options); })(innerFrom_1.innerFrom(target));\n        }\n    }\n    if (!add) {\n        throw new TypeError('Invalid event target');\n    }\n    return new Observable_1.Observable(function (subscriber) {\n        var handler = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return subscriber.next(1 < args.length ? args : args[0]);\n        };\n        add(handler);\n        return function () { return remove(handler); };\n    });\n}\nexports.fromEvent = fromEvent;\nfunction toCommonHandlerRegistry(target, eventName) {\n    return function (methodName) { return function (handler) { return target[methodName](eventName, handler); }; };\n}\nfunction isNodeStyleEventEmitter(target) {\n    return isFunction_1.isFunction(target.addListener) && isFunction_1.isFunction(target.removeListener);\n}\nfunction isJQueryStyleEventEmitter(target) {\n    return isFunction_1.isFunction(target.on) && isFunction_1.isFunction(target.off);\n}\nfunction isEventTarget(target) {\n    return isFunction_1.isFunction(target.addEventListener) && isFunction_1.isFunction(target.removeEventListener);\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,MAAM,GAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAK,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC;EAC1D,IAAI,CAACF,CAAC,EAAE,OAAOF,CAAC;EAChB,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAI,CAACN,CAAC,CAAC;IAAEO,CAAC;IAAEC,EAAE,GAAG,EAAE;IAAEC,CAAC;EAChC,IAAI;IACA,OAAO,CAACR,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAEH,EAAE,CAACI,IAAI,CAACL,CAAC,CAACM,KAAK,CAAC;EAC9E,CAAC,CACD,OAAOC,KAAK,EAAE;IAAEL,CAAC,GAAG;MAAEK,KAAK,EAAEA;IAAM,CAAC;EAAE,CAAC,SAC/B;IACJ,IAAI;MACA,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAI,KAAKT,CAAC,GAAGG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAEH,CAAC,CAACI,IAAI,CAACD,CAAC,CAAC;IACpD,CAAC,SACO;MAAE,IAAII,CAAC,EAAE,MAAMA,CAAC,CAACK,KAAK;IAAE;EACpC;EACA,OAAON,EAAE;AACb,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEJ,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DI,OAAO,CAACC,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,WAAW,GAAGC,OAAO,CAAC,yBAAyB,CAAC;AACpD,IAAIC,YAAY,GAAGD,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIE,UAAU,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AACjD,IAAIG,aAAa,GAAGH,OAAO,CAAC,qBAAqB,CAAC;AAClD,IAAII,YAAY,GAAGJ,OAAO,CAAC,oBAAoB,CAAC;AAChD,IAAIK,kBAAkB,GAAGL,OAAO,CAAC,0BAA0B,CAAC;AAC5D,IAAIM,uBAAuB,GAAG,CAAC,aAAa,EAAE,gBAAgB,CAAC;AAC/D,IAAIC,kBAAkB,GAAG,CAAC,kBAAkB,EAAE,qBAAqB,CAAC;AACpE,IAAIC,aAAa,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;AACjC,SAASV,SAASA,CAACW,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,cAAc,EAAE;EAC3D,IAAIR,YAAY,CAACS,UAAU,CAACF,OAAO,CAAC,EAAE;IAClCC,cAAc,GAAGD,OAAO;IACxBA,OAAO,GAAGG,SAAS;EACvB;EACA,IAAIF,cAAc,EAAE;IAChB,OAAOd,SAAS,CAACW,MAAM,EAAEC,SAAS,EAAEC,OAAO,CAAC,CAACI,IAAI,CAACV,kBAAkB,CAACW,gBAAgB,CAACJ,cAAc,CAAC,CAAC;EAC1G;EACA,IAAIK,EAAE,GAAGtC,MAAM,CAACuC,aAAa,CAACT,MAAM,CAAC,GAC/BF,kBAAkB,CAACY,GAAG,CAAC,UAAUC,UAAU,EAAE;MAAE,OAAO,UAAUC,OAAO,EAAE;QAAE,OAAOZ,MAAM,CAACW,UAAU,CAAC,CAACV,SAAS,EAAEW,OAAO,EAAEV,OAAO,CAAC;MAAE,CAAC;IAAE,CAAC,CAAC,GAEtIW,uBAAuB,CAACb,MAAM,CAAC,GACzBH,uBAAuB,CAACa,GAAG,CAACI,uBAAuB,CAACd,MAAM,EAAEC,SAAS,CAAC,CAAC,GACvEc,yBAAyB,CAACf,MAAM,CAAC,GAC7BD,aAAa,CAACW,GAAG,CAACI,uBAAuB,CAACd,MAAM,EAAEC,SAAS,CAAC,CAAC,GAC7D,EAAE,EAAE,CAAC,CAAC;IAAEe,GAAG,GAAGR,EAAE,CAAC,CAAC,CAAC;IAAES,MAAM,GAAGT,EAAE,CAAC,CAAC,CAAC;EACrD,IAAI,CAACQ,GAAG,EAAE;IACN,IAAItB,aAAa,CAACwB,WAAW,CAAClB,MAAM,CAAC,EAAE;MACnC,OAAOP,UAAU,CAAC0B,QAAQ,CAAC,UAAUC,SAAS,EAAE;QAAE,OAAO/B,SAAS,CAAC+B,SAAS,EAAEnB,SAAS,EAAEC,OAAO,CAAC;MAAE,CAAC,CAAC,CAACZ,WAAW,CAAC+B,SAAS,CAACrB,MAAM,CAAC,CAAC;IACxI;EACJ;EACA,IAAI,CAACgB,GAAG,EAAE;IACN,MAAM,IAAIM,SAAS,CAAC,sBAAsB,CAAC;EAC/C;EACA,OAAO,IAAI9B,YAAY,CAAC+B,UAAU,CAAC,UAAUC,UAAU,EAAE;IACrD,IAAIZ,OAAO,GAAG,SAAAA,CAAA,EAAY;MACtB,IAAIa,IAAI,GAAG,EAAE;MACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;QAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;MAC5B;MACA,OAAOF,UAAU,CAAC3C,IAAI,CAAC,CAAC,GAAG4C,IAAI,CAACG,MAAM,GAAGH,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;IACDT,GAAG,CAACJ,OAAO,CAAC;IACZ,OAAO,YAAY;MAAE,OAAOK,MAAM,CAACL,OAAO,CAAC;IAAE,CAAC;EAClD,CAAC,CAAC;AACN;AACAxB,OAAO,CAACC,SAAS,GAAGA,SAAS;AAC7B,SAASyB,uBAAuBA,CAACd,MAAM,EAAEC,SAAS,EAAE;EAChD,OAAO,UAAUU,UAAU,EAAE;IAAE,OAAO,UAAUC,OAAO,EAAE;MAAE,OAAOZ,MAAM,CAACW,UAAU,CAAC,CAACV,SAAS,EAAEW,OAAO,CAAC;IAAE,CAAC;EAAE,CAAC;AAClH;AACA,SAASC,uBAAuBA,CAACb,MAAM,EAAE;EACrC,OAAOL,YAAY,CAACS,UAAU,CAACJ,MAAM,CAAC6B,WAAW,CAAC,IAAIlC,YAAY,CAACS,UAAU,CAACJ,MAAM,CAAC8B,cAAc,CAAC;AACxG;AACA,SAASf,yBAAyBA,CAACf,MAAM,EAAE;EACvC,OAAOL,YAAY,CAACS,UAAU,CAACJ,MAAM,CAAC+B,EAAE,CAAC,IAAIpC,YAAY,CAACS,UAAU,CAACJ,MAAM,CAACgC,GAAG,CAAC;AACpF;AACA,SAASvB,aAAaA,CAACT,MAAM,EAAE;EAC3B,OAAOL,YAAY,CAACS,UAAU,CAACJ,MAAM,CAACiC,gBAAgB,CAAC,IAAItC,YAAY,CAACS,UAAU,CAACJ,MAAM,CAACkC,mBAAmB,CAAC;AAClH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}