{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.multicast = void 0;\nvar ConnectableObservable_1 = require(\"../observable/ConnectableObservable\");\nvar isFunction_1 = require(\"../util/isFunction\");\nvar connect_1 = require(\"./connect\");\nfunction multicast(subjectOrSubjectFactory, selector) {\n  var subjectFactory = isFunction_1.isFunction(subjectOrSubjectFactory) ? subjectOrSubjectFactory : function () {\n    return subjectOrSubjectFactory;\n  };\n  if (isFunction_1.isFunction(selector)) {\n    return connect_1.connect(selector, {\n      connector: subjectFactory\n    });\n  }\n  return function (source) {\n    return new ConnectableObservable_1.ConnectableObservable(source, subjectFactory);\n  };\n}\nexports.multicast = multicast;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "multicast", "ConnectableObservable_1", "require", "isFunction_1", "connect_1", "subjectOrSubjectFactory", "selector", "subjectFactory", "isFunction", "connect", "connector", "source", "ConnectableObservable"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/multicast.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.multicast = void 0;\nvar ConnectableObservable_1 = require(\"../observable/ConnectableObservable\");\nvar isFunction_1 = require(\"../util/isFunction\");\nvar connect_1 = require(\"./connect\");\nfunction multicast(subjectOrSubjectFactory, selector) {\n    var subjectFactory = isFunction_1.isFunction(subjectOrSubjectFactory) ? subjectOrSubjectFactory : function () { return subjectOrSubjectFactory; };\n    if (isFunction_1.isFunction(selector)) {\n        return connect_1.connect(selector, {\n            connector: subjectFactory,\n        });\n    }\n    return function (source) { return new ConnectableObservable_1.ConnectableObservable(source, subjectFactory); };\n}\nexports.multicast = multicast;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,uBAAuB,GAAGC,OAAO,CAAC,qCAAqC,CAAC;AAC5E,IAAIC,YAAY,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AAChD,IAAIE,SAAS,GAAGF,OAAO,CAAC,WAAW,CAAC;AACpC,SAASF,SAASA,CAACK,uBAAuB,EAAEC,QAAQ,EAAE;EAClD,IAAIC,cAAc,GAAGJ,YAAY,CAACK,UAAU,CAACH,uBAAuB,CAAC,GAAGA,uBAAuB,GAAG,YAAY;IAAE,OAAOA,uBAAuB;EAAE,CAAC;EACjJ,IAAIF,YAAY,CAACK,UAAU,CAACF,QAAQ,CAAC,EAAE;IACnC,OAAOF,SAAS,CAACK,OAAO,CAACH,QAAQ,EAAE;MAC/BI,SAAS,EAAEH;IACf,CAAC,CAAC;EACN;EACA,OAAO,UAAUI,MAAM,EAAE;IAAE,OAAO,IAAIV,uBAAuB,CAACW,qBAAqB,CAACD,MAAM,EAAEJ,cAAc,CAAC;EAAE,CAAC;AAClH;AACAT,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}