{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.animationFrames = void 0;\nvar Observable_1 = require(\"../../Observable\");\nvar performanceTimestampProvider_1 = require(\"../../scheduler/performanceTimestampProvider\");\nvar animationFrameProvider_1 = require(\"../../scheduler/animationFrameProvider\");\nfunction animationFrames(timestampProvider) {\n  return timestampProvider ? animationFramesFactory(timestampProvider) : DEFAULT_ANIMATION_FRAMES;\n}\nexports.animationFrames = animationFrames;\nfunction animationFramesFactory(timestampProvider) {\n  return new Observable_1.Observable(function (subscriber) {\n    var provider = timestampProvider || performanceTimestampProvider_1.performanceTimestampProvider;\n    var start = provider.now();\n    var id = 0;\n    var run = function () {\n      if (!subscriber.closed) {\n        id = animationFrameProvider_1.animationFrameProvider.requestAnimationFrame(function (timestamp) {\n          id = 0;\n          var now = provider.now();\n          subscriber.next({\n            timestamp: timestampProvider ? now : timestamp,\n            elapsed: now - start\n          });\n          run();\n        });\n      }\n    };\n    run();\n    return function () {\n      if (id) {\n        animationFrameProvider_1.animationFrameProvider.cancelAnimationFrame(id);\n      }\n    };\n  });\n}\nvar DEFAULT_ANIMATION_FRAMES = animationFramesFactory();", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "animationFrames", "Observable_1", "require", "performanceTimestampProvider_1", "animationFrameProvider_1", "timestampProvider", "animationFramesFactory", "DEFAULT_ANIMATION_FRAMES", "Observable", "subscriber", "provider", "performanceTimestampProvider", "start", "now", "id", "run", "closed", "animationFrameProvider", "requestAnimationFrame", "timestamp", "next", "elapsed", "cancelAnimationFrame"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/dom/animationFrames.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.animationFrames = void 0;\nvar Observable_1 = require(\"../../Observable\");\nvar performanceTimestampProvider_1 = require(\"../../scheduler/performanceTimestampProvider\");\nvar animationFrameProvider_1 = require(\"../../scheduler/animationFrameProvider\");\nfunction animationFrames(timestampProvider) {\n    return timestampProvider ? animationFramesFactory(timestampProvider) : DEFAULT_ANIMATION_FRAMES;\n}\nexports.animationFrames = animationFrames;\nfunction animationFramesFactory(timestampProvider) {\n    return new Observable_1.Observable(function (subscriber) {\n        var provider = timestampProvider || performanceTimestampProvider_1.performanceTimestampProvider;\n        var start = provider.now();\n        var id = 0;\n        var run = function () {\n            if (!subscriber.closed) {\n                id = animationFrameProvider_1.animationFrameProvider.requestAnimationFrame(function (timestamp) {\n                    id = 0;\n                    var now = provider.now();\n                    subscriber.next({\n                        timestamp: timestampProvider ? now : timestamp,\n                        elapsed: now - start,\n                    });\n                    run();\n                });\n            }\n        };\n        run();\n        return function () {\n            if (id) {\n                animationFrameProvider_1.animationFrameProvider.cancelAnimationFrame(id);\n            }\n        };\n    });\n}\nvar DEFAULT_ANIMATION_FRAMES = animationFramesFactory();\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,eAAe,GAAG,KAAK,CAAC;AAChC,IAAIC,YAAY,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC9C,IAAIC,8BAA8B,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAC5F,IAAIE,wBAAwB,GAAGF,OAAO,CAAC,wCAAwC,CAAC;AAChF,SAASF,eAAeA,CAACK,iBAAiB,EAAE;EACxC,OAAOA,iBAAiB,GAAGC,sBAAsB,CAACD,iBAAiB,CAAC,GAAGE,wBAAwB;AACnG;AACAT,OAAO,CAACE,eAAe,GAAGA,eAAe;AACzC,SAASM,sBAAsBA,CAACD,iBAAiB,EAAE;EAC/C,OAAO,IAAIJ,YAAY,CAACO,UAAU,CAAC,UAAUC,UAAU,EAAE;IACrD,IAAIC,QAAQ,GAAGL,iBAAiB,IAAIF,8BAA8B,CAACQ,4BAA4B;IAC/F,IAAIC,KAAK,GAAGF,QAAQ,CAACG,GAAG,CAAC,CAAC;IAC1B,IAAIC,EAAE,GAAG,CAAC;IACV,IAAIC,GAAG,GAAG,SAAAA,CAAA,EAAY;MAClB,IAAI,CAACN,UAAU,CAACO,MAAM,EAAE;QACpBF,EAAE,GAAGV,wBAAwB,CAACa,sBAAsB,CAACC,qBAAqB,CAAC,UAAUC,SAAS,EAAE;UAC5FL,EAAE,GAAG,CAAC;UACN,IAAID,GAAG,GAAGH,QAAQ,CAACG,GAAG,CAAC,CAAC;UACxBJ,UAAU,CAACW,IAAI,CAAC;YACZD,SAAS,EAAEd,iBAAiB,GAAGQ,GAAG,GAAGM,SAAS;YAC9CE,OAAO,EAAER,GAAG,GAAGD;UACnB,CAAC,CAAC;UACFG,GAAG,CAAC,CAAC;QACT,CAAC,CAAC;MACN;IACJ,CAAC;IACDA,GAAG,CAAC,CAAC;IACL,OAAO,YAAY;MACf,IAAID,EAAE,EAAE;QACJV,wBAAwB,CAACa,sBAAsB,CAACK,oBAAoB,CAACR,EAAE,CAAC;MAC5E;IACJ,CAAC;EACL,CAAC,CAAC;AACN;AACA,IAAIP,wBAAwB,GAAGD,sBAAsB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}