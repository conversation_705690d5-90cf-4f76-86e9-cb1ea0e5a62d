{"ast": null, "code": "import _asyncToGenerator from \"D:/permittracker/Angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"../../services/permits.service\";\nimport * as i4 from \"../../services/custom-layout.utils.service\";\nimport * as i5 from \"../../services/http-utils.service\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c1 = a0 => ({\n  active: a0\n});\nfunction AddEditInternalReviewDetailComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Review Detail - \", ctx_r1.reviewCategory || \"\", \"\");\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Add Review Detail - \", ctx_r1.reviewCategory || \"\", \"\");\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 34)(2, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template_button_click_2_listener() {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.deleteImportedRow(i_r7));\n    });\n    i0.ɵɵelement(3, \"i\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"input\", 37);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template_input_ngModelChange_5_listener($event) {\n      const r_r8 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵtwoWayBindingSet(r_r8.sheetNumber, $event) || (r_r8.sheetNumber = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_div_6_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\")(8, \"input\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template_input_ngModelChange_8_listener($event) {\n      const r_r8 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵtwoWayBindingSet(r_r8.codeRef, $event) || (r_r8.codeRef = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_div_9_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\")(11, \"textarea\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template_textarea_ngModelChange_11_listener($event) {\n      const r_r8 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵtwoWayBindingSet(r_r8.codeDescription, $event) || (r_r8.codeDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_div_12_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\")(14, \"textarea\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template_textarea_ngModelChange_14_listener($event) {\n      const r_r8 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵtwoWayBindingSet(r_r8.reasoning, $event) || (r_r8.reasoning = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_div_15_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\")(17, \"textarea\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template_textarea_ngModelChange_17_listener($event) {\n      const r_r8 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵtwoWayBindingSet(r_r8.nonCompliance, $event) || (r_r8.nonCompliance = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\")(19, \"textarea\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template_textarea_ngModelChange_19_listener($event) {\n      const r_r8 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵtwoWayBindingSet(r_r8.actionableStep, $event) || (r_r8.actionableStep = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const r_r8 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"sheetNumber\", i_r7, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", r_r8.sheetNumber);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(27, _c0, ctx_r1.importSubmitted && !(r_r8.sheetNumber || \"\").trim()));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.importSubmitted && !(r_r8.sheetNumber || \"\").trim());\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"codeRef\", i_r7, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", r_r8.codeRef);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(29, _c0, ctx_r1.importSubmitted && !(r_r8.codeRef || \"\").trim()));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.importSubmitted && !(r_r8.codeRef || \"\").trim());\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"codeDescription\", i_r7, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", r_r8.codeDescription);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(31, _c0, ctx_r1.importSubmitted && !(r_r8.codeDescription || \"\").trim()));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.importSubmitted && !(r_r8.codeDescription || \"\").trim());\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"reasoning\", i_r7, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", r_r8.reasoning);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(33, _c0, ctx_r1.importSubmitted && !(r_r8.reasoning || \"\").trim()));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.importSubmitted && !(r_r8.reasoning || \"\").trim());\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"nonCompliance\", i_r7, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", r_r8.nonCompliance);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"actionableStep\", i_r7, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", r_r8.actionableStep);\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_8_div_1_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"h6\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\")(5, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_div_8_div_1_div_19_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.addImportedRow());\n    });\n    i0.ɵɵelement(6, \"i\", 24);\n    i0.ɵɵtext(7, \" Add Row \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_div_8_div_1_div_19_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.saveAllImported());\n    });\n    i0.ɵɵtext(9, \"Save All\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 26)(11, \"table\", 27)(12, \"thead\", 28)(13, \"tr\")(14, \"th\", 29);\n    i0.ɵɵtext(15, \"Action\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 30);\n    i0.ɵɵtext(17, \"Sheet Number \");\n    i0.ɵɵelementStart(18, \"span\", 31);\n    i0.ɵɵtext(19, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"th\", 32);\n    i0.ɵɵtext(21, \"Code Ref \");\n    i0.ɵɵelementStart(22, \"span\", 31);\n    i0.ɵɵtext(23, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"th\");\n    i0.ɵɵtext(25, \"Code Description \");\n    i0.ɵɵelementStart(26, \"span\", 31);\n    i0.ɵɵtext(27, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"th\");\n    i0.ɵɵtext(29, \"Reasoning \");\n    i0.ɵɵelementStart(30, \"span\", 31);\n    i0.ɵɵtext(31, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"th\");\n    i0.ɵɵtext(33, \"Non Compliance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"th\");\n    i0.ɵɵtext(35, \"Actionable Step\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"tbody\");\n    i0.ɵɵtemplate(37, AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template, 20, 35, \"tr\", 33);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Imported Review Details (\", ctx_r1.importedRows.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || ctx_r1.isImporting);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || ctx_r1.isImporting || ctx_r1.hasImportedInvalid);\n    i0.ɵɵadvance(29);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.importedRows);\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 11)(2, \"div\")(3, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_div_8_div_1_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onBack());\n    });\n    i0.ɵɵtext(4, \"Back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 13)(6, \"a\", 14);\n    i0.ɵɵtext(7, \"Download Template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 15, 1);\n    i0.ɵɵlistener(\"change\", function AddEditInternalReviewDetailComponent_div_8_div_1_Template_input_change_8_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_div_8_div_1_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const fileInput_r4 = i0.ɵɵreference(9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.triggerFileImport(fileInput_r4));\n    });\n    i0.ɵɵtext(11, \"Import Excel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_div_8_div_1_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClickAddReview());\n    });\n    i0.ɵɵtext(13, \"Add Review Detail\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 18);\n    i0.ɵɵtext(15, \" You can upload review details by downloading the template, filling it, and importing the Excel. Or click \");\n    i0.ɵɵelementStart(16, \"strong\");\n    i0.ɵɵtext(17, \"Add Review Detail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18, \" to enter details manually. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, AddEditInternalReviewDetailComponent_div_8_div_1_div_19_Template, 38, 4, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"href\", ctx_r1.templateUrlPath, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.importedRows == null ? null : ctx_r1.importedRows.length);\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_8_form_2_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_8_form_2_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_8_form_2_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_8_form_2_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_8_form_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 43);\n    i0.ɵɵlistener(\"ngSubmit\", function AddEditInternalReviewDetailComponent_div_8_form_2_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 44);\n    i0.ɵɵelement(2, \"div\");\n    i0.ɵɵelementStart(3, \"div\")(4, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_div_8_form_2_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.cancelAddForm());\n    });\n    i0.ɵɵtext(5, \"Cancel\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"div\", 46)(8, \"label\", 47);\n    i0.ɵɵtext(9, \"Sheet Number \");\n    i0.ɵɵelementStart(10, \"span\", 31);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(12, \"input\", 48);\n    i0.ɵɵtemplate(13, AddEditInternalReviewDetailComponent_div_8_form_2_div_13_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 46)(15, \"label\", 47);\n    i0.ɵɵtext(16, \"Code Ref \");\n    i0.ɵɵelementStart(17, \"span\", 31);\n    i0.ɵɵtext(18, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(19, \"input\", 49);\n    i0.ɵɵtemplate(20, AddEditInternalReviewDetailComponent_div_8_form_2_div_20_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 50)(22, \"div\", 51)(23, \"label\", 47);\n    i0.ɵɵtext(24, \"Code Description \");\n    i0.ɵɵelementStart(25, \"span\", 31);\n    i0.ɵɵtext(26, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(27, \"textarea\", 52);\n    i0.ɵɵtemplate(28, AddEditInternalReviewDetailComponent_div_8_form_2_div_28_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 50)(30, \"div\", 51)(31, \"label\", 47);\n    i0.ɵɵtext(32, \"Reasoning \");\n    i0.ɵɵelementStart(33, \"span\", 31);\n    i0.ɵɵtext(34, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(35, \"textarea\", 53);\n    i0.ɵɵtemplate(36, AddEditInternalReviewDetailComponent_div_8_form_2_div_36_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 50)(38, \"div\", 51)(39, \"label\", 47);\n    i0.ɵɵtext(40, \"Non Compliance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(41, \"textarea\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 50)(43, \"div\", 51)(44, \"label\", 47);\n    i0.ɵɵtext(45, \"Actionable Step\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(46, \"textarea\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 56);\n    i0.ɵɵelement(48, \"div\");\n    i0.ɵɵelementStart(49, \"div\")(50, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_div_8_form_2_Template_button_click_50_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCancel());\n    });\n    i0.ɵɵtext(51, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"button\", 58);\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.detailForm);\n    i0.ɵɵadvance(12);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"sheetNumber\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"sheetNumber\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"codeRef\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"codeRef\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"codeDescription\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"codeDescription\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"reasoning\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"reasoning\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.isEdit ? \"Update\" : \"Create\");\n  }\n}\nfunction AddEditInternalReviewDetailComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, AddEditInternalReviewDetailComponent_div_8_div_1_Template, 20, 3, \"div\", 5)(2, AddEditInternalReviewDetailComponent_div_8_form_2_Template, 54, 22, \"form\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showExcelSection);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showForm);\n  }\n}\nfunction AddEditInternalReviewDetailComponent_ng_template_9_div_12_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_ng_template_9_div_12_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_ng_template_9_div_12_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_ng_template_9_div_12_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddEditInternalReviewDetailComponent_ng_template_9_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 45)(2, \"div\", 46)(3, \"label\", 47);\n    i0.ɵɵtext(4, \"Sheet Number \");\n    i0.ɵɵelementStart(5, \"span\", 31);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"input\", 48);\n    i0.ɵɵtemplate(8, AddEditInternalReviewDetailComponent_ng_template_9_div_12_div_8_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 46)(10, \"label\", 47);\n    i0.ɵɵtext(11, \"Code Ref \");\n    i0.ɵɵelementStart(12, \"span\", 31);\n    i0.ɵɵtext(13, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(14, \"input\", 49);\n    i0.ɵɵtemplate(15, AddEditInternalReviewDetailComponent_ng_template_9_div_12_div_15_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 50)(17, \"div\", 51)(18, \"label\", 47);\n    i0.ɵɵtext(19, \"Code Description \");\n    i0.ɵɵelementStart(20, \"span\", 31);\n    i0.ɵɵtext(21, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(22, \"textarea\", 67);\n    i0.ɵɵtemplate(23, AddEditInternalReviewDetailComponent_ng_template_9_div_12_div_23_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 50)(25, \"div\", 51)(26, \"label\", 47);\n    i0.ɵɵtext(27, \"Reasoning \");\n    i0.ɵɵelementStart(28, \"span\", 31);\n    i0.ɵɵtext(29, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(30, \"textarea\", 68);\n    i0.ɵɵtemplate(31, AddEditInternalReviewDetailComponent_ng_template_9_div_12_div_31_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 50)(33, \"div\", 51)(34, \"label\", 47);\n    i0.ɵɵtext(35, \"Non Compliance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"textarea\", 69);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"sheetNumber\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"sheetNumber\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"codeRef\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"codeRef\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"codeDescription\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"codeDescription\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.shouldShowValidationError(\"reasoning\"));\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowValidationError(\"reasoning\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction AddEditInternalReviewDetailComponent_ng_template_9_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 45)(2, \"div\", 51)(3, \"label\", 47);\n    i0.ɵɵtext(4, \"Actionable Step\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"textarea\", 70);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction AddEditInternalReviewDetailComponent_ng_template_9_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_ng_template_9_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToPrevious());\n    });\n    i0.ɵɵtext(1, \"Previous\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction AddEditInternalReviewDetailComponent_ng_template_9_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_ng_template_9_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setActiveTab(\"comments\"));\n    });\n    i0.ɵɵtext(1, \"Next\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction AddEditInternalReviewDetailComponent_ng_template_9_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.isEdit ? \"Update\" : \"Create\");\n  }\n}\nfunction AddEditInternalReviewDetailComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 59)(2, \"div\", 51)(3, \"div\", 60)(4, \"ul\", 61)(5, \"li\", 62)(6, \"a\", 63);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_ng_template_9_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setActiveTab(\"details\"));\n    });\n    i0.ɵɵtext(7, \" Review Details \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"li\", 62)(9, \"a\", 63);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_ng_template_9_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setActiveTab(\"comments\"));\n    });\n    i0.ɵɵtext(10, \" Review Comments \");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(11, \"form\", 43);\n    i0.ɵɵlistener(\"ngSubmit\", function AddEditInternalReviewDetailComponent_ng_template_9_Template_form_ngSubmit_11_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵtemplate(12, AddEditInternalReviewDetailComponent_ng_template_9_div_12_Template, 37, 17, \"div\", 5)(13, AddEditInternalReviewDetailComponent_ng_template_9_div_13_Template, 6, 1, \"div\", 5);\n    i0.ɵɵelementStart(14, \"div\", 56)(15, \"div\");\n    i0.ɵɵtemplate(16, AddEditInternalReviewDetailComponent_ng_template_9_button_16_Template, 2, 1, \"button\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\")(18, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_ng_template_9_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCancel());\n    });\n    i0.ɵɵtext(19, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, AddEditInternalReviewDetailComponent_ng_template_9_button_20_Template, 2, 1, \"button\", 65)(21, AddEditInternalReviewDetailComponent_ng_template_9_button_21_Template, 2, 2, \"button\", 66);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c1, ctx_r1.activeTab === \"details\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c1, ctx_r1.activeTab === \"comments\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.detailForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeTab === \"details\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeTab === \"comments\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeTab === \"comments\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeTab === \"details\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeTab === \"comments\");\n  }\n}\nexport class AddEditInternalReviewDetailComponent {\n  fb;\n  modal;\n  permitsService;\n  customLayoutUtilsService;\n  httpUtilsService;\n  permitId = null;\n  permitNumber = '';\n  reviewCategory = '';\n  internalCommentsId = null;\n  detailData = null; // For edit mode\n  loggedInUserId = 'user'; // Should be passed from parent\n  detailForm;\n  isEdit = false;\n  isLoading = false;\n  // Tabs removed; single-view form\n  formSubmitted = false;\n  showForm = false;\n  showExcelSection = true;\n  templateUrlPath = '/assets/excel/claimstempate.xlsx';\n  importedRows = [];\n  isImporting = false;\n  importSubmitted = false;\n  // Tab state for edit mode (old popup)\n  activeTab = 'details';\n  constructor(fb, modal, permitsService, customLayoutUtilsService, httpUtilsService) {\n    this.fb = fb;\n    this.modal = modal;\n    this.permitsService = permitsService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.httpUtilsService = httpUtilsService;\n  }\n  ngOnInit() {\n    this.isEdit = !!this.detailData;\n    this.detailForm = this.fb.group({\n      sheetNumber: [this.detailData?.sheetNumber || '', Validators.required],\n      codeRef: [this.detailData?.codeRef || '', Validators.required],\n      codeDescription: [this.detailData?.codeDescription || '', Validators.required],\n      reasoning: [this.detailData?.reasoning || '', Validators.required],\n      nonCompliance: [this.detailData?.nonCompliance || ''],\n      actionableStep: [this.detailData?.actionableStep || ''],\n      aeResponse: [this.detailData?.aeResponse || ''],\n      commentResponsedBy: [this.detailData?.commentResponsedBy || '']\n    });\n  }\n  // Tab navigation removed\n  shouldShowValidationError(fieldName) {\n    // Only show validation errors when form has been submitted\n    if (!this.formSubmitted) {\n      return false;\n    }\n    const field = this.detailForm.get(fieldName);\n    return !!(field && field.invalid);\n  }\n  onSubmit() {\n    this.formSubmitted = true;\n    if (this.detailForm.valid && this.permitId) {\n      this.isLoading = true;\n      // Enable common loader\n      this.httpUtilsService.loadingSubject.next(true);\n      const formData = {\n        ...this.detailForm.value,\n        permitId: this.permitId,\n        permitNumber: this.permitNumber,\n        reviewCategory: this.reviewCategory,\n        internalCommentsId: this.internalCommentsId || this.detailData?.internalCommentsId,\n        loggedInUserId: this.loggedInUserId\n      };\n      if (this.isEdit && this.detailData?.internalReviewCommentsId) {\n        formData.internalReviewCommentsId = this.detailData.internalReviewCommentsId;\n        this.permitsService.updateInternalPlanReviewDetail(formData).subscribe({\n          next: res => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to update review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess(res.responseData?.message || 'Review detail updated successfully!', '');\n              this.modal.close('updated');\n            }\n          },\n          error: err => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error updating review detail', '');\n            console.error(err);\n          }\n        });\n      } else {\n        this.permitsService.addInternalPlanReviewDetail(formData).subscribe({\n          next: res => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess('Review detail created successfully!', '');\n              this.modal.close('created');\n            }\n          },\n          error: err => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error creating review detail', '');\n            console.error(err);\n          }\n        });\n      }\n    } else {\n      this.detailForm.markAllAsTouched();\n      if (!this.permitId) {\n        this.customLayoutUtilsService.showError('Permit Id is required', '');\n      }\n    }\n  }\n  onCancel() {\n    this.modal.dismiss('cancelled');\n  }\n  onBack() {\n    this.modal.dismiss('back');\n  }\n  onClickAddReview() {\n    this.formSubmitted = false;\n    this.showForm = true;\n    this.showExcelSection = false;\n  }\n  cancelAddForm() {\n    if (this.detailForm) {\n      this.detailForm.reset({\n        sheetNumber: '',\n        codeRef: '',\n        codeDescription: '',\n        reasoning: '',\n        nonCompliance: '',\n        actionableStep: ''\n      });\n    }\n    this.formSubmitted = false;\n    this.showForm = false;\n    this.showExcelSection = true;\n  }\n  setActiveTab(tab) {\n    this.activeTab = tab;\n  }\n  goToPrevious() {\n    if (this.activeTab === 'comments') {\n      this.activeTab = 'details';\n    }\n  }\n  triggerFileImport(fileInput) {\n    if (this.isLoading) {\n      return;\n    }\n    fileInput.click();\n  }\n  onFileSelected(event) {\n    var _this = this;\n    const input = event.target;\n    const file = input?.files && input.files.length ? input.files[0] : null;\n    if (!file) {\n      return;\n    }\n    this.isImporting = true;\n    // Enable common loader\n    this.httpUtilsService.loadingSubject.next(true);\n    const reader = new FileReader();\n    reader.onload = /*#__PURE__*/_asyncToGenerator(function* () {\n      try {\n        const XLSX = yield import('xlsx');\n        const data = new Uint8Array(reader.result);\n        const workbook = XLSX.read(data, {\n          type: 'array'\n        });\n        const firstSheetName = workbook.SheetNames[0];\n        const worksheet = workbook.Sheets[firstSheetName];\n        const json = XLSX.utils.sheet_to_json(worksheet, {\n          defval: ''\n        });\n        _this.importedRows = _this.mapExcelRows(json);\n        // Ensure Excel section visible and form hidden\n        _this.showExcelSection = true;\n        _this.showForm = false;\n        if (!_this.importedRows.length) {\n          _this.customLayoutUtilsService.showError('No rows found in the uploaded Excel.', '');\n        } else {\n          _this.customLayoutUtilsService.showSuccess(`Imported ${_this.importedRows.length} row(s).`, '');\n        }\n      } catch (err) {\n        console.error('Error parsing Excel', err);\n        _this.importedRows = [];\n        _this.customLayoutUtilsService.showError('Failed to parse Excel file. Please check the template.', '');\n      } finally {\n        _this.isImporting = false;\n        // Disable common loader\n        _this.httpUtilsService.loadingSubject.next(false);\n        // Reset file input so the same file can be selected again if needed\n        input.value = '';\n      }\n    });\n    reader.readAsArrayBuffer(file);\n  }\n  normalizeHeader(header) {\n    return (header || '').toString().trim().toLowerCase().replace(/\\s+/g, '');\n  }\n  mapExcelRows(jsonRows) {\n    if (!jsonRows || !jsonRows.length) {\n      return [];\n    }\n    const headerMap = {};\n    // Build header map from first row's keys\n    const firstRow = jsonRows[0];\n    Object.keys(firstRow).forEach(key => {\n      const norm = this.normalizeHeader(key);\n      headerMap[norm] = key;\n    });\n    const pick = (row, keyCandidates) => {\n      for (const candidate of keyCandidates) {\n        const norm = this.normalizeHeader(candidate);\n        const actual = headerMap[norm];\n        if (actual && row.hasOwnProperty(actual)) {\n          return (row[actual] ?? '').toString();\n        }\n      }\n      return '';\n    };\n    // Expected columns with common variants\n    const rows = jsonRows.map(row => {\n      return {\n        sheetNumber: pick(row, ['Sheet Number', 'SheetNumber', 'Sheet#', 'Sheet No']),\n        codeRef: pick(row, ['Code Ref', 'Code Reference', 'CodeRef']),\n        codeDescription: pick(row, ['Code Description', 'Description']),\n        reasoning: pick(row, ['Reasoning', 'Reason']),\n        nonCompliance: pick(row, ['Non Compliance', 'Non-Compliance', 'NonCompliance']),\n        actionableStep: pick(row, ['Actionable Step', 'ActionableStep', 'Action Step'])\n      };\n    });\n    // Filter out completely empty rows\n    return rows.filter(r => Object.values(r).some(v => (v || '').toString().trim() !== ''));\n  }\n  deleteImportedRow(index) {\n    if (index < 0 || index >= this.importedRows.length) {\n      return;\n    }\n    this.importedRows.splice(index, 1);\n  }\n  addImportedRow() {\n    this.importedRows.unshift({\n      sheetNumber: '',\n      codeRef: '',\n      codeDescription: '',\n      reasoning: '',\n      nonCompliance: '',\n      actionableStep: ''\n    });\n  }\n  saveAllImported() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.importSubmitted = true;\n      if (_this2.hasImportedInvalid) {\n        _this2.customLayoutUtilsService.showError('Please fill all required fields in the imported rows.', '');\n        return;\n      }\n      if (!_this2.permitId) {\n        _this2.customLayoutUtilsService.showError('Permit Id is required to save imported rows', '');\n        return;\n      }\n      if (!_this2.reviewCategory) {\n        _this2.customLayoutUtilsService.showError('Review Category is required to save imported rows', '');\n        return;\n      }\n      if (!_this2.importedRows.length) {\n        _this2.customLayoutUtilsService.showError('No imported rows to save', '');\n        return;\n      }\n      _this2.isLoading = true;\n      // Enable common loader\n      _this2.httpUtilsService.loadingSubject.next(true);\n      try {\n        const requests = _this2.importedRows.map(r => {\n          const payload = {\n            sheetNumber: r.sheetNumber || '',\n            codeRef: r.codeRef || '',\n            codeDescription: r.codeDescription || '',\n            reasoning: r.reasoning || '',\n            nonCompliance: r.nonCompliance || '',\n            actionableStep: r.actionableStep || '',\n            permitId: _this2.permitId,\n            permitNumber: _this2.permitNumber,\n            reviewCategory: _this2.reviewCategory,\n            internalCommentsId: _this2.internalCommentsId,\n            loggedInUserId: _this2.loggedInUserId\n          };\n          return _this2.permitsService.addInternalPlanReviewDetail(payload);\n        });\n        // Execute all in parallel\n        yield new Promise((resolve, reject) => {\n          const {\n            forkJoin\n          } = require('rxjs');\n          forkJoin(requests).subscribe({\n            next: resolve,\n            error: reject\n          });\n        });\n        _this2.customLayoutUtilsService.showSuccess('All imported rows saved successfully!', '');\n        _this2.importedRows = [];\n        _this2.importSubmitted = false;\n        // Optionally close modal or refresh parent via close value\n        _this2.modal.close('bulk-created');\n      } catch (err) {\n        console.error('Error saving imported rows', err);\n        _this2.customLayoutUtilsService.showError('Failed to save one or more imported rows', '');\n      } finally {\n        _this2.isLoading = false;\n        // Disable common loader\n        _this2.httpUtilsService.loadingSubject.next(false);\n      }\n    })();\n  }\n  get hasImportedInvalid() {\n    if (!this.importedRows || !this.importedRows.length) {\n      return false;\n    }\n    return this.importedRows.some(r => !this.isImportedRowValid(r));\n  }\n  isImportedRowValid(r) {\n    return !!((r.sheetNumber || '').toString().trim() && (r.codeRef || '').toString().trim() && (r.codeDescription || '').toString().trim() && (r.reasoning || '').toString().trim());\n  }\n  get isFormValid() {\n    return this.detailForm.valid;\n  }\n  get isDetailsValid() {\n    if (!this.detailForm) {\n      return false;\n    }\n    const controls = this.detailForm.controls;\n    return !!controls.sheetNumber?.valid && !!controls.codeRef?.valid && !!controls.codeDescription?.valid && !!controls.reasoning?.valid;\n  }\n  static ɵfac = function AddEditInternalReviewDetailComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AddEditInternalReviewDetailComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.NgbActiveModal), i0.ɵɵdirectiveInject(i3.PermitsService), i0.ɵɵdirectiveInject(i4.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i5.HttpUtilsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddEditInternalReviewDetailComponent,\n    selectors: [[\"app-add-edit-internal-review-detail\"]],\n    inputs: {\n      permitId: \"permitId\",\n      permitNumber: \"permitNumber\",\n      reviewCategory: \"reviewCategory\",\n      internalCommentsId: \"internalCommentsId\",\n      detailData: \"detailData\",\n      loggedInUserId: \"loggedInUserId\"\n    },\n    decls: 11,\n    vars: 4,\n    consts: [[\"editModeOldPopup\", \"\"], [\"fileInput\", \"\"], [1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [\"class\", \"modal-body\", 4, \"ngIf\", \"ngIfElse\"], [1, \"modal-body\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", \"btn-sm\", 3, \"click\"], [1, \"d-flex\", \"align-items-center\"], [\"download\", \"\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"href\"], [\"type\", \"file\", \"accept\", \".xlsx,.xls\", 1, \"d-none\", 3, \"change\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"me-2\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [\"role\", \"alert\", 1, \"alert\", \"alert-info\", \"mt-4\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"mt-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [1, \"mb-0\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", \"btn-sm\", \"me-2\", 3, \"click\", \"disabled\"], [1, \"fa\", \"fa-plus\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"table-responsive\"], [1, \"table\", \"table-sm\", \"table-bordered\", \"align-middle\"], [1, \"table-light\"], [2, \"width\", \"60px\"], [2, \"width\", \"116px\"], [1, \"text-danger\"], [2, \"width\", \"125px\"], [4, \"ngFor\", \"ngForOf\"], [1, \"text-center\"], [\"type\", \"button\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"btn-light-danger\", 3, \"click\", \"disabled\"], [1, \"fa\", \"fa-trash\"], [1, \"form-control\", \"form-control-sm\", 2, \"max-width\", \"110px\", 3, \"ngModelChange\", \"ngModel\", \"name\", \"ngClass\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"form-control\", \"form-control-sm\", 2, \"max-width\", \"130px\", 3, \"ngModelChange\", \"ngModel\", \"name\", \"ngClass\"], [\"rows\", \"2\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\", \"name\", \"ngClass\"], [\"rows\", \"2\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\", \"name\"], [1, \"invalid-feedback\"], [\"novalidate\", \"\", 3, \"ngSubmit\", \"formGroup\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"row\", \"mt-3\"], [1, \"col-xl-6\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"type\", \"text\", \"formControlName\", \"sheetNumber\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"type\", \"text\", \"formControlName\", \"codeRef\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [1, \"row\", \"mt-4\"], [1, \"col-xl-12\"], [\"formControlName\", \"codeDescription\", \"rows\", \"4\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"reasoning\", \"rows\", \"4\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"nonCompliance\", \"rows\", \"4\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"actionableStep\", \"rows\", \"4\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [1, \"modal-footer\", \"justify-content-between\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"me-2\", \"mr-2\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"disabled\"], [1, \"row\"], [1, \"d-flex\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-3\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [\"data-toggle\", \"tab\", 1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [\"type\", \"button\", \"class\", \"btn btn-secondary btn-sm btn-elevate\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"submit\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", 4, \"ngIf\"], [\"formControlName\", \"codeDescription\", \"rows\", \"3\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"reasoning\", \"rows\", \"3\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"nonCompliance\", \"rows\", \"3\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"formControlName\", \"actionableStep\", \"rows\", \"3\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"btn-elevate\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"]],\n    template: function AddEditInternalReviewDetailComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵtemplate(4, AddEditInternalReviewDetailComponent_div_4_Template, 2, 1, \"div\", 5)(5, AddEditInternalReviewDetailComponent_div_5_Template, 2, 1, \"div\", 5);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 6)(7, \"i\", 7);\n        i0.ɵɵlistener(\"click\", function AddEditInternalReviewDetailComponent_Template_i_click_7_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onCancel());\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(8, AddEditInternalReviewDetailComponent_div_8_Template, 3, 2, \"div\", 8)(9, AddEditInternalReviewDetailComponent_ng_template_9_Template, 22, 13, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        const editModeOldPopup_r13 = i0.ɵɵreference(10);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.isEdit);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isEdit);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isEdit)(\"ngIfElse\", editModeOldPopup_r13);\n      }\n    },\n    dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.FormGroupDirective, i1.FormControlName],\n    styles: [\".modal-dialog {\\n  max-width: 1100px;\\n}\\n\\n  .modal-body {\\n  max-height: calc(100vh - 220px);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9wZXJtaXRzL2FkZC1lZGl0LWludGVybmFsLXJldmlldy1kZXRhaWwvYWRkLWVkaXQtaW50ZXJuYWwtcmV2aWV3LWRldGFpbC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSwrQkFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwIC5tb2RhbC1kaWFsb2cge1xyXG4gIG1heC13aWR0aDogMTEwMHB4O1xyXG59XHJcblxyXG46Om5nLWRlZXAgLm1vZGFsLWJvZHkge1xyXG4gIG1heC1oZWlnaHQ6IGNhbGMoMTAwdmggLSAyMjBweCk7XHJcbn1cclxuXHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "reviewCategory", "ɵɵlistener", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template_button_click_2_listener", "i_r7", "ɵɵrestoreView", "_r6", "index", "ɵɵnextContext", "ɵɵresetView", "deleteImportedRow", "ɵɵelement", "ɵɵtwoWayListener", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template_input_ngModelChange_5_listener", "$event", "r_r8", "$implicit", "ɵɵtwoWayBindingSet", "sheetNumber", "ɵɵtemplate", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_div_6_Template", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template_input_ngModelChange_8_listener", "codeRef", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_div_9_Template", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template_textarea_ngModelChange_11_listener", "codeDescription", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_div_12_Template", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template_textarea_ngModelChange_14_listener", "reasoning", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_div_15_Template", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template_textarea_ngModelChange_17_listener", "nonCompliance", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template_textarea_ngModelChange_19_listener", "actionableStep", "ɵɵproperty", "isLoading", "ɵɵpropertyInterpolate1", "ɵɵtwoWayProperty", "ɵɵpureFunction1", "_c0", "importSubmitted", "trim", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_Template_button_click_5_listener", "_r5", "addImportedRow", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_Template_button_click_8_listener", "saveAllImported", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_tr_37_Template", "importedRows", "length", "isImporting", "hasImportedInvalid", "AddEditInternalReviewDetailComponent_div_8_div_1_Template_button_click_3_listener", "_r3", "onBack", "AddEditInternalReviewDetailComponent_div_8_div_1_Template_input_change_8_listener", "onFileSelected", "AddEditInternalReviewDetailComponent_div_8_div_1_Template_button_click_10_listener", "fileInput_r4", "ɵɵreference", "triggerFileImport", "AddEditInternalReviewDetailComponent_div_8_div_1_Template_button_click_12_listener", "onClickAddReview", "AddEditInternalReviewDetailComponent_div_8_div_1_div_19_Template", "templateUrlPath", "ɵɵsanitizeUrl", "AddEditInternalReviewDetailComponent_div_8_form_2_Template_form_ngSubmit_0_listener", "_r9", "onSubmit", "AddEditInternalReviewDetailComponent_div_8_form_2_Template_button_click_4_listener", "cancelAddForm", "AddEditInternalReviewDetailComponent_div_8_form_2_div_13_Template", "AddEditInternalReviewDetailComponent_div_8_form_2_div_20_Template", "AddEditInternalReviewDetailComponent_div_8_form_2_div_28_Template", "AddEditInternalReviewDetailComponent_div_8_form_2_div_36_Template", "AddEditInternalReviewDetailComponent_div_8_form_2_Template_button_click_50_listener", "onCancel", "detailForm", "ɵɵclassProp", "shouldShowValidationError", "ɵɵtextInterpolate", "isEdit", "AddEditInternalReviewDetailComponent_div_8_div_1_Template", "AddEditInternalReviewDetailComponent_div_8_form_2_Template", "showExcelSection", "showForm", "AddEditInternalReviewDetailComponent_ng_template_9_div_12_div_8_Template", "AddEditInternalReviewDetailComponent_ng_template_9_div_12_div_15_Template", "AddEditInternalReviewDetailComponent_ng_template_9_div_12_div_23_Template", "AddEditInternalReviewDetailComponent_ng_template_9_div_12_div_31_Template", "AddEditInternalReviewDetailComponent_ng_template_9_button_16_Template_button_click_0_listener", "_r11", "goToPrevious", "AddEditInternalReviewDetailComponent_ng_template_9_button_20_Template_button_click_0_listener", "_r12", "setActiveTab", "AddEditInternalReviewDetailComponent_ng_template_9_Template_a_click_6_listener", "_r10", "AddEditInternalReviewDetailComponent_ng_template_9_Template_a_click_9_listener", "AddEditInternalReviewDetailComponent_ng_template_9_Template_form_ngSubmit_11_listener", "AddEditInternalReviewDetailComponent_ng_template_9_div_12_Template", "AddEditInternalReviewDetailComponent_ng_template_9_div_13_Template", "AddEditInternalReviewDetailComponent_ng_template_9_button_16_Template", "AddEditInternalReviewDetailComponent_ng_template_9_Template_button_click_18_listener", "AddEditInternalReviewDetailComponent_ng_template_9_button_20_Template", "AddEditInternalReviewDetailComponent_ng_template_9_button_21_Template", "_c1", "activeTab", "AddEditInternalReviewDetailComponent", "fb", "modal", "permitsService", "customLayoutUtilsService", "httpUtilsService", "permitId", "permitNumber", "internalCommentsId", "detailData", "loggedInUserId", "formSubmitted", "constructor", "ngOnInit", "group", "required", "aeResponse", "commentResponsedBy", "fieldName", "field", "get", "invalid", "valid", "loadingSubject", "next", "formData", "value", "internalReviewCommentsId", "updateInternalPlanReviewDetail", "subscribe", "res", "<PERSON><PERSON><PERSON>", "showError", "faultMessage", "showSuccess", "responseData", "message", "close", "error", "err", "console", "addInternalPlanReviewDetail", "mark<PERSON>llAsTouched", "dismiss", "reset", "tab", "fileInput", "click", "event", "_this", "input", "target", "file", "files", "reader", "FileReader", "onload", "_asyncToGenerator", "XLSX", "data", "Uint8Array", "result", "workbook", "read", "type", "firstSheetName", "SheetNames", "worksheet", "Sheets", "json", "utils", "sheet_to_json", "defval", "mapExcelRows", "readAsA<PERSON>y<PERSON><PERSON>er", "normalizeHeader", "header", "toString", "toLowerCase", "replace", "jsonRows", "headerMap", "firstRow", "Object", "keys", "for<PERSON>ach", "key", "norm", "pick", "row", "keyCandidates", "candidate", "actual", "hasOwnProperty", "rows", "map", "filter", "r", "values", "some", "v", "splice", "unshift", "_this2", "requests", "payload", "Promise", "resolve", "reject", "fork<PERSON><PERSON>n", "require", "isImportedRowValid", "isFormValid", "isDetailsValid", "controls", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "NgbActiveModal", "i3", "PermitsService", "i4", "CustomLayoutUtilsService", "i5", "HttpUtilsService", "selectors", "inputs", "decls", "vars", "consts", "template", "AddEditInternalReviewDetailComponent_Template", "rf", "ctx", "ɵɵelementContainerStart", "AddEditInternalReviewDetailComponent_div_4_Template", "AddEditInternalReviewDetailComponent_div_5_Template", "AddEditInternalReviewDetailComponent_Template_i_click_7_listener", "_r1", "AddEditInternalReviewDetailComponent_div_8_Template", "AddEditInternalReviewDetailComponent_ng_template_9_Template", "ɵɵtemplateRefExtractor", "editModeOldPopup_r13"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\add-edit-internal-review-detail\\add-edit-internal-review-detail.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\add-edit-internal-review-detail\\add-edit-internal-review-detail.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\nimport { PermitsService } from '../../services/permits.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { HttpUtilsService } from '../../services/http-utils.service';\n\n@Component({\n  selector: 'app-add-edit-internal-review-detail',\n  templateUrl: './add-edit-internal-review-detail.component.html',\n  styleUrls: ['./add-edit-internal-review-detail.component.scss']\n})\nexport class AddEditInternalReviewDetailComponent implements OnInit {\n  @Input() permitId: number | null = null;\n  @Input() permitNumber: string = '';\n  @Input() reviewCategory: string = '';\n  @Input() internalCommentsId: number | null = null;\n  @Input() detailData: any = null; // For edit mode\n  @Input() loggedInUserId: string = 'user'; // Should be passed from parent\n\n  detailForm!: FormGroup;\n  isEdit: boolean = false;\n  isLoading: boolean = false;\n  // Tabs removed; single-view form\n  formSubmitted: boolean = false;\n  showForm: boolean = false;\n  showExcelSection: boolean = true;\n  templateUrlPath: string = '/assets/excel/claimstempate.xlsx';\n  importedRows: Array<any> = [];\n  isImporting: boolean = false;\n  importSubmitted: boolean = false;\n  // Tab state for edit mode (old popup)\n  activeTab: 'details' | 'comments' = 'details';\n\n  constructor(\n    private fb: FormBuilder,\n    public modal: NgbActiveModal,\n    private permitsService: PermitsService,\n    private customLayoutUtilsService: CustomLayoutUtilsService,\n    private httpUtilsService: HttpUtilsService\n  ) {}\n\n  ngOnInit(): void {\n    this.isEdit = !!this.detailData;\n    this.detailForm = this.fb.group({\n      sheetNumber: [this.detailData?.sheetNumber || '', Validators.required],\n      codeRef: [this.detailData?.codeRef || '', Validators.required],\n      codeDescription: [this.detailData?.codeDescription || '', Validators.required],\n      reasoning: [this.detailData?.reasoning || '', Validators.required],\n      nonCompliance: [this.detailData?.nonCompliance || ''],\n      actionableStep: [this.detailData?.actionableStep || ''],\n      aeResponse: [this.detailData?.aeResponse || ''],\n      commentResponsedBy: [this.detailData?.commentResponsedBy || ''],\n    });\n  }\n\n  // Tab navigation removed\n\n  shouldShowValidationError(fieldName: string): boolean {\n    // Only show validation errors when form has been submitted\n    if (!this.formSubmitted) {\n      return false;\n    }\n    \n    const field = this.detailForm.get(fieldName);\n    return !!(field && field.invalid);\n  }\n\n  onSubmit(): void {\n    this.formSubmitted = true;\n    if (this.detailForm.valid && this.permitId) {\n      this.isLoading = true;\n      // Enable common loader\n      this.httpUtilsService.loadingSubject.next(true);\n      \n      const formData: any = {\n        ...this.detailForm.value,\n        permitId: this.permitId,\n        permitNumber: this.permitNumber,\n        reviewCategory: this.reviewCategory,\n        internalCommentsId: this.internalCommentsId || this.detailData?.internalCommentsId,\n        loggedInUserId: this.loggedInUserId\n      };\n      if (this.isEdit && this.detailData?.internalReviewCommentsId) {\n        formData.internalReviewCommentsId = this.detailData.internalReviewCommentsId;\n        this.permitsService.updateInternalPlanReviewDetail(formData).subscribe({\n          next: (res: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to update review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess(res.responseData?.message || 'Review detail updated successfully!', '');\n              this.modal.close('updated');\n            }\n          },\n          error: (err: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error updating review detail', '');\n            console.error(err);\n          }\n        });\n      } else {\n        this.permitsService.addInternalPlanReviewDetail(formData).subscribe({\n          next: (res: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess('Review detail created successfully!', '');\n              this.modal.close('created');\n            }\n          },\n          error: (err: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error creating review detail', '');\n            console.error(err);\n          }\n        });\n      }\n    } else {\n      this.detailForm.markAllAsTouched();\n      if (!this.permitId) {\n        this.customLayoutUtilsService.showError('Permit Id is required', '');\n      }\n    }\n  }\n\n  onCancel(): void {\n    this.modal.dismiss('cancelled');\n  }\n\n  onBack(): void {\n    this.modal.dismiss('back');\n  }\n\n  onClickAddReview(): void {\n    this.formSubmitted = false;\n    this.showForm = true;\n    this.showExcelSection = false;\n  }\n\n  cancelAddForm(): void {\n    if (this.detailForm) {\n      this.detailForm.reset({\n        sheetNumber: '',\n        codeRef: '',\n        codeDescription: '',\n        reasoning: '',\n        nonCompliance: '',\n        actionableStep: ''\n      });\n    }\n    this.formSubmitted = false;\n    this.showForm = false;\n    this.showExcelSection = true;\n  }\n\n  setActiveTab(tab: 'details' | 'comments'): void {\n    this.activeTab = tab;\n  }\n\n  goToPrevious(): void {\n    if (this.activeTab === 'comments') {\n      this.activeTab = 'details';\n    }\n  }\n\n  triggerFileImport(fileInput: HTMLInputElement): void {\n    if (this.isLoading) { return; }\n    fileInput.click();\n  }\n\n  onFileSelected(event: Event): void {\n    const input = event.target as HTMLInputElement;\n    const file = input?.files && input.files.length ? input.files[0] : null;\n    if (!file) { return; }\n    this.isImporting = true;\n    // Enable common loader\n    this.httpUtilsService.loadingSubject.next(true);\n    const reader = new FileReader();\n    reader.onload = async () => {\n      try {\n        const XLSX: any = await import('xlsx');\n        const data = new Uint8Array(reader.result as ArrayBuffer);\n        const workbook = XLSX.read(data, { type: 'array' });\n        const firstSheetName = workbook.SheetNames[0];\n        const worksheet = workbook.Sheets[firstSheetName];\n        const json: any[] = XLSX.utils.sheet_to_json(worksheet, { defval: '' });\n        this.importedRows = this.mapExcelRows(json);\n        // Ensure Excel section visible and form hidden\n        this.showExcelSection = true;\n        this.showForm = false;\n        if (!this.importedRows.length) {\n          this.customLayoutUtilsService.showError('No rows found in the uploaded Excel.', '');\n        } else {\n          this.customLayoutUtilsService.showSuccess(`Imported ${this.importedRows.length} row(s).`, '');\n        }\n      } catch (err) {\n        console.error('Error parsing Excel', err);\n        this.importedRows = [];\n        this.customLayoutUtilsService.showError('Failed to parse Excel file. Please check the template.', '');\n      } finally {\n        this.isImporting = false;\n        // Disable common loader\n        this.httpUtilsService.loadingSubject.next(false);\n        // Reset file input so the same file can be selected again if needed\n        input.value = '';\n      }\n    };\n    reader.readAsArrayBuffer(file);\n  }\n\n  private normalizeHeader(header: string): string {\n    return (header || '').toString().trim().toLowerCase().replace(/\\s+/g, '');\n  }\n\n  private mapExcelRows(jsonRows: any[]): Array<any> {\n    if (!jsonRows || !jsonRows.length) { return []; }\n    const headerMap: any = {};\n    // Build header map from first row's keys\n    const firstRow = jsonRows[0];\n    Object.keys(firstRow).forEach((key) => {\n      const norm = this.normalizeHeader(key);\n      headerMap[norm] = key;\n    });\n\n    const pick = (row: any, keyCandidates: string[]): string => {\n      for (const candidate of keyCandidates) {\n        const norm = this.normalizeHeader(candidate);\n        const actual = headerMap[norm];\n        if (actual && row.hasOwnProperty(actual)) {\n          return (row[actual] ?? '').toString();\n        }\n      }\n      return '';\n    };\n\n    // Expected columns with common variants\n    const rows = jsonRows.map((row) => {\n      return {\n        sheetNumber: pick(row, ['Sheet Number', 'SheetNumber', 'Sheet#', 'Sheet No']),\n        codeRef: pick(row, ['Code Ref', 'Code Reference', 'CodeRef']),\n        codeDescription: pick(row, ['Code Description', 'Description']),\n        reasoning: pick(row, ['Reasoning', 'Reason']),\n        nonCompliance: pick(row, ['Non Compliance', 'Non-Compliance', 'NonCompliance']),\n        actionableStep: pick(row, ['Actionable Step', 'ActionableStep', 'Action Step'])\n      };\n    });\n    // Filter out completely empty rows\n    return rows.filter((r) => Object.values(r).some((v) => (v || '').toString().trim() !== ''));\n  }\n\n  deleteImportedRow(index: number): void {\n    if (index < 0 || index >= this.importedRows.length) { return; }\n    this.importedRows.splice(index, 1);\n  }\n\n  addImportedRow(): void {\n    this.importedRows.unshift({\n      sheetNumber: '',\n      codeRef: '',\n      codeDescription: '',\n      reasoning: '',\n      nonCompliance: '',\n      actionableStep: ''\n    });\n  }\n\n  async saveAllImported(): Promise<void> {\n    this.importSubmitted = true;\n    if (this.hasImportedInvalid) {\n      this.customLayoutUtilsService.showError('Please fill all required fields in the imported rows.', '');\n      return;\n    }\n    if (!this.permitId) {\n      this.customLayoutUtilsService.showError('Permit Id is required to save imported rows', '');\n      return;\n    }\n    if (!this.reviewCategory) {\n      this.customLayoutUtilsService.showError('Review Category is required to save imported rows', '');\n      return;\n    }\n    if (!this.importedRows.length) {\n      this.customLayoutUtilsService.showError('No imported rows to save', '');\n      return;\n    }\n    this.isLoading = true;\n    // Enable common loader\n    this.httpUtilsService.loadingSubject.next(true);\n    try {\n      const requests = this.importedRows.map((r) => {\n        const payload: any = {\n          sheetNumber: r.sheetNumber || '',\n          codeRef: r.codeRef || '',\n          codeDescription: r.codeDescription || '',\n          reasoning: r.reasoning || '',\n          nonCompliance: r.nonCompliance || '',\n          actionableStep: r.actionableStep || '',\n          permitId: this.permitId,\n          permitNumber: this.permitNumber,\n          reviewCategory: this.reviewCategory,\n          internalCommentsId: this.internalCommentsId,\n          loggedInUserId: this.loggedInUserId\n        };\n        return this.permitsService.addInternalPlanReviewDetail(payload);\n      });\n      // Execute all in parallel\n      await new Promise((resolve, reject) => {\n        const { forkJoin } = require('rxjs');\n        forkJoin(requests).subscribe({ next: resolve, error: reject });\n      });\n      this.customLayoutUtilsService.showSuccess('All imported rows saved successfully!', '');\n      this.importedRows = [];\n      this.importSubmitted = false;\n      // Optionally close modal or refresh parent via close value\n      this.modal.close('bulk-created');\n    } catch (err) {\n      console.error('Error saving imported rows', err);\n      this.customLayoutUtilsService.showError('Failed to save one or more imported rows', '');\n    } finally {\n      this.isLoading = false;\n      // Disable common loader\n      this.httpUtilsService.loadingSubject.next(false);\n    }\n  }\n\n  get hasImportedInvalid(): boolean {\n    if (!this.importedRows || !this.importedRows.length) { return false; }\n    return this.importedRows.some((r) => !this.isImportedRowValid(r));\n  }\n\n  private isImportedRowValid(r: any): boolean {\n    return !!(\n      (r.sheetNumber || '').toString().trim() &&\n      (r.codeRef || '').toString().trim() &&\n      (r.codeDescription || '').toString().trim() &&\n      (r.reasoning || '').toString().trim()\n    );\n  }\n\n  get isFormValid(): boolean {\n    return this.detailForm.valid;\n  }\n\n  get isDetailsValid(): boolean {\n    if (!this.detailForm) { return false; }\n    const controls = this.detailForm.controls as any;\n    return (\n      !!controls.sheetNumber?.valid &&\n      !!controls.codeRef?.valid &&\n      !!controls.codeDescription?.valid &&\n      !!controls.reasoning?.valid\n    );\n  }\n}\n", "<div class=\"modal-content h-auto\">\n  <!-- Header -->\n  <div class=\"modal-header bg-light-primary\">\n    <div class=\"modal-title h5 fs-3\">\n      <ng-container>\n        <div *ngIf=\"isEdit\">Edit Review Detail - {{ reviewCategory || '' }}</div>\n        <div *ngIf=\"!isEdit\">Add Review Detail - {{ reviewCategory || '' }}</div>\n      </ng-container>\n    </div>\n    <div class=\"float-right\">\n      <i class=\"fa-solid fs-2 fa-xmark text-white\" (click)=\"onCancel()\"></i>\n    </div>\n  </div>\n\n  <!-- Body (Add vs Edit) -->\n  <div class=\"modal-body\" *ngIf=\"!isEdit; else editModeOldPopup\">\n    <!-- Excel section -->\n    <div *ngIf=\"showExcelSection\">\n      <div class=\"d-flex justify-content-between align-items-center\">\n        <div>\n          <button type=\"button\" class=\"btn btn-light btn-sm\" (click)=\"onBack()\">Back</button>\n        </div>\n        <div class=\"d-flex align-items-center\">\n          <a [href]=\"templateUrlPath\" download class=\"btn btn-outline-secondary btn-sm me-2\">Download Template</a>\n          <input #fileInput type=\"file\" class=\"d-none\" accept=\".xlsx,.xls\" (change)=\"onFileSelected($event)\" />\n          <button type=\"button\" class=\"btn btn-outline-primary btn-sm me-2\" (click)=\"triggerFileImport(fileInput)\" [disabled]=\"isLoading\">Import Excel</button>\n          <button type=\"button\" class=\"btn btn-primary btn-sm\" (click)=\"onClickAddReview()\">Add Review Detail</button>\n        </div>\n      </div>\n\n      <div class=\"alert alert-info mt-4\" role=\"alert\">\n        You can upload review details by downloading the template, filling it, and importing the Excel. Or click <strong>Add Review Detail</strong> to enter details manually.\n      </div>\n\n      <!-- Imported rows editable table -->\n      <div *ngIf=\"importedRows?.length\" class=\"mt-4\">\n        <div class=\"d-flex justify-content-between align-items-center mb-2\">\n          <h6 class=\"mb-0\">Imported Review Details ({{ importedRows.length }})</h6>\n          <div>\n            <button type=\"button\" class=\"btn btn-light btn-sm me-2\" (click)=\"addImportedRow()\" [disabled]=\"isLoading || isImporting\">\n              <i class=\"fa fa-plus me-1\"></i> Add Row\n            </button>\n            <button type=\"button\" class=\"btn btn-success btn-sm\" (click)=\"saveAllImported()\" [disabled]=\"isLoading || isImporting || hasImportedInvalid\">Save All</button>\n          </div>\n        </div>\n        <div class=\"table-responsive\">\n          <table class=\"table table-sm table-bordered align-middle\">\n            <thead class=\"table-light\">\n              <tr>\n                <th style=\"width: 60px;\">Action</th>\n                <th style=\"width: 116px;\">Sheet Number <span class=\"text-danger\">*</span></th>\n                <th style=\"width: 125px;\">Code Ref <span class=\"text-danger\">*</span></th>\n                <th>Code Description <span class=\"text-danger\">*</span></th>\n                <th>Reasoning <span class=\"text-danger\">*</span></th>\n                <th>Non Compliance</th>\n                <th>Actionable Step</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let r of importedRows; let i = index\">\n                <td class=\"text-center\">\n                  <button type=\"button\" class=\"btn btn-icon btn-sm btn-light-danger\" (click)=\"deleteImportedRow(i)\" [disabled]=\"isLoading\">\n                    <i class=\"fa fa-trash\"></i>\n                  </button>\n                </td>\n                <td>\n                  <input [(ngModel)]=\"r.sheetNumber\" name=\"sheetNumber{{i}}\" class=\"form-control form-control-sm\" style=\"max-width: 110px;\" [ngClass]=\"{'is-invalid': importSubmitted && !(r.sheetNumber||'').trim()}\" />\n                  <div class=\"invalid-feedback\" *ngIf=\"importSubmitted && !(r.sheetNumber||'').trim()\">Required Field</div>\n                </td>\n                <td>\n                  <input [(ngModel)]=\"r.codeRef\" name=\"codeRef{{i}}\" class=\"form-control form-control-sm\" style=\"max-width: 130px;\" [ngClass]=\"{'is-invalid': importSubmitted && !(r.codeRef||'').trim()}\" />\n                  <div class=\"invalid-feedback\" *ngIf=\"importSubmitted && !(r.codeRef||'').trim()\">Required Field</div>\n                </td>\n                <td>\n                  <textarea [(ngModel)]=\"r.codeDescription\" name=\"codeDescription{{i}}\" rows=\"2\" class=\"form-control form-control-sm\" [ngClass]=\"{'is-invalid': importSubmitted && !(r.codeDescription||'').trim()}\"></textarea>\n                  <div class=\"invalid-feedback\" *ngIf=\"importSubmitted && !(r.codeDescription||'').trim()\">Required Field</div>\n                </td>\n                <td>\n                  <textarea [(ngModel)]=\"r.reasoning\" name=\"reasoning{{i}}\" rows=\"2\" class=\"form-control form-control-sm\" [ngClass]=\"{'is-invalid': importSubmitted && !(r.reasoning||'').trim()}\"></textarea>\n                  <div class=\"invalid-feedback\" *ngIf=\"importSubmitted && !(r.reasoning||'').trim()\">Required Field</div>\n                </td>\n                <td><textarea [(ngModel)]=\"r.nonCompliance\" name=\"nonCompliance{{i}}\" rows=\"2\" class=\"form-control form-control-sm\"></textarea></td>\n                <td><textarea [(ngModel)]=\"r.actionableStep\" name=\"actionableStep{{i}}\" rows=\"2\" class=\"form-control form-control-sm\"></textarea></td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n\n    <!-- Imported rows editable table (single source of truth) -->\n\n    <!-- Form: single view (Review Details + Actionable Step) -->\n    <form *ngIf=\"showForm\" [formGroup]=\"detailForm\" (ngSubmit)=\"onSubmit()\" novalidate>\n      <div class=\"d-flex justify-content-between align-items-center mb-3\">\n        <div></div>\n        <div>\n          <button type=\"button\" class=\"btn btn-light btn-sm\" (click)=\"cancelAddForm()\">Cancel</button>\n        </div>\n      </div>\n      <div class=\"row mt-3\">\n        <div class=\"col-xl-6\">\n          <label class=\"fw-bold form-label mb-2\">Sheet Number <span class=\"text-danger\">*</span></label>\n          <input type=\"text\" formControlName=\"sheetNumber\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('sheetNumber')\" placeholder=\"Type here\" [disabled]=\"isLoading\" />\n          <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('sheetNumber')\">Required Field</div>\n        </div>\n        <div class=\"col-xl-6\">\n          <label class=\"fw-bold form-label mb-2\">Code Ref <span class=\"text-danger\">*</span></label>\n          <input type=\"text\" formControlName=\"codeRef\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('codeRef')\" placeholder=\"Type here\" [disabled]=\"isLoading\" />\n          <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('codeRef')\">Required Field</div>\n        </div>\n      </div>\n      <div class=\"row mt-4\">\n        <div class=\"col-xl-12\">\n          <label class=\"fw-bold form-label mb-2\">Code Description <span class=\"text-danger\">*</span></label>\n          <textarea formControlName=\"codeDescription\" rows=\"4\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('codeDescription')\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n          <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('codeDescription')\">Required Field</div>\n        </div>\n      </div>\n      <div class=\"row mt-4\">\n        <div class=\"col-xl-12\">\n          <label class=\"fw-bold form-label mb-2\">Reasoning <span class=\"text-danger\">*</span></label>\n          <textarea formControlName=\"reasoning\" rows=\"4\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('reasoning')\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n          <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('reasoning')\">Required Field</div>\n        </div>\n      </div>\n      <div class=\"row mt-4\">\n        <div class=\"col-xl-12\">\n          <label class=\"fw-bold form-label mb-2\">Non Compliance</label>\n          <textarea formControlName=\"nonCompliance\" rows=\"4\" class=\"form-control form-control-sm\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n        </div>\n      </div>\n      <div class=\"row mt-4\">\n        <div class=\"col-xl-12\">\n          <label class=\"fw-bold form-label mb-2\">Actionable Step</label>\n          <textarea formControlName=\"actionableStep\" rows=\"4\" class=\"form-control form-control-sm\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n        </div>\n      </div>\n\n      <div class=\"modal-footer justify-content-between\">\n        <div></div>\n        <div>\n          <button type=\"button\" class=\"btn btn-danger btn-sm btn-elevate me-2 mr-2\" (click)=\"onCancel()\" [disabled]=\"isLoading\">Cancel</button>\n          <button type=\"submit\" class=\"btn btn-primary btn-sm\" [disabled]=\"isLoading\">{{ isEdit ? 'Update' : 'Create' }}</button>\n        </div>\n      </div>\n    </form>\n  </div>\n  <!-- EDIT MODE: old popup with tabs -->\n  <ng-template #editModeOldPopup>\n  <div class=\"modal-body\">\n    <!-- Tabs -->\n    <div class=\"row\">\n      <div class=\"col-xl-12\">\n        <div class=\"d-flex\">\n          <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap\">\n            <li class=\"nav-item\">\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\" [ngClass]=\"{ active: activeTab === 'details' }\" (click)=\"setActiveTab('details')\">\n                Review Details\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\" [ngClass]=\"{ active: activeTab === 'comments' }\" (click)=\"setActiveTab('comments')\">\n                Review Comments\n              </a>\n            </li>\n          </ul>\n        </div>\n      </div>\n    </div>\n\n    <!-- Form -->\n    <form [formGroup]=\"detailForm\" (ngSubmit)=\"onSubmit()\" novalidate>\n      <!-- DETAILS TAB CONTENT -->\n      <div *ngIf=\"activeTab === 'details'\">\n        <div class=\"row mt-3\">\n          <div class=\"col-xl-6\">\n            <label class=\"fw-bold form-label mb-2\">Sheet Number <span class=\"text-danger\">*</span></label>\n            <input type=\"text\" formControlName=\"sheetNumber\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('sheetNumber')\" placeholder=\"Type here\" [disabled]=\"isLoading\" />\n            <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('sheetNumber')\">Required Field</div>\n          </div>\n          <div class=\"col-xl-6\">\n            <label class=\"fw-bold form-label mb-2\">Code Ref <span class=\"text-danger\">*</span></label>\n            <input type=\"text\" formControlName=\"codeRef\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('codeRef')\" placeholder=\"Type here\" [disabled]=\"isLoading\" />\n            <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('codeRef')\">Required Field</div>\n          </div>\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Code Description <span class=\"text-danger\">*</span></label>\n            <textarea formControlName=\"codeDescription\" rows=\"3\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('codeDescription')\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n            <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('codeDescription')\">Required Field</div>\n          </div>\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Reasoning <span class=\"text-danger\">*</span></label>\n            <textarea formControlName=\"reasoning\" rows=\"3\" class=\"form-control form-control-sm\" [class.is-invalid]=\"shouldShowValidationError('reasoning')\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n            <div class=\"invalid-feedback\" *ngIf=\"shouldShowValidationError('reasoning')\">Required Field</div>\n          </div>\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Non Compliance</label>\n            <textarea formControlName=\"nonCompliance\" rows=\"3\" class=\"form-control form-control-sm\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n          </div>\n        </div>\n      </div>\n      <!-- COMMENTS TAB CONTENT -->\n      <div *ngIf=\"activeTab === 'comments'\">\n        <div class=\"row mt-3\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Actionable Step</label>\n            <textarea formControlName=\"actionableStep\" rows=\"3\" class=\"form-control form-control-sm\" placeholder=\"Type here\" [disabled]=\"isLoading\"></textarea>\n          </div>\n        </div>\n      </div>\n        <div class=\"modal-footer justify-content-between\">\n          <div>\n            <button *ngIf=\"activeTab === 'comments'\" type=\"button\" class=\"btn btn-secondary btn-sm btn-elevate\" (click)=\"goToPrevious()\" [disabled]=\"isLoading\">Previous</button>\n          </div>\n          <div>\n            <button type=\"button\" class=\"btn btn-danger btn-sm btn-elevate me-2 mr-2\" (click)=\"onCancel()\" [disabled]=\"isLoading\">Cancel</button>\n            <button *ngIf=\"activeTab === 'details'\" type=\"button\" class=\"btn btn-primary btn-sm\" [disabled]=\"isLoading\" (click)=\"setActiveTab('comments')\">Next</button>\n            <button *ngIf=\"activeTab === 'comments'\" type=\"submit\" class=\"btn btn-primary btn-sm\" [disabled]=\"isLoading\">{{ isEdit ? 'Update' : 'Create' }}</button>\n          </div>\n        </div>\n    </form>\n  </div>\n  </ng-template>\n</div>\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;ICI3DC,EAAA,CAAAC,cAAA,UAAoB;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAArDH,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAK,kBAAA,0BAAAC,MAAA,CAAAC,cAAA,WAA+C;;;;;IACnEP,EAAA,CAAAC,cAAA,UAAqB;IAAAD,EAAA,CAAAE,MAAA,GAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAApDH,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAK,kBAAA,yBAAAC,MAAA,CAAAC,cAAA,WAA8C;;;;;IA6DzDP,EAAA,CAAAC,cAAA,cAAqF;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAIzGH,EAAA,CAAAC,cAAA,cAAiF;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAIrGH,EAAA,CAAAC,cAAA,cAAyF;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAI7GH,EAAA,CAAAC,cAAA,cAAmF;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAlBvGH,EAFJ,CAAAC,cAAA,SAAkD,aACxB,iBACmG;IAAtDD,EAAA,CAAAQ,UAAA,mBAAAC,+FAAA;MAAA,MAAAC,IAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAAU,iBAAA,CAAAN,IAAA,CAAoB;IAAA,EAAC;IAC/FV,EAAA,CAAAiB,SAAA,YAA2B;IAE/BjB,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,SAAI,gBACqM;IAAhMD,EAAA,CAAAkB,gBAAA,2BAAAC,sGAAAC,MAAA;MAAA,MAAAC,IAAA,GAAArB,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAU,SAAA;MAAAtB,EAAA,CAAAuB,kBAAA,CAAAF,IAAA,CAAAG,WAAA,EAAAJ,MAAA,MAAAC,IAAA,CAAAG,WAAA,GAAAJ,MAAA;MAAA,OAAApB,EAAA,CAAAe,WAAA,CAAAK,MAAA;IAAA,EAA2B;IAAlCpB,EAAA,CAAAG,YAAA,EAAuM;IACvMH,EAAA,CAAAyB,UAAA,IAAAC,4EAAA,kBAAqF;IACvF1B,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,SAAI,gBACyL;IAApLD,EAAA,CAAAkB,gBAAA,2BAAAS,sGAAAP,MAAA;MAAA,MAAAC,IAAA,GAAArB,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAU,SAAA;MAAAtB,EAAA,CAAAuB,kBAAA,CAAAF,IAAA,CAAAO,OAAA,EAAAR,MAAA,MAAAC,IAAA,CAAAO,OAAA,GAAAR,MAAA;MAAA,OAAApB,EAAA,CAAAe,WAAA,CAAAK,MAAA;IAAA,EAAuB;IAA9BpB,EAAA,CAAAG,YAAA,EAA2L;IAC3LH,EAAA,CAAAyB,UAAA,IAAAI,4EAAA,kBAAiF;IACnF7B,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,UAAI,oBACiM;IAAzLD,EAAA,CAAAkB,gBAAA,2BAAAY,0GAAAV,MAAA;MAAA,MAAAC,IAAA,GAAArB,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAU,SAAA;MAAAtB,EAAA,CAAAuB,kBAAA,CAAAF,IAAA,CAAAU,eAAA,EAAAX,MAAA,MAAAC,IAAA,CAAAU,eAAA,GAAAX,MAAA;MAAA,OAAApB,EAAA,CAAAe,WAAA,CAAAK,MAAA;IAAA,EAA+B;IAA0JpB,EAAA,CAAAG,YAAA,EAAW;IAC9MH,EAAA,CAAAyB,UAAA,KAAAO,6EAAA,kBAAyF;IAC3FhC,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,UAAI,oBAC+K;IAAvKD,EAAA,CAAAkB,gBAAA,2BAAAe,0GAAAb,MAAA;MAAA,MAAAC,IAAA,GAAArB,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAU,SAAA;MAAAtB,EAAA,CAAAuB,kBAAA,CAAAF,IAAA,CAAAa,SAAA,EAAAd,MAAA,MAAAC,IAAA,CAAAa,SAAA,GAAAd,MAAA;MAAA,OAAApB,EAAA,CAAAe,WAAA,CAAAK,MAAA;IAAA,EAAyB;IAA8IpB,EAAA,CAAAG,YAAA,EAAW;IAC5LH,EAAA,CAAAyB,UAAA,KAAAU,6EAAA,kBAAmF;IACrFnC,EAAA,CAAAG,YAAA,EAAK;IACDH,EAAJ,CAAAC,cAAA,UAAI,oBAAgH;IAAtGD,EAAA,CAAAkB,gBAAA,2BAAAkB,0GAAAhB,MAAA;MAAA,MAAAC,IAAA,GAAArB,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAU,SAAA;MAAAtB,EAAA,CAAAuB,kBAAA,CAAAF,IAAA,CAAAgB,aAAA,EAAAjB,MAAA,MAAAC,IAAA,CAAAgB,aAAA,GAAAjB,MAAA;MAAA,OAAApB,EAAA,CAAAe,WAAA,CAAAK,MAAA;IAAA,EAA6B;IAAoFpB,EAAX,CAAAG,YAAA,EAAW,EAAK;IAChIH,EAAJ,CAAAC,cAAA,UAAI,oBAAkH;IAAxGD,EAAA,CAAAkB,gBAAA,2BAAAoB,0GAAAlB,MAAA;MAAA,MAAAC,IAAA,GAAArB,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAU,SAAA;MAAAtB,EAAA,CAAAuB,kBAAA,CAAAF,IAAA,CAAAkB,cAAA,EAAAnB,MAAA,MAAAC,IAAA,CAAAkB,cAAA,GAAAnB,MAAA;MAAA,OAAApB,EAAA,CAAAe,WAAA,CAAAK,MAAA;IAAA,EAA8B;IAC9CpB,EADwH,CAAAG,YAAA,EAAW,EAAK,EACnI;;;;;;IAtBiGH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IAKrFzC,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAA0C,sBAAA,wBAAAhC,IAAA,KAAuB;IAAnDV,EAAA,CAAA2C,gBAAA,YAAAtB,IAAA,CAAAG,WAAA,CAA2B;IAAwFxB,EAAA,CAAAwC,UAAA,YAAAxC,EAAA,CAAA4C,eAAA,KAAAC,GAAA,EAAAvC,MAAA,CAAAwC,eAAA,MAAAzB,IAAA,CAAAG,WAAA,QAAAuB,IAAA,IAA0E;IACrK/C,EAAA,CAAAI,SAAA,EAAoD;IAApDJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAAwC,eAAA,MAAAzB,IAAA,CAAAG,WAAA,QAAAuB,IAAA,GAAoD;IAGpD/C,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAA0C,sBAAA,oBAAAhC,IAAA,KAAmB;IAA3CV,EAAA,CAAA2C,gBAAA,YAAAtB,IAAA,CAAAO,OAAA,CAAuB;IAAoF5B,EAAA,CAAAwC,UAAA,YAAAxC,EAAA,CAAA4C,eAAA,KAAAC,GAAA,EAAAvC,MAAA,CAAAwC,eAAA,MAAAzB,IAAA,CAAAO,OAAA,QAAAmB,IAAA,IAAsE;IACzJ/C,EAAA,CAAAI,SAAA,EAAgD;IAAhDJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAAwC,eAAA,MAAAzB,IAAA,CAAAO,OAAA,QAAAmB,IAAA,GAAgD;IAGrC/C,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAA0C,sBAAA,4BAAAhC,IAAA,KAA2B;IAA3DV,EAAA,CAAA2C,gBAAA,YAAAtB,IAAA,CAAAU,eAAA,CAA+B;IAA2E/B,EAAA,CAAAwC,UAAA,YAAAxC,EAAA,CAAA4C,eAAA,KAAAC,GAAA,EAAAvC,MAAA,CAAAwC,eAAA,MAAAzB,IAAA,CAAAU,eAAA,QAAAgB,IAAA,IAA8E;IACnK/C,EAAA,CAAAI,SAAA,EAAwD;IAAxDJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAAwC,eAAA,MAAAzB,IAAA,CAAAU,eAAA,QAAAgB,IAAA,GAAwD;IAGnD/C,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAA0C,sBAAA,sBAAAhC,IAAA,KAAqB;IAA/CV,EAAA,CAAA2C,gBAAA,YAAAtB,IAAA,CAAAa,SAAA,CAAyB;IAAqElC,EAAA,CAAAwC,UAAA,YAAAxC,EAAA,CAAA4C,eAAA,KAAAC,GAAA,EAAAvC,MAAA,CAAAwC,eAAA,MAAAzB,IAAA,CAAAa,SAAA,QAAAa,IAAA,IAAwE;IACjJ/C,EAAA,CAAAI,SAAA,EAAkD;IAAlDJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAAwC,eAAA,MAAAzB,IAAA,CAAAa,SAAA,QAAAa,IAAA,GAAkD;IAEvC/C,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAA0C,sBAAA,0BAAAhC,IAAA,KAAyB;IAAvDV,EAAA,CAAA2C,gBAAA,YAAAtB,IAAA,CAAAgB,aAAA,CAA6B;IACErC,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAA0C,sBAAA,2BAAAhC,IAAA,KAA0B;IAAzDV,EAAA,CAAA2C,gBAAA,YAAAtB,IAAA,CAAAkB,cAAA,CAA8B;;;;;;IA7ClDvC,EAFJ,CAAAC,cAAA,cAA+C,cACuB,aACjD;IAAAD,EAAA,CAAAE,MAAA,GAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEvEH,EADF,CAAAC,cAAA,UAAK,iBACsH;IAAjED,EAAA,CAAAQ,UAAA,mBAAAwC,yFAAA;MAAAhD,EAAA,CAAAW,aAAA,CAAAsC,GAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAA4C,cAAA,EAAgB;IAAA,EAAC;IAChFlD,EAAA,CAAAiB,SAAA,YAA+B;IAACjB,EAAA,CAAAE,MAAA,gBAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA6I;IAAxFD,EAAA,CAAAQ,UAAA,mBAAA2C,yFAAA;MAAAnD,EAAA,CAAAW,aAAA,CAAAsC,GAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAA8C,eAAA,EAAiB;IAAA,EAAC;IAA6DpD,EAAA,CAAAE,MAAA,eAAQ;IAEzJF,EAFyJ,CAAAG,YAAA,EAAS,EAC1J,EACF;IAKEH,EAJR,CAAAC,cAAA,eAA8B,iBAC8B,iBAC7B,UACrB,cACuB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAK;IAC9EH,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAK;IAC1EH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAK;IAC5DH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAK;IACrDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAEvBF,EAFuB,CAAAG,YAAA,EAAK,EACrB,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAyB,UAAA,KAAA4B,sEAAA,mBAAkD;IA4B1DrD,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACF;;;;IAlDeH,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,kBAAA,8BAAAC,MAAA,CAAAgD,YAAA,CAAAC,MAAA,MAAmD;IAEiBvD,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,IAAAnC,MAAA,CAAAkD,WAAA,CAAqC;IAGvCxD,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,IAAAnC,MAAA,CAAAkD,WAAA,IAAAlD,MAAA,CAAAmD,kBAAA,CAA2D;IAiBxHzD,EAAA,CAAAI,SAAA,IAAiB;IAAjBJ,EAAA,CAAAwC,UAAA,YAAAlC,MAAA,CAAAgD,YAAA,CAAiB;;;;;;IAvCvCtD,EAHN,CAAAC,cAAA,UAA8B,cACmC,UACxD,iBACmE;IAAnBD,EAAA,CAAAQ,UAAA,mBAAAkD,kFAAA;MAAA1D,EAAA,CAAAW,aAAA,CAAAgD,GAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAAsD,MAAA,EAAQ;IAAA,EAAC;IAAC5D,EAAA,CAAAE,MAAA,WAAI;IAC5EF,EAD4E,CAAAG,YAAA,EAAS,EAC/E;IAEJH,EADF,CAAAC,cAAA,cAAuC,YAC8C;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxGH,EAAA,CAAAC,cAAA,mBAAqG;IAApCD,EAAA,CAAAQ,UAAA,oBAAAqD,kFAAAzC,MAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAgD,GAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAUT,MAAA,CAAAwD,cAAA,CAAA1C,MAAA,CAAsB;IAAA,EAAC;IAAlGpB,EAAA,CAAAG,YAAA,EAAqG;IACrGH,EAAA,CAAAC,cAAA,kBAAgI;IAA9DD,EAAA,CAAAQ,UAAA,mBAAAuD,mFAAA;MAAA/D,EAAA,CAAAW,aAAA,CAAAgD,GAAA;MAAA,MAAAK,YAAA,GAAAhE,EAAA,CAAAiE,WAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAA4D,iBAAA,CAAAF,YAAA,CAA4B;IAAA,EAAC;IAAwBhE,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrJH,EAAA,CAAAC,cAAA,kBAAkF;IAA7BD,EAAA,CAAAQ,UAAA,mBAAA2D,mFAAA;MAAAnE,EAAA,CAAAW,aAAA,CAAAgD,GAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAA8D,gBAAA,EAAkB;IAAA,EAAC;IAACpE,EAAA,CAAAE,MAAA,yBAAiB;IAEvGF,EAFuG,CAAAG,YAAA,EAAS,EACxG,EACF;IAENH,EAAA,CAAAC,cAAA,eAAgD;IAC9CD,EAAA,CAAAE,MAAA,kHAAyG;IAAAF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,oCAC9I;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAyB,UAAA,KAAA4C,gEAAA,mBAA+C;IAqDjDrE,EAAA,CAAAG,YAAA,EAAM;;;;IAjEGH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAAgE,eAAA,EAAAtE,EAAA,CAAAuE,aAAA,CAAwB;IAE8EvE,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IAU7HzC,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAAgD,YAAA,kBAAAhD,MAAA,CAAAgD,YAAA,CAAAC,MAAA,CAA0B;;;;;IAqE5BvD,EAAA,CAAAC,cAAA,cAA+E;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKnGH,EAAA,CAAAC,cAAA,cAA2E;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAO/FH,EAAA,CAAAC,cAAA,cAAmF;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAOvGH,EAAA,CAAAC,cAAA,cAA6E;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IA9BvGH,EAAA,CAAAC,cAAA,eAAmF;IAAnCD,EAAA,CAAAQ,UAAA,sBAAAgE,oFAAA;MAAAxE,EAAA,CAAAW,aAAA,CAAA8D,GAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAYT,MAAA,CAAAoE,QAAA,EAAU;IAAA,EAAC;IACrE1E,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAiB,SAAA,UAAW;IAETjB,EADF,CAAAC,cAAA,UAAK,iBAC0E;IAA1BD,EAAA,CAAAQ,UAAA,mBAAAmE,mFAAA;MAAA3E,EAAA,CAAAW,aAAA,CAAA8D,GAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAAsE,aAAA,EAAe;IAAA,EAAC;IAAC5E,EAAA,CAAAE,MAAA,aAAM;IAEvFF,EAFuF,CAAAG,YAAA,EAAS,EACxF,EACF;IAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACE,gBACmB;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC9FH,EAAA,CAAAiB,SAAA,iBAAqM;IACrMjB,EAAA,CAAAyB,UAAA,KAAAoD,iEAAA,kBAA+E;IACjF7E,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC1FH,EAAA,CAAAiB,SAAA,iBAA6L;IAC7LjB,EAAA,CAAAyB,UAAA,KAAAqD,iEAAA,kBAA2E;IAE/E9E,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAClGH,EAAA,CAAAiB,SAAA,oBAAsN;IACtNjB,EAAA,CAAAyB,UAAA,KAAAsD,iEAAA,kBAAmF;IAEvF/E,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC3FH,EAAA,CAAAiB,SAAA,oBAA0M;IAC1MjB,EAAA,CAAAyB,UAAA,KAAAuD,iEAAA,kBAA6E;IAEjFhF,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7DH,EAAA,CAAAiB,SAAA,oBAAkJ;IAEtJjB,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAiB,SAAA,oBAAmJ;IAEvJjB,EADE,CAAAG,YAAA,EAAM,EACF;IAENH,EAAA,CAAAC,cAAA,eAAkD;IAChDD,EAAA,CAAAiB,SAAA,WAAW;IAETjB,EADF,CAAAC,cAAA,WAAK,kBACmH;IAA5CD,EAAA,CAAAQ,UAAA,mBAAAyE,oFAAA;MAAAjF,EAAA,CAAAW,aAAA,CAAA8D,GAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAA4E,QAAA,EAAU;IAAA,EAAC;IAAwBlF,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrIH,EAAA,CAAAC,cAAA,kBAA4E;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAGpHF,EAHoH,CAAAG,YAAA,EAAS,EACnH,EACF,EACD;;;;IArDgBH,EAAA,CAAAwC,UAAA,cAAAlC,MAAA,CAAA6E,UAAA,CAAwB;IAU6CnF,EAAA,CAAAI,SAAA,IAA6D;IAA7DJ,EAAA,CAAAoF,WAAA,eAAA9E,MAAA,CAAA+E,yBAAA,gBAA6D;IAAyBrF,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IACnKzC,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAA+E,yBAAA,gBAA8C;IAIKrF,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAoF,WAAA,eAAA9E,MAAA,CAAA+E,yBAAA,YAAyD;IAAyBrF,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IAC3JzC,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAA+E,yBAAA,YAA0C;IAMiBrF,EAAA,CAAAI,SAAA,GAAiE;IAAjEJ,EAAA,CAAAoF,WAAA,eAAA9E,MAAA,CAAA+E,yBAAA,oBAAiE;IAAyBrF,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IAC3KzC,EAAA,CAAAI,SAAA,EAAkD;IAAlDJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAA+E,yBAAA,oBAAkD;IAMGrF,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAoF,WAAA,eAAA9E,MAAA,CAAA+E,yBAAA,cAA2D;IAAyBrF,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IAC/JzC,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAA+E,yBAAA,cAA4C;IAMqCrF,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IAMrBzC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IAOxCzC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IAChEzC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IAACzC,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAsF,iBAAA,CAAAhF,MAAA,CAAAiF,MAAA,uBAAkC;;;;;IAhItHvF,EAAA,CAAAC,cAAA,aAA+D;IA8E7DD,EA5EA,CAAAyB,UAAA,IAAA+D,yDAAA,kBAA8B,IAAAC,0DAAA,qBA4EqD;IAsDrFzF,EAAA,CAAAG,YAAA,EAAM;;;;IAlIEH,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAAoF,gBAAA,CAAsB;IA4ErB1F,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAAqF,QAAA,CAAc;;;;;IAsFb3F,EAAA,CAAAC,cAAA,cAA+E;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKnGH,EAAA,CAAAC,cAAA,cAA2E;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAO/FH,EAAA,CAAAC,cAAA,cAAmF;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAOvGH,EAAA,CAAAC,cAAA,cAA6E;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IArBjGH,EAHN,CAAAC,cAAA,UAAqC,cACb,cACE,gBACmB;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC9FH,EAAA,CAAAiB,SAAA,gBAAqM;IACrMjB,EAAA,CAAAyB,UAAA,IAAAmE,wEAAA,kBAA+E;IACjF5F,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC1FH,EAAA,CAAAiB,SAAA,iBAA6L;IAC7LjB,EAAA,CAAAyB,UAAA,KAAAoE,yEAAA,kBAA2E;IAE/E7F,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAClGH,EAAA,CAAAiB,SAAA,oBAAsN;IACtNjB,EAAA,CAAAyB,UAAA,KAAAqE,yEAAA,kBAAmF;IAEvF9F,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC3FH,EAAA,CAAAiB,SAAA,oBAA0M;IAC1MjB,EAAA,CAAAyB,UAAA,KAAAsE,yEAAA,kBAA6E;IAEjF/F,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7DH,EAAA,CAAAiB,SAAA,oBAAkJ;IAGxJjB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IA7BsFH,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAoF,WAAA,eAAA9E,MAAA,CAAA+E,yBAAA,gBAA6D;IAAyBrF,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IACnKzC,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAA+E,yBAAA,gBAA8C;IAIKrF,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAoF,WAAA,eAAA9E,MAAA,CAAA+E,yBAAA,YAAyD;IAAyBrF,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IAC3JzC,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAA+E,yBAAA,YAA0C;IAMiBrF,EAAA,CAAAI,SAAA,GAAiE;IAAjEJ,EAAA,CAAAoF,WAAA,eAAA9E,MAAA,CAAA+E,yBAAA,oBAAiE;IAAyBrF,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IAC3KzC,EAAA,CAAAI,SAAA,EAAkD;IAAlDJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAA+E,yBAAA,oBAAkD;IAMGrF,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAoF,WAAA,eAAA9E,MAAA,CAAA+E,yBAAA,cAA2D;IAAyBrF,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IAC/JzC,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAA+E,yBAAA,cAA4C;IAMqCrF,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;;;;;IAQtIzC,EAHN,CAAAC,cAAA,UAAsC,cACd,cACG,gBACkB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAiB,SAAA,mBAAmJ;IAGzJjB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAHiHH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;;;;;;IAMvIzC,EAAA,CAAAC,cAAA,iBAAoJ;IAAhDD,EAAA,CAAAQ,UAAA,mBAAAwF,8FAAA;MAAAhG,EAAA,CAAAW,aAAA,CAAAsF,IAAA;MAAA,MAAA3F,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAA4F,YAAA,EAAc;IAAA,EAAC;IAAwBlG,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAxCH,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;;;;;;IAInJzC,EAAA,CAAAC,cAAA,iBAA+I;IAAnCD,EAAA,CAAAQ,UAAA,mBAAA2F,8FAAA;MAAAnG,EAAA,CAAAW,aAAA,CAAAyF,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAA+F,YAAA,CAAa,UAAU,CAAC;IAAA,EAAC;IAACrG,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAvEH,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;;;;;IAC3GzC,EAAA,CAAAC,cAAA,iBAA6G;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAlEH,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IAACzC,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAsF,iBAAA,CAAAhF,MAAA,CAAAiF,MAAA,uBAAkC;;;;;;IAnE7IvF,EAPZ,CAAAC,cAAA,aAAwB,cAEL,cACQ,cACD,aACqF,aAChF,YAC6I;IAAlCD,EAAA,CAAAQ,UAAA,mBAAA8F,+EAAA;MAAAtG,EAAA,CAAAW,aAAA,CAAA4F,IAAA;MAAA,MAAAjG,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAA+F,YAAA,CAAa,SAAS,CAAC;IAAA,EAAC;IAC7JrG,EAAA,CAAAE,MAAA,uBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;IAEHH,EADF,CAAAC,cAAA,aAAqB,YAC+I;IAAnCD,EAAA,CAAAQ,UAAA,mBAAAgG,+EAAA;MAAAxG,EAAA,CAAAW,aAAA,CAAA4F,IAAA;MAAA,MAAAjG,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAA+F,YAAA,CAAa,UAAU,CAAC;IAAA,EAAC;IAC/JrG,EAAA,CAAAE,MAAA,yBACF;IAKVF,EALU,CAAAG,YAAA,EAAI,EACD,EACF,EACD,EACF,EACF;IAGNH,EAAA,CAAAC,cAAA,gBAAkE;IAAnCD,EAAA,CAAAQ,UAAA,sBAAAiG,sFAAA;MAAAzG,EAAA,CAAAW,aAAA,CAAA4F,IAAA;MAAA,MAAAjG,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAYT,MAAA,CAAAoE,QAAA,EAAU;IAAA,EAAC;IAqCpD1E,EAnCA,CAAAyB,UAAA,KAAAiF,kEAAA,mBAAqC,KAAAC,kEAAA,iBAmCC;IASlC3G,EADF,CAAAC,cAAA,eAAkD,WAC3C;IACHD,EAAA,CAAAyB,UAAA,KAAAmF,qEAAA,qBAAoJ;IACtJ5G,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,WAAK,kBACmH;IAA5CD,EAAA,CAAAQ,UAAA,mBAAAqG,qFAAA;MAAA7G,EAAA,CAAAW,aAAA,CAAA4F,IAAA;MAAA,MAAAjG,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAA4E,QAAA,EAAU;IAAA,EAAC;IAAwBlF,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAErIH,EADA,CAAAyB,UAAA,KAAAqF,qEAAA,qBAA+I,KAAAC,qEAAA,qBAClC;IAIvH/G,EAHQ,CAAAG,YAAA,EAAM,EACF,EACH,EACH;;;;IAvEoFH,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAwC,UAAA,YAAAxC,EAAA,CAAA4C,eAAA,IAAAoE,GAAA,EAAA1G,MAAA,CAAA2G,SAAA,gBAA+C;IAK/CjH,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAwC,UAAA,YAAAxC,EAAA,CAAA4C,eAAA,KAAAoE,GAAA,EAAA1G,MAAA,CAAA2G,SAAA,iBAAgD;IAUlIjH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAwC,UAAA,cAAAlC,MAAA,CAAA6E,UAAA,CAAwB;IAEtBnF,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAA2G,SAAA,eAA6B;IAmC7BjH,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAA2G,SAAA,gBAA8B;IAUrBjH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAA2G,SAAA,gBAA8B;IAGwDjH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAwC,UAAA,aAAAlC,MAAA,CAAAmC,SAAA,CAAsB;IAC5GzC,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAA2G,SAAA,eAA6B;IAC7BjH,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAwC,UAAA,SAAAlC,MAAA,CAAA2G,SAAA,gBAA8B;;;ADpNnD,OAAM,MAAOC,oCAAoC;EAuBrCC,EAAA;EACDC,KAAA;EACCC,cAAA;EACAC,wBAAA;EACAC,gBAAA;EA1BDC,QAAQ,GAAkB,IAAI;EAC9BC,YAAY,GAAW,EAAE;EACzBlH,cAAc,GAAW,EAAE;EAC3BmH,kBAAkB,GAAkB,IAAI;EACxCC,UAAU,GAAQ,IAAI,CAAC,CAAC;EACxBC,cAAc,GAAW,MAAM,CAAC,CAAC;EAE1CzC,UAAU;EACVI,MAAM,GAAY,KAAK;EACvB9C,SAAS,GAAY,KAAK;EAC1B;EACAoF,aAAa,GAAY,KAAK;EAC9BlC,QAAQ,GAAY,KAAK;EACzBD,gBAAgB,GAAY,IAAI;EAChCpB,eAAe,GAAW,kCAAkC;EAC5DhB,YAAY,GAAe,EAAE;EAC7BE,WAAW,GAAY,KAAK;EAC5BV,eAAe,GAAY,KAAK;EAChC;EACAmE,SAAS,GAA2B,SAAS;EAE7Ca,YACUX,EAAe,EAChBC,KAAqB,EACpBC,cAA8B,EAC9BC,wBAAkD,EAClDC,gBAAkC;IAJlC,KAAAJ,EAAE,GAAFA,EAAE;IACH,KAAAC,KAAK,GAALA,KAAK;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,gBAAgB,GAAhBA,gBAAgB;EACvB;EAEHQ,QAAQA,CAAA;IACN,IAAI,CAACxC,MAAM,GAAG,CAAC,CAAC,IAAI,CAACoC,UAAU;IAC/B,IAAI,CAACxC,UAAU,GAAG,IAAI,CAACgC,EAAE,CAACa,KAAK,CAAC;MAC9BxG,WAAW,EAAE,CAAC,IAAI,CAACmG,UAAU,EAAEnG,WAAW,IAAI,EAAE,EAAEzB,UAAU,CAACkI,QAAQ,CAAC;MACtErG,OAAO,EAAE,CAAC,IAAI,CAAC+F,UAAU,EAAE/F,OAAO,IAAI,EAAE,EAAE7B,UAAU,CAACkI,QAAQ,CAAC;MAC9DlG,eAAe,EAAE,CAAC,IAAI,CAAC4F,UAAU,EAAE5F,eAAe,IAAI,EAAE,EAAEhC,UAAU,CAACkI,QAAQ,CAAC;MAC9E/F,SAAS,EAAE,CAAC,IAAI,CAACyF,UAAU,EAAEzF,SAAS,IAAI,EAAE,EAAEnC,UAAU,CAACkI,QAAQ,CAAC;MAClE5F,aAAa,EAAE,CAAC,IAAI,CAACsF,UAAU,EAAEtF,aAAa,IAAI,EAAE,CAAC;MACrDE,cAAc,EAAE,CAAC,IAAI,CAACoF,UAAU,EAAEpF,cAAc,IAAI,EAAE,CAAC;MACvD2F,UAAU,EAAE,CAAC,IAAI,CAACP,UAAU,EAAEO,UAAU,IAAI,EAAE,CAAC;MAC/CC,kBAAkB,EAAE,CAAC,IAAI,CAACR,UAAU,EAAEQ,kBAAkB,IAAI,EAAE;KAC/D,CAAC;EACJ;EAEA;EAEA9C,yBAAyBA,CAAC+C,SAAiB;IACzC;IACA,IAAI,CAAC,IAAI,CAACP,aAAa,EAAE;MACvB,OAAO,KAAK;IACd;IAEA,MAAMQ,KAAK,GAAG,IAAI,CAAClD,UAAU,CAACmD,GAAG,CAACF,SAAS,CAAC;IAC5C,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAACE,OAAO,CAAC;EACnC;EAEA7D,QAAQA,CAAA;IACN,IAAI,CAACmD,aAAa,GAAG,IAAI;IACzB,IAAI,IAAI,CAAC1C,UAAU,CAACqD,KAAK,IAAI,IAAI,CAAChB,QAAQ,EAAE;MAC1C,IAAI,CAAC/E,SAAS,GAAG,IAAI;MACrB;MACA,IAAI,CAAC8E,gBAAgB,CAACkB,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;MAE/C,MAAMC,QAAQ,GAAQ;QACpB,GAAG,IAAI,CAACxD,UAAU,CAACyD,KAAK;QACxBpB,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBC,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BlH,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCmH,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,IAAI,IAAI,CAACC,UAAU,EAAED,kBAAkB;QAClFE,cAAc,EAAE,IAAI,CAACA;OACtB;MACD,IAAI,IAAI,CAACrC,MAAM,IAAI,IAAI,CAACoC,UAAU,EAAEkB,wBAAwB,EAAE;QAC5DF,QAAQ,CAACE,wBAAwB,GAAG,IAAI,CAAClB,UAAU,CAACkB,wBAAwB;QAC5E,IAAI,CAACxB,cAAc,CAACyB,8BAA8B,CAACH,QAAQ,CAAC,CAACI,SAAS,CAAC;UACrEL,IAAI,EAAGM,GAAQ,IAAI;YACjB,IAAI,CAACvG,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAAC8E,gBAAgB,CAACkB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAIM,GAAG,EAAEC,OAAO,EAAE;cAChB,IAAI,CAAC3B,wBAAwB,CAAC4B,SAAS,CAACF,GAAG,CAACG,YAAY,IAAI,gCAAgC,EAAE,EAAE,CAAC;YACnG,CAAC,MAAM;cACL,IAAI,CAAC7B,wBAAwB,CAAC8B,WAAW,CAACJ,GAAG,CAACK,YAAY,EAAEC,OAAO,IAAI,qCAAqC,EAAE,EAAE,CAAC;cACjH,IAAI,CAAClC,KAAK,CAACmC,KAAK,CAAC,SAAS,CAAC;YAC7B;UACF,CAAC;UACDC,KAAK,EAAGC,GAAQ,IAAI;YAClB,IAAI,CAAChH,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAAC8E,gBAAgB,CAACkB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAI,CAACpB,wBAAwB,CAAC4B,SAAS,CAAC,8BAA8B,EAAE,EAAE,CAAC;YAC3EQ,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;UACpB;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACpC,cAAc,CAACsC,2BAA2B,CAAChB,QAAQ,CAAC,CAACI,SAAS,CAAC;UAClEL,IAAI,EAAGM,GAAQ,IAAI;YACjB,IAAI,CAACvG,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAAC8E,gBAAgB,CAACkB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAIM,GAAG,EAAEC,OAAO,EAAE;cAChB,IAAI,CAAC3B,wBAAwB,CAAC4B,SAAS,CAACF,GAAG,CAACG,YAAY,IAAI,gCAAgC,EAAE,EAAE,CAAC;YACnG,CAAC,MAAM;cACL,IAAI,CAAC7B,wBAAwB,CAAC8B,WAAW,CAAC,qCAAqC,EAAE,EAAE,CAAC;cACpF,IAAI,CAAChC,KAAK,CAACmC,KAAK,CAAC,SAAS,CAAC;YAC7B;UACF,CAAC;UACDC,KAAK,EAAGC,GAAQ,IAAI;YAClB,IAAI,CAAChH,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAAC8E,gBAAgB,CAACkB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAI,CAACpB,wBAAwB,CAAC4B,SAAS,CAAC,8BAA8B,EAAE,EAAE,CAAC;YAC3EQ,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;UACpB;SACD,CAAC;MACJ;IACF,CAAC,MAAM;MACL,IAAI,CAACtE,UAAU,CAACyE,gBAAgB,EAAE;MAClC,IAAI,CAAC,IAAI,CAACpC,QAAQ,EAAE;QAClB,IAAI,CAACF,wBAAwB,CAAC4B,SAAS,CAAC,uBAAuB,EAAE,EAAE,CAAC;MACtE;IACF;EACF;EAEAhE,QAAQA,CAAA;IACN,IAAI,CAACkC,KAAK,CAACyC,OAAO,CAAC,WAAW,CAAC;EACjC;EAEAjG,MAAMA,CAAA;IACJ,IAAI,CAACwD,KAAK,CAACyC,OAAO,CAAC,MAAM,CAAC;EAC5B;EAEAzF,gBAAgBA,CAAA;IACd,IAAI,CAACyD,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAClC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACD,gBAAgB,GAAG,KAAK;EAC/B;EAEAd,aAAaA,CAAA;IACX,IAAI,IAAI,CAACO,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAAC2E,KAAK,CAAC;QACpBtI,WAAW,EAAE,EAAE;QACfI,OAAO,EAAE,EAAE;QACXG,eAAe,EAAE,EAAE;QACnBG,SAAS,EAAE,EAAE;QACbG,aAAa,EAAE,EAAE;QACjBE,cAAc,EAAE;OACjB,CAAC;IACJ;IACA,IAAI,CAACsF,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAClC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACD,gBAAgB,GAAG,IAAI;EAC9B;EAEAW,YAAYA,CAAC0D,GAA2B;IACtC,IAAI,CAAC9C,SAAS,GAAG8C,GAAG;EACtB;EAEA7D,YAAYA,CAAA;IACV,IAAI,IAAI,CAACe,SAAS,KAAK,UAAU,EAAE;MACjC,IAAI,CAACA,SAAS,GAAG,SAAS;IAC5B;EACF;EAEA/C,iBAAiBA,CAAC8F,SAA2B;IAC3C,IAAI,IAAI,CAACvH,SAAS,EAAE;MAAE;IAAQ;IAC9BuH,SAAS,CAACC,KAAK,EAAE;EACnB;EAEAnG,cAAcA,CAACoG,KAAY;IAAA,IAAAC,KAAA;IACzB,MAAMC,KAAK,GAAGF,KAAK,CAACG,MAA0B;IAC9C,MAAMC,IAAI,GAAGF,KAAK,EAAEG,KAAK,IAAIH,KAAK,CAACG,KAAK,CAAChH,MAAM,GAAG6G,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;IACvE,IAAI,CAACD,IAAI,EAAE;MAAE;IAAQ;IACrB,IAAI,CAAC9G,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAAC+D,gBAAgB,CAACkB,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC/C,MAAM8B,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,gBAAAC,iBAAA,CAAG,aAAW;MACzB,IAAI;QACF,MAAMC,IAAI,SAAc,MAAM,CAAC,MAAM,CAAC;QACtC,MAAMC,IAAI,GAAG,IAAIC,UAAU,CAACN,MAAM,CAACO,MAAqB,CAAC;QACzD,MAAMC,QAAQ,GAAGJ,IAAI,CAACK,IAAI,CAACJ,IAAI,EAAE;UAAEK,IAAI,EAAE;QAAO,CAAE,CAAC;QACnD,MAAMC,cAAc,GAAGH,QAAQ,CAACI,UAAU,CAAC,CAAC,CAAC;QAC7C,MAAMC,SAAS,GAAGL,QAAQ,CAACM,MAAM,CAACH,cAAc,CAAC;QACjD,MAAMI,IAAI,GAAUX,IAAI,CAACY,KAAK,CAACC,aAAa,CAACJ,SAAS,EAAE;UAAEK,MAAM,EAAE;QAAE,CAAE,CAAC;QACvEvB,KAAI,CAAC7G,YAAY,GAAG6G,KAAI,CAACwB,YAAY,CAACJ,IAAI,CAAC;QAC3C;QACApB,KAAI,CAACzE,gBAAgB,GAAG,IAAI;QAC5ByE,KAAI,CAACxE,QAAQ,GAAG,KAAK;QACrB,IAAI,CAACwE,KAAI,CAAC7G,YAAY,CAACC,MAAM,EAAE;UAC7B4G,KAAI,CAAC7C,wBAAwB,CAAC4B,SAAS,CAAC,sCAAsC,EAAE,EAAE,CAAC;QACrF,CAAC,MAAM;UACLiB,KAAI,CAAC7C,wBAAwB,CAAC8B,WAAW,CAAC,YAAYe,KAAI,CAAC7G,YAAY,CAACC,MAAM,UAAU,EAAE,EAAE,CAAC;QAC/F;MACF,CAAC,CAAC,OAAOkG,GAAG,EAAE;QACZC,OAAO,CAACF,KAAK,CAAC,qBAAqB,EAAEC,GAAG,CAAC;QACzCU,KAAI,CAAC7G,YAAY,GAAG,EAAE;QACtB6G,KAAI,CAAC7C,wBAAwB,CAAC4B,SAAS,CAAC,wDAAwD,EAAE,EAAE,CAAC;MACvG,CAAC,SAAS;QACRiB,KAAI,CAAC3G,WAAW,GAAG,KAAK;QACxB;QACA2G,KAAI,CAAC5C,gBAAgB,CAACkB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAChD;QACA0B,KAAK,CAACxB,KAAK,GAAG,EAAE;MAClB;IACF,CAAC;IACD4B,MAAM,CAACoB,iBAAiB,CAACtB,IAAI,CAAC;EAChC;EAEQuB,eAAeA,CAACC,MAAc;IACpC,OAAO,CAACA,MAAM,IAAI,EAAE,EAAEC,QAAQ,EAAE,CAAChJ,IAAI,EAAE,CAACiJ,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;EAC3E;EAEQN,YAAYA,CAACO,QAAe;IAClC,IAAI,CAACA,QAAQ,IAAI,CAACA,QAAQ,CAAC3I,MAAM,EAAE;MAAE,OAAO,EAAE;IAAE;IAChD,MAAM4I,SAAS,GAAQ,EAAE;IACzB;IACA,MAAMC,QAAQ,GAAGF,QAAQ,CAAC,CAAC,CAAC;IAC5BG,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,OAAO,CAAEC,GAAG,IAAI;MACpC,MAAMC,IAAI,GAAG,IAAI,CAACZ,eAAe,CAACW,GAAG,CAAC;MACtCL,SAAS,CAACM,IAAI,CAAC,GAAGD,GAAG;IACvB,CAAC,CAAC;IAEF,MAAME,IAAI,GAAGA,CAACC,GAAQ,EAAEC,aAAuB,KAAY;MACzD,KAAK,MAAMC,SAAS,IAAID,aAAa,EAAE;QACrC,MAAMH,IAAI,GAAG,IAAI,CAACZ,eAAe,CAACgB,SAAS,CAAC;QAC5C,MAAMC,MAAM,GAAGX,SAAS,CAACM,IAAI,CAAC;QAC9B,IAAIK,MAAM,IAAIH,GAAG,CAACI,cAAc,CAACD,MAAM,CAAC,EAAE;UACxC,OAAO,CAACH,GAAG,CAACG,MAAM,CAAC,IAAI,EAAE,EAAEf,QAAQ,EAAE;QACvC;MACF;MACA,OAAO,EAAE;IACX,CAAC;IAED;IACA,MAAMiB,IAAI,GAAGd,QAAQ,CAACe,GAAG,CAAEN,GAAG,IAAI;MAChC,OAAO;QACLnL,WAAW,EAAEkL,IAAI,CAACC,GAAG,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QAC7E/K,OAAO,EAAE8K,IAAI,CAACC,GAAG,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAC7D5K,eAAe,EAAE2K,IAAI,CAACC,GAAG,EAAE,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;QAC/DzK,SAAS,EAAEwK,IAAI,CAACC,GAAG,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAC7CtK,aAAa,EAAEqK,IAAI,CAACC,GAAG,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;QAC/EpK,cAAc,EAAEmK,IAAI,CAACC,GAAG,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,aAAa,CAAC;OAC/E;IACH,CAAC,CAAC;IACF;IACA,OAAOK,IAAI,CAACE,MAAM,CAAEC,CAAC,IAAKd,MAAM,CAACe,MAAM,CAACD,CAAC,CAAC,CAACE,IAAI,CAAEC,CAAC,IAAK,CAACA,CAAC,IAAI,EAAE,EAAEvB,QAAQ,EAAE,CAAChJ,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;EAC7F;EAEA/B,iBAAiBA,CAACH,KAAa;IAC7B,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACyC,YAAY,CAACC,MAAM,EAAE;MAAE;IAAQ;IAC9D,IAAI,CAACD,YAAY,CAACiK,MAAM,CAAC1M,KAAK,EAAE,CAAC,CAAC;EACpC;EAEAqC,cAAcA,CAAA;IACZ,IAAI,CAACI,YAAY,CAACkK,OAAO,CAAC;MACxBhM,WAAW,EAAE,EAAE;MACfI,OAAO,EAAE,EAAE;MACXG,eAAe,EAAE,EAAE;MACnBG,SAAS,EAAE,EAAE;MACbG,aAAa,EAAE,EAAE;MACjBE,cAAc,EAAE;KACjB,CAAC;EACJ;EAEMa,eAAeA,CAAA;IAAA,IAAAqK,MAAA;IAAA,OAAA9C,iBAAA;MACnB8C,MAAI,CAAC3K,eAAe,GAAG,IAAI;MAC3B,IAAI2K,MAAI,CAAChK,kBAAkB,EAAE;QAC3BgK,MAAI,CAACnG,wBAAwB,CAAC4B,SAAS,CAAC,uDAAuD,EAAE,EAAE,CAAC;QACpG;MACF;MACA,IAAI,CAACuE,MAAI,CAACjG,QAAQ,EAAE;QAClBiG,MAAI,CAACnG,wBAAwB,CAAC4B,SAAS,CAAC,6CAA6C,EAAE,EAAE,CAAC;QAC1F;MACF;MACA,IAAI,CAACuE,MAAI,CAAClN,cAAc,EAAE;QACxBkN,MAAI,CAACnG,wBAAwB,CAAC4B,SAAS,CAAC,mDAAmD,EAAE,EAAE,CAAC;QAChG;MACF;MACA,IAAI,CAACuE,MAAI,CAACnK,YAAY,CAACC,MAAM,EAAE;QAC7BkK,MAAI,CAACnG,wBAAwB,CAAC4B,SAAS,CAAC,0BAA0B,EAAE,EAAE,CAAC;QACvE;MACF;MACAuE,MAAI,CAAChL,SAAS,GAAG,IAAI;MACrB;MACAgL,MAAI,CAAClG,gBAAgB,CAACkB,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;MAC/C,IAAI;QACF,MAAMgF,QAAQ,GAAGD,MAAI,CAACnK,YAAY,CAAC2J,GAAG,CAAEE,CAAC,IAAI;UAC3C,MAAMQ,OAAO,GAAQ;YACnBnM,WAAW,EAAE2L,CAAC,CAAC3L,WAAW,IAAI,EAAE;YAChCI,OAAO,EAAEuL,CAAC,CAACvL,OAAO,IAAI,EAAE;YACxBG,eAAe,EAAEoL,CAAC,CAACpL,eAAe,IAAI,EAAE;YACxCG,SAAS,EAAEiL,CAAC,CAACjL,SAAS,IAAI,EAAE;YAC5BG,aAAa,EAAE8K,CAAC,CAAC9K,aAAa,IAAI,EAAE;YACpCE,cAAc,EAAE4K,CAAC,CAAC5K,cAAc,IAAI,EAAE;YACtCiF,QAAQ,EAAEiG,MAAI,CAACjG,QAAQ;YACvBC,YAAY,EAAEgG,MAAI,CAAChG,YAAY;YAC/BlH,cAAc,EAAEkN,MAAI,CAAClN,cAAc;YACnCmH,kBAAkB,EAAE+F,MAAI,CAAC/F,kBAAkB;YAC3CE,cAAc,EAAE6F,MAAI,CAAC7F;WACtB;UACD,OAAO6F,MAAI,CAACpG,cAAc,CAACsC,2BAA2B,CAACgE,OAAO,CAAC;QACjE,CAAC,CAAC;QACF;QACA,MAAM,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;UACpC,MAAM;YAAEC;UAAQ,CAAE,GAAGC,OAAO,CAAC,MAAM,CAAC;UACpCD,QAAQ,CAACL,QAAQ,CAAC,CAAC3E,SAAS,CAAC;YAAEL,IAAI,EAAEmF,OAAO;YAAErE,KAAK,EAAEsE;UAAM,CAAE,CAAC;QAChE,CAAC,CAAC;QACFL,MAAI,CAACnG,wBAAwB,CAAC8B,WAAW,CAAC,uCAAuC,EAAE,EAAE,CAAC;QACtFqE,MAAI,CAACnK,YAAY,GAAG,EAAE;QACtBmK,MAAI,CAAC3K,eAAe,GAAG,KAAK;QAC5B;QACA2K,MAAI,CAACrG,KAAK,CAACmC,KAAK,CAAC,cAAc,CAAC;MAClC,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZC,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEC,GAAG,CAAC;QAChDgE,MAAI,CAACnG,wBAAwB,CAAC4B,SAAS,CAAC,0CAA0C,EAAE,EAAE,CAAC;MACzF,CAAC,SAAS;QACRuE,MAAI,CAAChL,SAAS,GAAG,KAAK;QACtB;QACAgL,MAAI,CAAClG,gBAAgB,CAACkB,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAClD;IAAC;EACH;EAEA,IAAIjF,kBAAkBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACH,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACC,MAAM,EAAE;MAAE,OAAO,KAAK;IAAE;IACrE,OAAO,IAAI,CAACD,YAAY,CAAC+J,IAAI,CAAEF,CAAC,IAAK,CAAC,IAAI,CAACc,kBAAkB,CAACd,CAAC,CAAC,CAAC;EACnE;EAEQc,kBAAkBA,CAACd,CAAM;IAC/B,OAAO,CAAC,EACN,CAACA,CAAC,CAAC3L,WAAW,IAAI,EAAE,EAAEuK,QAAQ,EAAE,CAAChJ,IAAI,EAAE,IACvC,CAACoK,CAAC,CAACvL,OAAO,IAAI,EAAE,EAAEmK,QAAQ,EAAE,CAAChJ,IAAI,EAAE,IACnC,CAACoK,CAAC,CAACpL,eAAe,IAAI,EAAE,EAAEgK,QAAQ,EAAE,CAAChJ,IAAI,EAAE,IAC3C,CAACoK,CAAC,CAACjL,SAAS,IAAI,EAAE,EAAE6J,QAAQ,EAAE,CAAChJ,IAAI,EAAE,CACtC;EACH;EAEA,IAAImL,WAAWA,CAAA;IACb,OAAO,IAAI,CAAC/I,UAAU,CAACqD,KAAK;EAC9B;EAEA,IAAI2F,cAAcA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAChJ,UAAU,EAAE;MAAE,OAAO,KAAK;IAAE;IACtC,MAAMiJ,QAAQ,GAAG,IAAI,CAACjJ,UAAU,CAACiJ,QAAe;IAChD,OACE,CAAC,CAACA,QAAQ,CAAC5M,WAAW,EAAEgH,KAAK,IAC7B,CAAC,CAAC4F,QAAQ,CAACxM,OAAO,EAAE4G,KAAK,IACzB,CAAC,CAAC4F,QAAQ,CAACrM,eAAe,EAAEyG,KAAK,IACjC,CAAC,CAAC4F,QAAQ,CAAClM,SAAS,EAAEsG,KAAK;EAE/B;;qCA7VWtB,oCAAoC,EAAAlH,EAAA,CAAAqO,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvO,EAAA,CAAAqO,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAzO,EAAA,CAAAqO,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA3O,EAAA,CAAAqO,iBAAA,CAAAO,EAAA,CAAAC,wBAAA,GAAA7O,EAAA,CAAAqO,iBAAA,CAAAS,EAAA,CAAAC,gBAAA;EAAA;;UAApC7H,oCAAoC;IAAA8H,SAAA;IAAAC,MAAA;MAAAzH,QAAA;MAAAC,YAAA;MAAAlH,cAAA;MAAAmH,kBAAA;MAAAC,UAAA;MAAAC,cAAA;IAAA;IAAAsH,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCT7CvP,EAHJ,CAAAC,cAAA,aAAkC,aAEW,aACR;QAC/BD,EAAA,CAAAyP,uBAAA,GAAc;QAEZzP,EADA,CAAAyB,UAAA,IAAAiO,mDAAA,iBAAoB,IAAAC,mDAAA,iBACC;;QAEzB3P,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAAyB,WAC2C;QAArBD,EAAA,CAAAQ,UAAA,mBAAAoP,iEAAA;UAAA5P,EAAA,CAAAW,aAAA,CAAAkP,GAAA;UAAA,OAAA7P,EAAA,CAAAe,WAAA,CAASyO,GAAA,CAAAtK,QAAA,EAAU;QAAA,EAAC;QAErElF,EAFsE,CAAAG,YAAA,EAAI,EAClE,EACF;QAyINH,EAtIA,CAAAyB,UAAA,IAAAqO,mDAAA,iBAA+D,IAAAC,2DAAA,kCAAA/P,EAAA,CAAAgQ,sBAAA,CAsIhC;QAiFjChQ,EAAA,CAAAG,YAAA,EAAM;;;;QAjOQH,EAAA,CAAAI,SAAA,GAAY;QAAZJ,EAAA,CAAAwC,UAAA,SAAAgN,GAAA,CAAAjK,MAAA,CAAY;QACZvF,EAAA,CAAAI,SAAA,EAAa;QAAbJ,EAAA,CAAAwC,UAAA,UAAAgN,GAAA,CAAAjK,MAAA,CAAa;QASAvF,EAAA,CAAAI,SAAA,GAAe;QAAAJ,EAAf,CAAAwC,UAAA,UAAAgN,GAAA,CAAAjK,MAAA,CAAe,aAAA0K,oBAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}