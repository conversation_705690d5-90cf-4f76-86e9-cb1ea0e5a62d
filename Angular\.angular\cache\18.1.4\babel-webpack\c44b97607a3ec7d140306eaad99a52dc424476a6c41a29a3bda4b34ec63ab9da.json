{"ast": null, "code": "import _asyncToGenerator from \"D:/permittracker/Angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, Input } from '@angular/core';\nimport { Validators } from '@angular/forms';\nlet AddEditInternalReviewDetailComponent = class AddEditInternalReviewDetailComponent {\n  fb;\n  modal;\n  permitsService;\n  customLayoutUtilsService;\n  httpUtilsService;\n  permitId = null;\n  permitNumber = '';\n  reviewCategory = '';\n  internalCommentsId = null;\n  detailData = null; // For edit mode\n  loggedInUserId = 'user'; // Should be passed from parent\n  detailForm;\n  isEdit = false;\n  isLoading = false;\n  // Tabs removed; single-view form\n  formSubmitted = false;\n  showForm = false;\n  showExcelSection = true; // instruction/download/import section\n  showImportTableSection = false; // table-only section after successful upload\n  templateUrlPath = '/assets/excel/claimstempate.xlsx';\n  importedRows = [];\n  isImporting = false;\n  importSubmitted = false;\n  // Tab state for edit mode (old popup)\n  activeTab = 'details';\n  constructor(fb, modal, permitsService, customLayoutUtilsService, httpUtilsService) {\n    this.fb = fb;\n    this.modal = modal;\n    this.permitsService = permitsService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.httpUtilsService = httpUtilsService;\n  }\n  ngOnInit() {\n    this.isEdit = !!this.detailData;\n    this.detailForm = this.fb.group({\n      sheetNumber: [this.detailData?.sheetNumber || '', Validators.required],\n      codeRef: [this.detailData?.codeRef || '', Validators.required],\n      codeDescription: [this.detailData?.codeDescription || '', Validators.required],\n      reasoning: [this.detailData?.reasoning || '', Validators.required],\n      nonCompliance: [this.detailData?.nonCompliance || ''],\n      actionableStep: [this.detailData?.actionableStep || ''],\n      aeResponse: [this.detailData?.aeResponse || ''],\n      commentResponsedBy: [this.detailData?.commentResponsedBy || '']\n    });\n  }\n  // Tab navigation removed\n  shouldShowValidationError(fieldName) {\n    // Only show validation errors when form has been submitted\n    if (!this.formSubmitted) {\n      return false;\n    }\n    const field = this.detailForm.get(fieldName);\n    return !!(field && field.invalid);\n  }\n  onSubmit() {\n    this.formSubmitted = true;\n    if (this.detailForm.valid && this.permitId) {\n      this.isLoading = true;\n      // Enable common loader\n      this.httpUtilsService.loadingSubject.next(true);\n      const formData = {\n        ...this.detailForm.value,\n        permitId: this.permitId,\n        permitNumber: this.permitNumber,\n        reviewCategory: this.reviewCategory,\n        internalCommentsId: this.internalCommentsId || this.detailData?.internalCommentsId,\n        loggedInUserId: this.loggedInUserId\n      };\n      if (this.isEdit && this.detailData?.internalReviewCommentsId) {\n        formData.internalReviewCommentsId = this.detailData.internalReviewCommentsId;\n        this.permitsService.updateInternalPlanReviewDetail(formData).subscribe({\n          next: res => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to update review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess(res.responseData?.message || 'Review detail updated successfully!', '');\n              this.modal.close('updated');\n            }\n          },\n          error: err => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error updating review detail', '');\n            console.error(err);\n          }\n        });\n      } else {\n        this.permitsService.addInternalPlanReviewDetail(formData).subscribe({\n          next: res => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess('Review detail created successfully!', '');\n              this.modal.close('created');\n            }\n          },\n          error: err => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error creating review detail', '');\n            console.error(err);\n          }\n        });\n      }\n    } else {\n      this.detailForm.markAllAsTouched();\n      if (!this.permitId) {\n        this.customLayoutUtilsService.showError('Permit Id is required', '');\n      }\n    }\n  }\n  onCancel() {\n    this.modal.dismiss('cancelled');\n  }\n  onBack() {\n    this.modal.dismiss('back');\n  }\n  onClickAddReview() {\n    this.formSubmitted = false;\n    this.showForm = true;\n    this.showExcelSection = false;\n    this.showImportTableSection = false;\n  }\n  cancelAddForm() {\n    if (this.detailForm) {\n      this.detailForm.reset({\n        sheetNumber: '',\n        codeRef: '',\n        codeDescription: '',\n        reasoning: '',\n        nonCompliance: '',\n        actionableStep: ''\n      });\n    }\n    this.formSubmitted = false;\n    this.showForm = false;\n    this.showExcelSection = true;\n    this.showImportTableSection = false;\n  }\n  setActiveTab(tab) {\n    this.activeTab = tab;\n  }\n  goToPrevious() {\n    if (this.activeTab === 'comments') {\n      this.activeTab = 'details';\n    }\n  }\n  triggerFileImport(fileInput) {\n    if (this.isLoading) {\n      return;\n    }\n    fileInput.click();\n  }\n  onFileSelected(event) {\n    var _this = this;\n    const input = event.target;\n    const file = input?.files && input.files.length ? input.files[0] : null;\n    if (!file) {\n      return;\n    }\n    this.isImporting = true;\n    // Enable common loader\n    this.httpUtilsService.loadingSubject.next(true);\n    const reader = new FileReader();\n    reader.onload = /*#__PURE__*/_asyncToGenerator(function* () {\n      try {\n        const XLSX = yield import('xlsx');\n        const data = new Uint8Array(reader.result);\n        const workbook = XLSX.read(data, {\n          type: 'array'\n        });\n        const firstSheetName = workbook.SheetNames[0];\n        const worksheet = workbook.Sheets[firstSheetName];\n        const json = XLSX.utils.sheet_to_json(worksheet, {\n          defval: ''\n        });\n        _this.importedRows = _this.mapExcelRows(json);\n        // After upload, switch to table section; hide instruction and form\n        _this.showExcelSection = false;\n        _this.showForm = false;\n        _this.showImportTableSection = _this.importedRows.length > 0;\n        if (!_this.importedRows.length) {\n          _this.customLayoutUtilsService.showError('No rows found in the uploaded Excel.', '');\n        } else {\n          _this.customLayoutUtilsService.showSuccess(`Imported ${_this.importedRows.length} row(s).`, '');\n        }\n      } catch (err) {\n        console.error('Error parsing Excel', err);\n        _this.importedRows = [];\n        _this.customLayoutUtilsService.showError('Failed to parse Excel file. Please check the template.', '');\n      } finally {\n        _this.isImporting = false;\n        // Disable common loader\n        _this.httpUtilsService.loadingSubject.next(false);\n        // Reset file input so the same file can be selected again if needed\n        input.value = '';\n      }\n    });\n    reader.readAsArrayBuffer(file);\n  }\n  cancelImportTable() {\n    // Hide table and return to instruction section\n    this.showImportTableSection = false;\n    this.showExcelSection = true;\n  }\n  normalizeHeader(header) {\n    return (header || '').toString().trim().toLowerCase().replace(/\\s+/g, '');\n  }\n  mapExcelRows(jsonRows) {\n    if (!jsonRows || !jsonRows.length) {\n      return [];\n    }\n    const headerMap = {};\n    // Build header map from first row's keys\n    const firstRow = jsonRows[0];\n    Object.keys(firstRow).forEach(key => {\n      const norm = this.normalizeHeader(key);\n      headerMap[norm] = key;\n    });\n    const pick = (row, keyCandidates) => {\n      for (const candidate of keyCandidates) {\n        const norm = this.normalizeHeader(candidate);\n        const actual = headerMap[norm];\n        if (actual && row.hasOwnProperty(actual)) {\n          return (row[actual] ?? '').toString();\n        }\n      }\n      return '';\n    };\n    // Expected columns with common variants\n    const rows = jsonRows.map(row => {\n      return {\n        sheetNumber: pick(row, ['Sheet Number', 'SheetNumber', 'Sheet#', 'Sheet No']),\n        codeRef: pick(row, ['Code Ref', 'Code Reference', 'CodeRef']),\n        codeDescription: pick(row, ['Code Description', 'Description']),\n        reasoning: pick(row, ['Reasoning', 'Reason']),\n        nonCompliance: pick(row, ['Non Compliance', 'Non-Compliance', 'NonCompliance']),\n        actionableStep: pick(row, ['Actionable Step', 'ActionableStep', 'Action Step'])\n      };\n    });\n    // Filter out completely empty rows\n    return rows.filter(r => Object.values(r).some(v => (v || '').toString().trim() !== ''));\n  }\n  deleteImportedRow(index) {\n    if (index < 0 || index >= this.importedRows.length) {\n      return;\n    }\n    this.importedRows.splice(index, 1);\n  }\n  addImportedRow() {\n    this.importedRows.unshift({\n      sheetNumber: '',\n      codeRef: '',\n      codeDescription: '',\n      reasoning: '',\n      nonCompliance: '',\n      actionableStep: ''\n    });\n  }\n  saveAllImported() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.importSubmitted = true;\n      if (_this2.hasImportedInvalid) {\n        _this2.customLayoutUtilsService.showError('Please fill all required fields in the imported rows.', '');\n        return;\n      }\n      if (!_this2.permitId) {\n        _this2.customLayoutUtilsService.showError('Permit Id is required to save imported rows', '');\n        return;\n      }\n      if (!_this2.reviewCategory) {\n        _this2.customLayoutUtilsService.showError('Review Category is required to save imported rows', '');\n        return;\n      }\n      if (!_this2.importedRows.length) {\n        _this2.customLayoutUtilsService.showError('No imported rows to save', '');\n        return;\n      }\n      _this2.isLoading = true;\n      // Enable common loader\n      _this2.httpUtilsService.loadingSubject.next(true);\n      try {\n        const requests = _this2.importedRows.map(r => {\n          const payload = {\n            sheetNumber: r.sheetNumber || '',\n            codeRef: r.codeRef || '',\n            codeDescription: r.codeDescription || '',\n            reasoning: r.reasoning || '',\n            nonCompliance: r.nonCompliance || '',\n            actionableStep: r.actionableStep || '',\n            permitId: _this2.permitId,\n            permitNumber: _this2.permitNumber,\n            reviewCategory: _this2.reviewCategory,\n            internalCommentsId: _this2.internalCommentsId,\n            loggedInUserId: _this2.loggedInUserId\n          };\n          return _this2.permitsService.addInternalPlanReviewDetail(payload);\n        });\n        // Execute all in parallel\n        yield new Promise((resolve, reject) => {\n          const {\n            forkJoin\n          } = require('rxjs');\n          forkJoin(requests).subscribe({\n            next: resolve,\n            error: reject\n          });\n        });\n        _this2.customLayoutUtilsService.showSuccess('All imported rows saved successfully!', '');\n        _this2.importedRows = [];\n        _this2.importSubmitted = false;\n        // Optionally close modal or refresh parent via close value\n        _this2.modal.close('bulk-created');\n      } catch (err) {\n        console.error('Error saving imported rows', err);\n        _this2.customLayoutUtilsService.showError('Failed to save one or more imported rows', '');\n      } finally {\n        _this2.isLoading = false;\n        // Disable common loader\n        _this2.httpUtilsService.loadingSubject.next(false);\n      }\n    })();\n  }\n  get hasImportedInvalid() {\n    if (!this.importedRows || !this.importedRows.length) {\n      return false;\n    }\n    return this.importedRows.some(r => !this.isImportedRowValid(r));\n  }\n  isImportedRowValid(r) {\n    return !!((r.sheetNumber || '').toString().trim() && (r.codeRef || '').toString().trim() && (r.codeDescription || '').toString().trim() && (r.reasoning || '').toString().trim());\n  }\n  get isFormValid() {\n    return this.detailForm.valid;\n  }\n  get isDetailsValid() {\n    if (!this.detailForm) {\n      return false;\n    }\n    const controls = this.detailForm.controls;\n    return !!controls.sheetNumber?.valid && !!controls.codeRef?.valid && !!controls.codeDescription?.valid && !!controls.reasoning?.valid;\n  }\n};\n__decorate([Input()], AddEditInternalReviewDetailComponent.prototype, \"permitId\", void 0);\n__decorate([Input()], AddEditInternalReviewDetailComponent.prototype, \"permitNumber\", void 0);\n__decorate([Input()], AddEditInternalReviewDetailComponent.prototype, \"reviewCategory\", void 0);\n__decorate([Input()], AddEditInternalReviewDetailComponent.prototype, \"internalCommentsId\", void 0);\n__decorate([Input()], AddEditInternalReviewDetailComponent.prototype, \"detailData\", void 0);\n__decorate([Input()], AddEditInternalReviewDetailComponent.prototype, \"loggedInUserId\", void 0);\nAddEditInternalReviewDetailComponent = __decorate([Component({\n  selector: 'app-add-edit-internal-review-detail',\n  templateUrl: './add-edit-internal-review-detail.component.html',\n  styleUrls: ['./add-edit-internal-review-detail.component.scss']\n})], AddEditInternalReviewDetailComponent);\nexport { AddEditInternalReviewDetailComponent };", "map": {"version": 3, "names": ["Component", "Input", "Validators", "AddEditInternalReviewDetailComponent", "fb", "modal", "permitsService", "customLayoutUtilsService", "httpUtilsService", "permitId", "permitNumber", "reviewCategory", "internalCommentsId", "detailData", "loggedInUserId", "detailForm", "isEdit", "isLoading", "formSubmitted", "showForm", "showExcelSection", "showImportTableSection", "templateUrlPath", "importedRows", "isImporting", "importSubmitted", "activeTab", "constructor", "ngOnInit", "group", "sheetNumber", "required", "codeRef", "codeDescription", "reasoning", "nonCompliance", "actionableStep", "aeResponse", "commentResponsedBy", "shouldShowValidationError", "fieldName", "field", "get", "invalid", "onSubmit", "valid", "loadingSubject", "next", "formData", "value", "internalReviewCommentsId", "updateInternalPlanReviewDetail", "subscribe", "res", "<PERSON><PERSON><PERSON>", "showError", "faultMessage", "showSuccess", "responseData", "message", "close", "error", "err", "console", "addInternalPlanReviewDetail", "mark<PERSON>llAsTouched", "onCancel", "dismiss", "onBack", "onClickAddReview", "cancelAddForm", "reset", "setActiveTab", "tab", "goToPrevious", "triggerFileImport", "fileInput", "click", "onFileSelected", "event", "_this", "input", "target", "file", "files", "length", "reader", "FileReader", "onload", "_asyncToGenerator", "XLSX", "data", "Uint8Array", "result", "workbook", "read", "type", "firstSheetName", "SheetNames", "worksheet", "Sheets", "json", "utils", "sheet_to_json", "defval", "mapExcelRows", "readAsA<PERSON>y<PERSON><PERSON>er", "cancelImportTable", "normalizeHeader", "header", "toString", "trim", "toLowerCase", "replace", "jsonRows", "headerMap", "firstRow", "Object", "keys", "for<PERSON>ach", "key", "norm", "pick", "row", "keyCandidates", "candidate", "actual", "hasOwnProperty", "rows", "map", "filter", "r", "values", "some", "v", "deleteImportedRow", "index", "splice", "addImportedRow", "unshift", "saveAllImported", "_this2", "hasImportedInvalid", "requests", "payload", "Promise", "resolve", "reject", "fork<PERSON><PERSON>n", "require", "isImportedRowValid", "isFormValid", "isDetailsValid", "controls", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\add-edit-internal-review-detail\\add-edit-internal-review-detail.component.ts"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\nimport { PermitsService } from '../../services/permits.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { HttpUtilsService } from '../../services/http-utils.service';\n\n@Component({\n  selector: 'app-add-edit-internal-review-detail',\n  templateUrl: './add-edit-internal-review-detail.component.html',\n  styleUrls: ['./add-edit-internal-review-detail.component.scss']\n})\nexport class AddEditInternalReviewDetailComponent implements OnInit {\n  @Input() permitId: number | null = null;\n  @Input() permitNumber: string = '';\n  @Input() reviewCategory: string = '';\n  @Input() internalCommentsId: number | null = null;\n  @Input() detailData: any = null; // For edit mode\n  @Input() loggedInUserId: string = 'user'; // Should be passed from parent\n\n  detailForm!: FormGroup;\n  isEdit: boolean = false;\n  isLoading: boolean = false;\n  // Tabs removed; single-view form\n  formSubmitted: boolean = false;\n  showForm: boolean = false;\n  showExcelSection: boolean = true; // instruction/download/import section\n  showImportTableSection: boolean = false; // table-only section after successful upload\n  templateUrlPath: string = '/assets/excel/claimstempate.xlsx';\n  importedRows: Array<any> = [];\n  isImporting: boolean = false;\n  importSubmitted: boolean = false;\n  // Tab state for edit mode (old popup)\n  activeTab: 'details' | 'comments' = 'details';\n\n  constructor(\n    private fb: FormBuilder,\n    public modal: NgbActiveModal,\n    private permitsService: PermitsService,\n    private customLayoutUtilsService: CustomLayoutUtilsService,\n    private httpUtilsService: HttpUtilsService\n  ) {}\n\n  ngOnInit(): void {\n    this.isEdit = !!this.detailData;\n    this.detailForm = this.fb.group({\n      sheetNumber: [this.detailData?.sheetNumber || '', Validators.required],\n      codeRef: [this.detailData?.codeRef || '', Validators.required],\n      codeDescription: [this.detailData?.codeDescription || '', Validators.required],\n      reasoning: [this.detailData?.reasoning || '', Validators.required],\n      nonCompliance: [this.detailData?.nonCompliance || ''],\n      actionableStep: [this.detailData?.actionableStep || ''],\n      aeResponse: [this.detailData?.aeResponse || ''],\n      commentResponsedBy: [this.detailData?.commentResponsedBy || ''],\n    });\n  }\n\n  // Tab navigation removed\n\n  shouldShowValidationError(fieldName: string): boolean {\n    // Only show validation errors when form has been submitted\n    if (!this.formSubmitted) {\n      return false;\n    }\n    \n    const field = this.detailForm.get(fieldName);\n    return !!(field && field.invalid);\n  }\n\n  onSubmit(): void {\n    this.formSubmitted = true;\n    if (this.detailForm.valid && this.permitId) {\n      this.isLoading = true;\n      // Enable common loader\n      this.httpUtilsService.loadingSubject.next(true);\n      \n      const formData: any = {\n        ...this.detailForm.value,\n        permitId: this.permitId,\n        permitNumber: this.permitNumber,\n        reviewCategory: this.reviewCategory,\n        internalCommentsId: this.internalCommentsId || this.detailData?.internalCommentsId,\n        loggedInUserId: this.loggedInUserId\n      };\n      if (this.isEdit && this.detailData?.internalReviewCommentsId) {\n        formData.internalReviewCommentsId = this.detailData.internalReviewCommentsId;\n        this.permitsService.updateInternalPlanReviewDetail(formData).subscribe({\n          next: (res: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to update review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess(res.responseData?.message || 'Review detail updated successfully!', '');\n              this.modal.close('updated');\n            }\n          },\n          error: (err: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error updating review detail', '');\n            console.error(err);\n          }\n        });\n      } else {\n        this.permitsService.addInternalPlanReviewDetail(formData).subscribe({\n          next: (res: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            if (res?.isFault) {\n              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create review detail', '');\n            } else {\n              this.customLayoutUtilsService.showSuccess('Review detail created successfully!', '');\n              this.modal.close('created');\n            }\n          },\n          error: (err: any) => {\n            this.isLoading = false;\n            // Disable common loader\n            this.httpUtilsService.loadingSubject.next(false);\n            this.customLayoutUtilsService.showError('Error creating review detail', '');\n            console.error(err);\n          }\n        });\n      }\n    } else {\n      this.detailForm.markAllAsTouched();\n      if (!this.permitId) {\n        this.customLayoutUtilsService.showError('Permit Id is required', '');\n      }\n    }\n  }\n\n  onCancel(): void {\n    this.modal.dismiss('cancelled');\n  }\n\n  onBack(): void {\n    this.modal.dismiss('back');\n  }\n\n  onClickAddReview(): void {\n    this.formSubmitted = false;\n    this.showForm = true;\n    this.showExcelSection = false;\n    this.showImportTableSection = false;\n  }\n\n  cancelAddForm(): void {\n    if (this.detailForm) {\n      this.detailForm.reset({\n        sheetNumber: '',\n        codeRef: '',\n        codeDescription: '',\n        reasoning: '',\n        nonCompliance: '',\n        actionableStep: ''\n      });\n    }\n    this.formSubmitted = false;\n    this.showForm = false;\n    this.showExcelSection = true;\n    this.showImportTableSection = false;\n  }\n\n  setActiveTab(tab: 'details' | 'comments'): void {\n    this.activeTab = tab;\n  }\n\n  goToPrevious(): void {\n    if (this.activeTab === 'comments') {\n      this.activeTab = 'details';\n    }\n  }\n\n  triggerFileImport(fileInput: HTMLInputElement): void {\n    if (this.isLoading) { return; }\n    fileInput.click();\n  }\n\n  onFileSelected(event: Event): void {\n    const input = event.target as HTMLInputElement;\n    const file = input?.files && input.files.length ? input.files[0] : null;\n    if (!file) { return; }\n    this.isImporting = true;\n    // Enable common loader\n    this.httpUtilsService.loadingSubject.next(true);\n    const reader = new FileReader();\n    reader.onload = async () => {\n      try {\n        const XLSX: any = await import('xlsx');\n        const data = new Uint8Array(reader.result as ArrayBuffer);\n        const workbook = XLSX.read(data, { type: 'array' });\n        const firstSheetName = workbook.SheetNames[0];\n        const worksheet = workbook.Sheets[firstSheetName];\n        const json: any[] = XLSX.utils.sheet_to_json(worksheet, { defval: '' });\n        this.importedRows = this.mapExcelRows(json);\n        // After upload, switch to table section; hide instruction and form\n        this.showExcelSection = false;\n        this.showForm = false;\n        this.showImportTableSection = this.importedRows.length > 0;\n        if (!this.importedRows.length) {\n          this.customLayoutUtilsService.showError('No rows found in the uploaded Excel.', '');\n        } else {\n          this.customLayoutUtilsService.showSuccess(`Imported ${this.importedRows.length} row(s).`, '');\n        }\n      } catch (err) {\n        console.error('Error parsing Excel', err);\n        this.importedRows = [];\n        this.customLayoutUtilsService.showError('Failed to parse Excel file. Please check the template.', '');\n      } finally {\n        this.isImporting = false;\n        // Disable common loader\n        this.httpUtilsService.loadingSubject.next(false);\n        // Reset file input so the same file can be selected again if needed\n        input.value = '';\n      }\n    };\n    reader.readAsArrayBuffer(file);\n  }\n\n  cancelImportTable(): void {\n    // Hide table and return to instruction section\n    this.showImportTableSection = false;\n    this.showExcelSection = true;\n  }\n\n  private normalizeHeader(header: string): string {\n    return (header || '').toString().trim().toLowerCase().replace(/\\s+/g, '');\n  }\n\n  private mapExcelRows(jsonRows: any[]): Array<any> {\n    if (!jsonRows || !jsonRows.length) { return []; }\n    const headerMap: any = {};\n    // Build header map from first row's keys\n    const firstRow = jsonRows[0];\n    Object.keys(firstRow).forEach((key) => {\n      const norm = this.normalizeHeader(key);\n      headerMap[norm] = key;\n    });\n\n    const pick = (row: any, keyCandidates: string[]): string => {\n      for (const candidate of keyCandidates) {\n        const norm = this.normalizeHeader(candidate);\n        const actual = headerMap[norm];\n        if (actual && row.hasOwnProperty(actual)) {\n          return (row[actual] ?? '').toString();\n        }\n      }\n      return '';\n    };\n\n    // Expected columns with common variants\n    const rows = jsonRows.map((row) => {\n      return {\n        sheetNumber: pick(row, ['Sheet Number', 'SheetNumber', 'Sheet#', 'Sheet No']),\n        codeRef: pick(row, ['Code Ref', 'Code Reference', 'CodeRef']),\n        codeDescription: pick(row, ['Code Description', 'Description']),\n        reasoning: pick(row, ['Reasoning', 'Reason']),\n        nonCompliance: pick(row, ['Non Compliance', 'Non-Compliance', 'NonCompliance']),\n        actionableStep: pick(row, ['Actionable Step', 'ActionableStep', 'Action Step'])\n      };\n    });\n    // Filter out completely empty rows\n    return rows.filter((r) => Object.values(r).some((v) => (v || '').toString().trim() !== ''));\n  }\n\n  deleteImportedRow(index: number): void {\n    if (index < 0 || index >= this.importedRows.length) { return; }\n    this.importedRows.splice(index, 1);\n  }\n\n  addImportedRow(): void {\n    this.importedRows.unshift({\n      sheetNumber: '',\n      codeRef: '',\n      codeDescription: '',\n      reasoning: '',\n      nonCompliance: '',\n      actionableStep: ''\n    });\n  }\n\n  async saveAllImported(): Promise<void> {\n    this.importSubmitted = true;\n    if (this.hasImportedInvalid) {\n      this.customLayoutUtilsService.showError('Please fill all required fields in the imported rows.', '');\n      return;\n    }\n    if (!this.permitId) {\n      this.customLayoutUtilsService.showError('Permit Id is required to save imported rows', '');\n      return;\n    }\n    if (!this.reviewCategory) {\n      this.customLayoutUtilsService.showError('Review Category is required to save imported rows', '');\n      return;\n    }\n    if (!this.importedRows.length) {\n      this.customLayoutUtilsService.showError('No imported rows to save', '');\n      return;\n    }\n    this.isLoading = true;\n    // Enable common loader\n    this.httpUtilsService.loadingSubject.next(true);\n    try {\n      const requests = this.importedRows.map((r) => {\n        const payload: any = {\n          sheetNumber: r.sheetNumber || '',\n          codeRef: r.codeRef || '',\n          codeDescription: r.codeDescription || '',\n          reasoning: r.reasoning || '',\n          nonCompliance: r.nonCompliance || '',\n          actionableStep: r.actionableStep || '',\n          permitId: this.permitId,\n          permitNumber: this.permitNumber,\n          reviewCategory: this.reviewCategory,\n          internalCommentsId: this.internalCommentsId,\n          loggedInUserId: this.loggedInUserId\n        };\n        return this.permitsService.addInternalPlanReviewDetail(payload);\n      });\n      // Execute all in parallel\n      await new Promise((resolve, reject) => {\n        const { forkJoin } = require('rxjs');\n        forkJoin(requests).subscribe({ next: resolve, error: reject });\n      });\n      this.customLayoutUtilsService.showSuccess('All imported rows saved successfully!', '');\n      this.importedRows = [];\n      this.importSubmitted = false;\n      // Optionally close modal or refresh parent via close value\n      this.modal.close('bulk-created');\n    } catch (err) {\n      console.error('Error saving imported rows', err);\n      this.customLayoutUtilsService.showError('Failed to save one or more imported rows', '');\n    } finally {\n      this.isLoading = false;\n      // Disable common loader\n      this.httpUtilsService.loadingSubject.next(false);\n    }\n  }\n\n  get hasImportedInvalid(): boolean {\n    if (!this.importedRows || !this.importedRows.length) { return false; }\n    return this.importedRows.some((r) => !this.isImportedRowValid(r));\n  }\n\n  private isImportedRowValid(r: any): boolean {\n    return !!(\n      (r.sheetNumber || '').toString().trim() &&\n      (r.codeRef || '').toString().trim() &&\n      (r.codeDescription || '').toString().trim() &&\n      (r.reasoning || '').toString().trim()\n    );\n  }\n\n  get isFormValid(): boolean {\n    return this.detailForm.valid;\n  }\n\n  get isDetailsValid(): boolean {\n    if (!this.detailForm) { return false; }\n    const controls = this.detailForm.controls as any;\n    return (\n      !!controls.sheetNumber?.valid &&\n      !!controls.codeRef?.valid &&\n      !!controls.codeDescription?.valid &&\n      !!controls.reasoning?.valid\n    );\n  }\n}\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,KAAK,QAAgB,eAAe;AACxD,SAAiCC,UAAU,QAAQ,gBAAgB;AAW5D,IAAMC,oCAAoC,GAA1C,MAAMA,oCAAoC;EAwBrCC,EAAA;EACDC,KAAA;EACCC,cAAA;EACAC,wBAAA;EACAC,gBAAA;EA3BDC,QAAQ,GAAkB,IAAI;EAC9BC,YAAY,GAAW,EAAE;EACzBC,cAAc,GAAW,EAAE;EAC3BC,kBAAkB,GAAkB,IAAI;EACxCC,UAAU,GAAQ,IAAI,CAAC,CAAC;EACxBC,cAAc,GAAW,MAAM,CAAC,CAAC;EAE1CC,UAAU;EACVC,MAAM,GAAY,KAAK;EACvBC,SAAS,GAAY,KAAK;EAC1B;EACAC,aAAa,GAAY,KAAK;EAC9BC,QAAQ,GAAY,KAAK;EACzBC,gBAAgB,GAAY,IAAI,CAAC,CAAC;EAClCC,sBAAsB,GAAY,KAAK,CAAC,CAAC;EACzCC,eAAe,GAAW,kCAAkC;EAC5DC,YAAY,GAAe,EAAE;EAC7BC,WAAW,GAAY,KAAK;EAC5BC,eAAe,GAAY,KAAK;EAChC;EACAC,SAAS,GAA2B,SAAS;EAE7CC,YACUvB,EAAe,EAChBC,KAAqB,EACpBC,cAA8B,EAC9BC,wBAAkD,EAClDC,gBAAkC;IAJlC,KAAAJ,EAAE,GAAFA,EAAE;IACH,KAAAC,KAAK,GAALA,KAAK;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,gBAAgB,GAAhBA,gBAAgB;EACvB;EAEHoB,QAAQA,CAAA;IACN,IAAI,CAACZ,MAAM,GAAG,CAAC,CAAC,IAAI,CAACH,UAAU;IAC/B,IAAI,CAACE,UAAU,GAAG,IAAI,CAACX,EAAE,CAACyB,KAAK,CAAC;MAC9BC,WAAW,EAAE,CAAC,IAAI,CAACjB,UAAU,EAAEiB,WAAW,IAAI,EAAE,EAAE5B,UAAU,CAAC6B,QAAQ,CAAC;MACtEC,OAAO,EAAE,CAAC,IAAI,CAACnB,UAAU,EAAEmB,OAAO,IAAI,EAAE,EAAE9B,UAAU,CAAC6B,QAAQ,CAAC;MAC9DE,eAAe,EAAE,CAAC,IAAI,CAACpB,UAAU,EAAEoB,eAAe,IAAI,EAAE,EAAE/B,UAAU,CAAC6B,QAAQ,CAAC;MAC9EG,SAAS,EAAE,CAAC,IAAI,CAACrB,UAAU,EAAEqB,SAAS,IAAI,EAAE,EAAEhC,UAAU,CAAC6B,QAAQ,CAAC;MAClEI,aAAa,EAAE,CAAC,IAAI,CAACtB,UAAU,EAAEsB,aAAa,IAAI,EAAE,CAAC;MACrDC,cAAc,EAAE,CAAC,IAAI,CAACvB,UAAU,EAAEuB,cAAc,IAAI,EAAE,CAAC;MACvDC,UAAU,EAAE,CAAC,IAAI,CAACxB,UAAU,EAAEwB,UAAU,IAAI,EAAE,CAAC;MAC/CC,kBAAkB,EAAE,CAAC,IAAI,CAACzB,UAAU,EAAEyB,kBAAkB,IAAI,EAAE;KAC/D,CAAC;EACJ;EAEA;EAEAC,yBAAyBA,CAACC,SAAiB;IACzC;IACA,IAAI,CAAC,IAAI,CAACtB,aAAa,EAAE;MACvB,OAAO,KAAK;IACd;IAEA,MAAMuB,KAAK,GAAG,IAAI,CAAC1B,UAAU,CAAC2B,GAAG,CAACF,SAAS,CAAC;IAC5C,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAACE,OAAO,CAAC;EACnC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC1B,aAAa,GAAG,IAAI;IACzB,IAAI,IAAI,CAACH,UAAU,CAAC8B,KAAK,IAAI,IAAI,CAACpC,QAAQ,EAAE;MAC1C,IAAI,CAACQ,SAAS,GAAG,IAAI;MACrB;MACA,IAAI,CAACT,gBAAgB,CAACsC,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;MAE/C,MAAMC,QAAQ,GAAQ;QACpB,GAAG,IAAI,CAACjC,UAAU,CAACkC,KAAK;QACxBxC,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBC,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BC,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,IAAI,IAAI,CAACC,UAAU,EAAED,kBAAkB;QAClFE,cAAc,EAAE,IAAI,CAACA;OACtB;MACD,IAAI,IAAI,CAACE,MAAM,IAAI,IAAI,CAACH,UAAU,EAAEqC,wBAAwB,EAAE;QAC5DF,QAAQ,CAACE,wBAAwB,GAAG,IAAI,CAACrC,UAAU,CAACqC,wBAAwB;QAC5E,IAAI,CAAC5C,cAAc,CAAC6C,8BAA8B,CAACH,QAAQ,CAAC,CAACI,SAAS,CAAC;UACrEL,IAAI,EAAGM,GAAQ,IAAI;YACjB,IAAI,CAACpC,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAACT,gBAAgB,CAACsC,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAIM,GAAG,EAAEC,OAAO,EAAE;cAChB,IAAI,CAAC/C,wBAAwB,CAACgD,SAAS,CAACF,GAAG,CAACG,YAAY,IAAI,gCAAgC,EAAE,EAAE,CAAC;YACnG,CAAC,MAAM;cACL,IAAI,CAACjD,wBAAwB,CAACkD,WAAW,CAACJ,GAAG,CAACK,YAAY,EAAEC,OAAO,IAAI,qCAAqC,EAAE,EAAE,CAAC;cACjH,IAAI,CAACtD,KAAK,CAACuD,KAAK,CAAC,SAAS,CAAC;YAC7B;UACF,CAAC;UACDC,KAAK,EAAGC,GAAQ,IAAI;YAClB,IAAI,CAAC7C,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAACT,gBAAgB,CAACsC,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAI,CAACxC,wBAAwB,CAACgD,SAAS,CAAC,8BAA8B,EAAE,EAAE,CAAC;YAC3EQ,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;UACpB;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACxD,cAAc,CAAC0D,2BAA2B,CAAChB,QAAQ,CAAC,CAACI,SAAS,CAAC;UAClEL,IAAI,EAAGM,GAAQ,IAAI;YACjB,IAAI,CAACpC,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAACT,gBAAgB,CAACsC,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAIM,GAAG,EAAEC,OAAO,EAAE;cAChB,IAAI,CAAC/C,wBAAwB,CAACgD,SAAS,CAACF,GAAG,CAACG,YAAY,IAAI,gCAAgC,EAAE,EAAE,CAAC;YACnG,CAAC,MAAM;cACL,IAAI,CAACjD,wBAAwB,CAACkD,WAAW,CAAC,qCAAqC,EAAE,EAAE,CAAC;cACpF,IAAI,CAACpD,KAAK,CAACuD,KAAK,CAAC,SAAS,CAAC;YAC7B;UACF,CAAC;UACDC,KAAK,EAAGC,GAAQ,IAAI;YAClB,IAAI,CAAC7C,SAAS,GAAG,KAAK;YACtB;YACA,IAAI,CAACT,gBAAgB,CAACsC,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAChD,IAAI,CAACxC,wBAAwB,CAACgD,SAAS,CAAC,8BAA8B,EAAE,EAAE,CAAC;YAC3EQ,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;UACpB;SACD,CAAC;MACJ;IACF,CAAC,MAAM;MACL,IAAI,CAAC/C,UAAU,CAACkD,gBAAgB,EAAE;MAClC,IAAI,CAAC,IAAI,CAACxD,QAAQ,EAAE;QAClB,IAAI,CAACF,wBAAwB,CAACgD,SAAS,CAAC,uBAAuB,EAAE,EAAE,CAAC;MACtE;IACF;EACF;EAEAW,QAAQA,CAAA;IACN,IAAI,CAAC7D,KAAK,CAAC8D,OAAO,CAAC,WAAW,CAAC;EACjC;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAAC/D,KAAK,CAAC8D,OAAO,CAAC,MAAM,CAAC;EAC5B;EAEAE,gBAAgBA,CAAA;IACd,IAAI,CAACnD,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,sBAAsB,GAAG,KAAK;EACrC;EAEAiD,aAAaA,CAAA;IACX,IAAI,IAAI,CAACvD,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACwD,KAAK,CAAC;QACpBzC,WAAW,EAAE,EAAE;QACfE,OAAO,EAAE,EAAE;QACXC,eAAe,EAAE,EAAE;QACnBC,SAAS,EAAE,EAAE;QACbC,aAAa,EAAE,EAAE;QACjBC,cAAc,EAAE;OACjB,CAAC;IACJ;IACA,IAAI,CAAClB,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,sBAAsB,GAAG,KAAK;EACrC;EAEAmD,YAAYA,CAACC,GAA2B;IACtC,IAAI,CAAC/C,SAAS,GAAG+C,GAAG;EACtB;EAEAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAAChD,SAAS,KAAK,UAAU,EAAE;MACjC,IAAI,CAACA,SAAS,GAAG,SAAS;IAC5B;EACF;EAEAiD,iBAAiBA,CAACC,SAA2B;IAC3C,IAAI,IAAI,CAAC3D,SAAS,EAAE;MAAE;IAAQ;IAC9B2D,SAAS,CAACC,KAAK,EAAE;EACnB;EAEAC,cAAcA,CAACC,KAAY;IAAA,IAAAC,KAAA;IACzB,MAAMC,KAAK,GAAGF,KAAK,CAACG,MAA0B;IAC9C,MAAMC,IAAI,GAAGF,KAAK,EAAEG,KAAK,IAAIH,KAAK,CAACG,KAAK,CAACC,MAAM,GAAGJ,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;IACvE,IAAI,CAACD,IAAI,EAAE;MAAE;IAAQ;IACrB,IAAI,CAAC3D,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAAChB,gBAAgB,CAACsC,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC/C,MAAMuC,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,gBAAAC,iBAAA,CAAG,aAAW;MACzB,IAAI;QACF,MAAMC,IAAI,SAAc,MAAM,CAAC,MAAM,CAAC;QACtC,MAAMC,IAAI,GAAG,IAAIC,UAAU,CAACN,MAAM,CAACO,MAAqB,CAAC;QACzD,MAAMC,QAAQ,GAAGJ,IAAI,CAACK,IAAI,CAACJ,IAAI,EAAE;UAAEK,IAAI,EAAE;QAAO,CAAE,CAAC;QACnD,MAAMC,cAAc,GAAGH,QAAQ,CAACI,UAAU,CAAC,CAAC,CAAC;QAC7C,MAAMC,SAAS,GAAGL,QAAQ,CAACM,MAAM,CAACH,cAAc,CAAC;QACjD,MAAMI,IAAI,GAAUX,IAAI,CAACY,KAAK,CAACC,aAAa,CAACJ,SAAS,EAAE;UAAEK,MAAM,EAAE;QAAE,CAAE,CAAC;QACvExB,KAAI,CAACzD,YAAY,GAAGyD,KAAI,CAACyB,YAAY,CAACJ,IAAI,CAAC;QAC3C;QACArB,KAAI,CAAC5D,gBAAgB,GAAG,KAAK;QAC7B4D,KAAI,CAAC7D,QAAQ,GAAG,KAAK;QACrB6D,KAAI,CAAC3D,sBAAsB,GAAG2D,KAAI,CAACzD,YAAY,CAAC8D,MAAM,GAAG,CAAC;QAC1D,IAAI,CAACL,KAAI,CAACzD,YAAY,CAAC8D,MAAM,EAAE;UAC7BL,KAAI,CAACzE,wBAAwB,CAACgD,SAAS,CAAC,sCAAsC,EAAE,EAAE,CAAC;QACrF,CAAC,MAAM;UACLyB,KAAI,CAACzE,wBAAwB,CAACkD,WAAW,CAAC,YAAYuB,KAAI,CAACzD,YAAY,CAAC8D,MAAM,UAAU,EAAE,EAAE,CAAC;QAC/F;MACF,CAAC,CAAC,OAAOvB,GAAG,EAAE;QACZC,OAAO,CAACF,KAAK,CAAC,qBAAqB,EAAEC,GAAG,CAAC;QACzCkB,KAAI,CAACzD,YAAY,GAAG,EAAE;QACtByD,KAAI,CAACzE,wBAAwB,CAACgD,SAAS,CAAC,wDAAwD,EAAE,EAAE,CAAC;MACvG,CAAC,SAAS;QACRyB,KAAI,CAACxD,WAAW,GAAG,KAAK;QACxB;QACAwD,KAAI,CAACxE,gBAAgB,CAACsC,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAChD;QACAkC,KAAK,CAAChC,KAAK,GAAG,EAAE;MAClB;IACF,CAAC;IACDqC,MAAM,CAACoB,iBAAiB,CAACvB,IAAI,CAAC;EAChC;EAEAwB,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACtF,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACD,gBAAgB,GAAG,IAAI;EAC9B;EAEQwF,eAAeA,CAACC,MAAc;IACpC,OAAO,CAACA,MAAM,IAAI,EAAE,EAAEC,QAAQ,EAAE,CAACC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;EAC3E;EAEQR,YAAYA,CAACS,QAAe;IAClC,IAAI,CAACA,QAAQ,IAAI,CAACA,QAAQ,CAAC7B,MAAM,EAAE;MAAE,OAAO,EAAE;IAAE;IAChD,MAAM8B,SAAS,GAAQ,EAAE;IACzB;IACA,MAAMC,QAAQ,GAAGF,QAAQ,CAAC,CAAC,CAAC;IAC5BG,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,OAAO,CAAEC,GAAG,IAAI;MACpC,MAAMC,IAAI,GAAG,IAAI,CAACb,eAAe,CAACY,GAAG,CAAC;MACtCL,SAAS,CAACM,IAAI,CAAC,GAAGD,GAAG;IACvB,CAAC,CAAC;IAEF,MAAME,IAAI,GAAGA,CAACC,GAAQ,EAAEC,aAAuB,KAAY;MACzD,KAAK,MAAMC,SAAS,IAAID,aAAa,EAAE;QACrC,MAAMH,IAAI,GAAG,IAAI,CAACb,eAAe,CAACiB,SAAS,CAAC;QAC5C,MAAMC,MAAM,GAAGX,SAAS,CAACM,IAAI,CAAC;QAC9B,IAAIK,MAAM,IAAIH,GAAG,CAACI,cAAc,CAACD,MAAM,CAAC,EAAE;UACxC,OAAO,CAACH,GAAG,CAACG,MAAM,CAAC,IAAI,EAAE,EAAEhB,QAAQ,EAAE;QACvC;MACF;MACA,OAAO,EAAE;IACX,CAAC;IAED;IACA,MAAMkB,IAAI,GAAGd,QAAQ,CAACe,GAAG,CAAEN,GAAG,IAAI;MAChC,OAAO;QACL7F,WAAW,EAAE4F,IAAI,CAACC,GAAG,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QAC7E3F,OAAO,EAAE0F,IAAI,CAACC,GAAG,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAC7D1F,eAAe,EAAEyF,IAAI,CAACC,GAAG,EAAE,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;QAC/DzF,SAAS,EAAEwF,IAAI,CAACC,GAAG,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAC7CxF,aAAa,EAAEuF,IAAI,CAACC,GAAG,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;QAC/EvF,cAAc,EAAEsF,IAAI,CAACC,GAAG,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,aAAa,CAAC;OAC/E;IACH,CAAC,CAAC;IACF;IACA,OAAOK,IAAI,CAACE,MAAM,CAAEC,CAAC,IAAKd,MAAM,CAACe,MAAM,CAACD,CAAC,CAAC,CAACE,IAAI,CAAEC,CAAC,IAAK,CAACA,CAAC,IAAI,EAAE,EAAExB,QAAQ,EAAE,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;EAC7F;EAEAwB,iBAAiBA,CAACC,KAAa;IAC7B,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACjH,YAAY,CAAC8D,MAAM,EAAE;MAAE;IAAQ;IAC9D,IAAI,CAAC9D,YAAY,CAACkH,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;EACpC;EAEAE,cAAcA,CAAA;IACZ,IAAI,CAACnH,YAAY,CAACoH,OAAO,CAAC;MACxB7G,WAAW,EAAE,EAAE;MACfE,OAAO,EAAE,EAAE;MACXC,eAAe,EAAE,EAAE;MACnBC,SAAS,EAAE,EAAE;MACbC,aAAa,EAAE,EAAE;MACjBC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEMwG,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAApD,iBAAA;MACnBoD,MAAI,CAACpH,eAAe,GAAG,IAAI;MAC3B,IAAIoH,MAAI,CAACC,kBAAkB,EAAE;QAC3BD,MAAI,CAACtI,wBAAwB,CAACgD,SAAS,CAAC,uDAAuD,EAAE,EAAE,CAAC;QACpG;MACF;MACA,IAAI,CAACsF,MAAI,CAACpI,QAAQ,EAAE;QAClBoI,MAAI,CAACtI,wBAAwB,CAACgD,SAAS,CAAC,6CAA6C,EAAE,EAAE,CAAC;QAC1F;MACF;MACA,IAAI,CAACsF,MAAI,CAAClI,cAAc,EAAE;QACxBkI,MAAI,CAACtI,wBAAwB,CAACgD,SAAS,CAAC,mDAAmD,EAAE,EAAE,CAAC;QAChG;MACF;MACA,IAAI,CAACsF,MAAI,CAACtH,YAAY,CAAC8D,MAAM,EAAE;QAC7BwD,MAAI,CAACtI,wBAAwB,CAACgD,SAAS,CAAC,0BAA0B,EAAE,EAAE,CAAC;QACvE;MACF;MACAsF,MAAI,CAAC5H,SAAS,GAAG,IAAI;MACrB;MACA4H,MAAI,CAACrI,gBAAgB,CAACsC,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;MAC/C,IAAI;QACF,MAAMgG,QAAQ,GAAGF,MAAI,CAACtH,YAAY,CAAC0G,GAAG,CAAEE,CAAC,IAAI;UAC3C,MAAMa,OAAO,GAAQ;YACnBlH,WAAW,EAAEqG,CAAC,CAACrG,WAAW,IAAI,EAAE;YAChCE,OAAO,EAAEmG,CAAC,CAACnG,OAAO,IAAI,EAAE;YACxBC,eAAe,EAAEkG,CAAC,CAAClG,eAAe,IAAI,EAAE;YACxCC,SAAS,EAAEiG,CAAC,CAACjG,SAAS,IAAI,EAAE;YAC5BC,aAAa,EAAEgG,CAAC,CAAChG,aAAa,IAAI,EAAE;YACpCC,cAAc,EAAE+F,CAAC,CAAC/F,cAAc,IAAI,EAAE;YACtC3B,QAAQ,EAAEoI,MAAI,CAACpI,QAAQ;YACvBC,YAAY,EAAEmI,MAAI,CAACnI,YAAY;YAC/BC,cAAc,EAAEkI,MAAI,CAAClI,cAAc;YACnCC,kBAAkB,EAAEiI,MAAI,CAACjI,kBAAkB;YAC3CE,cAAc,EAAE+H,MAAI,CAAC/H;WACtB;UACD,OAAO+H,MAAI,CAACvI,cAAc,CAAC0D,2BAA2B,CAACgF,OAAO,CAAC;QACjE,CAAC,CAAC;QACF;QACA,MAAM,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;UACpC,MAAM;YAAEC;UAAQ,CAAE,GAAGC,OAAO,CAAC,MAAM,CAAC;UACpCD,QAAQ,CAACL,QAAQ,CAAC,CAAC3F,SAAS,CAAC;YAAEL,IAAI,EAAEmG,OAAO;YAAErF,KAAK,EAAEsF;UAAM,CAAE,CAAC;QAChE,CAAC,CAAC;QACFN,MAAI,CAACtI,wBAAwB,CAACkD,WAAW,CAAC,uCAAuC,EAAE,EAAE,CAAC;QACtFoF,MAAI,CAACtH,YAAY,GAAG,EAAE;QACtBsH,MAAI,CAACpH,eAAe,GAAG,KAAK;QAC5B;QACAoH,MAAI,CAACxI,KAAK,CAACuD,KAAK,CAAC,cAAc,CAAC;MAClC,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZC,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEC,GAAG,CAAC;QAChD+E,MAAI,CAACtI,wBAAwB,CAACgD,SAAS,CAAC,0CAA0C,EAAE,EAAE,CAAC;MACzF,CAAC,SAAS;QACRsF,MAAI,CAAC5H,SAAS,GAAG,KAAK;QACtB;QACA4H,MAAI,CAACrI,gBAAgB,CAACsC,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MAClD;IAAC;EACH;EAEA,IAAI+F,kBAAkBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACvH,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAAC8D,MAAM,EAAE;MAAE,OAAO,KAAK;IAAE;IACrE,OAAO,IAAI,CAAC9D,YAAY,CAAC8G,IAAI,CAAEF,CAAC,IAAK,CAAC,IAAI,CAACmB,kBAAkB,CAACnB,CAAC,CAAC,CAAC;EACnE;EAEQmB,kBAAkBA,CAACnB,CAAM;IAC/B,OAAO,CAAC,EACN,CAACA,CAAC,CAACrG,WAAW,IAAI,EAAE,EAAEgF,QAAQ,EAAE,CAACC,IAAI,EAAE,IACvC,CAACoB,CAAC,CAACnG,OAAO,IAAI,EAAE,EAAE8E,QAAQ,EAAE,CAACC,IAAI,EAAE,IACnC,CAACoB,CAAC,CAAClG,eAAe,IAAI,EAAE,EAAE6E,QAAQ,EAAE,CAACC,IAAI,EAAE,IAC3C,CAACoB,CAAC,CAACjG,SAAS,IAAI,EAAE,EAAE4E,QAAQ,EAAE,CAACC,IAAI,EAAE,CACtC;EACH;EAEA,IAAIwC,WAAWA,CAAA;IACb,OAAO,IAAI,CAACxI,UAAU,CAAC8B,KAAK;EAC9B;EAEA,IAAI2G,cAAcA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACzI,UAAU,EAAE;MAAE,OAAO,KAAK;IAAE;IACtC,MAAM0I,QAAQ,GAAG,IAAI,CAAC1I,UAAU,CAAC0I,QAAe;IAChD,OACE,CAAC,CAACA,QAAQ,CAAC3H,WAAW,EAAEe,KAAK,IAC7B,CAAC,CAAC4G,QAAQ,CAACzH,OAAO,EAAEa,KAAK,IACzB,CAAC,CAAC4G,QAAQ,CAACxH,eAAe,EAAEY,KAAK,IACjC,CAAC,CAAC4G,QAAQ,CAACvH,SAAS,EAAEW,KAAK;EAE/B;CACD;AAvWU6G,UAAA,EAARzJ,KAAK,EAAE,C,qEAAgC;AAC/ByJ,UAAA,EAARzJ,KAAK,EAAE,C,yEAA2B;AAC1ByJ,UAAA,EAARzJ,KAAK,EAAE,C,2EAA6B;AAC5ByJ,UAAA,EAARzJ,KAAK,EAAE,C,+EAA0C;AACzCyJ,UAAA,EAARzJ,KAAK,EAAE,C,uEAAwB;AACvByJ,UAAA,EAARzJ,KAAK,EAAE,C,2EAAiC;AAN9BE,oCAAoC,GAAAuJ,UAAA,EALhD1J,SAAS,CAAC;EACT2J,QAAQ,EAAE,qCAAqC;EAC/CC,WAAW,EAAE,kDAAkD;EAC/DC,SAAS,EAAE,CAAC,kDAAkD;CAC/D,CAAC,C,EACW1J,oCAAoC,CAwWhD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}