{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.skipLast = void 0;\nvar identity_1 = require(\"../util/identity\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction skipLast(skipCount) {\n  return skipCount <= 0 ? identity_1.identity : lift_1.operate(function (source, subscriber) {\n    var ring = new Array(skipCount);\n    var seen = 0;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var valueIndex = seen++;\n      if (valueIndex < skipCount) {\n        ring[valueIndex] = value;\n      } else {\n        var index = valueIndex % skipCount;\n        var oldValue = ring[index];\n        ring[index] = value;\n        subscriber.next(oldValue);\n      }\n    }));\n    return function () {\n      ring = null;\n    };\n  });\n}\nexports.skipLast = skipLast;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "skipLast", "identity_1", "require", "lift_1", "OperatorSubscriber_1", "skip<PERSON><PERSON>nt", "identity", "operate", "source", "subscriber", "ring", "Array", "seen", "subscribe", "createOperatorSubscriber", "valueIndex", "index", "oldValue", "next"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/skipLast.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.skipLast = void 0;\nvar identity_1 = require(\"../util/identity\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction skipLast(skipCount) {\n    return skipCount <= 0\n        ?\n            identity_1.identity\n        : lift_1.operate(function (source, subscriber) {\n            var ring = new Array(skipCount);\n            var seen = 0;\n            source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n                var valueIndex = seen++;\n                if (valueIndex < skipCount) {\n                    ring[valueIndex] = value;\n                }\n                else {\n                    var index = valueIndex % skipCount;\n                    var oldValue = ring[index];\n                    ring[index] = value;\n                    subscriber.next(oldValue);\n                }\n            }));\n            return function () {\n                ring = null;\n            };\n        });\n}\nexports.skipLast = skipLast;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,QAAQ,GAAG,KAAK,CAAC;AACzB,IAAIC,UAAU,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC5C,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,QAAQA,CAACK,SAAS,EAAE;EACzB,OAAOA,SAAS,IAAI,CAAC,GAEbJ,UAAU,CAACK,QAAQ,GACrBH,MAAM,CAACI,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAC3C,IAAIC,IAAI,GAAG,IAAIC,KAAK,CAACN,SAAS,CAAC;IAC/B,IAAIO,IAAI,GAAG,CAAC;IACZJ,MAAM,CAACK,SAAS,CAACT,oBAAoB,CAACU,wBAAwB,CAACL,UAAU,EAAE,UAAUV,KAAK,EAAE;MACxF,IAAIgB,UAAU,GAAGH,IAAI,EAAE;MACvB,IAAIG,UAAU,GAAGV,SAAS,EAAE;QACxBK,IAAI,CAACK,UAAU,CAAC,GAAGhB,KAAK;MAC5B,CAAC,MACI;QACD,IAAIiB,KAAK,GAAGD,UAAU,GAAGV,SAAS;QAClC,IAAIY,QAAQ,GAAGP,IAAI,CAACM,KAAK,CAAC;QAC1BN,IAAI,CAACM,KAAK,CAAC,GAAGjB,KAAK;QACnBU,UAAU,CAACS,IAAI,CAACD,QAAQ,CAAC;MAC7B;IACJ,CAAC,CAAC,CAAC;IACH,OAAO,YAAY;MACfP,IAAI,GAAG,IAAI;IACf,CAAC;EACL,CAAC,CAAC;AACV;AACAZ,OAAO,CAACE,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}