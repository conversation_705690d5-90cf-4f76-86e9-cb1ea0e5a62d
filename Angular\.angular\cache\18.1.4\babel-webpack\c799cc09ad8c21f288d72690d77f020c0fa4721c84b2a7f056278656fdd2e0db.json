{"ast": null, "code": "\"use strict\";\n\nvar __generator = this && this.__generator || function (thisArg, body) {\n  var _ = {\n      label: 0,\n      sent: function () {\n        if (t[0] & 1) throw t[1];\n        return t[1];\n      },\n      trys: [],\n      ops: []\n    },\n    f,\n    y,\n    t,\n    g;\n  return g = {\n    next: verb(0),\n    \"throw\": verb(1),\n    \"return\": verb(2)\n  }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function () {\n    return this;\n  }), g;\n  function verb(n) {\n    return function (v) {\n      return step([n, v]);\n    };\n  }\n  function step(op) {\n    if (f) throw new TypeError(\"Generator is already executing.\");\n    while (_) try {\n      if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n      if (y = 0, t) op = [op[0] & 2, t.value];\n      switch (op[0]) {\n        case 0:\n        case 1:\n          t = op;\n          break;\n        case 4:\n          _.label++;\n          return {\n            value: op[1],\n            done: false\n          };\n        case 5:\n          _.label++;\n          y = op[1];\n          op = [0];\n          continue;\n        case 7:\n          op = _.ops.pop();\n          _.trys.pop();\n          continue;\n        default:\n          if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {\n            _ = 0;\n            continue;\n          }\n          if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {\n            _.label = op[1];\n            break;\n          }\n          if (op[0] === 6 && _.label < t[1]) {\n            _.label = t[1];\n            t = op;\n            break;\n          }\n          if (t && _.label < t[2]) {\n            _.label = t[2];\n            _.ops.push(op);\n            break;\n          }\n          if (t[2]) _.ops.pop();\n          _.trys.pop();\n          continue;\n      }\n      op = body.call(thisArg, _);\n    } catch (e) {\n      op = [6, e];\n      y = 0;\n    } finally {\n      f = t = 0;\n    }\n    if (op[0] & 5) throw op[1];\n    return {\n      value: op[0] ? op[1] : void 0,\n      done: true\n    };\n  }\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.generate = void 0;\nvar identity_1 = require(\"../util/identity\");\nvar isScheduler_1 = require(\"../util/isScheduler\");\nvar defer_1 = require(\"./defer\");\nvar scheduleIterable_1 = require(\"../scheduled/scheduleIterable\");\nfunction generate(initialStateOrOptions, condition, iterate, resultSelectorOrScheduler, scheduler) {\n  var _a, _b;\n  var resultSelector;\n  var initialState;\n  if (arguments.length === 1) {\n    _a = initialStateOrOptions, initialState = _a.initialState, condition = _a.condition, iterate = _a.iterate, _b = _a.resultSelector, resultSelector = _b === void 0 ? identity_1.identity : _b, scheduler = _a.scheduler;\n  } else {\n    initialState = initialStateOrOptions;\n    if (!resultSelectorOrScheduler || isScheduler_1.isScheduler(resultSelectorOrScheduler)) {\n      resultSelector = identity_1.identity;\n      scheduler = resultSelectorOrScheduler;\n    } else {\n      resultSelector = resultSelectorOrScheduler;\n    }\n  }\n  function gen() {\n    var state;\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          state = initialState;\n          _a.label = 1;\n        case 1:\n          if (!(!condition || condition(state))) return [3, 4];\n          return [4, resultSelector(state)];\n        case 2:\n          _a.sent();\n          _a.label = 3;\n        case 3:\n          state = iterate(state);\n          return [3, 1];\n        case 4:\n          return [2];\n      }\n    });\n  }\n  return defer_1.defer(scheduler ? function () {\n    return scheduleIterable_1.scheduleIterable(gen(), scheduler);\n  } : gen);\n}\nexports.generate = generate;", "map": {"version": 3, "names": ["__generator", "thisArg", "body", "_", "label", "sent", "t", "trys", "ops", "f", "y", "g", "next", "verb", "Symbol", "iterator", "n", "v", "step", "op", "TypeError", "call", "done", "value", "pop", "length", "push", "e", "Object", "defineProperty", "exports", "generate", "identity_1", "require", "isScheduler_1", "defer_1", "scheduleIterable_1", "initialStateOrOptions", "condition", "iterate", "resultSelectorOrScheduler", "scheduler", "_a", "_b", "resultSelector", "initialState", "arguments", "identity", "isScheduler", "gen", "state", "defer", "scheduleIterable"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/generate.js"], "sourcesContent": ["\"use strict\";\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.generate = void 0;\nvar identity_1 = require(\"../util/identity\");\nvar isScheduler_1 = require(\"../util/isScheduler\");\nvar defer_1 = require(\"./defer\");\nvar scheduleIterable_1 = require(\"../scheduled/scheduleIterable\");\nfunction generate(initialStateOrOptions, condition, iterate, resultSelectorOrScheduler, scheduler) {\n    var _a, _b;\n    var resultSelector;\n    var initialState;\n    if (arguments.length === 1) {\n        (_a = initialStateOrOptions, initialState = _a.initialState, condition = _a.condition, iterate = _a.iterate, _b = _a.resultSelector, resultSelector = _b === void 0 ? identity_1.identity : _b, scheduler = _a.scheduler);\n    }\n    else {\n        initialState = initialStateOrOptions;\n        if (!resultSelectorOrScheduler || isScheduler_1.isScheduler(resultSelectorOrScheduler)) {\n            resultSelector = identity_1.identity;\n            scheduler = resultSelectorOrScheduler;\n        }\n        else {\n            resultSelector = resultSelectorOrScheduler;\n        }\n    }\n    function gen() {\n        var state;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    state = initialState;\n                    _a.label = 1;\n                case 1:\n                    if (!(!condition || condition(state))) return [3, 4];\n                    return [4, resultSelector(state)];\n                case 2:\n                    _a.sent();\n                    _a.label = 3;\n                case 3:\n                    state = iterate(state);\n                    return [3, 1];\n                case 4: return [2];\n            }\n        });\n    }\n    return defer_1.defer((scheduler\n        ?\n            function () { return scheduleIterable_1.scheduleIterable(gen(), scheduler); }\n        :\n            gen));\n}\nexports.generate = generate;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAI,IAAI,IAAI,IAAI,CAACA,WAAW,IAAK,UAAUC,OAAO,EAAEC,IAAI,EAAE;EACrE,IAAIC,CAAC,GAAG;MAAEC,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE,SAAAA,CAAA,EAAW;QAAE,IAAIC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAMA,CAAC,CAAC,CAAC,CAAC;QAAE,OAAOA,CAAC,CAAC,CAAC,CAAC;MAAE,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAG,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEJ,CAAC;IAAEK,CAAC;EAChH,OAAOA,CAAC,GAAG;IAAEC,IAAI,EAAEC,IAAI,CAAC,CAAC,CAAC;IAAE,OAAO,EAAEA,IAAI,CAAC,CAAC,CAAC;IAAE,QAAQ,EAAEA,IAAI,CAAC,CAAC;EAAE,CAAC,EAAE,OAAOC,MAAM,KAAK,UAAU,KAAKH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,GAAG,YAAW;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,EAAEJ,CAAC;EACxJ,SAASE,IAAIA,CAACG,CAAC,EAAE;IAAE,OAAO,UAAUC,CAAC,EAAE;MAAE,OAAOC,IAAI,CAAC,CAACF,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC;EAAE;EACjE,SAASC,IAAIA,CAACC,EAAE,EAAE;IACd,IAAIV,CAAC,EAAE,MAAM,IAAIW,SAAS,CAAC,iCAAiC,CAAC;IAC7D,OAAOjB,CAAC,EAAE,IAAI;MACV,IAAIM,CAAC,GAAG,CAAC,EAAEC,CAAC,KAAKJ,CAAC,GAAGa,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGT,CAAC,CAAC,QAAQ,CAAC,GAAGS,EAAE,CAAC,CAAC,CAAC,GAAGT,CAAC,CAAC,OAAO,CAAC,KAAK,CAACJ,CAAC,GAAGI,CAAC,CAAC,QAAQ,CAAC,KAAKJ,CAAC,CAACe,IAAI,CAACX,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGA,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAACN,CAAC,GAAGA,CAAC,CAACe,IAAI,CAACX,CAAC,EAAES,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEG,IAAI,EAAE,OAAOhB,CAAC;MAC5J,IAAII,CAAC,GAAG,CAAC,EAAEJ,CAAC,EAAEa,EAAE,GAAG,CAACA,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEb,CAAC,CAACiB,KAAK,CAAC;MACvC,QAAQJ,EAAE,CAAC,CAAC,CAAC;QACT,KAAK,CAAC;QAAE,KAAK,CAAC;UAAEb,CAAC,GAAGa,EAAE;UAAE;QACxB,KAAK,CAAC;UAAEhB,CAAC,CAACC,KAAK,EAAE;UAAE,OAAO;YAAEmB,KAAK,EAAEJ,EAAE,CAAC,CAAC,CAAC;YAAEG,IAAI,EAAE;UAAM,CAAC;QACvD,KAAK,CAAC;UAAEnB,CAAC,CAACC,KAAK,EAAE;UAAEM,CAAC,GAAGS,EAAE,CAAC,CAAC,CAAC;UAAEA,EAAE,GAAG,CAAC,CAAC,CAAC;UAAE;QACxC,KAAK,CAAC;UAAEA,EAAE,GAAGhB,CAAC,CAACK,GAAG,CAACgB,GAAG,CAAC,CAAC;UAAErB,CAAC,CAACI,IAAI,CAACiB,GAAG,CAAC,CAAC;UAAE;QACxC;UACI,IAAI,EAAElB,CAAC,GAAGH,CAAC,CAACI,IAAI,EAAED,CAAC,GAAGA,CAAC,CAACmB,MAAM,GAAG,CAAC,IAAInB,CAAC,CAACA,CAAC,CAACmB,MAAM,GAAG,CAAC,CAAC,CAAC,KAAKN,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YAAEhB,CAAC,GAAG,CAAC;YAAE;UAAU;UAC3G,IAAIgB,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAACb,CAAC,IAAKa,EAAE,CAAC,CAAC,CAAC,GAAGb,CAAC,CAAC,CAAC,CAAC,IAAIa,EAAE,CAAC,CAAC,CAAC,GAAGb,CAAC,CAAC,CAAC,CAAE,CAAC,EAAE;YAAEH,CAAC,CAACC,KAAK,GAAGe,EAAE,CAAC,CAAC,CAAC;YAAE;UAAO;UACrF,IAAIA,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIhB,CAAC,CAACC,KAAK,GAAGE,CAAC,CAAC,CAAC,CAAC,EAAE;YAAEH,CAAC,CAACC,KAAK,GAAGE,CAAC,CAAC,CAAC,CAAC;YAAEA,CAAC,GAAGa,EAAE;YAAE;UAAO;UACpE,IAAIb,CAAC,IAAIH,CAAC,CAACC,KAAK,GAAGE,CAAC,CAAC,CAAC,CAAC,EAAE;YAAEH,CAAC,CAACC,KAAK,GAAGE,CAAC,CAAC,CAAC,CAAC;YAAEH,CAAC,CAACK,GAAG,CAACkB,IAAI,CAACP,EAAE,CAAC;YAAE;UAAO;UAClE,IAAIb,CAAC,CAAC,CAAC,CAAC,EAAEH,CAAC,CAACK,GAAG,CAACgB,GAAG,CAAC,CAAC;UACrBrB,CAAC,CAACI,IAAI,CAACiB,GAAG,CAAC,CAAC;UAAE;MACtB;MACAL,EAAE,GAAGjB,IAAI,CAACmB,IAAI,CAACpB,OAAO,EAAEE,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOwB,CAAC,EAAE;MAAER,EAAE,GAAG,CAAC,CAAC,EAAEQ,CAAC,CAAC;MAAEjB,CAAC,GAAG,CAAC;IAAE,CAAC,SAAS;MAAED,CAAC,GAAGH,CAAC,GAAG,CAAC;IAAE;IACzD,IAAIa,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAMA,EAAE,CAAC,CAAC,CAAC;IAAE,OAAO;MAAEI,KAAK,EAAEJ,EAAE,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;MAAEG,IAAI,EAAE;IAAK,CAAC;EACpF;AACJ,CAAC;AACDM,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEP,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DO,OAAO,CAACC,QAAQ,GAAG,KAAK,CAAC;AACzB,IAAIC,UAAU,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC5C,IAAIC,aAAa,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAClD,IAAIE,OAAO,GAAGF,OAAO,CAAC,SAAS,CAAC;AAChC,IAAIG,kBAAkB,GAAGH,OAAO,CAAC,+BAA+B,CAAC;AACjE,SAASF,QAAQA,CAACM,qBAAqB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,yBAAyB,EAAEC,SAAS,EAAE;EAC/F,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAIC,cAAc;EAClB,IAAIC,YAAY;EAChB,IAAIC,SAAS,CAACrB,MAAM,KAAK,CAAC,EAAE;IACvBiB,EAAE,GAAGL,qBAAqB,EAAEQ,YAAY,GAAGH,EAAE,CAACG,YAAY,EAAEP,SAAS,GAAGI,EAAE,CAACJ,SAAS,EAAEC,OAAO,GAAGG,EAAE,CAACH,OAAO,EAAEI,EAAE,GAAGD,EAAE,CAACE,cAAc,EAAEA,cAAc,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAGX,UAAU,CAACe,QAAQ,GAAGJ,EAAE,EAAEF,SAAS,GAAGC,EAAE,CAACD,SAAS;EAC5N,CAAC,MACI;IACDI,YAAY,GAAGR,qBAAqB;IACpC,IAAI,CAACG,yBAAyB,IAAIN,aAAa,CAACc,WAAW,CAACR,yBAAyB,CAAC,EAAE;MACpFI,cAAc,GAAGZ,UAAU,CAACe,QAAQ;MACpCN,SAAS,GAAGD,yBAAyB;IACzC,CAAC,MACI;MACDI,cAAc,GAAGJ,yBAAyB;IAC9C;EACJ;EACA,SAASS,GAAGA,CAAA,EAAG;IACX,IAAIC,KAAK;IACT,OAAOlD,WAAW,CAAC,IAAI,EAAE,UAAU0C,EAAE,EAAE;MACnC,QAAQA,EAAE,CAACtC,KAAK;QACZ,KAAK,CAAC;UACF8C,KAAK,GAAGL,YAAY;UACpBH,EAAE,CAACtC,KAAK,GAAG,CAAC;QAChB,KAAK,CAAC;UACF,IAAI,EAAE,CAACkC,SAAS,IAAIA,SAAS,CAACY,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;UACpD,OAAO,CAAC,CAAC,EAAEN,cAAc,CAACM,KAAK,CAAC,CAAC;QACrC,KAAK,CAAC;UACFR,EAAE,CAACrC,IAAI,CAAC,CAAC;UACTqC,EAAE,CAACtC,KAAK,GAAG,CAAC;QAChB,KAAK,CAAC;UACF8C,KAAK,GAAGX,OAAO,CAACW,KAAK,CAAC;UACtB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;QACjB,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,CAAC;MACtB;IACJ,CAAC,CAAC;EACN;EACA,OAAOf,OAAO,CAACgB,KAAK,CAAEV,SAAS,GAEvB,YAAY;IAAE,OAAOL,kBAAkB,CAACgB,gBAAgB,CAACH,GAAG,CAAC,CAAC,EAAER,SAAS,CAAC;EAAE,CAAC,GAE7EQ,GAAI,CAAC;AACjB;AACAnB,OAAO,CAACC,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}