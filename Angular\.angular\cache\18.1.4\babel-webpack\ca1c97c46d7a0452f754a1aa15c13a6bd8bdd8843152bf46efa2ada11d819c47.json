{"ast": null, "code": "\"use strict\";\n\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.windowToggle = void 0;\nvar Subject_1 = require(\"../Subject\");\nvar Subscription_1 = require(\"../Subscription\");\nvar lift_1 = require(\"../util/lift\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar noop_1 = require(\"../util/noop\");\nvar arrRemove_1 = require(\"../util/arrRemove\");\nfunction windowToggle(openings, closingSelector) {\n  return lift_1.operate(function (source, subscriber) {\n    var windows = [];\n    var handleError = function (err) {\n      while (0 < windows.length) {\n        windows.shift().error(err);\n      }\n      subscriber.error(err);\n    };\n    innerFrom_1.innerFrom(openings).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (openValue) {\n      var window = new Subject_1.Subject();\n      windows.push(window);\n      var closingSubscription = new Subscription_1.Subscription();\n      var closeWindow = function () {\n        arrRemove_1.arrRemove(windows, window);\n        window.complete();\n        closingSubscription.unsubscribe();\n      };\n      var closingNotifier;\n      try {\n        closingNotifier = innerFrom_1.innerFrom(closingSelector(openValue));\n      } catch (err) {\n        handleError(err);\n        return;\n      }\n      subscriber.next(window.asObservable());\n      closingSubscription.add(closingNotifier.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, closeWindow, noop_1.noop, handleError)));\n    }, noop_1.noop));\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var e_1, _a;\n      var windowsCopy = windows.slice();\n      try {\n        for (var windowsCopy_1 = __values(windowsCopy), windowsCopy_1_1 = windowsCopy_1.next(); !windowsCopy_1_1.done; windowsCopy_1_1 = windowsCopy_1.next()) {\n          var window_1 = windowsCopy_1_1.value;\n          window_1.next(value);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (windowsCopy_1_1 && !windowsCopy_1_1.done && (_a = windowsCopy_1.return)) _a.call(windowsCopy_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }, function () {\n      while (0 < windows.length) {\n        windows.shift().complete();\n      }\n      subscriber.complete();\n    }, handleError, function () {\n      while (0 < windows.length) {\n        windows.shift().unsubscribe();\n      }\n    }));\n  });\n}\nexports.windowToggle = windowToggle;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "Object", "defineProperty", "exports", "windowToggle", "Subject_1", "require", "Subscription_1", "lift_1", "innerFrom_1", "OperatorSubscriber_1", "noop_1", "arrRemove_1", "openings", "closingSelector", "operate", "source", "subscriber", "windows", "handleError", "err", "shift", "error", "innerFrom", "subscribe", "createOperatorSubscriber", "openValue", "window", "Subject", "push", "closingSubscription", "Subscription", "closeWindow", "arr<PERSON><PERSON><PERSON>", "complete", "unsubscribe", "closingNotifier", "asObservable", "add", "noop", "e_1", "_a", "windowsCopy", "slice", "windowsCopy_1", "windowsCopy_1_1", "window_1", "e_1_1", "return"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/windowToggle.js"], "sourcesContent": ["\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.windowToggle = void 0;\nvar Subject_1 = require(\"../Subject\");\nvar Subscription_1 = require(\"../Subscription\");\nvar lift_1 = require(\"../util/lift\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar noop_1 = require(\"../util/noop\");\nvar arrRemove_1 = require(\"../util/arrRemove\");\nfunction windowToggle(openings, closingSelector) {\n    return lift_1.operate(function (source, subscriber) {\n        var windows = [];\n        var handleError = function (err) {\n            while (0 < windows.length) {\n                windows.shift().error(err);\n            }\n            subscriber.error(err);\n        };\n        innerFrom_1.innerFrom(openings).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (openValue) {\n            var window = new Subject_1.Subject();\n            windows.push(window);\n            var closingSubscription = new Subscription_1.Subscription();\n            var closeWindow = function () {\n                arrRemove_1.arrRemove(windows, window);\n                window.complete();\n                closingSubscription.unsubscribe();\n            };\n            var closingNotifier;\n            try {\n                closingNotifier = innerFrom_1.innerFrom(closingSelector(openValue));\n            }\n            catch (err) {\n                handleError(err);\n                return;\n            }\n            subscriber.next(window.asObservable());\n            closingSubscription.add(closingNotifier.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, closeWindow, noop_1.noop, handleError)));\n        }, noop_1.noop));\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            var e_1, _a;\n            var windowsCopy = windows.slice();\n            try {\n                for (var windowsCopy_1 = __values(windowsCopy), windowsCopy_1_1 = windowsCopy_1.next(); !windowsCopy_1_1.done; windowsCopy_1_1 = windowsCopy_1.next()) {\n                    var window_1 = windowsCopy_1_1.value;\n                    window_1.next(value);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (windowsCopy_1_1 && !windowsCopy_1_1.done && (_a = windowsCopy_1.return)) _a.call(windowsCopy_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }, function () {\n            while (0 < windows.length) {\n                windows.shift().complete();\n            }\n            subscriber.complete();\n        }, handleError, function () {\n            while (0 < windows.length) {\n                windows.shift().unsubscribe();\n            }\n        }));\n    });\n}\nexports.windowToggle = windowToggle;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACDW,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEL,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DK,OAAO,CAACC,YAAY,GAAG,KAAK,CAAC;AAC7B,IAAIC,SAAS,GAAGC,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIC,cAAc,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAC/C,IAAIE,MAAM,GAAGF,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIG,WAAW,GAAGH,OAAO,CAAC,yBAAyB,CAAC;AACpD,IAAII,oBAAoB,GAAGJ,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIK,MAAM,GAAGL,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIM,WAAW,GAAGN,OAAO,CAAC,mBAAmB,CAAC;AAC9C,SAASF,YAAYA,CAACS,QAAQ,EAAEC,eAAe,EAAE;EAC7C,OAAON,MAAM,CAACO,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,WAAW,GAAG,SAAAA,CAAUC,GAAG,EAAE;MAC7B,OAAO,CAAC,GAAGF,OAAO,CAACtB,MAAM,EAAE;QACvBsB,OAAO,CAACG,KAAK,CAAC,CAAC,CAACC,KAAK,CAACF,GAAG,CAAC;MAC9B;MACAH,UAAU,CAACK,KAAK,CAACF,GAAG,CAAC;IACzB,CAAC;IACDX,WAAW,CAACc,SAAS,CAACV,QAAQ,CAAC,CAACW,SAAS,CAACd,oBAAoB,CAACe,wBAAwB,CAACR,UAAU,EAAE,UAAUS,SAAS,EAAE;MACrH,IAAIC,MAAM,GAAG,IAAItB,SAAS,CAACuB,OAAO,CAAC,CAAC;MACpCV,OAAO,CAACW,IAAI,CAACF,MAAM,CAAC;MACpB,IAAIG,mBAAmB,GAAG,IAAIvB,cAAc,CAACwB,YAAY,CAAC,CAAC;MAC3D,IAAIC,WAAW,GAAG,SAAAA,CAAA,EAAY;QAC1BpB,WAAW,CAACqB,SAAS,CAACf,OAAO,EAAES,MAAM,CAAC;QACtCA,MAAM,CAACO,QAAQ,CAAC,CAAC;QACjBJ,mBAAmB,CAACK,WAAW,CAAC,CAAC;MACrC,CAAC;MACD,IAAIC,eAAe;MACnB,IAAI;QACAA,eAAe,GAAG3B,WAAW,CAACc,SAAS,CAACT,eAAe,CAACY,SAAS,CAAC,CAAC;MACvE,CAAC,CACD,OAAON,GAAG,EAAE;QACRD,WAAW,CAACC,GAAG,CAAC;QAChB;MACJ;MACAH,UAAU,CAACpB,IAAI,CAAC8B,MAAM,CAACU,YAAY,CAAC,CAAC,CAAC;MACtCP,mBAAmB,CAACQ,GAAG,CAACF,eAAe,CAACZ,SAAS,CAACd,oBAAoB,CAACe,wBAAwB,CAACR,UAAU,EAAEe,WAAW,EAAErB,MAAM,CAAC4B,IAAI,EAAEpB,WAAW,CAAC,CAAC,CAAC;IACxJ,CAAC,EAAER,MAAM,CAAC4B,IAAI,CAAC,CAAC;IAChBvB,MAAM,CAACQ,SAAS,CAACd,oBAAoB,CAACe,wBAAwB,CAACR,UAAU,EAAE,UAAUnB,KAAK,EAAE;MACxF,IAAI0C,GAAG,EAAEC,EAAE;MACX,IAAIC,WAAW,GAAGxB,OAAO,CAACyB,KAAK,CAAC,CAAC;MACjC,IAAI;QACA,KAAK,IAAIC,aAAa,GAAGxD,QAAQ,CAACsD,WAAW,CAAC,EAAEG,eAAe,GAAGD,aAAa,CAAC/C,IAAI,CAAC,CAAC,EAAE,CAACgD,eAAe,CAAC9C,IAAI,EAAE8C,eAAe,GAAGD,aAAa,CAAC/C,IAAI,CAAC,CAAC,EAAE;UACnJ,IAAIiD,QAAQ,GAAGD,eAAe,CAAC/C,KAAK;UACpCgD,QAAQ,CAACjD,IAAI,CAACC,KAAK,CAAC;QACxB;MACJ,CAAC,CACD,OAAOiD,KAAK,EAAE;QAAEP,GAAG,GAAG;UAAElB,KAAK,EAAEyB;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIF,eAAe,IAAI,CAACA,eAAe,CAAC9C,IAAI,KAAK0C,EAAE,GAAGG,aAAa,CAACI,MAAM,CAAC,EAAEP,EAAE,CAAC9C,IAAI,CAACiD,aAAa,CAAC;QACvG,CAAC,SACO;UAAE,IAAIJ,GAAG,EAAE,MAAMA,GAAG,CAAClB,KAAK;QAAE;MACxC;IACJ,CAAC,EAAE,YAAY;MACX,OAAO,CAAC,GAAGJ,OAAO,CAACtB,MAAM,EAAE;QACvBsB,OAAO,CAACG,KAAK,CAAC,CAAC,CAACa,QAAQ,CAAC,CAAC;MAC9B;MACAjB,UAAU,CAACiB,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAEf,WAAW,EAAE,YAAY;MACxB,OAAO,CAAC,GAAGD,OAAO,CAACtB,MAAM,EAAE;QACvBsB,OAAO,CAACG,KAAK,CAAC,CAAC,CAACc,WAAW,CAAC,CAAC;MACjC;IACJ,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACAhC,OAAO,CAACC,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}