{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.delayWhen = void 0;\nvar concat_1 = require(\"../observable/concat\");\nvar take_1 = require(\"./take\");\nvar ignoreElements_1 = require(\"./ignoreElements\");\nvar mapTo_1 = require(\"./mapTo\");\nvar mergeMap_1 = require(\"./mergeMap\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction delayWhen(delayDurationSelector, subscriptionDelay) {\n  if (subscriptionDelay) {\n    return function (source) {\n      return concat_1.concat(subscriptionDelay.pipe(take_1.take(1), ignoreElements_1.ignoreElements()), source.pipe(delayWhen(delayDurationSelector)));\n    };\n  }\n  return mergeMap_1.mergeMap(function (value, index) {\n    return innerFrom_1.innerFrom(delayDurationSelector(value, index)).pipe(take_1.take(1), mapTo_1.mapTo(value));\n  });\n}\nexports.delayWhen = delayWhen;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "<PERSON><PERSON>hen", "concat_1", "require", "take_1", "ignoreElements_1", "mapTo_1", "mergeMap_1", "innerFrom_1", "delayDurationSelector", "subscriptionDelay", "source", "concat", "pipe", "take", "ignoreElements", "mergeMap", "index", "innerFrom", "mapTo"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/delayWhen.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.delayWhen = void 0;\nvar concat_1 = require(\"../observable/concat\");\nvar take_1 = require(\"./take\");\nvar ignoreElements_1 = require(\"./ignoreElements\");\nvar mapTo_1 = require(\"./mapTo\");\nvar mergeMap_1 = require(\"./mergeMap\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction delayWhen(delayDurationSelector, subscriptionDelay) {\n    if (subscriptionDelay) {\n        return function (source) {\n            return concat_1.concat(subscriptionDelay.pipe(take_1.take(1), ignoreElements_1.ignoreElements()), source.pipe(delayWhen(delayDurationSelector)));\n        };\n    }\n    return mergeMap_1.mergeMap(function (value, index) { return innerFrom_1.innerFrom(delayDurationSelector(value, index)).pipe(take_1.take(1), mapTo_1.mapTo(value)); });\n}\nexports.delayWhen = delayWhen;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,QAAQ,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC9C,IAAIC,MAAM,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAC9B,IAAIE,gBAAgB,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AAClD,IAAIG,OAAO,GAAGH,OAAO,CAAC,SAAS,CAAC;AAChC,IAAII,UAAU,GAAGJ,OAAO,CAAC,YAAY,CAAC;AACtC,IAAIK,WAAW,GAAGL,OAAO,CAAC,yBAAyB,CAAC;AACpD,SAASF,SAASA,CAACQ,qBAAqB,EAAEC,iBAAiB,EAAE;EACzD,IAAIA,iBAAiB,EAAE;IACnB,OAAO,UAAUC,MAAM,EAAE;MACrB,OAAOT,QAAQ,CAACU,MAAM,CAACF,iBAAiB,CAACG,IAAI,CAACT,MAAM,CAACU,IAAI,CAAC,CAAC,CAAC,EAAET,gBAAgB,CAACU,cAAc,CAAC,CAAC,CAAC,EAAEJ,MAAM,CAACE,IAAI,CAACZ,SAAS,CAACQ,qBAAqB,CAAC,CAAC,CAAC;IACpJ,CAAC;EACL;EACA,OAAOF,UAAU,CAACS,QAAQ,CAAC,UAAUhB,KAAK,EAAEiB,KAAK,EAAE;IAAE,OAAOT,WAAW,CAACU,SAAS,CAACT,qBAAqB,CAACT,KAAK,EAAEiB,KAAK,CAAC,CAAC,CAACJ,IAAI,CAACT,MAAM,CAACU,IAAI,CAAC,CAAC,CAAC,EAAER,OAAO,CAACa,KAAK,CAACnB,KAAK,CAAC,CAAC;EAAE,CAAC,CAAC;AACzK;AACAD,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}