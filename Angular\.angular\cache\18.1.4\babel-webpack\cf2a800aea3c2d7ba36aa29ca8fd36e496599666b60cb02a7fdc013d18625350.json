{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.of = void 0;\nvar args_1 = require(\"../util/args\");\nvar from_1 = require(\"./from\");\nfunction of() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var scheduler = args_1.popScheduler(args);\n  return from_1.from(args, scheduler);\n}\nexports.of = of;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "of", "args_1", "require", "from_1", "args", "_i", "arguments", "length", "scheduler", "popScheduler", "from"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/of.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.of = void 0;\nvar args_1 = require(\"../util/args\");\nvar from_1 = require(\"./from\");\nfunction of() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var scheduler = args_1.popScheduler(args);\n    return from_1.from(args, scheduler);\n}\nexports.of = of;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,EAAE,GAAG,KAAK,CAAC;AACnB,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,MAAM,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAC9B,SAASF,EAAEA,CAAA,EAAG;EACV,IAAII,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC5B;EACA,IAAIG,SAAS,GAAGP,MAAM,CAACQ,YAAY,CAACL,IAAI,CAAC;EACzC,OAAOD,MAAM,CAACO,IAAI,CAACN,IAAI,EAAEI,SAAS,CAAC;AACvC;AACAV,OAAO,CAACE,EAAE,GAAGA,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}