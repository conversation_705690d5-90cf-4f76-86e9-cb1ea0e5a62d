{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.observeOn = void 0;\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction observeOn(scheduler, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  return lift_1.operate(function (source, subscriber) {\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      return executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n        return subscriber.next(value);\n      }, delay);\n    }, function () {\n      return executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n        return subscriber.complete();\n      }, delay);\n    }, function (err) {\n      return executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n        return subscriber.error(err);\n      }, delay);\n    }));\n  });\n}\nexports.observeOn = observeOn;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "observeOn", "executeSchedule_1", "require", "lift_1", "OperatorSubscriber_1", "scheduler", "delay", "operate", "source", "subscriber", "subscribe", "createOperatorSubscriber", "executeSchedule", "next", "complete", "err", "error"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/observeOn.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.observeOn = void 0;\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction observeOn(scheduler, delay) {\n    if (delay === void 0) { delay = 0; }\n    return lift_1.operate(function (source, subscriber) {\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) { return executeSchedule_1.executeSchedule(subscriber, scheduler, function () { return subscriber.next(value); }, delay); }, function () { return executeSchedule_1.executeSchedule(subscriber, scheduler, function () { return subscriber.complete(); }, delay); }, function (err) { return executeSchedule_1.executeSchedule(subscriber, scheduler, function () { return subscriber.error(err); }, delay); }));\n    });\n}\nexports.observeOn = observeOn;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,iBAAiB,GAAGC,OAAO,CAAC,yBAAyB,CAAC;AAC1D,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,SAASA,CAACK,SAAS,EAAEC,KAAK,EAAE;EACjC,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IAAEA,KAAK,GAAG,CAAC;EAAE;EACnC,OAAOH,MAAM,CAACI,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChDD,MAAM,CAACE,SAAS,CAACN,oBAAoB,CAACO,wBAAwB,CAACF,UAAU,EAAE,UAAUV,KAAK,EAAE;MAAE,OAAOE,iBAAiB,CAACW,eAAe,CAACH,UAAU,EAAEJ,SAAS,EAAE,YAAY;QAAE,OAAOI,UAAU,CAACI,IAAI,CAACd,KAAK,CAAC;MAAE,CAAC,EAAEO,KAAK,CAAC;IAAE,CAAC,EAAE,YAAY;MAAE,OAAOL,iBAAiB,CAACW,eAAe,CAACH,UAAU,EAAEJ,SAAS,EAAE,YAAY;QAAE,OAAOI,UAAU,CAACK,QAAQ,CAAC,CAAC;MAAE,CAAC,EAAER,KAAK,CAAC;IAAE,CAAC,EAAE,UAAUS,GAAG,EAAE;MAAE,OAAOd,iBAAiB,CAACW,eAAe,CAACH,UAAU,EAAEJ,SAAS,EAAE,YAAY;QAAE,OAAOI,UAAU,CAACO,KAAK,CAACD,GAAG,CAAC;MAAE,CAAC,EAAET,KAAK,CAAC;IAAE,CAAC,CAAC,CAAC;EAChf,CAAC,CAAC;AACN;AACAR,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}