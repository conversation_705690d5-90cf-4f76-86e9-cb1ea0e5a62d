{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isValidDate = void 0;\nfunction isValidDate(value) {\n  return value instanceof Date && !isNaN(value);\n}\nexports.isValidDate = isValidDate;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "isValidDate", "Date", "isNaN"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/isDate.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isValidDate = void 0;\nfunction isValidDate(value) {\n    return value instanceof Date && !isNaN(value);\n}\nexports.isValidDate = isValidDate;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,WAAW,GAAG,KAAK,CAAC;AAC5B,SAASA,WAAWA,CAACD,KAAK,EAAE;EACxB,OAAOA,KAAK,YAAYE,IAAI,IAAI,CAACC,KAAK,CAACH,KAAK,CAAC;AACjD;AACAD,OAAO,CAACE,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}