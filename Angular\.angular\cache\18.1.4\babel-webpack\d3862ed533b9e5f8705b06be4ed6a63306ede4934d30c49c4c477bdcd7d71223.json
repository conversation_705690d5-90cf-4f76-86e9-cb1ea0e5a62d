{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isPromise = void 0;\nvar isFunction_1 = require(\"./isFunction\");\nfunction isPromise(value) {\n  return isFunction_1.isFunction(value === null || value === void 0 ? void 0 : value.then);\n}\nexports.isPromise = isPromise;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "isPromise", "isFunction_1", "require", "isFunction", "then"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/isPromise.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isPromise = void 0;\nvar isFunction_1 = require(\"./isFunction\");\nfunction isPromise(value) {\n    return isFunction_1.isFunction(value === null || value === void 0 ? void 0 : value.then);\n}\nexports.isPromise = isPromise;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAIC,YAAY,GAAGC,OAAO,CAAC,cAAc,CAAC;AAC1C,SAASF,SAASA,CAACD,KAAK,EAAE;EACtB,OAAOE,YAAY,CAACE,UAAU,CAACJ,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACK,IAAI,CAAC;AAC5F;AACAN,OAAO,CAACE,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}