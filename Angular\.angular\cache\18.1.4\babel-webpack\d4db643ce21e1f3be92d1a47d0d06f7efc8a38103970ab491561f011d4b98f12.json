{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.timeout = exports.TimeoutError = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar isDate_1 = require(\"../util/isDate\");\nvar lift_1 = require(\"../util/lift\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar createErrorClass_1 = require(\"../util/createErrorClass\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nexports.TimeoutError = createErrorClass_1.createErrorClass(function (_super) {\n  return function TimeoutErrorImpl(info) {\n    if (info === void 0) {\n      info = null;\n    }\n    _super(this);\n    this.message = 'Timeout has occurred';\n    this.name = 'TimeoutError';\n    this.info = info;\n  };\n});\nfunction timeout(config, schedulerArg) {\n  var _a = isDate_1.isValidDate(config) ? {\n      first: config\n    } : typeof config === 'number' ? {\n      each: config\n    } : config,\n    first = _a.first,\n    each = _a.each,\n    _b = _a.with,\n    _with = _b === void 0 ? timeoutErrorFactory : _b,\n    _c = _a.scheduler,\n    scheduler = _c === void 0 ? schedulerArg !== null && schedulerArg !== void 0 ? schedulerArg : async_1.asyncScheduler : _c,\n    _d = _a.meta,\n    meta = _d === void 0 ? null : _d;\n  if (first == null && each == null) {\n    throw new TypeError('No timeout provided.');\n  }\n  return lift_1.operate(function (source, subscriber) {\n    var originalSourceSubscription;\n    var timerSubscription;\n    var lastValue = null;\n    var seen = 0;\n    var startTimer = function (delay) {\n      timerSubscription = executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n        try {\n          originalSourceSubscription.unsubscribe();\n          innerFrom_1.innerFrom(_with({\n            meta: meta,\n            lastValue: lastValue,\n            seen: seen\n          })).subscribe(subscriber);\n        } catch (err) {\n          subscriber.error(err);\n        }\n      }, delay);\n    };\n    originalSourceSubscription = source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      seen++;\n      subscriber.next(lastValue = value);\n      each > 0 && startTimer(each);\n    }, undefined, undefined, function () {\n      if (!(timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.closed)) {\n        timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      }\n      lastValue = null;\n    }));\n    !seen && startTimer(first != null ? typeof first === 'number' ? first : +first - scheduler.now() : each);\n  });\n}\nexports.timeout = timeout;\nfunction timeoutErrorFactory(info) {\n  throw new exports.TimeoutError(info);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "timeout", "TimeoutError", "async_1", "require", "isDate_1", "lift_1", "innerFrom_1", "createErrorClass_1", "OperatorSubscriber_1", "executeSchedule_1", "createErrorClass", "_super", "TimeoutErrorImpl", "info", "message", "name", "config", "schedulerArg", "_a", "isValidDate", "first", "each", "_b", "with", "_with", "timeoutErrorFactory", "_c", "scheduler", "asyncScheduler", "_d", "meta", "TypeError", "operate", "source", "subscriber", "originalSourceSubscription", "timerSubscription", "lastValue", "seen", "startTimer", "delay", "executeSchedule", "unsubscribe", "innerFrom", "subscribe", "err", "error", "createOperatorSubscriber", "next", "undefined", "closed", "now"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/timeout.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.timeout = exports.TimeoutError = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar isDate_1 = require(\"../util/isDate\");\nvar lift_1 = require(\"../util/lift\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar createErrorClass_1 = require(\"../util/createErrorClass\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nexports.TimeoutError = createErrorClass_1.createErrorClass(function (_super) {\n    return function TimeoutErrorImpl(info) {\n        if (info === void 0) { info = null; }\n        _super(this);\n        this.message = 'Timeout has occurred';\n        this.name = 'TimeoutError';\n        this.info = info;\n    };\n});\nfunction timeout(config, schedulerArg) {\n    var _a = (isDate_1.isValidDate(config) ? { first: config } : typeof config === 'number' ? { each: config } : config), first = _a.first, each = _a.each, _b = _a.with, _with = _b === void 0 ? timeoutErrorFactory : _b, _c = _a.scheduler, scheduler = _c === void 0 ? schedulerArg !== null && schedulerArg !== void 0 ? schedulerArg : async_1.asyncScheduler : _c, _d = _a.meta, meta = _d === void 0 ? null : _d;\n    if (first == null && each == null) {\n        throw new TypeError('No timeout provided.');\n    }\n    return lift_1.operate(function (source, subscriber) {\n        var originalSourceSubscription;\n        var timerSubscription;\n        var lastValue = null;\n        var seen = 0;\n        var startTimer = function (delay) {\n            timerSubscription = executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n                try {\n                    originalSourceSubscription.unsubscribe();\n                    innerFrom_1.innerFrom(_with({\n                        meta: meta,\n                        lastValue: lastValue,\n                        seen: seen,\n                    })).subscribe(subscriber);\n                }\n                catch (err) {\n                    subscriber.error(err);\n                }\n            }, delay);\n        };\n        originalSourceSubscription = source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n            seen++;\n            subscriber.next((lastValue = value));\n            each > 0 && startTimer(each);\n        }, undefined, undefined, function () {\n            if (!(timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.closed)) {\n                timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n            }\n            lastValue = null;\n        }));\n        !seen && startTimer(first != null ? (typeof first === 'number' ? first : +first - scheduler.now()) : each);\n    });\n}\nexports.timeout = timeout;\nfunction timeoutErrorFactory(info) {\n    throw new exports.TimeoutError(info);\n}\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,OAAO,GAAGF,OAAO,CAACG,YAAY,GAAG,KAAK,CAAC;AAC/C,IAAIC,OAAO,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAC3C,IAAIC,QAAQ,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACxC,IAAIE,MAAM,GAAGF,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIG,WAAW,GAAGH,OAAO,CAAC,yBAAyB,CAAC;AACpD,IAAII,kBAAkB,GAAGJ,OAAO,CAAC,0BAA0B,CAAC;AAC5D,IAAIK,oBAAoB,GAAGL,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIM,iBAAiB,GAAGN,OAAO,CAAC,yBAAyB,CAAC;AAC1DL,OAAO,CAACG,YAAY,GAAGM,kBAAkB,CAACG,gBAAgB,CAAC,UAAUC,MAAM,EAAE;EACzE,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAE;IACnC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG,IAAI;IAAE;IACpCF,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACG,OAAO,GAAG,sBAAsB;IACrC,IAAI,CAACC,IAAI,GAAG,cAAc;IAC1B,IAAI,CAACF,IAAI,GAAGA,IAAI;EACpB,CAAC;AACL,CAAC,CAAC;AACF,SAASb,OAAOA,CAACgB,MAAM,EAAEC,YAAY,EAAE;EACnC,IAAIC,EAAE,GAAId,QAAQ,CAACe,WAAW,CAACH,MAAM,CAAC,GAAG;MAAEI,KAAK,EAAEJ;IAAO,CAAC,GAAG,OAAOA,MAAM,KAAK,QAAQ,GAAG;MAAEK,IAAI,EAAEL;IAAO,CAAC,GAAGA,MAAO;IAAEI,KAAK,GAAGF,EAAE,CAACE,KAAK;IAAEC,IAAI,GAAGH,EAAE,CAACG,IAAI;IAAEC,EAAE,GAAGJ,EAAE,CAACK,IAAI;IAAEC,KAAK,GAAGF,EAAE,KAAK,KAAK,CAAC,GAAGG,mBAAmB,GAAGH,EAAE;IAAEI,EAAE,GAAGR,EAAE,CAACS,SAAS;IAAEA,SAAS,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAGT,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGf,OAAO,CAAC0B,cAAc,GAAGF,EAAE;IAAEG,EAAE,GAAGX,EAAE,CAACY,IAAI;IAAEA,IAAI,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;EACpZ,IAAIT,KAAK,IAAI,IAAI,IAAIC,IAAI,IAAI,IAAI,EAAE;IAC/B,MAAM,IAAIU,SAAS,CAAC,sBAAsB,CAAC;EAC/C;EACA,OAAO1B,MAAM,CAAC2B,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,0BAA0B;IAC9B,IAAIC,iBAAiB;IACrB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAIC,UAAU,GAAG,SAAAA,CAAUC,KAAK,EAAE;MAC9BJ,iBAAiB,GAAG3B,iBAAiB,CAACgC,eAAe,CAACP,UAAU,EAAEP,SAAS,EAAE,YAAY;QACrF,IAAI;UACAQ,0BAA0B,CAACO,WAAW,CAAC,CAAC;UACxCpC,WAAW,CAACqC,SAAS,CAACnB,KAAK,CAAC;YACxBM,IAAI,EAAEA,IAAI;YACVO,SAAS,EAAEA,SAAS;YACpBC,IAAI,EAAEA;UACV,CAAC,CAAC,CAAC,CAACM,SAAS,CAACV,UAAU,CAAC;QAC7B,CAAC,CACD,OAAOW,GAAG,EAAE;UACRX,UAAU,CAACY,KAAK,CAACD,GAAG,CAAC;QACzB;MACJ,CAAC,EAAEL,KAAK,CAAC;IACb,CAAC;IACDL,0BAA0B,GAAGF,MAAM,CAACW,SAAS,CAACpC,oBAAoB,CAACuC,wBAAwB,CAACb,UAAU,EAAE,UAAUnC,KAAK,EAAE;MACrHqC,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACM,WAAW,CAAC,CAAC;MACrGJ,IAAI,EAAE;MACNJ,UAAU,CAACc,IAAI,CAAEX,SAAS,GAAGtC,KAAM,CAAC;MACpCsB,IAAI,GAAG,CAAC,IAAIkB,UAAU,CAAClB,IAAI,CAAC;IAChC,CAAC,EAAE4B,SAAS,EAAEA,SAAS,EAAE,YAAY;MACjC,IAAI,EAAEb,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACc,MAAM,CAAC,EAAE;QACnGd,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACM,WAAW,CAAC,CAAC;MACzG;MACAL,SAAS,GAAG,IAAI;IACpB,CAAC,CAAC,CAAC;IACH,CAACC,IAAI,IAAIC,UAAU,CAACnB,KAAK,IAAI,IAAI,GAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,CAACA,KAAK,GAAGO,SAAS,CAACwB,GAAG,CAAC,CAAC,GAAI9B,IAAI,CAAC;EAC9G,CAAC,CAAC;AACN;AACAvB,OAAO,CAACE,OAAO,GAAGA,OAAO;AACzB,SAASyB,mBAAmBA,CAACZ,IAAI,EAAE;EAC/B,MAAM,IAAIf,OAAO,CAACG,YAAY,CAACY,IAAI,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}