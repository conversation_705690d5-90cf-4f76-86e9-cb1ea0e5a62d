{"ast": null, "code": "\"use strict\";\n\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.bufferTime = void 0;\nvar Subscription_1 = require(\"../Subscription\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar arrRemove_1 = require(\"../util/arrRemove\");\nvar async_1 = require(\"../scheduler/async\");\nvar args_1 = require(\"../util/args\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nfunction bufferTime(bufferTimeSpan) {\n  var _a, _b;\n  var otherArgs = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    otherArgs[_i - 1] = arguments[_i];\n  }\n  var scheduler = (_a = args_1.popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : async_1.asyncScheduler;\n  var bufferCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n  var maxBufferSize = otherArgs[1] || Infinity;\n  return lift_1.operate(function (source, subscriber) {\n    var bufferRecords = [];\n    var restartOnEmit = false;\n    var emit = function (record) {\n      var buffer = record.buffer,\n        subs = record.subs;\n      subs.unsubscribe();\n      arrRemove_1.arrRemove(bufferRecords, record);\n      subscriber.next(buffer);\n      restartOnEmit && startBuffer();\n    };\n    var startBuffer = function () {\n      if (bufferRecords) {\n        var subs = new Subscription_1.Subscription();\n        subscriber.add(subs);\n        var buffer = [];\n        var record_1 = {\n          buffer: buffer,\n          subs: subs\n        };\n        bufferRecords.push(record_1);\n        executeSchedule_1.executeSchedule(subs, scheduler, function () {\n          return emit(record_1);\n        }, bufferTimeSpan);\n      }\n    };\n    if (bufferCreationInterval !== null && bufferCreationInterval >= 0) {\n      executeSchedule_1.executeSchedule(subscriber, scheduler, startBuffer, bufferCreationInterval, true);\n    } else {\n      restartOnEmit = true;\n    }\n    startBuffer();\n    var bufferTimeSubscriber = OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var e_1, _a;\n      var recordsCopy = bufferRecords.slice();\n      try {\n        for (var recordsCopy_1 = __values(recordsCopy), recordsCopy_1_1 = recordsCopy_1.next(); !recordsCopy_1_1.done; recordsCopy_1_1 = recordsCopy_1.next()) {\n          var record = recordsCopy_1_1.value;\n          var buffer = record.buffer;\n          buffer.push(value);\n          maxBufferSize <= buffer.length && emit(record);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (recordsCopy_1_1 && !recordsCopy_1_1.done && (_a = recordsCopy_1.return)) _a.call(recordsCopy_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }, function () {\n      while (bufferRecords === null || bufferRecords === void 0 ? void 0 : bufferRecords.length) {\n        subscriber.next(bufferRecords.shift().buffer);\n      }\n      bufferTimeSubscriber === null || bufferTimeSubscriber === void 0 ? void 0 : bufferTimeSubscriber.unsubscribe();\n      subscriber.complete();\n      subscriber.unsubscribe();\n    }, undefined, function () {\n      return bufferRecords = null;\n    });\n    source.subscribe(bufferTimeSubscriber);\n  });\n}\nexports.bufferTime = bufferTime;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "Object", "defineProperty", "exports", "bufferTime", "Subscription_1", "require", "lift_1", "OperatorSubscriber_1", "arrRemove_1", "async_1", "args_1", "executeSchedule_1", "bufferTimeSpan", "_a", "_b", "otherArgs", "_i", "arguments", "scheduler", "popScheduler", "asyncScheduler", "bufferCreationInterval", "maxBufferSize", "Infinity", "operate", "source", "subscriber", "bufferRecords", "restartOnEmit", "emit", "record", "buffer", "subs", "unsubscribe", "arr<PERSON><PERSON><PERSON>", "startBuffer", "Subscription", "add", "record_1", "push", "executeSchedule", "bufferTimeSubscriber", "createOperatorSubscriber", "e_1", "recordsCopy", "slice", "recordsCopy_1", "recordsCopy_1_1", "e_1_1", "error", "return", "shift", "complete", "undefined", "subscribe"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/bufferTime.js"], "sourcesContent": ["\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bufferTime = void 0;\nvar Subscription_1 = require(\"../Subscription\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar arrRemove_1 = require(\"../util/arrRemove\");\nvar async_1 = require(\"../scheduler/async\");\nvar args_1 = require(\"../util/args\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nfunction bufferTime(bufferTimeSpan) {\n    var _a, _b;\n    var otherArgs = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        otherArgs[_i - 1] = arguments[_i];\n    }\n    var scheduler = (_a = args_1.popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : async_1.asyncScheduler;\n    var bufferCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n    var maxBufferSize = otherArgs[1] || Infinity;\n    return lift_1.operate(function (source, subscriber) {\n        var bufferRecords = [];\n        var restartOnEmit = false;\n        var emit = function (record) {\n            var buffer = record.buffer, subs = record.subs;\n            subs.unsubscribe();\n            arrRemove_1.arrRemove(bufferRecords, record);\n            subscriber.next(buffer);\n            restartOnEmit && startBuffer();\n        };\n        var startBuffer = function () {\n            if (bufferRecords) {\n                var subs = new Subscription_1.Subscription();\n                subscriber.add(subs);\n                var buffer = [];\n                var record_1 = {\n                    buffer: buffer,\n                    subs: subs,\n                };\n                bufferRecords.push(record_1);\n                executeSchedule_1.executeSchedule(subs, scheduler, function () { return emit(record_1); }, bufferTimeSpan);\n            }\n        };\n        if (bufferCreationInterval !== null && bufferCreationInterval >= 0) {\n            executeSchedule_1.executeSchedule(subscriber, scheduler, startBuffer, bufferCreationInterval, true);\n        }\n        else {\n            restartOnEmit = true;\n        }\n        startBuffer();\n        var bufferTimeSubscriber = OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            var e_1, _a;\n            var recordsCopy = bufferRecords.slice();\n            try {\n                for (var recordsCopy_1 = __values(recordsCopy), recordsCopy_1_1 = recordsCopy_1.next(); !recordsCopy_1_1.done; recordsCopy_1_1 = recordsCopy_1.next()) {\n                    var record = recordsCopy_1_1.value;\n                    var buffer = record.buffer;\n                    buffer.push(value);\n                    maxBufferSize <= buffer.length && emit(record);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (recordsCopy_1_1 && !recordsCopy_1_1.done && (_a = recordsCopy_1.return)) _a.call(recordsCopy_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }, function () {\n            while (bufferRecords === null || bufferRecords === void 0 ? void 0 : bufferRecords.length) {\n                subscriber.next(bufferRecords.shift().buffer);\n            }\n            bufferTimeSubscriber === null || bufferTimeSubscriber === void 0 ? void 0 : bufferTimeSubscriber.unsubscribe();\n            subscriber.complete();\n            subscriber.unsubscribe();\n        }, undefined, function () { return (bufferRecords = null); });\n        source.subscribe(bufferTimeSubscriber);\n    });\n}\nexports.bufferTime = bufferTime;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACDW,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEL,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DK,OAAO,CAACC,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAIC,cAAc,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAC/C,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIG,WAAW,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AAC9C,IAAII,OAAO,GAAGJ,OAAO,CAAC,oBAAoB,CAAC;AAC3C,IAAIK,MAAM,GAAGL,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIM,iBAAiB,GAAGN,OAAO,CAAC,yBAAyB,CAAC;AAC1D,SAASF,UAAUA,CAACS,cAAc,EAAE;EAChC,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAIC,SAAS,GAAG,EAAE;EAClB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACtB,MAAM,EAAEqB,EAAE,EAAE,EAAE;IAC1CD,SAAS,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EACrC;EACA,IAAIE,SAAS,GAAG,CAACL,EAAE,GAAGH,MAAM,CAACS,YAAY,CAACJ,SAAS,CAAC,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGJ,OAAO,CAACW,cAAc;EAC7G,IAAIC,sBAAsB,GAAG,CAACP,EAAE,GAAGC,SAAS,CAAC,CAAC,CAAC,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;EACtF,IAAIQ,aAAa,GAAGP,SAAS,CAAC,CAAC,CAAC,IAAIQ,QAAQ;EAC5C,OAAOjB,MAAM,CAACkB,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIC,aAAa,GAAG,KAAK;IACzB,IAAIC,IAAI,GAAG,SAAAA,CAAUC,MAAM,EAAE;MACzB,IAAIC,MAAM,GAAGD,MAAM,CAACC,MAAM;QAAEC,IAAI,GAAGF,MAAM,CAACE,IAAI;MAC9CA,IAAI,CAACC,WAAW,CAAC,CAAC;MAClBzB,WAAW,CAAC0B,SAAS,CAACP,aAAa,EAAEG,MAAM,CAAC;MAC5CJ,UAAU,CAAC9B,IAAI,CAACmC,MAAM,CAAC;MACvBH,aAAa,IAAIO,WAAW,CAAC,CAAC;IAClC,CAAC;IACD,IAAIA,WAAW,GAAG,SAAAA,CAAA,EAAY;MAC1B,IAAIR,aAAa,EAAE;QACf,IAAIK,IAAI,GAAG,IAAI5B,cAAc,CAACgC,YAAY,CAAC,CAAC;QAC5CV,UAAU,CAACW,GAAG,CAACL,IAAI,CAAC;QACpB,IAAID,MAAM,GAAG,EAAE;QACf,IAAIO,QAAQ,GAAG;UACXP,MAAM,EAAEA,MAAM;UACdC,IAAI,EAAEA;QACV,CAAC;QACDL,aAAa,CAACY,IAAI,CAACD,QAAQ,CAAC;QAC5B3B,iBAAiB,CAAC6B,eAAe,CAACR,IAAI,EAAEd,SAAS,EAAE,YAAY;UAAE,OAAOW,IAAI,CAACS,QAAQ,CAAC;QAAE,CAAC,EAAE1B,cAAc,CAAC;MAC9G;IACJ,CAAC;IACD,IAAIS,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,IAAI,CAAC,EAAE;MAChEV,iBAAiB,CAAC6B,eAAe,CAACd,UAAU,EAAER,SAAS,EAAEiB,WAAW,EAAEd,sBAAsB,EAAE,IAAI,CAAC;IACvG,CAAC,MACI;MACDO,aAAa,GAAG,IAAI;IACxB;IACAO,WAAW,CAAC,CAAC;IACb,IAAIM,oBAAoB,GAAGlC,oBAAoB,CAACmC,wBAAwB,CAAChB,UAAU,EAAE,UAAU7B,KAAK,EAAE;MAClG,IAAI8C,GAAG,EAAE9B,EAAE;MACX,IAAI+B,WAAW,GAAGjB,aAAa,CAACkB,KAAK,CAAC,CAAC;MACvC,IAAI;QACA,KAAK,IAAIC,aAAa,GAAG3D,QAAQ,CAACyD,WAAW,CAAC,EAAEG,eAAe,GAAGD,aAAa,CAAClD,IAAI,CAAC,CAAC,EAAE,CAACmD,eAAe,CAACjD,IAAI,EAAEiD,eAAe,GAAGD,aAAa,CAAClD,IAAI,CAAC,CAAC,EAAE;UACnJ,IAAIkC,MAAM,GAAGiB,eAAe,CAAClD,KAAK;UAClC,IAAIkC,MAAM,GAAGD,MAAM,CAACC,MAAM;UAC1BA,MAAM,CAACQ,IAAI,CAAC1C,KAAK,CAAC;UAClByB,aAAa,IAAIS,MAAM,CAACpC,MAAM,IAAIkC,IAAI,CAACC,MAAM,CAAC;QAClD;MACJ,CAAC,CACD,OAAOkB,KAAK,EAAE;QAAEL,GAAG,GAAG;UAAEM,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAID,eAAe,IAAI,CAACA,eAAe,CAACjD,IAAI,KAAKe,EAAE,GAAGiC,aAAa,CAACI,MAAM,CAAC,EAAErC,EAAE,CAACnB,IAAI,CAACoD,aAAa,CAAC;QACvG,CAAC,SACO;UAAE,IAAIH,GAAG,EAAE,MAAMA,GAAG,CAACM,KAAK;QAAE;MACxC;IACJ,CAAC,EAAE,YAAY;MACX,OAAOtB,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAChC,MAAM,EAAE;QACvF+B,UAAU,CAAC9B,IAAI,CAAC+B,aAAa,CAACwB,KAAK,CAAC,CAAC,CAACpB,MAAM,CAAC;MACjD;MACAU,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACR,WAAW,CAAC,CAAC;MAC9GP,UAAU,CAAC0B,QAAQ,CAAC,CAAC;MACrB1B,UAAU,CAACO,WAAW,CAAC,CAAC;IAC5B,CAAC,EAAEoB,SAAS,EAAE,YAAY;MAAE,OAAQ1B,aAAa,GAAG,IAAI;IAAG,CAAC,CAAC;IAC7DF,MAAM,CAAC6B,SAAS,CAACb,oBAAoB,CAAC;EAC1C,CAAC,CAAC;AACN;AACAvC,OAAO,CAACC,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}