{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.reduce = void 0;\nvar scanInternals_1 = require(\"./scanInternals\");\nvar lift_1 = require(\"../util/lift\");\nfunction reduce(accumulator, seed) {\n  return lift_1.operate(scanInternals_1.scanInternals(accumulator, seed, arguments.length >= 2, false, true));\n}\nexports.reduce = reduce;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "reduce", "scanInternals_1", "require", "lift_1", "accumulator", "seed", "operate", "scanInternals", "arguments", "length"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/reduce.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.reduce = void 0;\nvar scanInternals_1 = require(\"./scanInternals\");\nvar lift_1 = require(\"../util/lift\");\nfunction reduce(accumulator, seed) {\n    return lift_1.operate(scanInternals_1.scanInternals(accumulator, seed, arguments.length >= 2, false, true));\n}\nexports.reduce = reduce;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,MAAM,GAAG,KAAK,CAAC;AACvB,IAAIC,eAAe,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAChD,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,SAASF,MAAMA,CAACI,WAAW,EAAEC,IAAI,EAAE;EAC/B,OAAOF,MAAM,CAACG,OAAO,CAACL,eAAe,CAACM,aAAa,CAACH,WAAW,EAAEC,IAAI,EAAEG,SAAS,CAACC,MAAM,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAC/G;AACAX,OAAO,CAACE,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}