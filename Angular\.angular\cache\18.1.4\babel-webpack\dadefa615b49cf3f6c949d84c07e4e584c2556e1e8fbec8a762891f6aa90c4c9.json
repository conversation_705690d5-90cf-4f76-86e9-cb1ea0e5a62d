{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.pairwise = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction pairwise() {\n  return lift_1.operate(function (source, subscriber) {\n    var prev;\n    var hasPrev = false;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var p = prev;\n      prev = value;\n      hasPrev && subscriber.next([p, value]);\n      hasPrev = true;\n    }));\n  });\n}\nexports.pairwise = pairwise;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "pairwise", "lift_1", "require", "OperatorSubscriber_1", "operate", "source", "subscriber", "prev", "has<PERSON>rev", "subscribe", "createOperatorSubscriber", "p", "next"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/pairwise.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.pairwise = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction pairwise() {\n    return lift_1.operate(function (source, subscriber) {\n        var prev;\n        var hasPrev = false;\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            var p = prev;\n            prev = value;\n            hasPrev && subscriber.next([p, value]);\n            hasPrev = true;\n        }));\n    });\n}\nexports.pairwise = pairwise;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,QAAQ,GAAG,KAAK,CAAC;AACzB,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,QAAQA,CAAA,EAAG;EAChB,OAAOC,MAAM,CAACG,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,IAAI;IACR,IAAIC,OAAO,GAAG,KAAK;IACnBH,MAAM,CAACI,SAAS,CAACN,oBAAoB,CAACO,wBAAwB,CAACJ,UAAU,EAAE,UAAUP,KAAK,EAAE;MACxF,IAAIY,CAAC,GAAGJ,IAAI;MACZA,IAAI,GAAGR,KAAK;MACZS,OAAO,IAAIF,UAAU,CAACM,IAAI,CAAC,CAACD,CAAC,EAAEZ,KAAK,CAAC,CAAC;MACtCS,OAAO,GAAG,IAAI;IAClB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACAV,OAAO,CAACE,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}