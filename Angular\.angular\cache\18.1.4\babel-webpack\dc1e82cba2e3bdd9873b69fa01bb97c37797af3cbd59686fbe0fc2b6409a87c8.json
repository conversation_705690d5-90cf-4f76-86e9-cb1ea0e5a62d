{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.argsOrArgArray = void 0;\nvar isArray = Array.isArray;\nfunction argsOrArgArray(args) {\n  return args.length === 1 && isArray(args[0]) ? args[0] : args;\n}\nexports.argsOrArgArray = argsOrArgArray;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "argsOrArgArray", "isArray", "Array", "args", "length"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/argsOrArgArray.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.argsOrArgArray = void 0;\nvar isArray = Array.isArray;\nfunction argsOrArgArray(args) {\n    return args.length === 1 && isArray(args[0]) ? args[0] : args;\n}\nexports.argsOrArgArray = argsOrArgArray;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAG,KAAK,CAAC;AAC/B,IAAIC,OAAO,GAAGC,KAAK,CAACD,OAAO;AAC3B,SAASD,cAAcA,CAACG,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACC,MAAM,KAAK,CAAC,IAAIH,OAAO,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI;AACjE;AACAL,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}