{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.scheduleArray = void 0;\nvar Observable_1 = require(\"../Observable\");\nfunction scheduleArray(input, scheduler) {\n  return new Observable_1.Observable(function (subscriber) {\n    var i = 0;\n    return scheduler.schedule(function () {\n      if (i === input.length) {\n        subscriber.complete();\n      } else {\n        subscriber.next(input[i++]);\n        if (!subscriber.closed) {\n          this.schedule();\n        }\n      }\n    });\n  });\n}\nexports.scheduleArray = scheduleArray;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "scheduleArray", "Observable_1", "require", "input", "scheduler", "Observable", "subscriber", "i", "schedule", "length", "complete", "next", "closed"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/scheduled/scheduleArray.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.scheduleArray = void 0;\nvar Observable_1 = require(\"../Observable\");\nfunction scheduleArray(input, scheduler) {\n    return new Observable_1.Observable(function (subscriber) {\n        var i = 0;\n        return scheduler.schedule(function () {\n            if (i === input.length) {\n                subscriber.complete();\n            }\n            else {\n                subscriber.next(input[i++]);\n                if (!subscriber.closed) {\n                    this.schedule();\n                }\n            }\n        });\n    });\n}\nexports.scheduleArray = scheduleArray;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,aAAa,GAAG,KAAK,CAAC;AAC9B,IAAIC,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,SAASF,aAAaA,CAACG,KAAK,EAAEC,SAAS,EAAE;EACrC,OAAO,IAAIH,YAAY,CAACI,UAAU,CAAC,UAAUC,UAAU,EAAE;IACrD,IAAIC,CAAC,GAAG,CAAC;IACT,OAAOH,SAAS,CAACI,QAAQ,CAAC,YAAY;MAClC,IAAID,CAAC,KAAKJ,KAAK,CAACM,MAAM,EAAE;QACpBH,UAAU,CAACI,QAAQ,CAAC,CAAC;MACzB,CAAC,MACI;QACDJ,UAAU,CAACK,IAAI,CAACR,KAAK,CAACI,CAAC,EAAE,CAAC,CAAC;QAC3B,IAAI,CAACD,UAAU,CAACM,MAAM,EAAE;UACpB,IAAI,CAACJ,QAAQ,CAAC,CAAC;QACnB;MACJ;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACAV,OAAO,CAACE,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}