{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isArrayLike = void 0;\nexports.isArrayLike = function (x) {\n  return x && typeof x.length === 'number' && typeof x !== 'function';\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "isArrayLike", "x", "length"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/isArrayLike.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isArrayLike = void 0;\nexports.isArrayLike = (function (x) { return x && typeof x.length === 'number' && typeof x !== 'function'; });\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,WAAW,GAAG,KAAK,CAAC;AAC5BF,OAAO,CAACE,WAAW,GAAI,UAAUC,CAAC,EAAE;EAAE,OAAOA,CAAC,IAAI,OAAOA,CAAC,CAACC,MAAM,KAAK,QAAQ,IAAI,OAAOD,CAAC,KAAK,UAAU;AAAE,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}