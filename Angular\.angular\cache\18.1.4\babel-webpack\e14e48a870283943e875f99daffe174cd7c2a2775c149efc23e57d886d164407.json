{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.distinct = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar noop_1 = require(\"../util/noop\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction distinct(keySelector, flushes) {\n  return lift_1.operate(function (source, subscriber) {\n    var distinctKeys = new Set();\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var key = keySelector ? keySelector(value) : value;\n      if (!distinctKeys.has(key)) {\n        distinctKeys.add(key);\n        subscriber.next(value);\n      }\n    }));\n    flushes && innerFrom_1.innerFrom(flushes).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n      return distinctKeys.clear();\n    }, noop_1.noop));\n  });\n}\nexports.distinct = distinct;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "distinct", "lift_1", "require", "OperatorSubscriber_1", "noop_1", "innerFrom_1", "keySelector", "flushes", "operate", "source", "subscriber", "distinctKeys", "Set", "subscribe", "createOperatorSubscriber", "key", "has", "add", "next", "innerFrom", "clear", "noop"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/distinct.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.distinct = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar noop_1 = require(\"../util/noop\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction distinct(keySelector, flushes) {\n    return lift_1.operate(function (source, subscriber) {\n        var distinctKeys = new Set();\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            var key = keySelector ? keySelector(value) : value;\n            if (!distinctKeys.has(key)) {\n                distinctKeys.add(key);\n                subscriber.next(value);\n            }\n        }));\n        flushes && innerFrom_1.innerFrom(flushes).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () { return distinctKeys.clear(); }, noop_1.noop));\n    });\n}\nexports.distinct = distinct;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,QAAQ,GAAG,KAAK,CAAC;AACzB,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIE,MAAM,GAAGF,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIG,WAAW,GAAGH,OAAO,CAAC,yBAAyB,CAAC;AACpD,SAASF,QAAQA,CAACM,WAAW,EAAEC,OAAO,EAAE;EACpC,OAAON,MAAM,CAACO,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC5BH,MAAM,CAACI,SAAS,CAACV,oBAAoB,CAACW,wBAAwB,CAACJ,UAAU,EAAE,UAAUX,KAAK,EAAE;MACxF,IAAIgB,GAAG,GAAGT,WAAW,GAAGA,WAAW,CAACP,KAAK,CAAC,GAAGA,KAAK;MAClD,IAAI,CAACY,YAAY,CAACK,GAAG,CAACD,GAAG,CAAC,EAAE;QACxBJ,YAAY,CAACM,GAAG,CAACF,GAAG,CAAC;QACrBL,UAAU,CAACQ,IAAI,CAACnB,KAAK,CAAC;MAC1B;IACJ,CAAC,CAAC,CAAC;IACHQ,OAAO,IAAIF,WAAW,CAACc,SAAS,CAACZ,OAAO,CAAC,CAACM,SAAS,CAACV,oBAAoB,CAACW,wBAAwB,CAACJ,UAAU,EAAE,YAAY;MAAE,OAAOC,YAAY,CAACS,KAAK,CAAC,CAAC;IAAE,CAAC,EAAEhB,MAAM,CAACiB,IAAI,CAAC,CAAC;EAC7K,CAAC,CAAC;AACN;AACAvB,OAAO,CAACE,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}