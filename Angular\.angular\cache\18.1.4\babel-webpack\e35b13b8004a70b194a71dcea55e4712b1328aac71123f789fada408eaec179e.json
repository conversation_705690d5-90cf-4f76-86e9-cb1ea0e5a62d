{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.sampleTime = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar sample_1 = require(\"./sample\");\nvar interval_1 = require(\"../observable/interval\");\nfunction sampleTime(period, scheduler) {\n  if (scheduler === void 0) {\n    scheduler = async_1.asyncScheduler;\n  }\n  return sample_1.sample(interval_1.interval(period, scheduler));\n}\nexports.sampleTime = sampleTime;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "sampleTime", "async_1", "require", "sample_1", "interval_1", "period", "scheduler", "asyncScheduler", "sample", "interval"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/sampleTime.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sampleTime = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar sample_1 = require(\"./sample\");\nvar interval_1 = require(\"../observable/interval\");\nfunction sampleTime(period, scheduler) {\n    if (scheduler === void 0) { scheduler = async_1.asyncScheduler; }\n    return sample_1.sample(interval_1.interval(period, scheduler));\n}\nexports.sampleTime = sampleTime;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAIC,OAAO,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAC3C,IAAIC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;AAClC,IAAIE,UAAU,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAClD,SAASF,UAAUA,CAACK,MAAM,EAAEC,SAAS,EAAE;EACnC,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IAAEA,SAAS,GAAGL,OAAO,CAACM,cAAc;EAAE;EAChE,OAAOJ,QAAQ,CAACK,MAAM,CAACJ,UAAU,CAACK,QAAQ,CAACJ,MAAM,EAAEC,SAAS,CAAC,CAAC;AAClE;AACAR,OAAO,CAACE,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}