{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.exhaustMap = void 0;\nvar map_1 = require(\"./map\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction exhaustMap(project, resultSelector) {\n  if (resultSelector) {\n    return function (source) {\n      return source.pipe(exhaustMap(function (a, i) {\n        return innerFrom_1.innerFrom(project(a, i)).pipe(map_1.map(function (b, ii) {\n          return resultSelector(a, b, i, ii);\n        }));\n      }));\n    };\n  }\n  return lift_1.operate(function (source, subscriber) {\n    var index = 0;\n    var innerSub = null;\n    var isComplete = false;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (outerValue) {\n      if (!innerSub) {\n        innerSub = OperatorSubscriber_1.createOperatorSubscriber(subscriber, undefined, function () {\n          innerSub = null;\n          isComplete && subscriber.complete();\n        });\n        innerFrom_1.innerFrom(project(outerValue, index++)).subscribe(innerSub);\n      }\n    }, function () {\n      isComplete = true;\n      !innerSub && subscriber.complete();\n    }));\n  });\n}\nexports.exhaustMap = exhaustMap;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "exhaustMap", "map_1", "require", "innerFrom_1", "lift_1", "OperatorSubscriber_1", "project", "resultSelector", "source", "pipe", "a", "i", "innerFrom", "map", "b", "ii", "operate", "subscriber", "index", "innerSub", "isComplete", "subscribe", "createOperatorSubscriber", "outerValue", "undefined", "complete"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/exhaustMap.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.exhaustMap = void 0;\nvar map_1 = require(\"./map\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction exhaustMap(project, resultSelector) {\n    if (resultSelector) {\n        return function (source) {\n            return source.pipe(exhaustMap(function (a, i) { return innerFrom_1.innerFrom(project(a, i)).pipe(map_1.map(function (b, ii) { return resultSelector(a, b, i, ii); })); }));\n        };\n    }\n    return lift_1.operate(function (source, subscriber) {\n        var index = 0;\n        var innerSub = null;\n        var isComplete = false;\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (outerValue) {\n            if (!innerSub) {\n                innerSub = OperatorSubscriber_1.createOperatorSubscriber(subscriber, undefined, function () {\n                    innerSub = null;\n                    isComplete && subscriber.complete();\n                });\n                innerFrom_1.innerFrom(project(outerValue, index++)).subscribe(innerSub);\n            }\n        }, function () {\n            isComplete = true;\n            !innerSub && subscriber.complete();\n        }));\n    });\n}\nexports.exhaustMap = exhaustMap;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAIC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC5B,IAAIC,WAAW,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AACpD,IAAIE,MAAM,GAAGF,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIG,oBAAoB,GAAGH,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,UAAUA,CAACM,OAAO,EAAEC,cAAc,EAAE;EACzC,IAAIA,cAAc,EAAE;IAChB,OAAO,UAAUC,MAAM,EAAE;MACrB,OAAOA,MAAM,CAACC,IAAI,CAACT,UAAU,CAAC,UAAUU,CAAC,EAAEC,CAAC,EAAE;QAAE,OAAOR,WAAW,CAACS,SAAS,CAACN,OAAO,CAACI,CAAC,EAAEC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACR,KAAK,CAACY,GAAG,CAAC,UAAUC,CAAC,EAAEC,EAAE,EAAE;UAAE,OAAOR,cAAc,CAACG,CAAC,EAAEI,CAAC,EAAEH,CAAC,EAAEI,EAAE,CAAC;QAAE,CAAC,CAAC,CAAC;MAAE,CAAC,CAAC,CAAC;IAC9K,CAAC;EACL;EACA,OAAOX,MAAM,CAACY,OAAO,CAAC,UAAUR,MAAM,EAAES,UAAU,EAAE;IAChD,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,UAAU,GAAG,KAAK;IACtBZ,MAAM,CAACa,SAAS,CAAChB,oBAAoB,CAACiB,wBAAwB,CAACL,UAAU,EAAE,UAAUM,UAAU,EAAE;MAC7F,IAAI,CAACJ,QAAQ,EAAE;QACXA,QAAQ,GAAGd,oBAAoB,CAACiB,wBAAwB,CAACL,UAAU,EAAEO,SAAS,EAAE,YAAY;UACxFL,QAAQ,GAAG,IAAI;UACfC,UAAU,IAAIH,UAAU,CAACQ,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC;QACFtB,WAAW,CAACS,SAAS,CAACN,OAAO,CAACiB,UAAU,EAAEL,KAAK,EAAE,CAAC,CAAC,CAACG,SAAS,CAACF,QAAQ,CAAC;MAC3E;IACJ,CAAC,EAAE,YAAY;MACXC,UAAU,GAAG,IAAI;MACjB,CAACD,QAAQ,IAAIF,UAAU,CAACQ,QAAQ,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACA3B,OAAO,CAACE,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}