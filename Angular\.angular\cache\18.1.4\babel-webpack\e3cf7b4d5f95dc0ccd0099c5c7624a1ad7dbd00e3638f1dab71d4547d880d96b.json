{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.pipeFromArray = exports.pipe = void 0;\nvar identity_1 = require(\"./identity\");\nfunction pipe() {\n  var fns = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    fns[_i] = arguments[_i];\n  }\n  return pipeFromArray(fns);\n}\nexports.pipe = pipe;\nfunction pipeFromArray(fns) {\n  if (fns.length === 0) {\n    return identity_1.identity;\n  }\n  if (fns.length === 1) {\n    return fns[0];\n  }\n  return function piped(input) {\n    return fns.reduce(function (prev, fn) {\n      return fn(prev);\n    }, input);\n  };\n}\nexports.pipeFromArray = pipeFromArray;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "pipeFromArray", "pipe", "identity_1", "require", "fns", "_i", "arguments", "length", "identity", "piped", "input", "reduce", "prev", "fn"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/pipe.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.pipeFromArray = exports.pipe = void 0;\nvar identity_1 = require(\"./identity\");\nfunction pipe() {\n    var fns = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        fns[_i] = arguments[_i];\n    }\n    return pipeFromArray(fns);\n}\nexports.pipe = pipe;\nfunction pipeFromArray(fns) {\n    if (fns.length === 0) {\n        return identity_1.identity;\n    }\n    if (fns.length === 1) {\n        return fns[0];\n    }\n    return function piped(input) {\n        return fns.reduce(function (prev, fn) { return fn(prev); }, input);\n    };\n}\nexports.pipeFromArray = pipeFromArray;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,aAAa,GAAGF,OAAO,CAACG,IAAI,GAAG,KAAK,CAAC;AAC7C,IAAIC,UAAU,GAAGC,OAAO,CAAC,YAAY,CAAC;AACtC,SAASF,IAAIA,CAAA,EAAG;EACZ,IAAIG,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,GAAG,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC3B;EACA,OAAOL,aAAa,CAACI,GAAG,CAAC;AAC7B;AACAN,OAAO,CAACG,IAAI,GAAGA,IAAI;AACnB,SAASD,aAAaA,CAACI,GAAG,EAAE;EACxB,IAAIA,GAAG,CAACG,MAAM,KAAK,CAAC,EAAE;IAClB,OAAOL,UAAU,CAACM,QAAQ;EAC9B;EACA,IAAIJ,GAAG,CAACG,MAAM,KAAK,CAAC,EAAE;IAClB,OAAOH,GAAG,CAAC,CAAC,CAAC;EACjB;EACA,OAAO,SAASK,KAAKA,CAACC,KAAK,EAAE;IACzB,OAAON,GAAG,CAACO,MAAM,CAAC,UAAUC,IAAI,EAAEC,EAAE,EAAE;MAAE,OAAOA,EAAE,CAACD,IAAI,CAAC;IAAE,CAAC,EAAEF,KAAK,CAAC;EACtE,CAAC;AACL;AACAZ,OAAO,CAACE,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}