{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createInvalidObservableTypeError = void 0;\nfunction createInvalidObservableTypeError(input) {\n  return new TypeError(\"You provided \" + (input !== null && typeof input === 'object' ? 'an invalid object' : \"'\" + input + \"'\") + \" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.\");\n}\nexports.createInvalidObservableTypeError = createInvalidObservableTypeError;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "createInvalidObservableTypeError", "input", "TypeError"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/util/throwUnobservableError.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createInvalidObservableTypeError = void 0;\nfunction createInvalidObservableTypeError(input) {\n    return new TypeError(\"You provided \" + (input !== null && typeof input === 'object' ? 'an invalid object' : \"'\" + input + \"'\") + \" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.\");\n}\nexports.createInvalidObservableTypeError = createInvalidObservableTypeError;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,gCAAgC,GAAG,KAAK,CAAC;AACjD,SAASA,gCAAgCA,CAACC,KAAK,EAAE;EAC7C,OAAO,IAAIC,SAAS,CAAC,eAAe,IAAID,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAG,mBAAmB,GAAG,GAAG,GAAGA,KAAK,GAAG,GAAG,CAAC,GAAG,0HAA0H,CAAC;AAChQ;AACAH,OAAO,CAACE,gCAAgC,GAAGA,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}