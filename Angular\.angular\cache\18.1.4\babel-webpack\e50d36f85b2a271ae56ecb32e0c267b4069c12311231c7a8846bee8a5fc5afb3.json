{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Observable = void 0;\nvar Subscriber_1 = require(\"./Subscriber\");\nvar Subscription_1 = require(\"./Subscription\");\nvar observable_1 = require(\"./symbol/observable\");\nvar pipe_1 = require(\"./util/pipe\");\nvar config_1 = require(\"./config\");\nvar isFunction_1 = require(\"./util/isFunction\");\nvar errorContext_1 = require(\"./util/errorContext\");\nvar Observable = function () {\n  function Observable(subscribe) {\n    if (subscribe) {\n      this._subscribe = subscribe;\n    }\n  }\n  Observable.prototype.lift = function (operator) {\n    var observable = new Observable();\n    observable.source = this;\n    observable.operator = operator;\n    return observable;\n  };\n  Observable.prototype.subscribe = function (observerOrNext, error, complete) {\n    var _this = this;\n    var subscriber = isSubscriber(observerOrNext) ? observerOrNext : new Subscriber_1.SafeSubscriber(observerOrNext, error, complete);\n    errorContext_1.errorContext(function () {\n      var _a = _this,\n        operator = _a.operator,\n        source = _a.source;\n      subscriber.add(operator ? operator.call(subscriber, source) : source ? _this._subscribe(subscriber) : _this._trySubscribe(subscriber));\n    });\n    return subscriber;\n  };\n  Observable.prototype._trySubscribe = function (sink) {\n    try {\n      return this._subscribe(sink);\n    } catch (err) {\n      sink.error(err);\n    }\n  };\n  Observable.prototype.forEach = function (next, promiseCtor) {\n    var _this = this;\n    promiseCtor = getPromiseCtor(promiseCtor);\n    return new promiseCtor(function (resolve, reject) {\n      var subscriber = new Subscriber_1.SafeSubscriber({\n        next: function (value) {\n          try {\n            next(value);\n          } catch (err) {\n            reject(err);\n            subscriber.unsubscribe();\n          }\n        },\n        error: reject,\n        complete: resolve\n      });\n      _this.subscribe(subscriber);\n    });\n  };\n  Observable.prototype._subscribe = function (subscriber) {\n    var _a;\n    return (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber);\n  };\n  Observable.prototype[observable_1.observable] = function () {\n    return this;\n  };\n  Observable.prototype.pipe = function () {\n    var operations = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      operations[_i] = arguments[_i];\n    }\n    return pipe_1.pipeFromArray(operations)(this);\n  };\n  Observable.prototype.toPromise = function (promiseCtor) {\n    var _this = this;\n    promiseCtor = getPromiseCtor(promiseCtor);\n    return new promiseCtor(function (resolve, reject) {\n      var value;\n      _this.subscribe(function (x) {\n        return value = x;\n      }, function (err) {\n        return reject(err);\n      }, function () {\n        return resolve(value);\n      });\n    });\n  };\n  Observable.create = function (subscribe) {\n    return new Observable(subscribe);\n  };\n  return Observable;\n}();\nexports.Observable = Observable;\nfunction getPromiseCtor(promiseCtor) {\n  var _a;\n  return (_a = promiseCtor !== null && promiseCtor !== void 0 ? promiseCtor : config_1.config.Promise) !== null && _a !== void 0 ? _a : Promise;\n}\nfunction isObserver(value) {\n  return value && isFunction_1.isFunction(value.next) && isFunction_1.isFunction(value.error) && isFunction_1.isFunction(value.complete);\n}\nfunction isSubscriber(value) {\n  return value && value instanceof Subscriber_1.Subscriber || isObserver(value) && Subscription_1.isSubscription(value);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Observable", "Subscriber_1", "require", "Subscription_1", "observable_1", "pipe_1", "config_1", "isFunction_1", "errorContext_1", "subscribe", "_subscribe", "prototype", "lift", "operator", "observable", "source", "observerOrNext", "error", "complete", "_this", "subscriber", "isSubscriber", "SafeSubscriber", "errorContext", "_a", "add", "call", "_trySubscribe", "sink", "err", "for<PERSON>ach", "next", "promiseCtor", "getPromiseCtor", "resolve", "reject", "unsubscribe", "pipe", "operations", "_i", "arguments", "length", "pipeFromArray", "to<PERSON>romise", "x", "create", "config", "Promise", "isObserver", "isFunction", "Subscriber", "isSubscription"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/Observable.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Observable = void 0;\nvar Subscriber_1 = require(\"./Subscriber\");\nvar Subscription_1 = require(\"./Subscription\");\nvar observable_1 = require(\"./symbol/observable\");\nvar pipe_1 = require(\"./util/pipe\");\nvar config_1 = require(\"./config\");\nvar isFunction_1 = require(\"./util/isFunction\");\nvar errorContext_1 = require(\"./util/errorContext\");\nvar Observable = (function () {\n    function Observable(subscribe) {\n        if (subscribe) {\n            this._subscribe = subscribe;\n        }\n    }\n    Observable.prototype.lift = function (operator) {\n        var observable = new Observable();\n        observable.source = this;\n        observable.operator = operator;\n        return observable;\n    };\n    Observable.prototype.subscribe = function (observerOrNext, error, complete) {\n        var _this = this;\n        var subscriber = isSubscriber(observerOrNext) ? observerOrNext : new Subscriber_1.SafeSubscriber(observerOrNext, error, complete);\n        errorContext_1.errorContext(function () {\n            var _a = _this, operator = _a.operator, source = _a.source;\n            subscriber.add(operator\n                ?\n                    operator.call(subscriber, source)\n                : source\n                    ?\n                        _this._subscribe(subscriber)\n                    :\n                        _this._trySubscribe(subscriber));\n        });\n        return subscriber;\n    };\n    Observable.prototype._trySubscribe = function (sink) {\n        try {\n            return this._subscribe(sink);\n        }\n        catch (err) {\n            sink.error(err);\n        }\n    };\n    Observable.prototype.forEach = function (next, promiseCtor) {\n        var _this = this;\n        promiseCtor = getPromiseCtor(promiseCtor);\n        return new promiseCtor(function (resolve, reject) {\n            var subscriber = new Subscriber_1.SafeSubscriber({\n                next: function (value) {\n                    try {\n                        next(value);\n                    }\n                    catch (err) {\n                        reject(err);\n                        subscriber.unsubscribe();\n                    }\n                },\n                error: reject,\n                complete: resolve,\n            });\n            _this.subscribe(subscriber);\n        });\n    };\n    Observable.prototype._subscribe = function (subscriber) {\n        var _a;\n        return (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber);\n    };\n    Observable.prototype[observable_1.observable] = function () {\n        return this;\n    };\n    Observable.prototype.pipe = function () {\n        var operations = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            operations[_i] = arguments[_i];\n        }\n        return pipe_1.pipeFromArray(operations)(this);\n    };\n    Observable.prototype.toPromise = function (promiseCtor) {\n        var _this = this;\n        promiseCtor = getPromiseCtor(promiseCtor);\n        return new promiseCtor(function (resolve, reject) {\n            var value;\n            _this.subscribe(function (x) { return (value = x); }, function (err) { return reject(err); }, function () { return resolve(value); });\n        });\n    };\n    Observable.create = function (subscribe) {\n        return new Observable(subscribe);\n    };\n    return Observable;\n}());\nexports.Observable = Observable;\nfunction getPromiseCtor(promiseCtor) {\n    var _a;\n    return (_a = promiseCtor !== null && promiseCtor !== void 0 ? promiseCtor : config_1.config.Promise) !== null && _a !== void 0 ? _a : Promise;\n}\nfunction isObserver(value) {\n    return value && isFunction_1.isFunction(value.next) && isFunction_1.isFunction(value.error) && isFunction_1.isFunction(value.complete);\n}\nfunction isSubscriber(value) {\n    return (value && value instanceof Subscriber_1.Subscriber) || (isObserver(value) && Subscription_1.isSubscription(value));\n}\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAIC,YAAY,GAAGC,OAAO,CAAC,cAAc,CAAC;AAC1C,IAAIC,cAAc,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AAC9C,IAAIE,YAAY,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AACjD,IAAIG,MAAM,GAAGH,OAAO,CAAC,aAAa,CAAC;AACnC,IAAII,QAAQ,GAAGJ,OAAO,CAAC,UAAU,CAAC;AAClC,IAAIK,YAAY,GAAGL,OAAO,CAAC,mBAAmB,CAAC;AAC/C,IAAIM,cAAc,GAAGN,OAAO,CAAC,qBAAqB,CAAC;AACnD,IAAIF,UAAU,GAAI,YAAY;EAC1B,SAASA,UAAUA,CAACS,SAAS,EAAE;IAC3B,IAAIA,SAAS,EAAE;MACX,IAAI,CAACC,UAAU,GAAGD,SAAS;IAC/B;EACJ;EACAT,UAAU,CAACW,SAAS,CAACC,IAAI,GAAG,UAAUC,QAAQ,EAAE;IAC5C,IAAIC,UAAU,GAAG,IAAId,UAAU,CAAC,CAAC;IACjCc,UAAU,CAACC,MAAM,GAAG,IAAI;IACxBD,UAAU,CAACD,QAAQ,GAAGA,QAAQ;IAC9B,OAAOC,UAAU;EACrB,CAAC;EACDd,UAAU,CAACW,SAAS,CAACF,SAAS,GAAG,UAAUO,cAAc,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACxE,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIC,UAAU,GAAGC,YAAY,CAACL,cAAc,CAAC,GAAGA,cAAc,GAAG,IAAIf,YAAY,CAACqB,cAAc,CAACN,cAAc,EAAEC,KAAK,EAAEC,QAAQ,CAAC;IACjIV,cAAc,CAACe,YAAY,CAAC,YAAY;MACpC,IAAIC,EAAE,GAAGL,KAAK;QAAEN,QAAQ,GAAGW,EAAE,CAACX,QAAQ;QAAEE,MAAM,GAAGS,EAAE,CAACT,MAAM;MAC1DK,UAAU,CAACK,GAAG,CAACZ,QAAQ,GAEfA,QAAQ,CAACa,IAAI,CAACN,UAAU,EAAEL,MAAM,CAAC,GACnCA,MAAM,GAEAI,KAAK,CAACT,UAAU,CAACU,UAAU,CAAC,GAE5BD,KAAK,CAACQ,aAAa,CAACP,UAAU,CAAC,CAAC;IAChD,CAAC,CAAC;IACF,OAAOA,UAAU;EACrB,CAAC;EACDpB,UAAU,CAACW,SAAS,CAACgB,aAAa,GAAG,UAAUC,IAAI,EAAE;IACjD,IAAI;MACA,OAAO,IAAI,CAAClB,UAAU,CAACkB,IAAI,CAAC;IAChC,CAAC,CACD,OAAOC,GAAG,EAAE;MACRD,IAAI,CAACX,KAAK,CAACY,GAAG,CAAC;IACnB;EACJ,CAAC;EACD7B,UAAU,CAACW,SAAS,CAACmB,OAAO,GAAG,UAAUC,IAAI,EAAEC,WAAW,EAAE;IACxD,IAAIb,KAAK,GAAG,IAAI;IAChBa,WAAW,GAAGC,cAAc,CAACD,WAAW,CAAC;IACzC,OAAO,IAAIA,WAAW,CAAC,UAAUE,OAAO,EAAEC,MAAM,EAAE;MAC9C,IAAIf,UAAU,GAAG,IAAInB,YAAY,CAACqB,cAAc,CAAC;QAC7CS,IAAI,EAAE,SAAAA,CAAUhC,KAAK,EAAE;UACnB,IAAI;YACAgC,IAAI,CAAChC,KAAK,CAAC;UACf,CAAC,CACD,OAAO8B,GAAG,EAAE;YACRM,MAAM,CAACN,GAAG,CAAC;YACXT,UAAU,CAACgB,WAAW,CAAC,CAAC;UAC5B;QACJ,CAAC;QACDnB,KAAK,EAAEkB,MAAM;QACbjB,QAAQ,EAAEgB;MACd,CAAC,CAAC;MACFf,KAAK,CAACV,SAAS,CAACW,UAAU,CAAC;IAC/B,CAAC,CAAC;EACN,CAAC;EACDpB,UAAU,CAACW,SAAS,CAACD,UAAU,GAAG,UAAUU,UAAU,EAAE;IACpD,IAAII,EAAE;IACN,OAAO,CAACA,EAAE,GAAG,IAAI,CAACT,MAAM,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACf,SAAS,CAACW,UAAU,CAAC;EAC3F,CAAC;EACDpB,UAAU,CAACW,SAAS,CAACP,YAAY,CAACU,UAAU,CAAC,GAAG,YAAY;IACxD,OAAO,IAAI;EACf,CAAC;EACDd,UAAU,CAACW,SAAS,CAAC0B,IAAI,GAAG,YAAY;IACpC,IAAIC,UAAU,GAAG,EAAE;IACnB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC1CD,UAAU,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAClC;IACA,OAAOlC,MAAM,CAACqC,aAAa,CAACJ,UAAU,CAAC,CAAC,IAAI,CAAC;EACjD,CAAC;EACDtC,UAAU,CAACW,SAAS,CAACgC,SAAS,GAAG,UAAUX,WAAW,EAAE;IACpD,IAAIb,KAAK,GAAG,IAAI;IAChBa,WAAW,GAAGC,cAAc,CAACD,WAAW,CAAC;IACzC,OAAO,IAAIA,WAAW,CAAC,UAAUE,OAAO,EAAEC,MAAM,EAAE;MAC9C,IAAIpC,KAAK;MACToB,KAAK,CAACV,SAAS,CAAC,UAAUmC,CAAC,EAAE;QAAE,OAAQ7C,KAAK,GAAG6C,CAAC;MAAG,CAAC,EAAE,UAAUf,GAAG,EAAE;QAAE,OAAOM,MAAM,CAACN,GAAG,CAAC;MAAE,CAAC,EAAE,YAAY;QAAE,OAAOK,OAAO,CAACnC,KAAK,CAAC;MAAE,CAAC,CAAC;IACzI,CAAC,CAAC;EACN,CAAC;EACDC,UAAU,CAAC6C,MAAM,GAAG,UAAUpC,SAAS,EAAE;IACrC,OAAO,IAAIT,UAAU,CAACS,SAAS,CAAC;EACpC,CAAC;EACD,OAAOT,UAAU;AACrB,CAAC,CAAC,CAAE;AACJF,OAAO,CAACE,UAAU,GAAGA,UAAU;AAC/B,SAASiC,cAAcA,CAACD,WAAW,EAAE;EACjC,IAAIR,EAAE;EACN,OAAO,CAACA,EAAE,GAAGQ,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG1B,QAAQ,CAACwC,MAAM,CAACC,OAAO,MAAM,IAAI,IAAIvB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGuB,OAAO;AACjJ;AACA,SAASC,UAAUA,CAACjD,KAAK,EAAE;EACvB,OAAOA,KAAK,IAAIQ,YAAY,CAAC0C,UAAU,CAAClD,KAAK,CAACgC,IAAI,CAAC,IAAIxB,YAAY,CAAC0C,UAAU,CAAClD,KAAK,CAACkB,KAAK,CAAC,IAAIV,YAAY,CAAC0C,UAAU,CAAClD,KAAK,CAACmB,QAAQ,CAAC;AAC1I;AACA,SAASG,YAAYA,CAACtB,KAAK,EAAE;EACzB,OAAQA,KAAK,IAAIA,KAAK,YAAYE,YAAY,CAACiD,UAAU,IAAMF,UAAU,CAACjD,KAAK,CAAC,IAAII,cAAc,CAACgD,cAAc,CAACpD,KAAK,CAAE;AAC7H", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}