{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.mergeMapTo = void 0;\nvar mergeMap_1 = require(\"./mergeMap\");\nvar isFunction_1 = require(\"../util/isFunction\");\nfunction mergeMapTo(innerObservable, resultSelector, concurrent) {\n  if (concurrent === void 0) {\n    concurrent = Infinity;\n  }\n  if (isFunction_1.isFunction(resultSelector)) {\n    return mergeMap_1.mergeMap(function () {\n      return innerObservable;\n    }, resultSelector, concurrent);\n  }\n  if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n  return mergeMap_1.mergeMap(function () {\n    return innerObservable;\n  }, concurrent);\n}\nexports.mergeMapTo = mergeMapTo;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "mergeMapTo", "mergeMap_1", "require", "isFunction_1", "innerObservable", "resultSelector", "concurrent", "Infinity", "isFunction", "mergeMap"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/mergeMapTo.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeMapTo = void 0;\nvar mergeMap_1 = require(\"./mergeMap\");\nvar isFunction_1 = require(\"../util/isFunction\");\nfunction mergeMapTo(innerObservable, resultSelector, concurrent) {\n    if (concurrent === void 0) { concurrent = Infinity; }\n    if (isFunction_1.isFunction(resultSelector)) {\n        return mergeMap_1.mergeMap(function () { return innerObservable; }, resultSelector, concurrent);\n    }\n    if (typeof resultSelector === 'number') {\n        concurrent = resultSelector;\n    }\n    return mergeMap_1.mergeMap(function () { return innerObservable; }, concurrent);\n}\nexports.mergeMapTo = mergeMapTo;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAIC,UAAU,GAAGC,OAAO,CAAC,YAAY,CAAC;AACtC,IAAIC,YAAY,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AAChD,SAASF,UAAUA,CAACI,eAAe,EAAEC,cAAc,EAAEC,UAAU,EAAE;EAC7D,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAGC,QAAQ;EAAE;EACpD,IAAIJ,YAAY,CAACK,UAAU,CAACH,cAAc,CAAC,EAAE;IACzC,OAAOJ,UAAU,CAACQ,QAAQ,CAAC,YAAY;MAAE,OAAOL,eAAe;IAAE,CAAC,EAAEC,cAAc,EAAEC,UAAU,CAAC;EACnG;EACA,IAAI,OAAOD,cAAc,KAAK,QAAQ,EAAE;IACpCC,UAAU,GAAGD,cAAc;EAC/B;EACA,OAAOJ,UAAU,CAACQ,QAAQ,CAAC,YAAY;IAAE,OAAOL,eAAe;EAAE,CAAC,EAAEE,UAAU,CAAC;AACnF;AACAR,OAAO,CAACE,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}