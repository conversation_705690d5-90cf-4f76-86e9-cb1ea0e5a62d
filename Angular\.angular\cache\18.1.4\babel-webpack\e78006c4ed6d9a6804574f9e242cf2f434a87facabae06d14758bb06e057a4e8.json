{"ast": null, "code": "\"use strict\";\n\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.VirtualAction = exports.VirtualTimeScheduler = void 0;\nvar AsyncAction_1 = require(\"./AsyncAction\");\nvar Subscription_1 = require(\"../Subscription\");\nvar AsyncScheduler_1 = require(\"./AsyncScheduler\");\nvar VirtualTimeScheduler = function (_super) {\n  __extends(VirtualTimeScheduler, _super);\n  function VirtualTimeScheduler(schedulerActionCtor, maxFrames) {\n    if (schedulerActionCtor === void 0) {\n      schedulerActionCtor = VirtualAction;\n    }\n    if (maxFrames === void 0) {\n      maxFrames = Infinity;\n    }\n    var _this = _super.call(this, schedulerActionCtor, function () {\n      return _this.frame;\n    }) || this;\n    _this.maxFrames = maxFrames;\n    _this.frame = 0;\n    _this.index = -1;\n    return _this;\n  }\n  VirtualTimeScheduler.prototype.flush = function () {\n    var _a = this,\n      actions = _a.actions,\n      maxFrames = _a.maxFrames;\n    var error;\n    var action;\n    while ((action = actions[0]) && action.delay <= maxFrames) {\n      actions.shift();\n      this.frame = action.delay;\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    }\n    if (error) {\n      while (action = actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  };\n  VirtualTimeScheduler.frameTimeFactor = 10;\n  return VirtualTimeScheduler;\n}(AsyncScheduler_1.AsyncScheduler);\nexports.VirtualTimeScheduler = VirtualTimeScheduler;\nvar VirtualAction = function (_super) {\n  __extends(VirtualAction, _super);\n  function VirtualAction(scheduler, work, index) {\n    if (index === void 0) {\n      index = scheduler.index += 1;\n    }\n    var _this = _super.call(this, scheduler, work) || this;\n    _this.scheduler = scheduler;\n    _this.work = work;\n    _this.index = index;\n    _this.active = true;\n    _this.index = scheduler.index = index;\n    return _this;\n  }\n  VirtualAction.prototype.schedule = function (state, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (Number.isFinite(delay)) {\n      if (!this.id) {\n        return _super.prototype.schedule.call(this, state, delay);\n      }\n      this.active = false;\n      var action = new VirtualAction(this.scheduler, this.work);\n      this.add(action);\n      return action.schedule(state, delay);\n    } else {\n      return Subscription_1.Subscription.EMPTY;\n    }\n  };\n  VirtualAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    this.delay = scheduler.frame + delay;\n    var actions = scheduler.actions;\n    actions.push(this);\n    actions.sort(VirtualAction.sortActions);\n    return 1;\n  };\n  VirtualAction.prototype.recycleAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    return undefined;\n  };\n  VirtualAction.prototype._execute = function (state, delay) {\n    if (this.active === true) {\n      return _super.prototype._execute.call(this, state, delay);\n    }\n  };\n  VirtualAction.sortActions = function (a, b) {\n    if (a.delay === b.delay) {\n      if (a.index === b.index) {\n        return 0;\n      } else if (a.index > b.index) {\n        return 1;\n      } else {\n        return -1;\n      }\n    } else if (a.delay > b.delay) {\n      return 1;\n    } else {\n      return -1;\n    }\n  };\n  return VirtualAction;\n}(AsyncAction_1.AsyncAction);\nexports.VirtualAction = VirtualAction;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "defineProperty", "exports", "value", "VirtualAction", "VirtualTimeScheduler", "AsyncAction_1", "require", "Subscription_1", "AsyncScheduler_1", "_super", "schedulerActionCtor", "maxFrames", "Infinity", "_this", "frame", "index", "flush", "_a", "actions", "error", "action", "delay", "shift", "execute", "state", "unsubscribe", "frameTimeFactor", "AsyncScheduler", "scheduler", "work", "active", "schedule", "Number", "isFinite", "id", "add", "Subscription", "EMPTY", "requestAsyncId", "push", "sort", "sortActions", "recycleAsyncId", "undefined", "_execute", "a", "AsyncAction"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/scheduler/VirtualTimeScheduler.js"], "sourcesContent": ["\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.VirtualAction = exports.VirtualTimeScheduler = void 0;\nvar AsyncAction_1 = require(\"./AsyncAction\");\nvar Subscription_1 = require(\"../Subscription\");\nvar AsyncScheduler_1 = require(\"./AsyncScheduler\");\nvar VirtualTimeScheduler = (function (_super) {\n    __extends(VirtualTimeScheduler, _super);\n    function VirtualTimeScheduler(schedulerActionCtor, maxFrames) {\n        if (schedulerActionCtor === void 0) { schedulerActionCtor = VirtualAction; }\n        if (maxFrames === void 0) { maxFrames = Infinity; }\n        var _this = _super.call(this, schedulerActionCtor, function () { return _this.frame; }) || this;\n        _this.maxFrames = maxFrames;\n        _this.frame = 0;\n        _this.index = -1;\n        return _this;\n    }\n    VirtualTimeScheduler.prototype.flush = function () {\n        var _a = this, actions = _a.actions, maxFrames = _a.maxFrames;\n        var error;\n        var action;\n        while ((action = actions[0]) && action.delay <= maxFrames) {\n            actions.shift();\n            this.frame = action.delay;\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        }\n        if (error) {\n            while ((action = actions.shift())) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    };\n    VirtualTimeScheduler.frameTimeFactor = 10;\n    return VirtualTimeScheduler;\n}(AsyncScheduler_1.AsyncScheduler));\nexports.VirtualTimeScheduler = VirtualTimeScheduler;\nvar VirtualAction = (function (_super) {\n    __extends(VirtualAction, _super);\n    function VirtualAction(scheduler, work, index) {\n        if (index === void 0) { index = (scheduler.index += 1); }\n        var _this = _super.call(this, scheduler, work) || this;\n        _this.scheduler = scheduler;\n        _this.work = work;\n        _this.index = index;\n        _this.active = true;\n        _this.index = scheduler.index = index;\n        return _this;\n    }\n    VirtualAction.prototype.schedule = function (state, delay) {\n        if (delay === void 0) { delay = 0; }\n        if (Number.isFinite(delay)) {\n            if (!this.id) {\n                return _super.prototype.schedule.call(this, state, delay);\n            }\n            this.active = false;\n            var action = new VirtualAction(this.scheduler, this.work);\n            this.add(action);\n            return action.schedule(state, delay);\n        }\n        else {\n            return Subscription_1.Subscription.EMPTY;\n        }\n    };\n    VirtualAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n        if (delay === void 0) { delay = 0; }\n        this.delay = scheduler.frame + delay;\n        var actions = scheduler.actions;\n        actions.push(this);\n        actions.sort(VirtualAction.sortActions);\n        return 1;\n    };\n    VirtualAction.prototype.recycleAsyncId = function (scheduler, id, delay) {\n        if (delay === void 0) { delay = 0; }\n        return undefined;\n    };\n    VirtualAction.prototype._execute = function (state, delay) {\n        if (this.active === true) {\n            return _super.prototype._execute.call(this, state, delay);\n        }\n    };\n    VirtualAction.sortActions = function (a, b) {\n        if (a.delay === b.delay) {\n            if (a.index === b.index) {\n                return 0;\n            }\n            else if (a.index > b.index) {\n                return 1;\n            }\n            else {\n                return -1;\n            }\n        }\n        else if (a.delay > b.delay) {\n            return 1;\n        }\n        else {\n            return -1;\n        }\n    };\n    return VirtualAction;\n}(AsyncAction_1.AsyncAction));\nexports.VirtualAction = VirtualAction;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIC,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IACrG,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnB,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAIA,CAAC,KAAK,IAAI,EACrC,MAAM,IAAIS,SAAS,CAAC,sBAAsB,GAAGC,MAAM,CAACV,CAAC,CAAC,GAAG,+BAA+B,CAAC;IAC7FF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASW,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGb,CAAC;IAAE;IACtCA,CAAC,CAACO,SAAS,GAAGN,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACY,MAAM,CAACb,CAAC,CAAC,IAAIW,EAAE,CAACL,SAAS,GAAGN,CAAC,CAACM,SAAS,EAAE,IAAIK,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJV,MAAM,CAACa,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,aAAa,GAAGF,OAAO,CAACG,oBAAoB,GAAG,KAAK,CAAC;AAC7D,IAAIC,aAAa,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC5C,IAAIC,cAAc,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAC/C,IAAIE,gBAAgB,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AAClD,IAAIF,oBAAoB,GAAI,UAAUK,MAAM,EAAE;EAC1C1B,SAAS,CAACqB,oBAAoB,EAAEK,MAAM,CAAC;EACvC,SAASL,oBAAoBA,CAACM,mBAAmB,EAAEC,SAAS,EAAE;IAC1D,IAAID,mBAAmB,KAAK,KAAK,CAAC,EAAE;MAAEA,mBAAmB,GAAGP,aAAa;IAAE;IAC3E,IAAIQ,SAAS,KAAK,KAAK,CAAC,EAAE;MAAEA,SAAS,GAAGC,QAAQ;IAAE;IAClD,IAAIC,KAAK,GAAGJ,MAAM,CAACf,IAAI,CAAC,IAAI,EAAEgB,mBAAmB,EAAE,YAAY;MAAE,OAAOG,KAAK,CAACC,KAAK;IAAE,CAAC,CAAC,IAAI,IAAI;IAC/FD,KAAK,CAACF,SAAS,GAAGA,SAAS;IAC3BE,KAAK,CAACC,KAAK,GAAG,CAAC;IACfD,KAAK,CAACE,KAAK,GAAG,CAAC,CAAC;IAChB,OAAOF,KAAK;EAChB;EACAT,oBAAoB,CAACZ,SAAS,CAACwB,KAAK,GAAG,YAAY;IAC/C,IAAIC,EAAE,GAAG,IAAI;MAAEC,OAAO,GAAGD,EAAE,CAACC,OAAO;MAAEP,SAAS,GAAGM,EAAE,CAACN,SAAS;IAC7D,IAAIQ,KAAK;IACT,IAAIC,MAAM;IACV,OAAO,CAACA,MAAM,GAAGF,OAAO,CAAC,CAAC,CAAC,KAAKE,MAAM,CAACC,KAAK,IAAIV,SAAS,EAAE;MACvDO,OAAO,CAACI,KAAK,CAAC,CAAC;MACf,IAAI,CAACR,KAAK,GAAGM,MAAM,CAACC,KAAK;MACzB,IAAKF,KAAK,GAAGC,MAAM,CAACG,OAAO,CAACH,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACC,KAAK,CAAC,EAAG;QACtD;MACJ;IACJ;IACA,IAAIF,KAAK,EAAE;MACP,OAAQC,MAAM,GAAGF,OAAO,CAACI,KAAK,CAAC,CAAC,EAAG;QAC/BF,MAAM,CAACK,WAAW,CAAC,CAAC;MACxB;MACA,MAAMN,KAAK;IACf;EACJ,CAAC;EACDf,oBAAoB,CAACsB,eAAe,GAAG,EAAE;EACzC,OAAOtB,oBAAoB;AAC/B,CAAC,CAACI,gBAAgB,CAACmB,cAAc,CAAE;AACnC1B,OAAO,CAACG,oBAAoB,GAAGA,oBAAoB;AACnD,IAAID,aAAa,GAAI,UAAUM,MAAM,EAAE;EACnC1B,SAAS,CAACoB,aAAa,EAAEM,MAAM,CAAC;EAChC,SAASN,aAAaA,CAACyB,SAAS,EAAEC,IAAI,EAAEd,KAAK,EAAE;IAC3C,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAIa,SAAS,CAACb,KAAK,IAAI,CAAE;IAAE;IACxD,IAAIF,KAAK,GAAGJ,MAAM,CAACf,IAAI,CAAC,IAAI,EAAEkC,SAAS,EAAEC,IAAI,CAAC,IAAI,IAAI;IACtDhB,KAAK,CAACe,SAAS,GAAGA,SAAS;IAC3Bf,KAAK,CAACgB,IAAI,GAAGA,IAAI;IACjBhB,KAAK,CAACE,KAAK,GAAGA,KAAK;IACnBF,KAAK,CAACiB,MAAM,GAAG,IAAI;IACnBjB,KAAK,CAACE,KAAK,GAAGa,SAAS,CAACb,KAAK,GAAGA,KAAK;IACrC,OAAOF,KAAK;EAChB;EACAV,aAAa,CAACX,SAAS,CAACuC,QAAQ,GAAG,UAAUP,KAAK,EAAEH,KAAK,EAAE;IACvD,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,IAAIW,MAAM,CAACC,QAAQ,CAACZ,KAAK,CAAC,EAAE;MACxB,IAAI,CAAC,IAAI,CAACa,EAAE,EAAE;QACV,OAAOzB,MAAM,CAACjB,SAAS,CAACuC,QAAQ,CAACrC,IAAI,CAAC,IAAI,EAAE8B,KAAK,EAAEH,KAAK,CAAC;MAC7D;MACA,IAAI,CAACS,MAAM,GAAG,KAAK;MACnB,IAAIV,MAAM,GAAG,IAAIjB,aAAa,CAAC,IAAI,CAACyB,SAAS,EAAE,IAAI,CAACC,IAAI,CAAC;MACzD,IAAI,CAACM,GAAG,CAACf,MAAM,CAAC;MAChB,OAAOA,MAAM,CAACW,QAAQ,CAACP,KAAK,EAAEH,KAAK,CAAC;IACxC,CAAC,MACI;MACD,OAAOd,cAAc,CAAC6B,YAAY,CAACC,KAAK;IAC5C;EACJ,CAAC;EACDlC,aAAa,CAACX,SAAS,CAAC8C,cAAc,GAAG,UAAUV,SAAS,EAAEM,EAAE,EAAEb,KAAK,EAAE;IACrE,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,IAAI,CAACA,KAAK,GAAGO,SAAS,CAACd,KAAK,GAAGO,KAAK;IACpC,IAAIH,OAAO,GAAGU,SAAS,CAACV,OAAO;IAC/BA,OAAO,CAACqB,IAAI,CAAC,IAAI,CAAC;IAClBrB,OAAO,CAACsB,IAAI,CAACrC,aAAa,CAACsC,WAAW,CAAC;IACvC,OAAO,CAAC;EACZ,CAAC;EACDtC,aAAa,CAACX,SAAS,CAACkD,cAAc,GAAG,UAAUd,SAAS,EAAEM,EAAE,EAAEb,KAAK,EAAE;IACrE,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,OAAOsB,SAAS;EACpB,CAAC;EACDxC,aAAa,CAACX,SAAS,CAACoD,QAAQ,GAAG,UAAUpB,KAAK,EAAEH,KAAK,EAAE;IACvD,IAAI,IAAI,CAACS,MAAM,KAAK,IAAI,EAAE;MACtB,OAAOrB,MAAM,CAACjB,SAAS,CAACoD,QAAQ,CAAClD,IAAI,CAAC,IAAI,EAAE8B,KAAK,EAAEH,KAAK,CAAC;IAC7D;EACJ,CAAC;EACDlB,aAAa,CAACsC,WAAW,GAAG,UAAUI,CAAC,EAAE3D,CAAC,EAAE;IACxC,IAAI2D,CAAC,CAACxB,KAAK,KAAKnC,CAAC,CAACmC,KAAK,EAAE;MACrB,IAAIwB,CAAC,CAAC9B,KAAK,KAAK7B,CAAC,CAAC6B,KAAK,EAAE;QACrB,OAAO,CAAC;MACZ,CAAC,MACI,IAAI8B,CAAC,CAAC9B,KAAK,GAAG7B,CAAC,CAAC6B,KAAK,EAAE;QACxB,OAAO,CAAC;MACZ,CAAC,MACI;QACD,OAAO,CAAC,CAAC;MACb;IACJ,CAAC,MACI,IAAI8B,CAAC,CAACxB,KAAK,GAAGnC,CAAC,CAACmC,KAAK,EAAE;MACxB,OAAO,CAAC;IACZ,CAAC,MACI;MACD,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EACD,OAAOlB,aAAa;AACxB,CAAC,CAACE,aAAa,CAACyC,WAAW,CAAE;AAC7B7C,OAAO,CAACE,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}