{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.from = void 0;\nvar scheduled_1 = require(\"../scheduled/scheduled\");\nvar innerFrom_1 = require(\"./innerFrom\");\nfunction from(input, scheduler) {\n  return scheduler ? scheduled_1.scheduled(input, scheduler) : innerFrom_1.innerFrom(input);\n}\nexports.from = from;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "from", "scheduled_1", "require", "innerFrom_1", "input", "scheduler", "scheduled", "innerFrom"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/observable/from.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.from = void 0;\nvar scheduled_1 = require(\"../scheduled/scheduled\");\nvar innerFrom_1 = require(\"./innerFrom\");\nfunction from(input, scheduler) {\n    return scheduler ? scheduled_1.scheduled(input, scheduler) : innerFrom_1.innerFrom(input);\n}\nexports.from = from;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,IAAI,GAAG,KAAK,CAAC;AACrB,IAAIC,WAAW,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AACnD,IAAIC,WAAW,GAAGD,OAAO,CAAC,aAAa,CAAC;AACxC,SAASF,IAAIA,CAACI,KAAK,EAAEC,SAAS,EAAE;EAC5B,OAAOA,SAAS,GAAGJ,WAAW,CAACK,SAAS,CAACF,KAAK,EAAEC,SAAS,CAAC,GAAGF,WAAW,CAACI,SAAS,CAACH,KAAK,CAAC;AAC7F;AACAN,OAAO,CAACE,IAAI,GAAGA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}