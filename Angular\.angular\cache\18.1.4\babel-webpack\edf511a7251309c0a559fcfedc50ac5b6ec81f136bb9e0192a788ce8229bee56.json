{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.distinctUntilChanged = void 0;\nvar identity_1 = require(\"../util/identity\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction distinctUntilChanged(comparator, keySelector) {\n  if (keySelector === void 0) {\n    keySelector = identity_1.identity;\n  }\n  comparator = comparator !== null && comparator !== void 0 ? comparator : defaultCompare;\n  return lift_1.operate(function (source, subscriber) {\n    var previousKey;\n    var first = true;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var currentKey = keySelector(value);\n      if (first || !comparator(previousKey, currentKey)) {\n        first = false;\n        previousKey = currentKey;\n        subscriber.next(value);\n      }\n    }));\n  });\n}\nexports.distinctUntilChanged = distinctUntilChanged;\nfunction defaultCompare(a, b) {\n  return a === b;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "distinctUntilChanged", "identity_1", "require", "lift_1", "OperatorSubscriber_1", "comparator", "keySelector", "identity", "defaultCompare", "operate", "source", "subscriber", "previousKey", "first", "subscribe", "createOperatorSubscriber", "current<PERSON><PERSON>", "next", "a", "b"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/distinctUntilChanged.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.distinctUntilChanged = void 0;\nvar identity_1 = require(\"../util/identity\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction distinctUntilChanged(comparator, keySelector) {\n    if (keySelector === void 0) { keySelector = identity_1.identity; }\n    comparator = comparator !== null && comparator !== void 0 ? comparator : defaultCompare;\n    return lift_1.operate(function (source, subscriber) {\n        var previousKey;\n        var first = true;\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            var currentKey = keySelector(value);\n            if (first || !comparator(previousKey, currentKey)) {\n                first = false;\n                previousKey = currentKey;\n                subscriber.next(value);\n            }\n        }));\n    });\n}\nexports.distinctUntilChanged = distinctUntilChanged;\nfunction defaultCompare(a, b) {\n    return a === b;\n}\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,oBAAoB,GAAG,KAAK,CAAC;AACrC,IAAIC,UAAU,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC5C,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC1D,SAASF,oBAAoBA,CAACK,UAAU,EAAEC,WAAW,EAAE;EACnD,IAAIA,WAAW,KAAK,KAAK,CAAC,EAAE;IAAEA,WAAW,GAAGL,UAAU,CAACM,QAAQ;EAAE;EACjEF,UAAU,GAAGA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGG,cAAc;EACvF,OAAOL,MAAM,CAACM,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,WAAW;IACf,IAAIC,KAAK,GAAG,IAAI;IAChBH,MAAM,CAACI,SAAS,CAACV,oBAAoB,CAACW,wBAAwB,CAACJ,UAAU,EAAE,UAAUZ,KAAK,EAAE;MACxF,IAAIiB,UAAU,GAAGV,WAAW,CAACP,KAAK,CAAC;MACnC,IAAIc,KAAK,IAAI,CAACR,UAAU,CAACO,WAAW,EAAEI,UAAU,CAAC,EAAE;QAC/CH,KAAK,GAAG,KAAK;QACbD,WAAW,GAAGI,UAAU;QACxBL,UAAU,CAACM,IAAI,CAAClB,KAAK,CAAC;MAC1B;IACJ,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACAD,OAAO,CAACE,oBAAoB,GAAGA,oBAAoB;AACnD,SAASQ,cAAcA,CAACU,CAAC,EAAEC,CAAC,EAAE;EAC1B,OAAOD,CAAC,KAAKC,CAAC;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}