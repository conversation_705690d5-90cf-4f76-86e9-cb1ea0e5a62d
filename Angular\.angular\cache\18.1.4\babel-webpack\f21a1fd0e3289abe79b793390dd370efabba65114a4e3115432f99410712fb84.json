{"ast": null, "code": "\"use strict\";\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from) {\n  for (var i = 0, il = from.length, j = to.length; i < il; i++, j++) to[j] = from[i];\n  return to;\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.zip = void 0;\nvar zip_1 = require(\"../observable/zip\");\nvar lift_1 = require(\"../util/lift\");\nfunction zip() {\n  var sources = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    sources[_i] = arguments[_i];\n  }\n  return lift_1.operate(function (source, subscriber) {\n    zip_1.zip.apply(void 0, __spreadArray([source], __read(sources))).subscribe(subscriber);\n  });\n}\nexports.zip = zip;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "il", "length", "j", "Object", "defineProperty", "exports", "zip", "zip_1", "require", "lift_1", "sources", "_i", "arguments", "operate", "source", "subscriber", "apply", "subscribe"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/zip.js"], "sourcesContent": ["\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from) {\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\n        to[j] = from[i];\n    return to;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.zip = void 0;\nvar zip_1 = require(\"../observable/zip\");\nvar lift_1 = require(\"../util/lift\");\nfunction zip() {\n    var sources = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        sources[_i] = arguments[_i];\n    }\n    return lift_1.operate(function (source, subscriber) {\n        zip_1.zip.apply(void 0, __spreadArray([source], __read(sources))).subscribe(subscriber);\n    });\n}\nexports.zip = zip;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,MAAM,GAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAK,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC;EAC1D,IAAI,CAACF,CAAC,EAAE,OAAOF,CAAC;EAChB,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAI,CAACN,CAAC,CAAC;IAAEO,CAAC;IAAEC,EAAE,GAAG,EAAE;IAAEC,CAAC;EAChC,IAAI;IACA,OAAO,CAACR,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAEH,EAAE,CAACI,IAAI,CAACL,CAAC,CAACM,KAAK,CAAC;EAC9E,CAAC,CACD,OAAOC,KAAK,EAAE;IAAEL,CAAC,GAAG;MAAEK,KAAK,EAAEA;IAAM,CAAC;EAAE,CAAC,SAC/B;IACJ,IAAI;MACA,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAI,KAAKT,CAAC,GAAGG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAEH,CAAC,CAACI,IAAI,CAACD,CAAC,CAAC;IACpD,CAAC,SACO;MAAE,IAAII,CAAC,EAAE,MAAMA,CAAC,CAACK,KAAK;IAAE;EACpC;EACA,OAAON,EAAE;AACb,CAAC;AACD,IAAIO,aAAa,GAAI,IAAI,IAAI,IAAI,CAACA,aAAa,IAAK,UAAUC,EAAE,EAAEC,IAAI,EAAE;EACpE,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEa,EAAE,GAAGD,IAAI,CAACE,MAAM,EAAEC,CAAC,GAAGJ,EAAE,CAACG,MAAM,EAAEd,CAAC,GAAGa,EAAE,EAAEb,CAAC,EAAE,EAAEe,CAAC,EAAE,EAC7DJ,EAAE,CAACI,CAAC,CAAC,GAAGH,IAAI,CAACZ,CAAC,CAAC;EACnB,OAAOW,EAAE;AACb,CAAC;AACDK,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEV,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DU,OAAO,CAACC,GAAG,GAAG,KAAK,CAAC;AACpB,IAAIC,KAAK,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AACxC,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;AACpC,SAASF,GAAGA,CAAA,EAAG;EACX,IAAII,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACX,MAAM,EAAEU,EAAE,EAAE,EAAE;IAC1CD,OAAO,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC/B;EACA,OAAOF,MAAM,CAACI,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChDR,KAAK,CAACD,GAAG,CAACU,KAAK,CAAC,KAAK,CAAC,EAAEnB,aAAa,CAAC,CAACiB,MAAM,CAAC,EAAEjC,MAAM,CAAC6B,OAAO,CAAC,CAAC,CAAC,CAACO,SAAS,CAACF,UAAU,CAAC;EAC3F,CAAC,CAAC;AACN;AACAV,OAAO,CAACC,GAAG,GAAGA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}