{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.publishBehavior = void 0;\nvar BehaviorSubject_1 = require(\"../BehaviorSubject\");\nvar ConnectableObservable_1 = require(\"../observable/ConnectableObservable\");\nfunction publishBehavior(initialValue) {\n  return function (source) {\n    var subject = new BehaviorSubject_1.BehaviorSubject(initialValue);\n    return new ConnectableObservable_1.ConnectableObservable(source, function () {\n      return subject;\n    });\n  };\n}\nexports.publishBehavior = publishBehavior;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "publish<PERSON>eh<PERSON>or", "BehaviorSubject_1", "require", "ConnectableObservable_1", "initialValue", "source", "subject", "BehaviorSubject", "ConnectableObservable"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/publishBehavior.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.publishBehavior = void 0;\nvar BehaviorSubject_1 = require(\"../BehaviorSubject\");\nvar ConnectableObservable_1 = require(\"../observable/ConnectableObservable\");\nfunction publishBehavior(initialValue) {\n    return function (source) {\n        var subject = new BehaviorSubject_1.BehaviorSubject(initialValue);\n        return new ConnectableObservable_1.ConnectableObservable(source, function () { return subject; });\n    };\n}\nexports.publishBehavior = publishBehavior;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,eAAe,GAAG,KAAK,CAAC;AAChC,IAAIC,iBAAiB,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACrD,IAAIC,uBAAuB,GAAGD,OAAO,CAAC,qCAAqC,CAAC;AAC5E,SAASF,eAAeA,CAACI,YAAY,EAAE;EACnC,OAAO,UAAUC,MAAM,EAAE;IACrB,IAAIC,OAAO,GAAG,IAAIL,iBAAiB,CAACM,eAAe,CAACH,YAAY,CAAC;IACjE,OAAO,IAAID,uBAAuB,CAACK,qBAAqB,CAACH,MAAM,EAAE,YAAY;MAAE,OAAOC,OAAO;IAAE,CAAC,CAAC;EACrG,CAAC;AACL;AACAR,OAAO,CAACE,eAAe,GAAGA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}