{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.scan = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar scanInternals_1 = require(\"./scanInternals\");\nfunction scan(accumulator, seed) {\n  return lift_1.operate(scanInternals_1.scanInternals(accumulator, seed, arguments.length >= 2, true));\n}\nexports.scan = scan;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "scan", "lift_1", "require", "scanInternals_1", "accumulator", "seed", "operate", "scanInternals", "arguments", "length"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/scan.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.scan = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar scanInternals_1 = require(\"./scanInternals\");\nfunction scan(accumulator, seed) {\n    return lift_1.operate(scanInternals_1.scanInternals(accumulator, seed, arguments.length >= 2, true));\n}\nexports.scan = scan;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,IAAI,GAAG,KAAK,CAAC;AACrB,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,eAAe,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAChD,SAASF,IAAIA,CAACI,WAAW,EAAEC,IAAI,EAAE;EAC7B,OAAOJ,MAAM,CAACK,OAAO,CAACH,eAAe,CAACI,aAAa,CAACH,WAAW,EAAEC,IAAI,EAAEG,SAAS,CAACC,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACxG;AACAX,OAAO,CAACE,IAAI,GAAGA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}