{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.mergeAll = void 0;\nvar mergeMap_1 = require(\"./mergeMap\");\nvar identity_1 = require(\"../util/identity\");\nfunction mergeAll(concurrent) {\n  if (concurrent === void 0) {\n    concurrent = Infinity;\n  }\n  return mergeMap_1.mergeMap(identity_1.identity, concurrent);\n}\nexports.mergeAll = mergeAll;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "mergeAll", "mergeMap_1", "require", "identity_1", "concurrent", "Infinity", "mergeMap", "identity"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/mergeAll.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeAll = void 0;\nvar mergeMap_1 = require(\"./mergeMap\");\nvar identity_1 = require(\"../util/identity\");\nfunction mergeAll(concurrent) {\n    if (concurrent === void 0) { concurrent = Infinity; }\n    return mergeMap_1.mergeMap(identity_1.identity, concurrent);\n}\nexports.mergeAll = mergeAll;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,QAAQ,GAAG,KAAK,CAAC;AACzB,IAAIC,UAAU,GAAGC,OAAO,CAAC,YAAY,CAAC;AACtC,IAAIC,UAAU,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AAC5C,SAASF,QAAQA,CAACI,UAAU,EAAE;EAC1B,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAGC,QAAQ;EAAE;EACpD,OAAOJ,UAAU,CAACK,QAAQ,CAACH,UAAU,CAACI,QAAQ,EAAEH,UAAU,CAAC;AAC/D;AACAN,OAAO,CAACE,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}