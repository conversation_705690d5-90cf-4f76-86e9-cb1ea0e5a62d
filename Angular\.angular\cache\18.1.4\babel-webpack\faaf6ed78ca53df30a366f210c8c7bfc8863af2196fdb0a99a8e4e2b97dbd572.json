{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.windowTime = void 0;\nvar Subject_1 = require(\"../Subject\");\nvar async_1 = require(\"../scheduler/async\");\nvar Subscription_1 = require(\"../Subscription\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar arrRemove_1 = require(\"../util/arrRemove\");\nvar args_1 = require(\"../util/args\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nfunction windowTime(windowTimeSpan) {\n  var _a, _b;\n  var otherArgs = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    otherArgs[_i - 1] = arguments[_i];\n  }\n  var scheduler = (_a = args_1.popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : async_1.asyncScheduler;\n  var windowCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n  var maxWindowSize = otherArgs[1] || Infinity;\n  return lift_1.operate(function (source, subscriber) {\n    var windowRecords = [];\n    var restartOnClose = false;\n    var closeWindow = function (record) {\n      var window = record.window,\n        subs = record.subs;\n      window.complete();\n      subs.unsubscribe();\n      arrRemove_1.arrRemove(windowRecords, record);\n      restartOnClose && startWindow();\n    };\n    var startWindow = function () {\n      if (windowRecords) {\n        var subs = new Subscription_1.Subscription();\n        subscriber.add(subs);\n        var window_1 = new Subject_1.Subject();\n        var record_1 = {\n          window: window_1,\n          subs: subs,\n          seen: 0\n        };\n        windowRecords.push(record_1);\n        subscriber.next(window_1.asObservable());\n        executeSchedule_1.executeSchedule(subs, scheduler, function () {\n          return closeWindow(record_1);\n        }, windowTimeSpan);\n      }\n    };\n    if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n      executeSchedule_1.executeSchedule(subscriber, scheduler, startWindow, windowCreationInterval, true);\n    } else {\n      restartOnClose = true;\n    }\n    startWindow();\n    var loop = function (cb) {\n      return windowRecords.slice().forEach(cb);\n    };\n    var terminate = function (cb) {\n      loop(function (_a) {\n        var window = _a.window;\n        return cb(window);\n      });\n      cb(subscriber);\n      subscriber.unsubscribe();\n    };\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      loop(function (record) {\n        record.window.next(value);\n        maxWindowSize <= ++record.seen && closeWindow(record);\n      });\n    }, function () {\n      return terminate(function (consumer) {\n        return consumer.complete();\n      });\n    }, function (err) {\n      return terminate(function (consumer) {\n        return consumer.error(err);\n      });\n    }));\n    return function () {\n      windowRecords = null;\n    };\n  });\n}\nexports.windowTime = windowTime;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "windowTime", "Subject_1", "require", "async_1", "Subscription_1", "lift_1", "OperatorSubscriber_1", "arrRemove_1", "args_1", "executeSchedule_1", "windowTimeSpan", "_a", "_b", "otherArgs", "_i", "arguments", "length", "scheduler", "popScheduler", "asyncScheduler", "windowCreationInterval", "maxWindowSize", "Infinity", "operate", "source", "subscriber", "windowRecords", "restartOnClose", "closeWindow", "record", "window", "subs", "complete", "unsubscribe", "arr<PERSON><PERSON><PERSON>", "startWindow", "Subscription", "add", "window_1", "Subject", "record_1", "seen", "push", "next", "asObservable", "executeSchedule", "loop", "cb", "slice", "for<PERSON>ach", "terminate", "subscribe", "createOperatorSubscriber", "consumer", "err", "error"], "sources": ["D:/permittracker/Angular/node_modules/rxjs/dist/cjs/internal/operators/windowTime.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.windowTime = void 0;\nvar Subject_1 = require(\"../Subject\");\nvar async_1 = require(\"../scheduler/async\");\nvar Subscription_1 = require(\"../Subscription\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar arrRemove_1 = require(\"../util/arrRemove\");\nvar args_1 = require(\"../util/args\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nfunction windowTime(windowTimeSpan) {\n    var _a, _b;\n    var otherArgs = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        otherArgs[_i - 1] = arguments[_i];\n    }\n    var scheduler = (_a = args_1.popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : async_1.asyncScheduler;\n    var windowCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n    var maxWindowSize = otherArgs[1] || Infinity;\n    return lift_1.operate(function (source, subscriber) {\n        var windowRecords = [];\n        var restartOnClose = false;\n        var closeWindow = function (record) {\n            var window = record.window, subs = record.subs;\n            window.complete();\n            subs.unsubscribe();\n            arrRemove_1.arrRemove(windowRecords, record);\n            restartOnClose && startWindow();\n        };\n        var startWindow = function () {\n            if (windowRecords) {\n                var subs = new Subscription_1.Subscription();\n                subscriber.add(subs);\n                var window_1 = new Subject_1.Subject();\n                var record_1 = {\n                    window: window_1,\n                    subs: subs,\n                    seen: 0,\n                };\n                windowRecords.push(record_1);\n                subscriber.next(window_1.asObservable());\n                executeSchedule_1.executeSchedule(subs, scheduler, function () { return closeWindow(record_1); }, windowTimeSpan);\n            }\n        };\n        if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n            executeSchedule_1.executeSchedule(subscriber, scheduler, startWindow, windowCreationInterval, true);\n        }\n        else {\n            restartOnClose = true;\n        }\n        startWindow();\n        var loop = function (cb) { return windowRecords.slice().forEach(cb); };\n        var terminate = function (cb) {\n            loop(function (_a) {\n                var window = _a.window;\n                return cb(window);\n            });\n            cb(subscriber);\n            subscriber.unsubscribe();\n        };\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            loop(function (record) {\n                record.window.next(value);\n                maxWindowSize <= ++record.seen && closeWindow(record);\n            });\n        }, function () { return terminate(function (consumer) { return consumer.complete(); }); }, function (err) { return terminate(function (consumer) { return consumer.error(err); }); }));\n        return function () {\n            windowRecords = null;\n        };\n    });\n}\nexports.windowTime = windowTime;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAIC,SAAS,GAAGC,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIC,OAAO,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AAC3C,IAAIE,cAAc,GAAGF,OAAO,CAAC,iBAAiB,CAAC;AAC/C,IAAIG,MAAM,GAAGH,OAAO,CAAC,cAAc,CAAC;AACpC,IAAII,oBAAoB,GAAGJ,OAAO,CAAC,sBAAsB,CAAC;AAC1D,IAAIK,WAAW,GAAGL,OAAO,CAAC,mBAAmB,CAAC;AAC9C,IAAIM,MAAM,GAAGN,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIO,iBAAiB,GAAGP,OAAO,CAAC,yBAAyB,CAAC;AAC1D,SAASF,UAAUA,CAACU,cAAc,EAAE;EAChC,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAIC,SAAS,GAAG,EAAE;EAClB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,SAAS,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EACrC;EACA,IAAIG,SAAS,GAAG,CAACN,EAAE,GAAGH,MAAM,CAACU,YAAY,CAACL,SAAS,CAAC,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGR,OAAO,CAACgB,cAAc;EAC7G,IAAIC,sBAAsB,GAAG,CAACR,EAAE,GAAGC,SAAS,CAAC,CAAC,CAAC,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;EACtF,IAAIS,aAAa,GAAGR,SAAS,CAAC,CAAC,CAAC,IAAIS,QAAQ;EAC5C,OAAOjB,MAAM,CAACkB,OAAO,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IAChD,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIC,cAAc,GAAG,KAAK;IAC1B,IAAIC,WAAW,GAAG,SAAAA,CAAUC,MAAM,EAAE;MAChC,IAAIC,MAAM,GAAGD,MAAM,CAACC,MAAM;QAAEC,IAAI,GAAGF,MAAM,CAACE,IAAI;MAC9CD,MAAM,CAACE,QAAQ,CAAC,CAAC;MACjBD,IAAI,CAACE,WAAW,CAAC,CAAC;MAClB1B,WAAW,CAAC2B,SAAS,CAACR,aAAa,EAAEG,MAAM,CAAC;MAC5CF,cAAc,IAAIQ,WAAW,CAAC,CAAC;IACnC,CAAC;IACD,IAAIA,WAAW,GAAG,SAAAA,CAAA,EAAY;MAC1B,IAAIT,aAAa,EAAE;QACf,IAAIK,IAAI,GAAG,IAAI3B,cAAc,CAACgC,YAAY,CAAC,CAAC;QAC5CX,UAAU,CAACY,GAAG,CAACN,IAAI,CAAC;QACpB,IAAIO,QAAQ,GAAG,IAAIrC,SAAS,CAACsC,OAAO,CAAC,CAAC;QACtC,IAAIC,QAAQ,GAAG;UACXV,MAAM,EAAEQ,QAAQ;UAChBP,IAAI,EAAEA,IAAI;UACVU,IAAI,EAAE;QACV,CAAC;QACDf,aAAa,CAACgB,IAAI,CAACF,QAAQ,CAAC;QAC5Bf,UAAU,CAACkB,IAAI,CAACL,QAAQ,CAACM,YAAY,CAAC,CAAC,CAAC;QACxCnC,iBAAiB,CAACoC,eAAe,CAACd,IAAI,EAAEd,SAAS,EAAE,YAAY;UAAE,OAAOW,WAAW,CAACY,QAAQ,CAAC;QAAE,CAAC,EAAE9B,cAAc,CAAC;MACrH;IACJ,CAAC;IACD,IAAIU,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,IAAI,CAAC,EAAE;MAChEX,iBAAiB,CAACoC,eAAe,CAACpB,UAAU,EAAER,SAAS,EAAEkB,WAAW,EAAEf,sBAAsB,EAAE,IAAI,CAAC;IACvG,CAAC,MACI;MACDO,cAAc,GAAG,IAAI;IACzB;IACAQ,WAAW,CAAC,CAAC;IACb,IAAIW,IAAI,GAAG,SAAAA,CAAUC,EAAE,EAAE;MAAE,OAAOrB,aAAa,CAACsB,KAAK,CAAC,CAAC,CAACC,OAAO,CAACF,EAAE,CAAC;IAAE,CAAC;IACtE,IAAIG,SAAS,GAAG,SAAAA,CAAUH,EAAE,EAAE;MAC1BD,IAAI,CAAC,UAAUnC,EAAE,EAAE;QACf,IAAImB,MAAM,GAAGnB,EAAE,CAACmB,MAAM;QACtB,OAAOiB,EAAE,CAACjB,MAAM,CAAC;MACrB,CAAC,CAAC;MACFiB,EAAE,CAACtB,UAAU,CAAC;MACdA,UAAU,CAACQ,WAAW,CAAC,CAAC;IAC5B,CAAC;IACDT,MAAM,CAAC2B,SAAS,CAAC7C,oBAAoB,CAAC8C,wBAAwB,CAAC3B,UAAU,EAAE,UAAU1B,KAAK,EAAE;MACxF+C,IAAI,CAAC,UAAUjB,MAAM,EAAE;QACnBA,MAAM,CAACC,MAAM,CAACa,IAAI,CAAC5C,KAAK,CAAC;QACzBsB,aAAa,IAAI,EAAEQ,MAAM,CAACY,IAAI,IAAIb,WAAW,CAACC,MAAM,CAAC;MACzD,CAAC,CAAC;IACN,CAAC,EAAE,YAAY;MAAE,OAAOqB,SAAS,CAAC,UAAUG,QAAQ,EAAE;QAAE,OAAOA,QAAQ,CAACrB,QAAQ,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,EAAE,UAAUsB,GAAG,EAAE;MAAE,OAAOJ,SAAS,CAAC,UAAUG,QAAQ,EAAE;QAAE,OAAOA,QAAQ,CAACE,KAAK,CAACD,GAAG,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,CAAC,CAAC;IACtL,OAAO,YAAY;MACf5B,aAAa,GAAG,IAAI;IACxB,CAAC;EACL,CAAC,CAAC;AACN;AACA5B,OAAO,CAACE,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}