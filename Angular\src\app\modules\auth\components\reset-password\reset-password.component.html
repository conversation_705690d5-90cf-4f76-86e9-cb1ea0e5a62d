


<form class="form w-100 fv-plugins-bootstrap5 fv-plugins-framework" novalidate="novalidate"
  [formGroup]="resetPasswordForm" (ngSubmit)="submit()" id="kt_login_password_reset_form">

 <div class="text-center mb-10">
    <img src="./assets/media/logos/cropped-Pacifica-Logo.png" alt="Monarch Health Services, Inc." class="h-100px logo" style="width:250px">
    <!-- <img alt="Logo" src="./assets/media/logos/Practice-Logo.png" class="h-50px logo" style="width:140px"> -->
  </div>

  <div class="text-center mb-11">
  <h1 class="text-dark fw-bolder mb-3">
    Reset Password
  </h1>
</div>
  <!--begin::Form group-->
  <div class="fv-row mb-10">

    <div class="form-group">
      <label class="fw-semibold fs-6 mb-2 ">New Password<span class="text-danger">*</span></label>
      <div class="input-group mb-0">
        <input [type]="newPasswordShown === true ?'password':'text'" class="form-control form-control-sm"
          name="password" formControlName="password" placeholder="Type Here" required maxlength="20">
        <div class="input-group-text">
          <span [ngClass]="newPasswordShown===true? 'bi bi-eye-slash-fill':'bi bi-eye-fill'"
            (click)="newPasswordShown===true? newshowpassword(false):newshowpassword(true)"></span>
        </div>
      </div>
      <span class="custom-error-css" *ngIf="isControlHasError('password', 'required')"> Required Field</span>&nbsp;
      <span class="custom-error-css" *ngIf="isControlHasError('password', 'minlength')"> Minimum password length:
        {{passwordMinLength}}</span>&nbsp;
      <span class="custom-error-css" *ngIf="isControlHasError('password', 'maxlength')"> Required 20</span>&nbsp;
      <span class="custom-error-css" *ngIf="samePassword">New Password shouldn't be same as Current Password</span>

    </div>
    <div class="form-group">
      <label class="fw-semibold fs-6 mb-2">Confirm Password<span class="text-danger">*</span></label>
      <div class="input-group mb-0">
        <input [type]="newConpasswordShown === true ?'password':'text'" class="form-control form-control-sm"
          name="confirmPassword" formControlName="confirmPassword" placeholder="Type Here" required
          maxlength="20">
        <div class="input-group-text">
          <span [ngClass]="newConpasswordShown===true? 'bi bi-eye-slash-fill':'bi bi-eye-fill'"
            (click)="newConpasswordShown===true? newconshowpassword(false):newconshowpassword(true)"></span>
        </div>
      </div>
      <span class="custom-error-css" *ngIf="isControlHasError('confirmPassword', 'required')">Required Field</span>&nbsp;
      <span class="custom-error-css" *ngIf="isControlHasError('confirmPassword', 'minlength')"> Minimum password
        length: {{passwordMinLength}}</span>&nbsp;
      <span class="custom-error-css" *ngIf="isControlHasError('confirmPassword', 'maxlength')"> Required 20</span>
      &nbsp;
      <span class="custom-error-css" *ngIf="confirmPasswordError">Passwords doesn't match</span>

    </div>
    <div class="form-group mb-2">
      <label class="fw-semibold fs-6 mb-2">Password Strength </label>
      <app-password-strength [passwordToCheck]="resetPasswordForm.controls['password'].value"
        [minLength]='passwordMinLength' (passwordStrength)="onPasswordChange($event)"></app-password-strength>
    </div>

  </div>
  <!--end::Form group-->
  <!--begin::Form group-->
  <!-- <div class="btn btn-danger btn-sm btn-elevate mr-2 d-flex flex-grow-1 justify-content-center  pb-lg-0">
    <a routerLink="/auth/login" id="kt_login_password_reset_form_cancel_button"
      class="btn btn-sm btn-secondary">
      Cancel
    </a>&nbsp;
    
    <div class="d-flex">
      <button type="submit" id="kt_password_reset_submit" class="btn btn-sm btn-yellow">
        <span class="indicator-label">Submit</span>
      </button>
    </div>


  </div> -->
  <div style="margin-left:55%">
      <button type="button"  id="kt_login_password_reset_form_cancel_button" class="btn btn-danger btn-sm btn-elevate mr-2" (click)="routeLogin()" >
        Cancel
      </button>
      &nbsp;
   
      <button  type="submit" id="kt_password_reset_submit" class="btn btn-primary btn-elevate btn-sm" >
        Submit
      </button>
    </div>
  <!--end::Form group-->
</form>
<!--end::Form-->

<ng-template #formError let-control="control" let-message="message" let-validation="validation">
  <ng-container *ngIf="control.hasError(validation) && (control.dirty || control.touched)">
    <div class="fv-plugins-message-container">
      <div class="fv-help-block">
        <span role="alert">{{ message }}</span>
      </div>
    </div>
  </ng-container>
</ng-template>