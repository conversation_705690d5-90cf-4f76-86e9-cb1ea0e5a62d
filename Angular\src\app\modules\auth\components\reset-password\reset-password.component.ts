import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {  Subscription } from 'rxjs';
import { AuthService } from '../../services/auth.service';
import { HttpUtilsService } from 'src/app/modules/services/http-utils.service';
import { CustomLayoutUtilsService } from 'src/app/modules/services/custom-layout.utils.service';
import { AppService } from '../../../services/app.service';
import { UserService } from '../../../services/user.service';

@Component({
  selector: 'app-reset-password',

  templateUrl: './reset-password.component.html',
  styleUrl: './reset-password.component.scss'
})
export class ResetPasswordComponent {
 resetPasswordForm: FormGroup; //fo
  confirmPasswordError: boolean = false; //boolean for checking password error
  password: string = ''; //to save password
  passwordChange: boolean = true; //boolean to change password
  samePassword: boolean = false; //boolean to check passwords are same
  passwordIsValid = false; //boolean to check password strength
  passwordMinLength: number = 7; //set minimum password length
  token: any;  //store route subscription token
  newPasswordShown = true; //boolean for password shown
  newConpasswordShown = true; //boolean for confirm password shown
  loginUser: any = {}; //store localstorage json value
  // private fields
  private unsubscribe: Subscription[] = []; // Read more: => https://brianflove.com/2016/12/11/anguar-2-unsubscribe-observables/
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private authService: AuthService, private httpUtilService: HttpUtilsService,
    private layoutUtilService: CustomLayoutUtilsService,
    private activatedRoute: ActivatedRoute,
    public appService: AppService,
    private userService: UserService) {
  }
   /**
   * On init
   */
  ngOnInit(): void {
    // this.loginUser = this.appService.getLocalStorageItem('ctuser', true);

    this.route.params.subscribe((params: any) => {
      this.token = params['token'];

    });
    this.initForm();

    //get password's minimum length from API
    // this.authService.resetPassword({ CompanyPID: 2 }).subscribe((res) => {
    //   if (!res.isFault) {
    //     this.httpUtilService.loadingSubject.next(false);
    //     this.passwordMinLength = res.responseData.MinimumLength;
    //     this.cdr.markForCheck();

    //   } else {
    //     this.httpUtilService.loadingSubject.next(false);
    //   }
    // });
  }
  // convenience getter for easy access to form fields
  get f() {
    return this.resetPasswordForm.controls;
  }

  /**
  * Form initialization
  * Default params, validators
  */
  initForm() {
    this.resetPasswordForm = this.fb.group({
      password: new FormControl('', [Validators.required, Validators.minLength(this.passwordMinLength), Validators.maxLength(20)]),
      confirmPassword: new FormControl('', [Validators.required, Validators.minLength(this.passwordMinLength), Validators.maxLength(20)])

    }, {
      validator: this.ConfirmedValidator('password', 'confirmPassword')
    });
  }
  //function to modify boolean depending on whether the  password eye symbol is on or off
  newshowpassword(event: any) {
    this.newPasswordShown = event;
  }
  //function to modify boolean depending on whether the confirm password eye symbol is on or off
  newconshowpassword(event: any) {
    this.newConpasswordShown = event;
  }

  // Form validation for new password and confirm password
  //param:controlName - new password field value, matchingControlName - confirm password field value
  ConfirmedValidator(controlName: string, matchingControlName: string) {
    return (formGroup: FormGroup) => {
      const control = formGroup.controls[controlName];
      const matchingControl = formGroup.controls[matchingControlName];
      if (matchingControl.errors && !matchingControl.errors.confirmedValidator) {
        return;
      }
      if (control.value !== matchingControl.value) {
        matchingControl.setErrors({ confirmedValidator: true });
        this.confirmPasswordError = true;
      } else {
        this.confirmPasswordError = false;
        matchingControl.setErrors(null);
      }
    }
  }

  // function to check whether the form has any error
  isControlHasError(controlName: string, validationType: string): boolean {
    const control = this.resetPasswordForm.controls[controlName];
    if (!control) {
      return false;
    }
    const result =
      this.resetPasswordForm.controls[controlName].hasError(validationType) &&
      (control.dirty || control.touched);
    return result;
  }
  // function to check the password strength
  // Param : event: event fires when password strength is changed.
  onPasswordChange(event: any) {
    this.password = this.resetPasswordForm.controls['password'].value;
    this.passwordIsValid = event;
    this.cdr.markForCheck();
  }

   //Form Submit
  submit() {
    let controls = this.resetPasswordForm.controls;
    //function to check whether validated fields are filled
    if (this.resetPasswordForm.invalid) {
      Object.keys(controls).forEach(controlName =>
        controls[controlName].markAsTouched()
      );
      this.layoutUtilService.showError('Please fill all required fields', '');
      return;
    }
    const jwtData = this.token.split('.')[1]
    const decodedJwtJsonData = window.atob(jwtData)
    const decodedJwtData = JSON.parse(decodedJwtJsonData);
    const user: any = {
      newPassword: controls.password.value,
      token: this.token,
      // UserPID: decodedJwtData.UserPID
    };
    this.httpUtilService.loadingSubject.next(true);
    this.authService.resetPassword(user).subscribe((res: any) => {
      if (!res.isFault) {
        this.httpUtilService.loadingSubject.next(false);
        this.layoutUtilService.showSuccess(res.responseData.message, '');
        this.router.navigate(['/auth/login'], { relativeTo: this.activatedRoute });
      } else {
        this.httpUtilService.loadingSubject.next(false);
        this.layoutUtilService.showError(res.responseData.message, '');
      }
    }, (err: string) => {
    })
  }
  routeLogin(){
    this.router.navigate(['/auth/logins'])
  }
}
