<div class="modal-content h-auto">
  <!-- Header -->
  <div class="modal-header bg-light-primary">
    <div class="modal-title h5 fs-3">
      <ng-container>
        <div *ngIf="isEdit">Edit Review Detail - {{ reviewCategory || '' }}</div>
        <div *ngIf="!isEdit">Add Review Detail - {{ reviewCategory || '' }}</div>
      </ng-container>
    </div>
    <div class="float-right">
      <i class="fa-solid fs-2 fa-xmark text-white" (click)="onCancel()"></i>
    </div>
  </div>

  <!-- Body (Add vs Edit) -->
  <div class="modal-body" *ngIf="!isEdit; else editModeOldPopup">
    <!-- Section 1: Instructions + Download + Import + Add Review -->
    <div *ngIf="showExcelSection">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <button type="button" class="btn btn-light btn-sm" (click)="onBack()">Back</button>
        </div>
        <div class="d-flex align-items-center">
          <a [href]="templateUrlPath" download class="btn btn-outline-secondary btn-sm me-2">Download Template</a>
          <input #fileInput type="file" class="d-none" accept=".xlsx,.xls" (change)="onFileSelected($event)" />
          <button type="button" class="btn btn-outline-primary btn-sm me-2" (click)="triggerFileImport(fileInput)" [disabled]="isLoading">Import Excel</button>
          <button type="button" class="btn btn-primary btn-sm" (click)="onClickAddReview()">Add Review Detail</button>
        </div>
      </div>

      <div class="alert alert-info mt-4" role="alert">
        You can upload review details by downloading the template, filling it, and importing the Excel. Or click <strong>Add Review Detail</strong> to enter details manually.
      </div>

    </div>

    <!-- Section 2: Imported rows editable table (Add Row + Cancel) -->
    <div *ngIf="showImportTableSection" class="mt-4">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <h6 class="mb-0">Imported Review Details ({{ importedRows.length }})</h6>
          <div>
            <button type="button" class="btn btn-light btn-sm me-2" (click)="addImportedRow()" [disabled]="isLoading || isImporting">
              <i class="fa fa-plus me-1"></i> Add Row
            </button>
          <button type="button" class="btn btn-success btn-sm me-2" (click)="saveAllImported()" [disabled]="isLoading || isImporting || hasImportedInvalid">Save All</button>
          <button type="button" class="btn btn-outline-secondary btn-sm" (click)="cancelImportTable()">Cancel</button>
          </div>
        </div>
        <div class="table-responsive">
          <table class="table table-sm table-bordered align-middle">
            <thead class="table-light">
              <tr>
                <th style="width: 60px;">Action</th>
                <th style="width: 116px;">Sheet Number <span class="text-danger">*</span></th>
                <th style="width: 125px;">Code Ref <span class="text-danger">*</span></th>
                <th>Code Description <span class="text-danger">*</span></th>
                <th>Reasoning <span class="text-danger">*</span></th>
                <th>Non Compliance</th>
                <th>Actionable Step</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let r of importedRows; let i = index">
                <td class="text-center">
                  <button type="button" class="btn btn-icon btn-sm btn-light-danger" (click)="deleteImportedRow(i)" [disabled]="isLoading">
                    <i class="fa fa-trash"></i>
                  </button>
                </td>
                <td>
                  <input [(ngModel)]="r.sheetNumber" name="sheetNumber{{i}}" class="form-control form-control-sm" style="max-width: 110px;" [ngClass]="{'is-invalid': importSubmitted && !(r.sheetNumber||'').trim()}" />
                  <div class="invalid-feedback" *ngIf="importSubmitted && !(r.sheetNumber||'').trim()">Required Field</div>
                </td>
                <td>
                  <input [(ngModel)]="r.codeRef" name="codeRef{{i}}" class="form-control form-control-sm" style="max-width: 130px;" [ngClass]="{'is-invalid': importSubmitted && !(r.codeRef||'').trim()}" />
                  <div class="invalid-feedback" *ngIf="importSubmitted && !(r.codeRef||'').trim()">Required Field</div>
                </td>
                <td>
                  <textarea [(ngModel)]="r.codeDescription" name="codeDescription{{i}}" rows="2" class="form-control form-control-sm" [ngClass]="{'is-invalid': importSubmitted && !(r.codeDescription||'').trim()}"></textarea>
                  <div class="invalid-feedback" *ngIf="importSubmitted && !(r.codeDescription||'').trim()">Required Field</div>
                </td>
                <td>
                  <textarea [(ngModel)]="r.reasoning" name="reasoning{{i}}" rows="2" class="form-control form-control-sm" [ngClass]="{'is-invalid': importSubmitted && !(r.reasoning||'').trim()}"></textarea>
                  <div class="invalid-feedback" *ngIf="importSubmitted && !(r.reasoning||'').trim()">Required Field</div>
                </td>
                <td><textarea [(ngModel)]="r.nonCompliance" name="nonCompliance{{i}}" rows="2" class="form-control form-control-sm"></textarea></td>
                <td><textarea [(ngModel)]="r.actionableStep" name="actionableStep{{i}}" rows="2" class="form-control form-control-sm"></textarea></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Imported rows editable table (single source of truth) -->

    <!-- Section 3: Form: single view (Review Details + Actionable Step) -->
    <form *ngIf="showForm" [formGroup]="detailForm" (ngSubmit)="onSubmit()" novalidate>
      <div class="d-flex justify-content-between align-items-center mb-3">
        <div></div>
        <div>
          <button type="button" class="btn btn-light btn-sm" (click)="cancelAddForm()">Cancel</button>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-xl-6">
          <label class="fw-bold form-label mb-2">Sheet Number <span class="text-danger">*</span></label>
          <input type="text" formControlName="sheetNumber" class="form-control form-control-sm" [class.is-invalid]="shouldShowValidationError('sheetNumber')" placeholder="Type here" [disabled]="isLoading" />
          <div class="invalid-feedback" *ngIf="shouldShowValidationError('sheetNumber')">Required Field</div>
        </div>
        <div class="col-xl-6">
          <label class="fw-bold form-label mb-2">Code Ref <span class="text-danger">*</span></label>
          <input type="text" formControlName="codeRef" class="form-control form-control-sm" [class.is-invalid]="shouldShowValidationError('codeRef')" placeholder="Type here" [disabled]="isLoading" />
          <div class="invalid-feedback" *ngIf="shouldShowValidationError('codeRef')">Required Field</div>
        </div>
      </div>
      <div class="row mt-4">
        <div class="col-xl-12">
          <label class="fw-bold form-label mb-2">Code Description <span class="text-danger">*</span></label>
          <textarea formControlName="codeDescription" rows="4" class="form-control form-control-sm" [class.is-invalid]="shouldShowValidationError('codeDescription')" placeholder="Type here" [disabled]="isLoading"></textarea>
          <div class="invalid-feedback" *ngIf="shouldShowValidationError('codeDescription')">Required Field</div>
        </div>
      </div>
      <div class="row mt-4">
        <div class="col-xl-12">
          <label class="fw-bold form-label mb-2">Reasoning <span class="text-danger">*</span></label>
          <textarea formControlName="reasoning" rows="4" class="form-control form-control-sm" [class.is-invalid]="shouldShowValidationError('reasoning')" placeholder="Type here" [disabled]="isLoading"></textarea>
          <div class="invalid-feedback" *ngIf="shouldShowValidationError('reasoning')">Required Field</div>
        </div>
      </div>
      <div class="row mt-4">
        <div class="col-xl-12">
          <label class="fw-bold form-label mb-2">Non Compliance</label>
          <textarea formControlName="nonCompliance" rows="4" class="form-control form-control-sm" placeholder="Type here" [disabled]="isLoading"></textarea>
        </div>
      </div>
      <div class="row mt-4">
        <div class="col-xl-12">
          <label class="fw-bold form-label mb-2">Actionable Step</label>
          <textarea formControlName="actionableStep" rows="4" class="form-control form-control-sm" placeholder="Type here" [disabled]="isLoading"></textarea>
        </div>
      </div>

      <!-- A/E Response and Responded By (Respond fields) -->
      <div class="row mt-4">
        <div class="col-xl-12">
          <label class="fw-bold form-label mb-2">A/E Response</label>
          <textarea formControlName="aeResponse" rows="3" class="form-control form-control-sm" placeholder="Type here" [disabled]="isLoading"></textarea>
        </div>
      </div>
      <div class="row mt-4">
        <div class="col-xl-12">
          <label class="fw-bold form-label mb-2">Responded By</label>
          <input type="text" formControlName="commentResponsedBy" class="form-control form-control-sm" placeholder="Type here" [disabled]="isLoading" />
        </div>
      </div>

    </form>
  </div>
  <!-- EDIT MODE: old popup with tabs -->
  <ng-template #editModeOldPopup>
  <div class="modal-body">
    <!-- Tabs -->
    <div class="row">
      <div class="col-xl-12">
        <div class="d-flex">
          <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap">
            <li class="nav-item">
              <a class="nav-link text-active-primary me-6 cursor-pointer" data-toggle="tab" [ngClass]="{ active: activeTab === 'details' }" (click)="setActiveTab('details')">
                Review Details
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link text-active-primary me-6 cursor-pointer" data-toggle="tab" [ngClass]="{ active: activeTab === 'comments' }" (click)="setActiveTab('comments')">
                Review Comments
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Form -->
    <form [formGroup]="detailForm" (ngSubmit)="onSubmit()" novalidate>
      <!-- DETAILS TAB CONTENT -->
      <div *ngIf="activeTab === 'details'">
        <div class="row mt-3">
          <div class="col-xl-6">
            <label class="fw-bold form-label mb-2">Sheet Number <span class="text-danger">*</span></label>
            <input type="text" formControlName="sheetNumber" class="form-control form-control-sm" [class.is-invalid]="shouldShowValidationError('sheetNumber')" placeholder="Type here" [disabled]="isLoading" />
            <div class="invalid-feedback" *ngIf="shouldShowValidationError('sheetNumber')">Required Field</div>
          </div>
          <div class="col-xl-6">
            <label class="fw-bold form-label mb-2">Code Ref <span class="text-danger">*</span></label>
            <input type="text" formControlName="codeRef" class="form-control form-control-sm" [class.is-invalid]="shouldShowValidationError('codeRef')" placeholder="Type here" [disabled]="isLoading" />
            <div class="invalid-feedback" *ngIf="shouldShowValidationError('codeRef')">Required Field</div>
          </div>
        </div>
        <div class="row mt-4">
          <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Code Description <span class="text-danger">*</span></label>
            <textarea formControlName="codeDescription" rows="3" class="form-control form-control-sm" [class.is-invalid]="shouldShowValidationError('codeDescription')" placeholder="Type here" [disabled]="isLoading"></textarea>
            <div class="invalid-feedback" *ngIf="shouldShowValidationError('codeDescription')">Required Field</div>
          </div>
        </div>
        <div class="row mt-4">
          <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Reasoning <span class="text-danger">*</span></label>
            <textarea formControlName="reasoning" rows="3" class="form-control form-control-sm" [class.is-invalid]="shouldShowValidationError('reasoning')" placeholder="Type here" [disabled]="isLoading"></textarea>
            <div class="invalid-feedback" *ngIf="shouldShowValidationError('reasoning')">Required Field</div>
          </div>
        </div>
        <div class="row mt-4">
          <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Non Compliance</label>
            <textarea formControlName="nonCompliance" rows="3" class="form-control form-control-sm" placeholder="Type here" [disabled]="isLoading"></textarea>
          </div>
        </div>
      </div>
      <!-- COMMENTS TAB CONTENT -->
      <div *ngIf="activeTab === 'comments'">
        <div class="row mt-3">
          <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Actionable Step</label>
            <textarea formControlName="actionableStep" rows="3" class="form-control form-control-sm" placeholder="Type here" [disabled]="isLoading"></textarea>
          </div>
        </div>
      </div>
    </form>
  </div>
 </ng-template>

 <!-- Fixed footer aligning with Add Review Cycle popup -->
<div class="modal-footer d-flex justify-content-between align-items-center">
        <div>
    <!-- Previous in Edit mode (tabs) -->
    <button
      *ngIf="isEdit && activeTab === 'comments'"
      type="button"
      class="btn btn-light btn-sm"
      (click)="goToPrevious()"
      [disabled]="isLoading"
    >
      Previous
    </button>
    <!-- Previous in Add mode (form visible) returns to Excel section -->
    <button
      *ngIf="!isEdit && !showExcelSection"
      type="button"
      class="btn btn-light btn-sm"
      (click)="cancelAddForm()"
      [disabled]="isLoading"
    >
            Previous
          </button>
        </div>
        <div>
    <button
      type="button"
      class="btn btn-danger btn-sm btn-elevate me-2"
      (click)="onCancel()"
      [disabled]="isLoading"
    >
            Cancel
          </button>

    <!-- Next in Edit mode (tabs) -->
    <button
      *ngIf="isEdit && activeTab === 'details'"
      type="button"
      class="btn btn-primary btn-sm me-2"
      [disabled]="isLoading"
      (click)="setActiveTab('comments')"
    >
      Next
    </button>

    <!-- Next in Add mode (Excel section visible) shows form -->
    <button
      *ngIf="!isEdit && showExcelSection"
      type="button"
      class="btn btn-primary btn-sm me-2"
      [disabled]="isLoading"
      (click)="onClickAddReview()"
    >
            Next
          </button>

    <!-- Submit in Edit mode (on comments tab) -->
    <button
      *ngIf="isEdit && activeTab === 'comments'"
      type="button"
      class="btn btn-primary btn-sm"
      [disabled]="isLoading"
      (click)="onSubmit()"
    >
            {{ isEdit ? 'Update' : 'Create' }}
          </button>
    <!-- Submit in Add mode (form visible) -->
    <button
      *ngIf="!isEdit && !showExcelSection"
      type="button"
      class="btn btn-primary btn-sm"
      [disabled]="isLoading"
      (click)="onSubmit()"
    >
      Create
    </button>
  </div>
 </div>
</div>
