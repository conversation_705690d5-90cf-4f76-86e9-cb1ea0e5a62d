import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { PermitsService } from '../../services/permits.service';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { HttpUtilsService } from '../../services/http-utils.service';

@Component({
  selector: 'app-add-edit-internal-review-detail',
  templateUrl: './add-edit-internal-review-detail.component.html',
  styleUrls: ['./add-edit-internal-review-detail.component.scss']
})
export class AddEditInternalReviewDetailComponent implements OnInit {
  @Input() permitId: number | null = null;
  @Input() permitNumber: string = '';
  @Input() reviewCategory: string = '';
  @Input() internalCommentsId: number | null = null;
  @Input() detailData: any = null; // For edit mode
  @Input() loggedInUserId: string = 'user'; // Should be passed from parent

  detailForm!: FormGroup;
  isEdit: boolean = false;
  isLoading: boolean = false;
  // Tabs removed; single-view form
  formSubmitted: boolean = false;
  showForm: boolean = false;
  showExcelSection: boolean = true; // instruction/download/import section
  showImportTableSection: boolean = false; // table-only section after successful upload
  templateUrlPath: string = '/assets/excel/claimstempate.xlsx';
  importedRows: Array<any> = [];
  isImporting: boolean = false;
  importSubmitted: boolean = false;
  // Tab state for edit mode (old popup)
  activeTab: 'details' | 'comments' = 'details';

  constructor(
    private fb: FormBuilder,
    public modal: NgbActiveModal,
    private permitsService: PermitsService,
    private customLayoutUtilsService: CustomLayoutUtilsService,
    private httpUtilsService: HttpUtilsService
  ) {}

  ngOnInit(): void {
    this.isEdit = !!this.detailData;
    this.detailForm = this.fb.group({
      sheetNumber: [this.detailData?.sheetNumber || '', Validators.required],
      codeRef: [this.detailData?.codeRef || '', Validators.required],
      codeDescription: [this.detailData?.codeDescription || '', Validators.required],
      reasoning: [this.detailData?.reasoning || '', Validators.required],
      nonCompliance: [this.detailData?.nonCompliance || ''],
      actionableStep: [this.detailData?.actionableStep || ''],
      aeResponse: [this.detailData?.aeResponse || ''],
      commentResponsedBy: [this.detailData?.commentResponsedBy || ''],
    });
  }

  // Tab navigation removed

  shouldShowValidationError(fieldName: string): boolean {
    // Only show validation errors when form has been submitted
    if (!this.formSubmitted) {
      return false;
    }
    
    const field = this.detailForm.get(fieldName);
    return !!(field && field.invalid);
  }

  onSubmit(): void {
    this.formSubmitted = true;
    if (this.detailForm.valid && this.permitId) {
      this.isLoading = true;
      // Enable common loader
      this.httpUtilsService.loadingSubject.next(true);
      
      const formData: any = {
        ...this.detailForm.value,
        permitId: this.permitId,
        permitNumber: this.permitNumber,
        reviewCategory: this.reviewCategory,
        internalCommentsId: this.internalCommentsId || this.detailData?.internalCommentsId,
        loggedInUserId: this.loggedInUserId
      };
      if (this.isEdit && this.detailData?.internalReviewCommentsId) {
        formData.internalReviewCommentsId = this.detailData.internalReviewCommentsId;
        this.permitsService.updateInternalPlanReviewDetail(formData).subscribe({
          next: (res: any) => {
            this.isLoading = false;
            // Disable common loader
            this.httpUtilsService.loadingSubject.next(false);
            if (res?.isFault) {
              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to update review detail', '');
            } else {
              this.customLayoutUtilsService.showSuccess(res.responseData?.message || 'Review detail updated successfully!', '');
              this.modal.close('updated');
            }
          },
          error: (err: any) => {
            this.isLoading = false;
            // Disable common loader
            this.httpUtilsService.loadingSubject.next(false);
            this.customLayoutUtilsService.showError('Error updating review detail', '');
            console.error(err);
          }
        });
      } else {
        this.permitsService.addInternalPlanReviewDetail(formData).subscribe({
          next: (res: any) => {
            this.isLoading = false;
            // Disable common loader
            this.httpUtilsService.loadingSubject.next(false);
            if (res?.isFault) {
              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create review detail', '');
            } else {
              this.customLayoutUtilsService.showSuccess('Review detail created successfully!', '');
              this.modal.close('created');
            }
          },
          error: (err: any) => {
            this.isLoading = false;
            // Disable common loader
            this.httpUtilsService.loadingSubject.next(false);
            this.customLayoutUtilsService.showError('Error creating review detail', '');
            console.error(err);
          }
        });
      }
    } else {
      this.detailForm.markAllAsTouched();
      if (!this.permitId) {
        this.customLayoutUtilsService.showError('Permit Id is required', '');
      }
    }
  }

  onCancel(): void {
    this.modal.dismiss('cancelled');
  }

  onBack(): void {
    this.modal.dismiss('back');
  }

  onClickAddReview(): void {
    this.formSubmitted = false;
    this.showForm = true;
    this.showExcelSection = false;
    this.showImportTableSection = false;
  }

  cancelAddForm(): void {
    if (this.detailForm) {
      this.detailForm.reset({
        sheetNumber: '',
        codeRef: '',
        codeDescription: '',
        reasoning: '',
        nonCompliance: '',
        actionableStep: ''
      });
    }
    this.formSubmitted = false;
    this.showForm = false;
    this.showExcelSection = true;
    this.showImportTableSection = false;
  }

  setActiveTab(tab: 'details' | 'comments'): void {
    this.activeTab = tab;
  }

  goToPrevious(): void {
    if (this.activeTab === 'comments') {
      this.activeTab = 'details';
    }
  }

  triggerFileImport(fileInput: HTMLInputElement): void {
    if (this.isLoading) { return; }
    fileInput.click();
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input?.files && input.files.length ? input.files[0] : null;
    if (!file) { return; }
    this.isImporting = true;
    // Enable common loader
    this.httpUtilsService.loadingSubject.next(true);
    const reader = new FileReader();
    reader.onload = async () => {
      try {
        const XLSX: any = await import('xlsx');
        const data = new Uint8Array(reader.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const json: any[] = XLSX.utils.sheet_to_json(worksheet, { defval: '' });
        this.importedRows = this.mapExcelRows(json);
        // After upload, switch to table section; hide instruction and form
        this.showExcelSection = false;
        this.showForm = false;
        this.showImportTableSection = this.importedRows.length > 0;
        if (!this.importedRows.length) {
          this.customLayoutUtilsService.showError('No rows found in the uploaded Excel.', '');
        } else {
          this.customLayoutUtilsService.showSuccess(`Imported ${this.importedRows.length} row(s).`, '');
        }
      } catch (err) {
        console.error('Error parsing Excel', err);
        this.importedRows = [];
        this.customLayoutUtilsService.showError('Failed to parse Excel file. Please check the template.', '');
      } finally {
        this.isImporting = false;
        // Disable common loader
        this.httpUtilsService.loadingSubject.next(false);
        // Reset file input so the same file can be selected again if needed
        input.value = '';
      }
    };
    reader.readAsArrayBuffer(file);
  }

  cancelImportTable(): void {
    // Hide table and return to instruction section
    this.showImportTableSection = false;
    this.showExcelSection = true;
  }

  private normalizeHeader(header: string): string {
    return (header || '').toString().trim().toLowerCase().replace(/\s+/g, '');
  }

  private mapExcelRows(jsonRows: any[]): Array<any> {
    if (!jsonRows || !jsonRows.length) { return []; }
    const headerMap: any = {};
    // Build header map from first row's keys
    const firstRow = jsonRows[0];
    Object.keys(firstRow).forEach((key) => {
      const norm = this.normalizeHeader(key);
      headerMap[norm] = key;
    });

    const pick = (row: any, keyCandidates: string[]): string => {
      for (const candidate of keyCandidates) {
        const norm = this.normalizeHeader(candidate);
        const actual = headerMap[norm];
        if (actual && row.hasOwnProperty(actual)) {
          return (row[actual] ?? '').toString();
        }
      }
      return '';
    };

    // Expected columns with common variants
    const rows = jsonRows.map((row) => {
      return {
        sheetNumber: pick(row, ['Sheet Number', 'SheetNumber', 'Sheet#', 'Sheet No']),
        codeRef: pick(row, ['Code Ref', 'Code Reference', 'CodeRef']),
        codeDescription: pick(row, ['Code Description', 'Description']),
        reasoning: pick(row, ['Reasoning', 'Reason']),
        nonCompliance: pick(row, ['Non Compliance', 'Non-Compliance', 'NonCompliance']),
        actionableStep: pick(row, ['Actionable Step', 'ActionableStep', 'Action Step'])
      };
    });
    // Filter out completely empty rows
    return rows.filter((r) => Object.values(r).some((v) => (v || '').toString().trim() !== ''));
  }

  deleteImportedRow(index: number): void {
    if (index < 0 || index >= this.importedRows.length) { return; }
    this.importedRows.splice(index, 1);
  }

  addImportedRow(): void {
    this.importedRows.unshift({
      sheetNumber: '',
      codeRef: '',
      codeDescription: '',
      reasoning: '',
      nonCompliance: '',
      actionableStep: ''
    });
  }

  async saveAllImported(): Promise<void> {
    this.importSubmitted = true;
    if (this.hasImportedInvalid) {
      this.customLayoutUtilsService.showError('Please fill all required fields in the imported rows.', '');
      return;
    }
    if (!this.permitId) {
      this.customLayoutUtilsService.showError('Permit Id is required to save imported rows', '');
      return;
    }
    if (!this.reviewCategory) {
      this.customLayoutUtilsService.showError('Review Category is required to save imported rows', '');
      return;
    }
    if (!this.importedRows.length) {
      this.customLayoutUtilsService.showError('No imported rows to save', '');
      return;
    }
    this.isLoading = true;
    // Enable common loader
    this.httpUtilsService.loadingSubject.next(true);
    try {
      const requests = this.importedRows.map((r) => {
        const payload: any = {
          sheetNumber: r.sheetNumber || '',
          codeRef: r.codeRef || '',
          codeDescription: r.codeDescription || '',
          reasoning: r.reasoning || '',
          nonCompliance: r.nonCompliance || '',
          actionableStep: r.actionableStep || '',
          permitId: this.permitId,
          permitNumber: this.permitNumber,
          reviewCategory: this.reviewCategory,
          internalCommentsId: this.internalCommentsId,
          loggedInUserId: this.loggedInUserId
        };
        return this.permitsService.addInternalPlanReviewDetail(payload);
      });
      // Execute all in parallel
      await new Promise((resolve, reject) => {
        const { forkJoin } = require('rxjs');
        forkJoin(requests).subscribe({ next: resolve, error: reject });
      });
      this.customLayoutUtilsService.showSuccess('All imported rows saved successfully!', '');
      this.importedRows = [];
      this.importSubmitted = false;
      // Optionally close modal or refresh parent via close value
      this.modal.close('bulk-created');
    } catch (err) {
      console.error('Error saving imported rows', err);
      this.customLayoutUtilsService.showError('Failed to save one or more imported rows', '');
    } finally {
      this.isLoading = false;
      // Disable common loader
      this.httpUtilsService.loadingSubject.next(false);
    }
  }

  get hasImportedInvalid(): boolean {
    if (!this.importedRows || !this.importedRows.length) { return false; }
    return this.importedRows.some((r) => !this.isImportedRowValid(r));
  }

  private isImportedRowValid(r: any): boolean {
    return !!(
      (r.sheetNumber || '').toString().trim() &&
      (r.codeRef || '').toString().trim() &&
      (r.codeDescription || '').toString().trim() &&
      (r.reasoning || '').toString().trim()
    );
  }

  get isFormValid(): boolean {
    return this.detailForm.valid;
  }

  get isDetailsValid(): boolean {
    if (!this.detailForm) { return false; }
    const controls = this.detailForm.controls as any;
    return (
      !!controls.sheetNumber?.valid &&
      !!controls.codeRef?.valid &&
      !!controls.codeDescription?.valid &&
      !!controls.reasoning?.valid
    );
  }
}
