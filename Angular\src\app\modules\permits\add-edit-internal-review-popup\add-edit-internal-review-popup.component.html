<div class="modal-content h-auto">
  <!-- Header -->
  <div class="modal-header bg-light-primary">
    <div class="modal-title h5 fs-3">
      <ng-container>
        <div *ngIf="isEdit">Edit Review Category - {{ permitNumber || '' }}</div>
        <div *ngIf="!isEdit">Add Review Category - {{ permitNumber || '' }}</div>
      </ng-container>
    </div>
    <div class="float-right">
      <i class="fa-solid fs-2 fa-xmark text-white" (click)="onCancel()"></i>
    </div>
  </div>

  <!-- Body -->
  <div class="modal-body">

    <!-- Form -->
    <form [formGroup]="reviewForm" (ngSubmit)="onSubmit()" novalidate>
      <div class="row mt-3">
        <div class="col-xl-6">
          <label class="fw-bold form-label mb-2">Review Category Type <span class="text-danger">*</span></label>
          <select
            formControlName="reviewCategory"
            class="form-select form-select-sm"
            [class.is-invalid]="shouldShowValidationError('reviewCategory')"
            [disabled]="isLoading"
          >
            <option value="">Select Category</option>
            <option *ngFor="let cat of reviewCategoryOptions" [value]="cat">{{ cat }}</option>
          </select>
          <div class="invalid-feedback" *ngIf="shouldShowValidationError('reviewCategory')">
            Required Field
          </div>
        </div>
      </div>
      <div class="row mt-4">
        <div class="col-xl-6">
          <label class="fw-bold form-label mb-2">Reviewer <span class="text-danger">*</span></label>
          <input
            type="text"
            formControlName="reviewer"
            class="form-control form-control-sm"
            [class.is-invalid]="shouldShowValidationError('reviewer')"
            placeholder="Type here"
            [disabled]="isLoading"
          />
          <div class="invalid-feedback" *ngIf="shouldShowValidationError('reviewer')">
            Required Field
          </div>
        </div>
        <div class="col-xl-6">
          <label class="fw-bold form-label mb-2">Review Status <span class="text-danger">*</span></label>
          <select
            formControlName="reviewStatus"
            class="form-select form-select-sm"
            [class.is-invalid]="shouldShowValidationError('reviewStatus')"
            [disabled]="isLoading"
          >
            <option value="">Select Status</option>
            <option *ngFor="let status of reviewStatusOptions" [value]="status">{{ status }}</option>
          </select>
          <div class="invalid-feedback" *ngIf="shouldShowValidationError('reviewStatus')">
            Required Field
          </div>
        </div>
      </div>
      <div class="row mt-4">
         <div class="col-xl-6">
          <label class="fw-bold form-label mb-2">Reviewed Date <span class="text-danger">*</span></label>
          <input
            type="date"
            formControlName="reviewedDate"
            class="form-control form-control-sm"
            [class.is-invalid]="shouldShowValidationError('reviewedDate')"
            [disabled]="isLoading"
          />
          <div class="invalid-feedback" *ngIf="shouldShowValidationError('reviewedDate')">
            Required Field
          </div>
        </div>
      </div>
      <div class="row mt-4">
        <div class="col-xl-12">
          <label class="fw-bold form-label mb-2">Comments</label>
          <textarea
            formControlName="comments"
            rows="3"
            class="form-control form-control-sm"
            placeholder="Type here"
            [disabled]="isLoading"
          ></textarea>
        </div>
      </div>
      <div class="modal-footer justify-content-between">
        <div></div>
        <div>
          <button type="button" class="btn btn-danger btn-sm btn-elevate me-2 mr-2" (click)="onCancel()" [disabled]="isLoading">
            Cancel
          </button>
          <button type="submit" class="btn btn-primary btn-sm" [disabled]="isLoading">
            {{ isEdit ? 'Update' : 'Create' }}
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
