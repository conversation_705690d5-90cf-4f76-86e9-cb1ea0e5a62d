import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { PermitsService } from '../../services/permits.service';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { HttpUtilsService } from '../../services/http-utils.service';

@Component({
  selector: 'app-add-edit-internal-review-popup',
  templateUrl: './add-edit-internal-review-popup.component.html',
  styleUrls: ['./add-edit-internal-review-popup.component.scss']
})
export class AddEditInternalReviewPopupComponent implements OnInit {
  @Input() permitId: number | null = null;
  @Input() internalSubmitId: number | null = null;
  @Input() permitNumber: string = '';
  @Input() reviewSubmitCategory: string = '';
  @Input() reviewData: any = null; // For edit mode
  @Input() loggedInUserId: string = 'user'; // Should be passed from parent

  reviewForm!: FormGroup;
  isEdit: boolean = false;
  isLoading: boolean = false;
  formSubmitted: boolean = false;

  reviewCategoryOptions = [
    'Building',
    'Electrical',
    'Mechanical',
    'Plumbing',
    'Structural',
    'Other'
  ];
  reviewStatusOptions = [
    'Pending',
    'In Progress',
    'Completed',
    'Verified',
    'Rejected'
  ];

  constructor(
    private fb: FormBuilder,
    public modal: NgbActiveModal,
    private permitsService: PermitsService,
    private customLayoutUtilsService: CustomLayoutUtilsService,
    private httpUtilsService: HttpUtilsService
  ) {}

  ngOnInit(): void {
    this.isEdit = !!this.reviewData;
    this.reviewForm = this.fb.group({
      reviewCategory: [this.reviewData?.reviewCategory || '', Validators.required],
      reviewer: [this.reviewData?.reviewer || '', Validators.required],
      reviewStatus: [this.reviewData?.reviewStatus || '', Validators.required],
      reviewedDate: [this.reviewData?.reviewedDate ? this.formatDateForInput(this.reviewData.reviewedDate) : this.getTodayDateString(), Validators.required],
      comments: [this.reviewData?.comments || ''], // Not required
    });
  }

  private formatDateForInput(date: string | Date): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type="date"]
  }

  private getTodayDateString(): string {
    const today = new Date();
    return today.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type="date"]
  }


  shouldShowValidationError(fieldName: string): boolean {
    // Only show validation errors when form has been submitted
    if (!this.formSubmitted) {
      return false;
    }
    
    const field = this.reviewForm.get(fieldName);
    return !!(field && field.invalid);
  }

  onSubmit(): void {
    this.formSubmitted = true;
    if (this.reviewForm.valid && this.permitId) {
      this.isLoading = true;
      // Enable common loader
      this.httpUtilsService.loadingSubject.next(true);
      
      const formData: any = {
        ...this.reviewForm.value,
        permitId: this.permitId,
        internalSubmitId: this.internalSubmitId,
        permitNumber: this.permitNumber,
        reviewSubmitCategory: this.reviewSubmitCategory,
        loggedInUserId: this.loggedInUserId
      };
      if (this.isEdit && this.reviewData?.internalCommentsId) {
        formData.internalCommentsId = this.reviewData.internalCommentsId;
        this.permitsService.updateInternalPlanReview(formData).subscribe({
          next: (res: any) => {
            this.isLoading = false;
            // Disable common loader
            this.httpUtilsService.loadingSubject.next(false);
            if (res?.isFault) {
              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to update review', '');
            } else {
              this.customLayoutUtilsService.showSuccess(res.responseData?.message || 'Review updated successfully!', '');
              this.modal.close('updated');
            }
          },
          error: (err: any) => {
            this.isLoading = false;
            // Disable common loader
            this.httpUtilsService.loadingSubject.next(false);
            this.customLayoutUtilsService.showError('Error updating review', '');
            console.error(err);
          }
        });
      } else {
        this.permitsService.addInternalPlanReview(formData).subscribe({
          next: (res: any) => {
            this.isLoading = false;
            // Disable common loader
            this.httpUtilsService.loadingSubject.next(false);
            if (res?.isFault) {
              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create review', '');
            } else {
              this.customLayoutUtilsService.showSuccess('Review created successfully!', '');
              this.modal.close('created');
            }
          },
          error: (err: any) => {
            this.isLoading = false;
            // Disable common loader
            this.httpUtilsService.loadingSubject.next(false);
            this.customLayoutUtilsService.showError('Error creating review', '');
            console.error(err);
          }
        });
      }
    } else {
      this.reviewForm.markAllAsTouched();
      if (!this.permitId) {
        this.customLayoutUtilsService.showError('Permit Id is required', '');
      }
    }
  }

  onCancel(): void {
    this.modal.dismiss('cancelled');
  }
}
