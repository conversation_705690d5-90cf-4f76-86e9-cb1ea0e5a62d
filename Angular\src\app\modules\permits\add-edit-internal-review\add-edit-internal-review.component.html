<div class="modal-content h-auto">
  <!-- Header -->
  <div class="modal-header bg-light-primary">
    <div class="modal-title h5 fs-3">
      <ng-container>
        <div *ngIf="isEdit">Edit Add Review Category - {{ permitNumber || '' }}</div>
        <div *ngIf="!isEdit">Add Review - {{ permitNumber || '' }}</div>
      </ng-container>
    </div>
    <div class="float-right">
      <i
        class="fa-solid fs-2 fa-xmark text-white"
        (click)="onCancel()"
      ></i>
    </div>
  </div>

  <!-- Body -->
  <div
    class="modal-body "
    style="max-height: calc(100vh - 250px); overflow-y: auto; position: relative;"
  >

    <!-- Tabs (match Project Popup style) -->
    <div class="row">
      <div class="col-xl-12">
        <div class="d-flex">
          <ul
            class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap"
          >
            <li class="nav-item">
              <a
                class="nav-link text-active-primary me-6 cursor-pointer"
                data-toggle="tab"
                [ngClass]="{ active: activeTab === 'details' }"
                (click)="setActiveTab('details')"
              >
                Review Details
              </a>
            </li>
            <li class="nav-item">
              <a
                class="nav-link text-active-primary me-6 cursor-pointer"
                data-toggle="tab"
                [ngClass]="{ active: activeTab === 'comments' }"
                (click)="setActiveTab('comments')"
              >
                Review Comments
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Form -->
    <form [formGroup]="reviewForm" (ngSubmit)="onSubmit()" novalidate>
        <!-- DETAILS TAB CONTENT -->
        <div *ngIf="activeTab === 'details'">
          <!-- Review Category and Type/Code/Drawing Row -->
          <div class="row mt-3">
            <div class="col-xl-6">
              <label class="fw-bold form-label mb-2">Review Cycle Name <span class="text-danger">*</span></label>
              <select
                formControlName="reviewCategory"
                class="form-select form-select-sm"
                [class.is-invalid]="shouldShowValidationError('reviewCategory')"
                [disabled]="isLoading"
              >
                <option value="">Select Category</option>
                <option value="Building">Building</option>
                <option value="Electrical">Electrical</option>
                <option value="Mechanical">Mechanical</option>
                <option value="Plumbing">Plumbing</option>
                <option value="Structural">Structural</option>
                <option value="Other">Other</option>
              </select>
              <div class="invalid-feedback" *ngIf="shouldShowValidationError('reviewCategory')">
                Required Field
              </div>
            </div>
            <div class="col-xl-6">
              <label class="fw-bold form-label mb-2">Type/Code/Drawing # <span class="text-danger">*</span></label>
              <input
                type="text"
                formControlName="typeCodeDrawing"
                class="form-control form-control-sm"
                [class.is-invalid]="shouldShowValidationError('typeCodeDrawing')"
                placeholder="Type here"
                [disabled]="isLoading"
              />
              <div class="invalid-feedback" *ngIf="shouldShowValidationError('typeCodeDrawing')">
                Required Field
              </div>
            </div>
          </div>

          <!-- Internal Reviewer and Internal Verification Status Row -->
          <div class="row mt-4">
            <div class="col-xl-6">
              <label class="fw-bold form-label mb-2">Internal Reviewer <span class="text-danger">*</span></label>
              <input
                type="text"
                formControlName="internalReviewer"
                class="form-control form-control-sm"
                [class.is-invalid]="shouldShowValidationError('internalReviewer')"
                placeholder="Type here"
                [disabled]="isLoading"
              />
              <div class="invalid-feedback" *ngIf="shouldShowValidationError('internalReviewer')">
                Required Field
              </div>
            </div>
            <div class="col-xl-6">
              <label class="fw-bold form-label mb-2">Internal Verification Status <span class="text-danger">*</span></label>
              <select
                formControlName="internalVerificationStatus"
                class="form-select form-select-sm"
                [class.is-invalid]="shouldShowValidationError('internalVerificationStatus')"
                [disabled]="isLoading"
              >
                <option value="">Select Status</option>
                <option>Pending</option>
                <option>In Progress</option>
                <option>Completed</option>
                <option>Verified</option>
                <option>Rejected</option>
              </select>
              <div class="invalid-feedback" *ngIf="shouldShowValidationError('internalVerificationStatus')">
                Required Field
              </div>
            </div>
          </div>

          <!-- Dates Row (Reviewed & Completed) -->
          <div class="row mt-4">
            <div class="col-xl-6">
              <label class="fw-bold form-label mb-2">Reviewed Date <span class="text-danger">*</span></label>
              <input
                type="date"
                formControlName="reviewedDate"
                class="form-control form-control-sm"
                [class.is-invalid]="shouldShowValidationError('reviewedDate')"
                [disabled]="isLoading"
              />
              <div class="invalid-feedback" *ngIf="shouldShowValidationError('reviewedDate')">
                Required Field
              </div>
            </div>
            <div class="col-xl-6">
              <label class="fw-bold form-label mb-2">Completed Date</label>
              <input
                type="date"
                formControlName="completedDate"
                class="form-control form-control-sm"
                [class.is-invalid]="shouldShowValidationError('completedDate')"
                [disabled]="isLoading"
              />
              <div class="invalid-feedback" *ngIf="shouldShowValidationError('completedDate')">
                Required Field
              </div>
            </div>
          </div>
        </div>

        <!-- COMMENTS TAB CONTENT -->
        <div *ngIf="activeTab === 'comments'">
          <!-- Review Comments -->
          <div class="row mt-3">
            <div class="col-xl-12">
              <label class="fw-bold form-label mb-2">Review Comments <span class="text-danger">*</span></label>
              <textarea
                formControlName="reviewComments"
                rows="3"
                class="form-control form-control-sm"
                [class.is-invalid]="shouldShowValidationError('reviewComments')"
                placeholder="Type here"
                [disabled]="isLoading"
              ></textarea>
              <div class="invalid-feedback" *ngIf="shouldShowValidationError('reviewComments')">
                Required Field
              </div>
            </div>
          </div>

          <!-- Non Compliance Items -->
          <div class="row mt-4">
            <div class="col-xl-12">
              <label class="fw-bold form-label mb-2">Non Compliance Items</label>
              <textarea
                formControlName="nonComplianceItems"
                rows="3"
                class="form-control form-control-sm"
                placeholder="Type here"
                [disabled]="isLoading"
              ></textarea>
            </div>
          </div>

          <!-- A/E Response -->
          <div class="row mt-4">
            <div class="col-xl-12">
              <label class="fw-bold form-label mb-2">A/E Response</label>
              <textarea
                formControlName="aeResponse"
                rows="3"
                class="form-control form-control-sm"
                placeholder="Type here"
                [disabled]="isLoading"
              ></textarea>
            </div>
          </div>
        </div>
    </form>
  </div>

  <!-- Footer -->
  <div class="modal-footer d-flex justify-content-between align-items-center">
    <div>
      <button
        type="button"
        class="btn btn-light btn-sm"
        (click)="goToPrevious()"
        [disabled]="isLoading || activeTab === 'details'"
      >
        Previous
      </button>
    </div>
    <div>
      <button
        type="button"
        class="btn btn-danger btn-sm btn-elevate me-2"
        (click)="onCancel()"
        [disabled]="isLoading"
      >
        Cancel
      </button>
      <button
        *ngIf="activeTab === 'details'"
        type="button"
        class="btn btn-primary btn-sm"
        [disabled]="isLoading"
        (click)="setActiveTab('comments')"
      >
        Next
      </button>
      <button
        *ngIf="activeTab === 'comments'"
        type="submit"
        class="btn btn-primary btn-sm"
        [disabled]="isLoading"
        (click)="onSubmit()"
      >
        {{ isEdit ? 'Update' : 'Create' }}
      </button>
    </div>
  </div>
</div>

