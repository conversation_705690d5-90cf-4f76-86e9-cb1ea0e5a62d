import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Component, Input } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { PermitsService } from '../../services/permits.service';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { HttpUtilsService } from '../../services/http-utils.service';

@Component({
  selector: 'app-add-edit-internal-review',
  standalone: false,
  templateUrl: './add-edit-internal-review.component.html',
  styleUrl: './add-edit-internal-review.component.scss',
})
export class AddEditInternalReviewComponent {
  @Input() permitId: number | null = null;
  @Input() reviewData: any = null; // For edit mode
  @Input() loggedInUserId: string = 'user'; // Should be passed from parent
  @Input() permitNumber: string = '';

  reviewForm!: FormGroup;
  isEdit: boolean = false;
  isLoading: boolean = false;
  activeTab: 'details' | 'comments' = 'details';
  formSubmitted: boolean = false;

  constructor(
    private fb: FormBuilder,
    public modal: NgbActiveModal,
    private modalService: NgbModal,
    private permitsService: PermitsService,
    private customLayoutUtilsService: CustomLayoutUtilsService,
    private httpUtilsService: HttpUtilsService
  ) {}

  ngOnInit(): void {
    this.isEdit = !!this.reviewData;

    this.reviewForm = this.fb.group({
      reviewCategory: [this.reviewData?.reviewCategory || '', Validators.required],
      typeCodeDrawing: [this.reviewData?.typeCodeDrawing || '', Validators.required],
      reviewComments: [this.reviewData?.reviewComments || '', Validators.required],
      nonComplianceItems: [this.reviewData?.nonComplianceItems || ''],
      aeResponse: [this.reviewData?.aeResponse || ''],
      internalReviewer: [this.reviewData?.internalReviewer || '', Validators.required],
      internalVerificationStatus: [this.reviewData?.internalVerificationStatus || '', Validators.required],
      reviewedDate: [this.reviewData?.reviewedDate ? this.formatDateForInput(this.reviewData.reviewedDate) : this.getTodayDateString(), Validators.required],
      completedDate: [
        this.reviewData?.completedDate
          ? this.formatDateForInput(this.reviewData.completedDate)
          : ''
      ],
    });
  }

  setActiveTab(tab: 'details' | 'comments'): void {
    this.activeTab = tab;
  }

  goToPrevious(): void {
    if (this.activeTab === 'comments') {
      this.activeTab = 'details';
    }
  }

  private formatDateForInput(date: string | Date): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type="date"]
  }

  private getTodayDateString(): string {
    const today = new Date();
    return today.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type="date"]
  }


  shouldShowValidationError(fieldName: string): boolean {
    // Only show validation errors when form has been submitted
    if (!this.formSubmitted) {
      return false;
    }
    
    const field = this.reviewForm.get(fieldName);
    return !!(field && field.invalid);
  }

  onSubmit(): void {
    this.formSubmitted = true;
    if (this.reviewForm.valid && this.permitId) {
      this.isLoading = true;
      // Enable common loader
      this.httpUtilsService.loadingSubject.next(true);

      const formData = {
        ...this.reviewForm.value,
        permitId: this.permitId,
        loggedInUserId: this.loggedInUserId
      };

      if (this.isEdit && this.reviewData?.commentsId) {
        // Update existing review
        formData.commentsId = this.reviewData.commentsId;

        this.permitsService.updateInternalReview(formData).subscribe({
          next: (res: any) => {
            this.isLoading = false;
            // Disable common loader
            this.httpUtilsService.loadingSubject.next(false);
            if (res?.isFault) {
              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to update internal review', '');
            } else {
              this.customLayoutUtilsService.showSuccess(res.responseData.message, '');
              this.modal.close('updated');
            }
          },
          error: (err: any) => {
            this.isLoading = false;
            // Disable common loader
            this.httpUtilsService.loadingSubject.next(false);
            this.customLayoutUtilsService.showError('Error updating internal review', '');
            console.error(err);
          }
        });
      } else {
        // Create new review
        this.permitsService.addInternalReview(formData).subscribe({
          next: (res: any) => {
            this.isLoading = false;
            // Disable common loader
            this.httpUtilsService.loadingSubject.next(false);
            if (res?.isFault) {
              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create internal review', '');
            } else {
              this.customLayoutUtilsService.showSuccess('Internal review created successfully!', '');
              this.modal.close('created');
            }
          },
          error: (err: any) => {
            this.isLoading = false;
            // Disable common loader
            this.httpUtilsService.loadingSubject.next(false);
            this.customLayoutUtilsService.showError('Error creating internal review', '');
            console.error(err);
          }
        });
      }
    } else {
      this.reviewForm.markAllAsTouched();
      if (!this.permitId) {
        this.customLayoutUtilsService.showError('Permit Id is required', '');
      }
    }
  }

  onCancel(): void {
    this.modal.dismiss('cancelled');
  }

  get isFormValid(): boolean {
    return this.reviewForm.valid;
  }

  get isDetailsValid(): boolean {
    if (!this.reviewForm) { return false; }
    const controls = this.reviewForm.controls as any;
    return (
      !!controls.reviewCategory?.valid &&
      !!controls.typeCodeDrawing?.valid &&
      !!controls.internalReviewer?.valid &&
      !!controls.internalVerificationStatus?.valid &&
      !!controls.reviewedDate?.valid
    );
  }
}
