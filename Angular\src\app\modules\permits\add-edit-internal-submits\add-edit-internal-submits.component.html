<div class="modal-content h-auto">
  <!-- Header -->
  <div class="modal-header bg-light-primary">
    <div class="modal-title h5 fs-3">
      <ng-container>
        <div *ngIf="isEdit">Edit Review Cycle - {{ permitNumber || '' }}</div>
        <div *ngIf="!isEdit">Add Review Cycle - {{ permitNumber || '' }}</div>
      </ng-container>
    </div>
    <div class="float-right">
      <i class="fa-solid fs-2 fa-xmark text-white" (click)="onCancel()"></i>
    </div>
  </div>

  <!-- Body -->
  <div class="modal-body">
    <!-- Form -->
    <form [formGroup]="submitForm" (ngSubmit)="onSubmit()" novalidate>
      <div class="row mt-3">
        <div class="col-xl-6">
          <label class="fw-bold form-label mb-2">Review Cycle Name <span class="text-danger">*</span></label>
          <input
            type="text"
            formControlName="reviewCategory"
            class="form-control form-control-sm"
            [class.is-invalid]="shouldShowValidationError('reviewCategory')"
            placeholder="Type here"
            [disabled]="isLoading"
          />
          <div class="invalid-feedback" *ngIf="shouldShowValidationError('reviewCategory')">
            Required Field
          </div>
        </div>
        <div class="col-xl-6">
          <label class="fw-bold form-label mb-2">Cycle # <span class="text-danger">*</span></label>
          <input
            type="number"
            formControlName="cycleNumber"
            class="form-control form-control-sm"
            [class.is-invalid]="shouldShowValidationError('cycleNumber')"
            placeholder="Enter cycle #"
            [min]="0"
            [disabled]="isLoading"
          />
          <div class="invalid-feedback" *ngIf="shouldShowValidationError('cycleNumber')">
            Must be a non-negative number
          </div>
        </div>
      </div>
      <div class="row mt-4">
        <div class="col-xl-6">
          <label class="fw-bold form-label mb-2">Reviewer <span class="text-danger">*</span></label>
          <input
            type="text"
            formControlName="reviewer"
            class="form-control form-control-sm"
            [class.is-invalid]="shouldShowValidationError('reviewer')"
            placeholder="Type here"
            [disabled]="isLoading"
          />
          <div class="invalid-feedback" *ngIf="shouldShowValidationError('reviewer')">
            Required Field
          </div>
        </div>
        <div class="col-xl-6">
          <label class="fw-bold form-label mb-2">Submittal Status <span class="text-danger">*</span></label>
          <select
            formControlName="submittalStatus"
            class="form-select form-select-sm"
            [class.is-invalid]="shouldShowValidationError('submittalStatus')"
            [disabled]="isLoading"
          >
            <option value="">Select Status</option>
            <option value="Pending">Pending</option>
            <option value="In Progress">In Progress</option>
            <option value="Completed">Completed</option>
            <option value="Verified">Verified</option>
            <option value="Rejected">Rejected</option>
          </select>
          <div class="invalid-feedback" *ngIf="shouldShowValidationError('submittalStatus')">
            Required Field
          </div>
        </div>
      </div>
      <div class="row mt-4">
        <div class="col-xl-6">
          <label class="fw-bold form-label mb-2">Reviewed Date <span class="text-danger">*</span></label>
          <input
            type="date"
            formControlName="reviewedDate"
            class="form-control form-control-sm"
            [class.is-invalid]="shouldShowValidationError('reviewedDate')"
            [disabled]="isLoading"
          />
          <div class="invalid-feedback" *ngIf="shouldShowValidationError('reviewedDate')">
            Required Field
          </div>
        </div>
      </div>
      <div class="row mt-4">
        <div class="col-xl-12">
          <label class="fw-bold form-label mb-2">Notes</label>
          <textarea
            formControlName="notes"
            rows="3"
            class="form-control form-control-sm"
            placeholder="Type here"
            [disabled]="isLoading"
          ></textarea>
        </div>
      </div>
      <div class="modal-footer justify-content-between">
        <div></div>
        <div>
          <button type="button" class="btn btn-danger btn-sm btn-elevate me-2 mr-2" (click)="onCancel()" [disabled]="isLoading">
            Cancel
          </button>
          <button type="submit" class="btn btn-primary btn-sm" [disabled]="isLoading">
            {{ isEdit ? 'Update' : 'Create' }}
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
