import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { PermitsService } from '../../services/permits.service';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { HttpUtilsService } from '../../services/http-utils.service';

@Component({
  selector: 'app-add-edit-internal-submits',
  templateUrl: './add-edit-internal-submits.component.html',
  styleUrl: './add-edit-internal-submits.component.scss'
})
export class AddEditInternalSubmitsComponent implements OnInit {
  @Input() permitId: number | null = null;
  @Input() submitData: any = null; // For edit mode
  @Input() loggedInUserId: string = 'user'; // Should be passed from parent
  @Input() permitNumber: string = '';

  submitForm!: FormGroup;
  isEdit: boolean = false;
  isLoading: boolean = false;
  formSubmitted: boolean = false;

  constructor(
    private fb: FormBuilder,
    public modal: NgbActiveModal,
    private permitsService: PermitsService,
    private customLayoutUtilsService: CustomLayoutUtilsService,
    private httpUtilsService: HttpUtilsService
  ) {}

  ngOnInit(): void {
    this.isEdit = !!this.submitData;
    this.submitForm = this.fb.group({
      reviewCategory: [this.submitData?.reviewCategory || '', Validators.required],
      cycleNumber: [
        this.submitData?.cycleNumber ?? '',
        [Validators.required, Validators.min(0)]
      ],
      reviewer: [this.submitData?.reviewer || '', Validators.required],
      submittalStatus: [this.submitData?.submittalStatus || 'Pending', Validators.required],
      reviewedDate: [this.submitData?.reviewedDate ? this.formatDateForInput(this.submitData.reviewedDate) : this.getTodayDateString(), Validators.required],
      notes: [this.submitData?.notes || ''],
    });
  }

  private formatDateForInput(date: string | Date): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type="date"]
  }

  private getTodayDateString(): string {
    const today = new Date();
    return today.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type="date"]
  }


  shouldShowValidationError(fieldName: string): boolean {
    // Only show validation errors when form has been submitted
    if (!this.formSubmitted) {
      return false;
    }
    
    const field = this.submitForm.get(fieldName);
    return !!(field && field.invalid);
  }

  onSubmit(): void {
    this.formSubmitted = true;
    if (this.submitForm.valid && this.permitId) {
      this.isLoading = true;
      // Enable common loader
      this.httpUtilsService.loadingSubject.next(true);
      
      const formData = {
        ...this.submitForm.value,
        permitId: this.permitId,
        loggedInUserId: this.loggedInUserId
      };
      if (this.isEdit && this.submitData?.submitId) {
        formData.submitId = this.submitData.submitId;
        this.permitsService.updateInternalSubmit(formData).subscribe({
          next: (res: any) => {
            this.isLoading = false;
            this.httpUtilsService.loadingSubject.next(false);
            if (res?.isFault) {
              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to update Review Cycle', '');
            } else {
              this.customLayoutUtilsService.showSuccess(res.responseData?.message || 'Review Cycle updated successfully!', '');
              this.modal.close('updated');
            }
          },
          error: (err: any) => {
            this.isLoading = false;
            this.httpUtilsService.loadingSubject.next(false);
            this.customLayoutUtilsService.showError('Error updating Review Cycle', '');
            console.error(err);
          }
        });
      } else {
        this.permitsService.addInternalSubmit(formData).subscribe({
          next: (res: any) => {
            this.isLoading = false;
            this.httpUtilsService.loadingSubject.next(false);
            if (res?.isFault) {
              this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create Review Cycle', '');
            } else {
              this.customLayoutUtilsService.showSuccess('Review Cycle created successfully!', '');
              this.modal.close('created');
            }
          },
          error: (err: any) => {
            this.isLoading = false;
            this.httpUtilsService.loadingSubject.next(false);
            this.customLayoutUtilsService.showError('Error creating Review Cycle', '');
            console.error(err);
          }
        });
      }
    } else {
      this.submitForm.markAllAsTouched();
      if (!this.permitId) {
        this.customLayoutUtilsService.showError('Permit Id is required', '');
      }
    }
  }

  onCancel(): void {
    this.modal.dismiss('cancelled');
  }
}
