<div class="modal-content h-auto">
  <div class="modal-header bg-light-primary">
    <div class="modal-title h5 fs-3">Respond</div>
    <div class="float-right">
      <i class="fa-solid fs-2 fa-xmark text-white" (click)="cancel()"></i>
    </div>
  </div>

  <div class="modal-body">
    <form [formGroup]="form" novalidate>
      <!-- A/E Response fields only -->
      <div class="row mt-3">
        <div class="col-xl-12">
          <label class="fw-bold form-label mb-2">A/E Response</label>
          <textarea formControlName="aeResponse" rows="3" class="form-control form-control-sm" placeholder="Type here"></textarea>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-xl-12">
          <label class="fw-bold form-label mb-2">Responded By</label>
          <input type="text" formControlName="commentResponsedBy" class="form-control form-control-sm" placeholder="Type here" />
        </div>
      </div>
    </form>
  </div>

  <div class="modal-footer d-flex justify-content-end align-items-center">
    <button type="button" class="btn btn-danger btn-sm btn-elevate me-2" (click)="cancel()" [disabled]="isLoading">Cancel</button>
    <button type="button" class="btn btn-primary btn-sm" (click)="submit()" [disabled]="isLoading">
      {{ isLoading ? 'Saving...' : 'Save' }}
    </button>
  </div>
</div>


