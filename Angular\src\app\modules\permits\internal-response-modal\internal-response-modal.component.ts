import { Component, Input } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { PermitsService } from '../../services/permits.service';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { HttpUtilsService } from '../../services/http-utils.service';

@Component({
  selector: 'app-internal-response-modal',
  templateUrl: './internal-response-modal.component.html',
  styleUrls: ['./internal-response-modal.component.scss']
})
export class InternalResponseModalComponent {
  @Input() permitId: number | null = null;
  @Input() submitAudit: any = null; // contains commentsId (submitId), cycleNumber, reviewer, status, reviewedDate, notes, title
  @Input() planReview: any = null; // contains internalCommentsId, reviewCategory
  @Input() detail: any = null; // contains internalReviewCommentsId, aeResponse, commentResponsedBy
  @Input() loggedInUserId: any;

  form!: FormGroup;
  isLoading: boolean = false;
  formSubmitted: boolean = false;

  constructor(
    private fb: FormBuilder,
    public activeModal: NgbActiveModal,
    private permitsService: PermitsService,
    private customLayoutUtilsService: CustomLayoutUtilsService,
    private httpUtilsService: HttpUtilsService
  ) {}

  ngOnInit(): void {
    const d = this.detail || {};
    this.form = this.fb.group({
      aeResponse: [d.aeResponse || ''],
      commentResponsedBy: [d.commentResponsedBy || '']
    });
  }

  shouldShowValidationError(fieldName: string): boolean {
    if (!this.formSubmitted) { return false; }
    const field = this.form.get(fieldName);
    return !!(field && field.invalid);
  }

  submit(): void {
    this.formSubmitted = true;
    if (!this.permitId || !this.planReview || !this.detail) { return; }

    this.isLoading = true;
    this.httpUtilsService.loadingSubject.next(true);

    const detailPayload: any = {
      permitId: this.permitId,
      permitNumber: this.submitAudit?.permitNumber || '',
      reviewCategory: this.planReview?.reviewCategory || this.submitAudit?.reviewCategory || '',
      internalCommentsId: this.planReview?.internalCommentsId,
      internalReviewCommentsId: this.detail?.internalReviewCommentsId,
      aeResponse: this.form.value.aeResponse,
      commentResponsedBy: this.form.value.commentResponsedBy,
      loggedInUserId: this.loggedInUserId
    };

    this.permitsService.updateInternalPlanReviewDetail(detailPayload).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        this.httpUtilsService.loadingSubject.next(false);
        if (res?.isFault) {
          this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to update detail', '');
        } else {
          this.customLayoutUtilsService.showSuccess('Response saved successfully', '');
          this.activeModal.close('updated');
        }
      },
      error: () => this.handleError('Error updating detail')
    });
  }

  cancel(): void {
    this.activeModal.dismiss('cancelled');
  }

  private handleError(msg: string): void {
    this.isLoading = false;
    this.httpUtilsService.loadingSubject.next(false);
    this.customLayoutUtilsService.showError(msg, '');
  }
}


