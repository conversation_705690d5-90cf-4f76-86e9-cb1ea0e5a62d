<!-- Full Screen Loading Overlay -->
<div *ngIf="loading || isLoading" class="fullscreen-loading-overlay">
  <div class="loading-content">
    <div class="custom-colored-spinner" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-4 text-primary fs-5">Loading...</div>
  </div>
</div>

<div class="grid-container">
  <kendo-grid
    #normalGrid
    [data]="gridData"
    [pageSize]="page.size"
    [sort]="sort"
    [pageable]="{
      pageSizes: [15, 20, 50, 100],
      previousNext: true,
      info: true,
      type: 'numeric',
      buttonCount: 5
    }"
    [total]="page.totalElements"
    [sortable]="{ allowUnsort: true, mode: 'single' }"
    [groupable]="false"
    [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
    (selectionChange)="onSelectionChange($event)"
    [reorderable]="true"
    style="width: auto; overflow-x: auto"
    [resizable]="false"
    [height]="720"
    [skip]="skip"
    [filter]="filter"
    [columnMenu]="{ filter: true }"
    (filterChange)="filterChange($event)"
    (pageChange)="pageChange($event)"
    (pageSizeChange)="onPageSizeChange($event)"
    (dataStateChange)="onDataStateChange($event)"
    (sortChange)="onSortChange($event)"
    (columnVisibilityChange)="updateColumnVisibility($event)"
    [loading]="false"
  >
    <ng-template kendoGridToolbarTemplate>
      <!-- Search Section -->
      <div class="d-flex align-items-center me-3 search-section">
        <kendo-textbox
          [style.width.px]="500"
          placeholder="Search..."
          [(ngModel)]="searchData"
          [clearButton]="true"
          (keydown)="onSearchKeyDown($event)"
          (blur)="onSearchBlur()"
          (valueChange)="onSearchValueChange()"
        ></kendo-textbox>
        <!-- <button kendoButton [disabled]="!searchData || searchData.trim() === ''" (click)="loadTable()" class="ms-2">
          <i class="fas fa-search"></i> Search
        </button> -->
      </div>

      <kendo-grid-spacer></kendo-grid-spacer>

      <!-- Total Count - Repositioned to the right -->
      <div class="d-flex align-items-center me-3">
        <span class="text-muted">Total: </span>
        <span class="fw-bold ms-1">{{ page.totalElements || 0 }}</span>
      </div>

      <!-- Action Buttons -->
      <button type="button" class="btn btn-success btn-sm toolbar-btn" (click)="add()" title="Add Permit">
        <i class="fas fa-plus text-white"></i>Add
      </button>

      <button
        type="button"
        class="btn btn-icon btn-sm toolbar-btn"
        (click)="toggleExpand()"
        title="Toggle Grid Expansion"
      >
        <i
          class="fas text-secondary"
          [class.fa-expand]="!isExpanded"
          [class.fa-compress]="isExpanded"
        ></i>
      </button>

      <!-- Excel Export Dropdown -->
      <div class="custom-dropdown toolbar-btn" [class.show]="isExcelDropdownOpen" #excelDropdown>
        <button class="btn btn-icon btn-sm" type="button" (click)="toggleExcelDropdown($event)" title="Export Excel" #excelButton>
          <i class="fas fa-file-excel text-success"></i>
        </button>
        <div class="custom-dropdown-menu" *ngIf="isExcelDropdownOpen" [style.top.px]="dropdownTop" [style.left.px]="dropdownLeft">
          <a class="custom-dropdown-item" href="#" (click)="onExportClick({value: 'all'}); closeExcelDropdown(); $event.preventDefault()">All</a>
          <a class="custom-dropdown-item" href="#" (click)="onExportClick({value: 'selected'}); closeExcelDropdown(); $event.preventDefault()">Page Results</a>
        </div>
      </div>

      <!-- Save Column Settings Button -->
      <!-- <button
        type="button"
        class="btn btn-icon btn-sm toolbar-btn"
        (click)="saveHead()"
        title="Save Column Settings"
      >
        <i class="fas fa-save text-success"></i>
      </button> -->

      <!-- Reset Button -->
      <button
        type="button"
        class="btn btn-icon btn-sm toolbar-btn"
        (click)="resetTable()"
        title="Reset to Default"
      >
        <i class="fas fa-undo text-warning"></i>
      </button>

      <!-- Refresh Button -->
      <button
        type="button"
        class="btn btn-icon btn-sm toolbar-btn"
        (click)="refreshGrid()"
        title="Refresh Grid Data"
      >
        <i class="fas fa-sync-alt text-info"></i>
      </button>
    </ng-template>

    <!-- Advanced Filters Panel -->
    <ng-template kendoGridToolbarTemplate>
      <div
        *ngIf="showAdvancedFilters"
        class="advanced-filters-panel p-3 bg-light border-bottom"
      >
        <div class="row">
          <div class="col-md-3">
            <label class="form-label">Status</label>
            <kendo-dropdownlist
              [data]="advancedFilterOptions.status"
              textField="text"
              valueField="value"
              [(ngModel)]="appliedFilters.status"
              placeholder="Select Status"
              class="k-dropdownlist-sm"
            >
            </kendo-dropdownlist>
          </div>
          <div class="col-md-3">
            <label class="form-label">Category</label>
            <kendo-dropdownlist
              [data]="advancedFilterOptions.categories"
              textField="text"
              valueField="value"
              [(ngModel)]="appliedFilters.category"
              placeholder="Select Category"
              class="k-dropdownlist-sm"
            >
            </kendo-dropdownlist>
          </div>
          <div class="col-md-6 d-flex align-items-end">
            <button
              kendoButton
              themeColor="primary"
              class="me-2"
              (click)="applyAdvancedFilters()"
            >
              Apply Filters
            </button>
            <button
              kendoButton
              themeColor="secondary"
              class="me-2"
              (click)="clearAdvancedFilters()"
            >
              Clear Filters
            </button>
            <button
              kendoButton
              themeColor="light"
              (click)="showAdvancedFilters = false"
            >
              Hide Filters
            </button>
          </div>
        </div>
      </div>
    </ng-template>

    <!-- Toggle Advanced Filters Button -->
    <ng-template kendoGridToolbarTemplate>
      <button
        type="button"
        class="btn btn-outline-secondary btn-sm me-2"
        (click)="showAdvancedFilters = !showAdvancedFilters"
        title="Toggle Advanced Filters"
      >
        <i class="fas fa-filter"></i>
        {{ showAdvancedFilters ? "Hide" : "Show" }} Advanced Filters
      </button>
    </ng-template>

    <ng-container *ngFor="let column of gridColumns">
      <!-- Action Column -->
      <kendo-grid-column
        *ngIf="column === 'action'"
        field="action"
        title="Actions"
        [width]="80"
        [sortable]="false"
        [filterable]="false"
        [includeInChooser]="false"
        [columnMenu]="false"
        [hidden]="getHiddenField('action')"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          <div class="d-flex gap-1">
            <!-- <a
              title="View"
              class="btn btn-icon btn-sm"
              (click)="view(dataItem.permitId)"
            >
              <span
                [inlineSVG]="'./assets/media/icons/duotune/general/gen019.svg'"
                class="svg-icon svg-icon-3 svg-icon-primary"
              >
              </span>
            </a> -->
            <a
              title="View"
              class="btn btn-icon btn-sm"
              (click)="edit(dataItem.permitId)"
            >
              <i class="fas fa-edit text-primary"></i>
            </a>
          </div>
        </ng-template>
      </kendo-grid-column>

      <!-- Merged Permit Number and Name Column -->
      <kendo-grid-column
        *ngIf="column === 'permitNumber'"
        field="permitNumber"
        title="Permit # / Permit/Sub Project Name"
        [width]="250"
        [headerStyle]="{ 'font-weight': '600' }"
        [hidden]="getHiddenField('permitNumber')"
        [filterable]="true"
        [sortable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          <div class="permit-info">
            <div class="permit-number fw-bold d-flex align-items-center">
              {{ dataItem.permitNumber | truncateText:45 }}
              <span class="badge badge-permit-type ms-2" *ngIf="dataItem.permitReviewType">
                {{ dataItem.permitReviewType.charAt(0) }}
              </span>
            </div>
            <div class="permit-name">{{ dataItem.permitName | truncateText:45 }}</div>
          </div>
        </ng-template>

        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
            operator="contains"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>
  <kendo-grid-column
        *ngIf="column === 'projectName'"
        field="projectName"
        title="Project Name"
        [width]="180"
        [includeInChooser]="false"
        [hidden]="getHiddenField('projectName')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          {{ dataItem.projectName | truncateText:45 }}
        </ng-template>

        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
            operator="contains"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>
      <!-- Permit Type Column -->
      <kendo-grid-column
        *ngIf="column === 'permitType'"
        field="permitType"
        title="Permit Type"
        [width]="200"
        [headerStyle]="{ 'font-weight': '600' }"
        [hidden]="getHiddenField('permitType')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          {{ dataItem.permitType | truncateText:45 }}
        </ng-template>

        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
            operator="contains"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Permit Category Column -->
      <kendo-grid-column
        *ngIf="column === 'permitCategory'"
        field="permitCategory"
        title="Category"
        [width]="120"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          {{ dataItem.permitCategory | truncateText:45 }}
        </ng-template>

        <ng-template kendoGridFilterMenuTemplate let-filter  let-filterService="filterService" let-column="column">
           <kendo-dropdownlist
       [column]="column"
      [filter]="filter"
      [data]="advancedFilterOptions.categories"
      [(ngModel)]="appliedFilters.status"
      textField="text"
      valueField="value"
      [valuePrimitive]="true"
      [defaultItem]="{ text: 'Select', value: null }"
      (valueChange)="onMenuDropdownChange($event, column.field, filterService)"
      class="k-dropdownlist-sm">
    </kendo-dropdownlist>
        </ng-template>
      </kendo-grid-column>

      <!-- Permit Status Column -->
      <kendo-grid-column
        *ngIf="column === 'internalReviewStatus'"
        field="internalReviewStatus"
        title="Internal Review Status"
        [width]="165"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          {{ dataItem.internalReviewStatus }}
        </ng-template>

       <ng-template kendoGridFilterMenuTemplate let-filter let-filterService="filterService" let-column="column">
            <kendo-dropdownlist
       [column]="column"
      [filter]="filter"
      [data]="advancedFilterOptions.categoriess"
      [(ngModel)]="appliedFilters.internalReviewStatus"
      textField="text"
      valueField="value"
      [valuePrimitive]="true"
      [defaultItem]="{ text: 'Select', value: null }"
      (valueChange)="onMenuDropdownChange($event, column.field, filterService)"
      class="k-dropdownlist-sm">
    </kendo-dropdownlist>
          <!-- <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
            operator="contains"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
          </kendo-grid-string-filter-menu> -->
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column
        *ngIf="column === 'permitStatus'"
        field="permitStatus"
        title="Permit Status"
        [width]="145"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          <span class="badge" [ngClass]="getStatusClass(dataItem.permitStatus)">
            {{ dataItem.permitStatus }}
          </span>
        </ng-template>

        <ng-template kendoGridFilterMenuTemplate let-filter let-filterService="filterService" let-column="column">
            <kendo-dropdownlist
       [column]="column"
      [filter]="filter"
      [data]="advancedFilterOptions.status"
      [(ngModel)]="appliedFilters.status"
      textField="text"
      valueField="value"
      [valuePrimitive]="true"
      [defaultItem]="{ text: 'Select', value: null }"
      (valueChange)="onMenuDropdownChange($event, column.field, filterService)"
      class="k-dropdownlist-sm">
    </kendo-dropdownlist>
          <!-- <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
            operator="contains"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
          </kendo-grid-string-filter-menu> -->
        </ng-template>
      </kendo-grid-column>

      <!-- Location Column -->
      <kendo-grid-column
        *ngIf="column === 'location'"
        field="location"
        title="Location"
        [width]="200"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          {{ dataItem.location | truncateText:45 }}
        </ng-template>

        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
            operator="contains"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Project Name Column -->
    

      <!-- Permit Applied Date Column -->
      <kendo-grid-column
        *ngIf="column === 'permitAppliedDate'"
        field="permitAppliedDate"
        title="Applied Date"
        [width]="130"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          {{ formatDate(dataItem.permitAppliedDate) }}
        </ng-template>

        <ng-template
          kendoGridFilterMenuTemplate
          let-filter
          let-column="column"
          let-filterService="filterService"
        >
          <kendo-grid-date-filter-menu
            [column]="column"
            [filter]="filter"
            operator="eq"
            [filterService]="filterService"
            format="MM/dd/yyyy"
          >
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-before-operator></kendo-filter-before-operator>
            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>
            <kendo-filter-after-operator></kendo-filter-after-operator>
            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>
          </kendo-grid-date-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Permit Expiration Date Column -->
      <kendo-grid-column
        *ngIf="column === 'permitExpirationDate'"
        field="permitExpirationDate"
        title="Expiration Date"
        [width]="130"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          {{ formatDate(dataItem.permitExpirationDate) }}
        </ng-template>

        <ng-template
          kendoGridFilterMenuTemplate
          let-filter
          let-column="column"
          let-filterService="filterService"
        >
          <kendo-grid-date-filter-menu
            [column]="column"
            [filter]="filter"
            operator="eq"
            [filterService]="filterService"
            format="MM/dd/yyyy"
          >
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-before-operator></kendo-filter-before-operator>
            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>
            <kendo-filter-after-operator></kendo-filter-after-operator>
            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>
          </kendo-grid-date-filter-menu>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column
        *ngIf="column === 'permitFinalDate'"
        field="permitFinalDate"
        title="Final Date"
        [width]="130"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          {{ formatDate(dataItem.permitFinalDate) }}
        </ng-template>

        <ng-template
          kendoGridFilterMenuTemplate
          let-filter
          let-column="column"
          let-filterService="filterService"
        >
          <kendo-grid-date-filter-menu
            [column]="column"
            [filter]="filter"
            operator="eq"
            [filterService]="filterService"
            format="MM/dd/yyyy"
          >
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-before-operator></kendo-filter-before-operator>
            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>
            <kendo-filter-after-operator></kendo-filter-after-operator>
            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>
          </kendo-grid-date-filter-menu>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column
        *ngIf="column === 'permitCompleteDate'"
        field="permitCompleteDate"
        title="Complete Date"
        [width]="130"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          {{ formatDate(dataItem.permitCompleteDate) }}
        </ng-template>

        <ng-template
          kendoGridFilterMenuTemplate
          let-filter
          let-column="column"
          let-filterService="filterService"
        >
          <kendo-grid-date-filter-menu
            [column]="column"
            [filter]="filter"
            operator="eq"
            [filterService]="filterService"
            format="MM/dd/yyyy"
          >
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-before-operator></kendo-filter-before-operator>
            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>
            <kendo-filter-after-operator></kendo-filter-after-operator>
            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>
          </kendo-grid-date-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Attention Reason Column -->
      <kendo-grid-column
        *ngIf="column === 'attentionReason'"
        field="attentionReason"
        title="Attention Reason"
        [width]="180"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          {{ (dataItem.attentionReason || "") | truncateText:45 }}
        </ng-template>

        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
            operator="contains"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Last Updated Date Column -->
      <kendo-grid-column
        *ngIf="column === 'lastUpdatedDate'"
        field="lastUpdatedDate"
        title="Updated Date"
        [width]="130"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          {{ formatDate(dataItem.lastUpdatedDate) }}
        </ng-template>

        <ng-template
          kendoGridFilterMenuTemplate
          let-filter
          let-column="column"
          let-filterService="filterService"
        >
          <kendo-grid-date-filter-menu
            [column]="column"
            [filter]="filter"
            operator="eq"
            [filterService]="filterService"
            format="MM/dd/yyyy"
          >
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-before-operator></kendo-filter-before-operator>
            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>
            <kendo-filter-after-operator></kendo-filter-after-operator>
            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>
          </kendo-grid-date-filter-menu>
        </ng-template>
      </kendo-grid-column>
    </ng-container>

    <!-- No Data Template -->
    <ng-template kendoGridNoRecordsTemplate>
      <div class="custom-no-records" *ngIf="!loading && !isLoading">
        <div class="text-center">
          <p class="text-muted">No data found</p>
        </div>
      </div>
    </ng-template>
  </kendo-grid>
</div>

<!-- Example trigger button -->
<!-- <button class="btn btn-danger" (click)="open(deleteModal)">Delete</button> -->
