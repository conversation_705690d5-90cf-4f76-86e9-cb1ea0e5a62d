// Grid Container Styles
.grid-container {
  // position: relative;
  // width: 100%;
  // height: 100%;
  // transition: all 0.3s ease;
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  &.fullscreen-grid {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: white;
    padding: 20px;
    overflow: auto;
  }
}

// Loading Overlay Styles handled globally in styles.scss

// Search Section Styles
// .search-section {
//   .k-textbox {
//     border-radius: 6px;

//     &:focus {
//       box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
//     }
//   }
// }

.search-section {
  .k-textbox {
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem; // reduced inner spacing for shorter height
    width: 80%; // full width (or set custom px)
    border: 2px solid #afc7dd; // highlight border color
    box-shadow: 0 0 6px rgba(59, 83, 135, 0.5); // glowing effect
  }

  .k-button {
    border-radius: 0.375rem;
    padding: 0.75rem 1.25rem; // bigger button
    min-width: 120px; // longer button
    background-color: #4c4e4f;
    color: white;
    font-weight: 500;
    transition: background 0.3s, transform 0.2s;

    &:hover {
      background-color: #4c4e4f;
      transform: scale(1.05);
    }
  }
}

// Advanced Filters Panel
.advanced-filters-panel {
  border-radius: 8px;
  margin-bottom: 1rem;

  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
  }

  .k-dropdownlist {
    width: 100%;
    border-radius: 6px;
  }
}


  // Custom Dropdown Button - Match Bootstrap button styling
  .k-dropdownbutton.btn {
    .k-button {
      border-radius: 0.375rem !important;
      font-weight: 500 !important;
      font-size: 0.75rem !important;
      padding: 0.25rem 0.5rem !important;
      background-color: #6c757d !important;
      border-color: #6c757d !important;
      color: #fff !important;
      margin-right: 0.5rem !important;
      transition: all 0.2s ease !important;
      height: auto !important;
      min-height: 31px !important;

      &:hover {
        background-color: #5a6268 !important;
        border-color: #545b62 !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
      }

      &:focus {
        box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5) !important;
      }
    }
  }
// Kendo Grid Customization
:host ::ng-deep {
  .k-grid {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    .k-grid-header {
      background: #f8f9fa;
      border-bottom: 2px solid #dee2e6;

      .k-header {
        background: #f8f9fa;
        border-color: #dee2e6;
        font-weight: 600;
        color: #495057;

        &:hover {
          background: #e9ecef;
        }
      }
    }

    .k-grid-toolbar {
      background: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
      // padding: 1rem;

      .k-button {
        border-radius: 6px;
        font-weight: 500;

        &.k-primary {
          background: #007bff;
          border-color: #007bff;

          &:hover {
            background: #0056b3;
            border-color: #0056b3;
          }
        }
      }

      /* Toolbar button styles for icon-only buttons */
      .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
        font-size: 0.875rem;
        font-weight: 500;
        border-radius: 0.375rem;
        transition: all 0.15s ease-in-out;
        min-width: 40px;
        height: 40px;
        border: 1px solid transparent;
        background-color: transparent;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          background-color: rgba(0, 0, 0, 0.05);
        }

        /* Icon-only buttons */
        &.btn-icon {
          padding: 0.5rem;
          min-width: 40px;
          width: 40px;

          // Icon sizing now handled by common-toolbar.scss
        }

        /* Buttons with text - ensure consistent spacing */
        &:not(.btn-icon) {
          padding: 0.375rem 0.75rem;
          gap: 0.5rem;
        }
      }
    }

    .k-grid-content {
      .k-grid-row {
        &:hover {
          background: #f8f9fa;
        }

        &.k-alt {
          background: #f8f9fa;

          &:hover {
            background: #e9ecef;
          }
        }
      }
    }

    .k-pager {
      background: #f8f9fa;
      border-top: 1px solid #dee2e6;

      .k-pager-info {
        color: #6c757d;
      }

      .k-pager-numbers {
        .k-link {
          border-radius: 4px;

          &:hover {
            background: #e9ecef;
          }

          &.k-state-selected {
            background: #007bff;
            color: white;
          }
        }
      }
    }
  }

  // Custom Dropdown Button - now in common-toolbar.scss

  // Kendo Textbox
  .k-textbox {
    border-radius: 6px;

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }

  // Kendo Dropdownlist
  .k-dropdownlist {
    border-radius: 6px;

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }
}

// Permit Info Styles for Merged Column
.permit-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  
  .permit-number {
    font-weight: 700;
    color: #212529;
    font-size: 0.9rem;
    line-height: 1.2;
  }
  
  .permit-name {
    font-weight: 400;
    color: #6c757d;
    font-size: 0.85rem;
    line-height: 1.2;
  }
}


// Badge Styles
.badge {
  padding: 0.5em 0.75em;
  font-size: 0.75em;
  font-weight: 600;
  border-radius: 6px;

  &.badge-light-success {
    background: #d4edda;
    color: #155724;
  }

  &.badge-light-warning {
    background: #fff3cd;
    color: #856404;
  }

  &.badge-light-danger {
    background: #f8d7da;
    color: #721c24;
  }

  &.badge-light-info {
    background: #d1ecf1;
    color: #0c5460;
  }

  &.badge-light-secondary {
    background: #e2e3e5;
    color: #383d41;
  }

  &.badge-light-primary {
    background: #cce7ff;
    color: #004085;
  }

  &.badge-permit-type {
    background-color: #e3f2fd;  /* light blue background */
    color: #1565c0;             /* darker blue text for contrast */
    border: 1px solid #bbdefb;  /* subtle border */
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    white-space: nowrap;
    display: inline-block;
  }
}

// Button Styles - now in common-toolbar.scss
.btn {
  // &.btn-primary {
  //   background: #007bff;
  //   border-color: #007bff;

  //   &:hover {
  //     background: #0056b3;
  //     border-color: #0056b3;
  //   }
  // }

  // &.btn-secondary {
  //   background-color: #5a6268;
  //   border-color: #5a6268;

  //   &:hover {
  //     background-color: #5a6268;
  //     border-color: #545b62;
  //   }
  // }

  &.btn-success {
    background: #28a745;
    border-color: #28a745;

    &:hover {
      background: #1e7e34;
      border-color: #1e7e34;
    }
  }

  &.btn-warning {
    background: #ffc107;
    border-color: #ffc107;
    color: #212529;

    &:hover {
      background: #e0a800;
      border-color: #d39e00;
    }
  }

  &.btn-info {
    background: #17a2b8;
    border-color: #17a2b8;

    &:hover {
      background: #138496;
      border-color: #138496;
    }
  }

  &.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;

    &:hover {
      background: #6c757d;
      color: white;
    }
  }
}

// Action Buttons - styles now in common-toolbar.scss

// Toolbar styles are now imported from common-toolbar.scss

// No Data Message
.text-muted {
  .fas {
    color: #6c757d;
  }

  h4 {
    color: #495057;
    margin-top: 1rem;
  }

  p {
    color: #6c757d;
    margin-bottom: 1.5rem;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .grid-container {
    .k-grid-toolbar {
      flex-direction: column;
      gap: 1rem;

      .search-section {
        width: 100%;

        .k-textbox {
          width: 100% !important;
        }
      }

      .d-flex {
        justify-content: center;
      }
    }
  }

  .advanced-filters-panel {
    .row {
      .col-md-3,
      .col-md-6 {
        margin-bottom: 1rem;
      }
    }
  }
}

// Animation for grid expansion
.grid-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.fullscreen-grid {
    animation: expandGrid 0.3s ease-out;
  }
}

@keyframes expandGrid {
  from {
    opacity: 0.8;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Force Excel dropdown to appear above Kendo Grid */
:host ::ng-deep .dropdown {
  z-index: 999999 !important;
  position: relative !important;
}

:host ::ng-deep .excel-dropdown-menu {
  z-index: 999999 !important;
  position: absolute !important;
}

/* Reduce Kendo Grid z-index in this component */
:host ::ng-deep .k-grid,
:host ::ng-deep .k-grid-header,
:host ::ng-deep .k-grid-header-wrap {
  z-index: 1 !important;
}


