<div class="modal-content h-auto">
  <div class="modal-header bg-light-primary">
    <div class="modal-title h5 fs-3">
      <ng-container> 
        <div *ngIf="id === 0">Add Permit</div>
        <div *ngIf="id !== 0">Edit Permit # - {{ permitNumber }}</div>
      </ng-container>
    </div>
    <div class="float-right">
      <i class="fa-solid fs-2 fa-xmark text-white" (click)="modal.dismiss()"></i>
    </div>
  </div>

  <div class="modal-body">
    <!-- Loading overlay removed; global loader handles this -->

    <div class="row">
      <div class="col-xl-12">
        <div class="d-flex">
          <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap">
            <li class="nav-item">
              <a class="nav-link text-active-primary me-6 cursor-pointer" data-toggle="tab"
                [ngClass]="{ active: selectedTab === 'basic' }" (click)="showTab('basic', $event)">
                Basic Info
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link text-active-primary me-6 cursor-pointer" data-toggle="tab"
                [ngClass]="{ active: selectedTab === 'details' }" (click)="showTab('details', $event)">
                Permit Details
              </a>
            </li>
            <!-- <li class="nav-item">
              <a class="nav-link text-active-primary me-6 cursor-pointer" data-toggle="tab"
                [ngClass]="{ active: selectedTab === 'notes' }" (click)="showTab('notes', $event)">
                Notes/Actions
              </a>
            </li> -->
          </ul>
        </div>
      </div>
    </div>

    <form class="form form-label-right" [formGroup]="permitForm">
      <ng-container *ngIf="selectedTab == 'basic'">
        <div class="row mt-4">
          <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Project <span class="text-danger">*</span></label>
            <ng-select [items]="projects" [clearable]="false" [multiple]="false" bindLabel="projectName"
              formControlName="projectId" bindValue="projectId" placeholder="Select Project"
              (change)="onProjectChange($event)" class="ng-select-sm">
            </ng-select>
            <div *ngIf="shouldShowValidationError('projectId')"
              class="text-danger mt-1 small">
              <div *ngIf="permitForm.get('projectId')?.errors?.['required']">
                Required Field
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-4">
          <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Permit / Sub Project Name <span class="text-danger">*</span></label>
            <input type="text" class="form-control form-control-sm" formControlName="permitName" />
            <div *ngIf="shouldShowValidationError('permitName')"
              class="text-danger mt-1 small">
              <div *ngIf="permitForm.get('permitName')?.errors?.['required']">
                Required Field
              </div>
            </div>
          </div>
        </div>
                <div class="row mt-4">
          <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Location<span class="text-danger">*</span></label>
            <input type="text" class="form-control form-control-sm" formControlName="location" />
            <div *ngIf="shouldShowValidationError('location')"
              class="text-danger mt-1 small">
              <div *ngIf="permitForm.get('location')?.errors?.['required']">
                Required Field
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-4">
           <div class="col-xl-6">
             <label class="fw-bold form-label mb-2">Permit Review Type<span
                 class="text-danger">*</span></label>
             <ng-select [items]="reviewTypeArray" formControlName="permitReviewType"
               [clearable]="permitForm.get('permitReviewType')?.value"
               (change)="onPermitReviewTypeChange($event)" (clear)="onPermitReviewTypeClear()" 
               placeholder="Select Review Type" class="ng-select-sm">
             </ng-select>
             <div *ngIf="shouldShowValidationError('permitReviewType')"
               class="text-danger mt-1 small">
               <div *ngIf="permitForm.get('permitReviewType')?.errors?.['required']">
                  Required Field
               </div>
             </div>
           </div>
          <!-- <div  class="col-xl-4">
            <ng-container *ngIf="isHideInternalReviewStatus">

            <label class="fw-bold form-label mb-2">Internal Review Status <span class="text-danger">*</span></label>
            <ng-select [items]="internalStatusArray" [clearable]="false" [multiple]="false"
              formControlName="internalReviewStatus" placeholder="Select Status">
            </ng-select>
            <div
              *ngIf="shouldShowValidationError('internalReviewStatus')"
              class="text-danger mt-1 small">
              <div *ngIf="permitForm.get('internalReviewStatus')?.errors?.['required']">
                 Required Field
              </div>
            </div>
            </ng-container>

          </div> -->
          <div class="col-xl-6">
            <label class="fw-bold form-label mb-2">Permit Category <span class="text-danger">*</span></label>
            <ng-select [items]="categories" [clearable]="false" [multiple]="false" formControlName="permitCategory"
              placeholder="Select Category" class="ng-select-sm">
            </ng-select>
            <div *ngIf="shouldShowValidationError('permitCategory')"
              class="text-danger mt-1 small">
              <div *ngIf="permitForm.get('permitCategory')?.errors?.['required']">
                 Required Field
              </div>
            </div>
          </div>

        </div>
                <div class="row mt-3">
          <div class="col-xl-12">
            <div class="text-muted small d-flex align-items-center" style="white-space: normal;">
              <i class="fas fa-info-circle me-2"></i>
              <span>For External Review, permit details can be retrieved from the Municipality City website when Municipality is chosen.</span>
            </div>
          </div>
        </div>
         <div class="row mt-4">
           <div class="col-xl-6">
           <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Permit # <span class="text-danger">*</span></label>
            <div class="position-relative">
              <input type="text" class="form-control form-control-sm" formControlName="permitNumber" 
                     (blur)="triggerPermitNumberValidation()"
                     [class.is-invalid]="permitForm.get('permitNumber')?.invalid && permitForm.get('permitNumber')?.touched" />
              <div *ngIf="isPermitNumberValidating" class="position-absolute top-50 end-0 translate-middle-y me-2">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                  <span class="visually-hidden">Validating...</span>
                </div>
              </div>
            </div>
            <!-- Validation error messages -->
            <div *ngIf="shouldShowValidationError('permitNumber')"
              class="text-danger mt-1 small">
              <div *ngIf="permitForm.get('permitNumber')?.errors?.['required']">
                Required Field
              </div>
              <div *ngIf="permitForm.get('permitNumber')?.errors?.['permitNumberExists']">
                {{ permitNumberValidationError }}
              </div>
            </div>
            <!-- Show validation error even if field is not touched but has error (e.g., after blur) -->
            <div *ngIf="permitNumberValidationError && !permitForm.get('permitNumber')?.touched"
              class="text-danger mt-1 small">
              {{ permitNumberValidationError }}
            </div>
          </div>
           </div>
           <div class="col-xl-6" *ngIf="permitForm.get('permitReviewType')?.value !== 'Internal'">
              <div class="d-flex align-items-end">
                <div class="flex-grow-1 me-3">
                  <label class="fw-bold form-label mb-2">Municipality (External)<span
                   *ngIf="isPermitMunicipalRequired" class="text-danger">*</span></label>
               <ng-select [items]="muncipalities" [clearable]="permitForm.get('permitMunicipality')?.value" [multiple]="false" bindLabel="cityName"
                 formControlName="permitMunicipality" bindValue="municipalityId" placeholder="Select Municipality"
                 (change)="onPermitMunicipalityChange($event)" (clear)="onPermitMunicipalityClear()" class="ng-select-sm">
               </ng-select>
               <div *ngIf="shouldShowValidationError('permitMunicipality')"
                 class="text-danger mt-1 small">
                 <div *ngIf="permitForm.get('permitMunicipality')?.errors?.['required']">
                    Required Field
                 </div>
               </div>
                </div>
                <div class="flex-shrink-0">
                 <button type="button" class="btn btn-primary btn-sm"
                         (click)="syncPermitDetails()">
                   <i class="fas fa-sync-alt"></i> Sync
                 </button>
               </div>
              </div>
              <!-- Syncing message moved inside Municipality section -->
              <div class="mt-3">
                <div class="small text-muted" style="white-space: normal;">
                  The syncing of permit details may take up to 3 minutes.
                </div>
              </div>
           </div>
         </div>
        
      </ng-container>
      <ng-container *ngIf="selectedTab == 'details'">
        <div class="row mt-4">
        <div class="col-xl-4">
          <label class="fw-bold form-label mb-2">Review Responsible Party<span class="text-danger">*</span></label>
          <ng-select
            [items]="internalAndAdminUsers"
            [clearable]="false"
            [multiple]="false"
            bindLabel="userFullName"
            formControlName="reviewResponsibleParty"
            bindValue="userId"
            placeholder="Select Responsible Party"
            class="ng-select-sm">
          </ng-select>
          <div
            *ngIf="shouldShowValidationError('reviewResponsibleParty')"
            class="text-danger mt-1 small">
            <div *ngIf="permitForm.get('reviewResponsibleParty')?.errors?.['required']">
              Required Field
            </div>
          </div>
        </div>
          <div class="col-xl-4">
            <label class="fw-bold form-label mb-2">Primary Contact Name</label>
            <input type="text" class="form-control form-control-sm" formControlName="primaryContact" />
          </div>
          <div class="col-xl-4">
            <label class="fw-bold form-label mb-2">Permit Status <span class="text-danger">*</span></label>
            <ng-select [items]="statuses" [clearable]="false" [multiple]="false" formControlName="permitStatus"
              placeholder="Select Status" class="ng-select-sm">
            </ng-select>
            <div *ngIf="shouldShowValidationError('permitStatus')"
              class="text-danger mt-1 small">
              <div *ngIf="permitForm.get('permitStatus')?.errors?.['required']">
                 Required Field
              </div>
            </div>
          </div>

        </div>
        <div class="row mt-4">
          <div class="col-xl-4">
            <label class="fw-bold form-label mb-2">Permit Type <span class="text-danger">*</span></label>
            <ng-select [items]="permitTypes" [clearable]="false" [multiple]="false" formControlName="permitType"
              placeholder="Select Type" class="ng-select-sm">
            </ng-select>
            <div *ngIf="shouldShowValidationError('permitType')"
              class="text-danger mt-1 small">
              <div *ngIf="permitForm.get('permitType')?.errors?.['required']">
                Required Field
              </div>
            </div>
          </div>
          <div class="col-xl-4">
            <label class="fw-bold form-label mb-2">Issue Date</label>
            <input type="date" class="form-control form-control-sm" formControlName="permitIssueDate" />
          </div>
          <div class="col-xl-4">
            <label class="fw-bold form-label mb-2">Applied Date <span class="text-danger">*</span></label>
            <input type="date" class="form-control form-control-sm" formControlName="permitAppliedDate" />
            <div *ngIf="shouldShowValidationError('permitAppliedDate')"
              class="text-danger mt-1 small">
              <div *ngIf="permitForm.get('permitAppliedDate')?.errors?.['required']">
                 Required Field
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-4">


          <div class="col-xl-4">
            <label class="fw-bold form-label mb-2">Expiration Date</label>
            <input type="date" class="form-control form-control-sm" formControlName="permitExpirationDate" />
          </div>
          <div class="col-xl-4">
            <label class="fw-bold form-label mb-2">Completed Date</label>
            <input type="date" class="form-control form-control-sm" formControlName="permitCompleteDate" />
          </div>
          <div class="col-xl-4">
            <label class="fw-bold form-label mb-2">Final Date</label>
            <input type="date" class="form-control form-control-sm" formControlName="permitFinalDate" />
          </div>
        </div>

        <div class="row mt-4">
          <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Description</label>
            <textarea class="form-control form-control-sm" rows="3" formControlName="description"></textarea>
          </div>
        </div>
      </ng-container>
      <!-- <ng-container *ngIf="selectedTab == 'role'">

        <div class="row mt-4">
          <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Permit City Review Link<span *ngIf="isPermitMunicipalRequired"
                class="text-danger">*</span></label>
            <input type="url" class="form-control form-control-sm" formControlName="cityReviewLink" />
            <div *ngIf="shouldShowValidationError('cityReviewLink')"
              class="text-danger mt-1 small">
              <div *ngIf="permitForm.get('cityReviewLink')?.errors?.['required']">
                Permit City Review Link is required
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-4">

        </div>


      </ng-container> -->
      <!-- <ng-container *ngIf="selectedTab == 'notes'">
        <div class="row mt-4">
          <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Attention Reason</label>
            <textarea class="form-control form-control-sm" rows="3" formControlName="attentionReason"></textarea>
          </div>
        </div>
        <div class="row mt-4">
          <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Internal Notes</label>
            <textarea class="form-control form-control-sm" rows="3" formControlName="internalNotes"></textarea>
          </div>
        </div>

        <div class="row mt-4">
          <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Action Taken</label>
            <textarea class="form-control form-control-sm" rows="3" formControlName="actionTaken"></textarea>
          </div>
        </div>
      </ng-container> -->
    </form>
  </div>

  <div class="modal-footer justify-content-between">
    <div>
      <button *ngIf="selectedTab == 'details'" type="button" class="btn btn-secondary btn-sm btn-elevate"
        (click)="goToPreviousTab()">
        Previous
      </button>
    </div>
    <div>
      <button type="button" class="btn btn-danger btn-sm btn-elevate mr-2" (click)="modal.dismiss()">
        Cancel</button>&nbsp;
      <!-- *ngIf="selectedTab == 'notes'"  -->
      <button type="button" class="btn btn-primary btn-sm" *ngIf="selectedTab == 'details'"
        [disabled]="isPermitNumberValidating" (click)="save()">
        Save
      </button>
      <button *ngIf="selectedTab == 'basic'" type="button" class="btn btn-primary btn-sm" (click)="goToNextTab()">
        Next
      </button>
    </div>
  </div>
</div>
