import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, ActivationEnd, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ReviewDetailsModalComponent } from '../review-details-modal/review-details-modal.component';
import { ResponseModalComponent } from '../response-modal/response-modal.component';
import { AddEditInternalReviewComponent } from '../add-edit-internal-review/add-edit-internal-review.component';
import { AddEditInternalSubmitsComponent } from '../add-edit-internal-submits/add-edit-internal-submits.component';
import { PermitPopupComponent } from '../permit-popup/permit-popup.component';
import { PermitsService } from '../../services/permits.service';
import { EditExternalReviewComponent } from '../edit-external-review/edit-external-review.component';
import { AddEditInternalReviewPopupComponent } from '../add-edit-internal-review-popup/add-edit-internal-review-popup.component';
import { AddEditInternalReviewDetailComponent } from '../add-edit-internal-review-detail/add-edit-internal-review-detail.component';
import { InternalResponseModalComponent } from '../internal-response-modal/internal-response-modal.component';
import { AppService } from '../../services/app.service';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { autoTable } from 'jspdf-autotable';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-permit-view',
  templateUrl: './permit-view.component.html',
  styleUrls: ['./permit-view.component.scss'],
})
export class PermitViewComponent implements OnInit, OnDestroy {
    notesForm: FormGroup;
  internalStatusArray =['Approved','Pacifica Verification','Dis-Approved','Pending','Not Required','In Review','1 Cycle Completed']

  public permitId: number | null = null;
  public permit: any = null;
  public isLoading: boolean = false; // Main page loader
  public auditEntries: any[] = [];
  public selectedAuditIndex: number = 0; // Set to 0 to make first item initially active
  public selectedAuditName: any = '';
  public selectedAuditStatus: any = '';
  selectedTab: any = 'details';
  permitReviewType:any = '';
  public externalReviews: any[] = [];
  public reviewsError: string = '';
  public externalSubmittals: Array<{ id: any; title: string; submittalStatus: string; receivedDate: Date | null; dueDate: Date | null; completedDate: Date | null; }> = [];
  public selectedExternalSubmittalId: any = null;
  public internalReviews: any[] = [];
  public loginUser:any ={};
  public isAdmin: boolean = false;
  singlePermit: boolean;
  public expandedSubmittals: Set<number> = new Set();
  public expandedReviews: Set<string> = new Set();
  public reviewSelectedTabs: { [key: string]: string } = {};
  private routeSubscription: Subscription = new Subscription();
  private queryParamsSubscription: Subscription = new Subscription();
statusList = [
  { text: 'Pending', value: 'Pending' },
  { text: 'In Progress', value: 'In Progress' },
  { text: 'Completed', value: 'Completed' },
  { text: 'On Hold', value: 'On Hold' }
];
  // Navigation tracking
  public previousPage: string = 'permit-list'; // Default fallback
  public projectId: number | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef,
    private fb: FormBuilder,
// private modal: NgbActiveModal,

    private appService:AppService,
        private customLayoutUtilsService: CustomLayoutUtilsService,

    private permitsService: PermitsService
  ) {}

  ngOnInit(): void {
    this.loginUser = this.appService.getLoggedInUser();
    this.isAdmin = this.checkIfAdmin();

    // Read query parameters for navigation tracking
    this.queryParamsSubscription = this.route.queryParams.subscribe(params => {
      this.previousPage = params['from'] || 'permit-list';
      this.projectId = params['projectId'] ? Number(params['projectId']) : null;
      console.log('Permit view - query params:', { previousPage: this.previousPage, projectId: this.projectId });
    });

    // Listen for route parameter changes
    this.routeSubscription = this.route.paramMap.subscribe(params => {
      const idParam = params.get('id');
      this.permitId = idParam ? Number(idParam) : null;

      if (this.permitId) {
        this.fetchPermitDetails();
        this.fetchExternalReviews();
        this.fetchInternalReviews();
      }
    });
    this.loadForm()
  }
  loadForm() {
    this.notesForm = this.fb.group({
     attentionReason:[''],
     internalNotes:[''],
     actionTaken:[''],
     internalReviewStatus:[null],
    });

    // Trigger change detection to update the view
    this.cdr.detectChanges();
  }
  ngOnDestroy(): void {
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
    if (this.queryParamsSubscription) {
      this.queryParamsSubscription.unsubscribe();
    }
  }

  public fetchPermitDetails(): void {
    if (!this.permitId) { return; }
    this.isLoading = true;
    this.permitsService.getPermit({ permitId: this.permitId }).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        console.log('Permit API Response:', res);
        if (!res?.isFault) {
          this.permit = res.responseData?.data || res.responseData || null;
          console.log('Permit data assigned:', this.permit);
          console.log('Permit permitName field:', this.permit?.permitName);
          console.log('All permit fields:', Object.keys(this.permit || {}));
          this.permitReviewType = this.permit?.permitReviewType || '';
          // Default to details tab, user can navigate to reviews as needed
          this.selectedTab = 'details'
        } else {
          console.error('API returned fault:', res.faultMessage);
          this.permit = null;
        }
        this.cdr.markForCheck();
      },
      error: () => {
        this.isLoading = false;
        this.cdr.markForCheck();
      }
    });
  }

  public fetchExternalReviews(): void {
    if (!this.permitId) { return; }
    this.isLoading = true;
    this.reviewsError = '';
    this.permitsService.getAllReviews({ permitId: this.permitId }).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res?.isFault) {
          this.reviewsError = res.faultMessage || 'Failed to load reviews';
        } else {
          const reviews = res.responseData?.reviews || [];
          this.externalReviews = reviews.map((r: any) => ({
            commentsId:r.commentsId,
            name: r.TypeName,
            reviewer: r.AssignedTo,
            status: r.StatusText,
            completedDate: r.CompletedDate ? new Date(r.CompletedDate) : null,
            dueDate: r.DueDate ? new Date(r.DueDate) : null,
            receivedDate: r.receivedDate ? new Date(r.receivedDate) : null,
            comments: r.Comments,
            corrections: r.Corrections || [],
            submittalId: r.SubmittalId,
            FailureFlag: r.FailureFlag,
            reviewCategory: r.reviewCategory,
            EORAOROwner_Response: r.EORAOROwner_Response,
            commentResponsedBy: r.commentResponsedBy
          }));

          // Build submittal list grouped from reviews
          const idToReviews: { [key: string]: any[] } = {};
          this.externalReviews.forEach((rv: any) => {
            const key = String(rv.submittalId ?? 'unknown');
            if (!idToReviews[key]) { idToReviews[key] = []; }
            idToReviews[key].push(rv);
          });

          this.externalSubmittals = Object.keys(idToReviews).map((key) => {
            const items = idToReviews[key];
            // Determine status priority: Requires Re-submit > Under Review > Approved w/ Conditions > Approved
            const statusOrder: any = {
              'Requires Re-submit': 4,
              'Under Review': 3,
              'Approved w/ Conditions': 2,
              'Approved': 1
            };
            const submittalStatus = items.reduce((acc: string, it: any) => {
              const a = statusOrder[acc] || 0; const b = statusOrder[it.status] || 0; return b > a ? it.status : acc;
            }, '');

            // Aggregate dates
            const dueDate = items.reduce((acc: Date | null, it: any) => {
              if (!it.dueDate) { return acc; }
              if (!acc) { return it.dueDate; }
              return acc > it.dueDate ? it.dueDate : acc; // earliest due date
            }, null as Date | null);

            const completedDate = items.reduce((acc: Date | null, it: any) => {
              if (!it.completedDate) { return acc; }
              if (!acc) { return it.completedDate; }
              return acc < it.completedDate ? it.completedDate : acc; // latest completed date
            }, null as Date | null);

            // Get received date from the first item that has it
            const receivedDate = items.find((it: any) => it.receivedDate)?.receivedDate || 
                                items.find((it: any) => it.createdDate)?.createdDate || 
                                null;

            // Get submittal name from the first item (all items in this group have same submittalId)
            const reviewCategory = items[0]?.reviewCategory || `Submittal ${key}`;

            return {
              id: key,
              title: reviewCategory,
              submittalStatus: submittalStatus || (items[0]?.status || ''),
              receivedDate: receivedDate ? new Date(receivedDate) : null,
              dueDate: dueDate,
              completedDate: completedDate
            };
          }).sort((a, b) => {
            // Sort by received date in descending order (latest first)
            if (!a.receivedDate && !b.receivedDate) return 0;
            if (!a.receivedDate) return 1;
            if (!b.receivedDate) return -1;
            return b.receivedDate.getTime() - a.receivedDate.getTime();
          });

          // Select first submittal by default
          if (this.externalSubmittals.length > 0) {
            this.selectedExternalSubmittalId = this.externalSubmittals[0].id;
            this.selectedAuditName = this.externalSubmittals[0].title;
            this.selectedAuditStatus = this.externalSubmittals[0].submittalStatus;
          }
        }
        this.cdr.markForCheck();
      },
      error: (err: any) => {
        this.isLoading = false;
        this.reviewsError = 'Failed to load reviews';
        this.cdr.markForCheck();
      }
    });
  }

  public fetchInternalReviews(): void {
    if (!this.permitId) { return; }
    console.log('Fetching internal reviews for permitId:', this.permitId);
    this.isLoading = true;

    // Use consolidated API to get all internal submittals and reviews
    this.permitsService.getAllInternalReviews(this.permitId).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        console.log('Internal reviews API response:', res);
        if (res?.isFault) {
          console.error('Failed to load internal reviews:', res.faultMessage);
          this.auditEntries = [];
        } else {
          const reviews = res?.responseData?.reviews || res?.reviews || [];
          console.log('Raw reviews data:', reviews);

          // Process reviews similar to fetchExternalReviews - group by submittal first
          const internalReviews = reviews.map((r: any) => ({
            internalCommentsId: r.internalCommentsId,
            internalSubmitId: r.internalSubmitId,
            reviewCategory: r.reviewCategory || '',
            typeName: r.TypeName,
            reviewer: r.AssignedTo || r.reviewer || '',
            reviewStatus: r.reviewStatus || '',
            reviewedDate: r.itemReviewDate ? new Date(r.itemReviewDate) : (r.reviewedDate ? new Date(r.reviewedDate) : null),
            comments: r.comments || '',
            corrections: r.corrections || [],
            submittalStatus: r.submittalStatus || '',
            cycleNumber: r.cycleNumber || '',
            notes: r.notes || ''
          }));

          // Build submittal list grouped from reviews (similar to external reviews)
          const idToReviews: { [key: string]: any[] } = {};
          internalReviews.forEach((rv: any) => {
            const key = String(rv.internalSubmitId ?? 'unknown');
            if (!idToReviews[key]) { idToReviews[key] = []; }
            idToReviews[key].push(rv);
          });

          this.auditEntries = Object.keys(idToReviews).map((key) => {
            const items = idToReviews[key];
            
            // Determine status priority for submittal status
            const statusOrder: any = {
              'Dis-Approved': 4,
              'Pacifica Verification': 3,
              'In Review': 2,
              'Approved': 1,
              '1 Cycle Completed': 1
            };
            const submittalStatus = items.reduce((acc: string, it: any) => {
              const a = statusOrder[acc] || 0; 
              const b = statusOrder[it.submittalStatus] || 0; 
              return b > a ? it.submittalStatus : acc;
            }, '');

            // Aggregate dates
            const reviewedDate = items.reduce((acc: Date | null, it: any) => {
              if (!it.reviewedDate) { return acc; }
              if (!acc) { return it.reviewedDate; }
              return acc < it.reviewedDate ? it.reviewedDate : acc; // latest reviewed date
            }, null as Date | null);

            // Get submittal info from the first item
            const firstItem = items[0];
            const reviewCategory = firstItem?.reviewCategory || `Submittal ${key}`;

            // Map plan reviews from the items
            const planReviews = items.map((review: any) => {
              return {
                internalCommentsId: review.internalCommentsId,
                reviewCategory: review.TypeName || review.typeName || '',
                reviewer: review.AssignedTo || review.reviewer || '',
                reviewStatus: review.reviewStatus || '',
                reviewedDate: review.itemReviewDate || review.reviewedDate,
                comments: review.comments || '',
                aeResponse: '', // Not available in current structure
                details: (review.corrections || []).map((c: any) => ({
                  internalReviewCommentsId: c.internalReviewCommentsId,
                  sheetNumber: c.sheetNumber,
                  codeRef: c.codeRef,
                  codeDescription: c.codeDescription,
                  reasoning: c.reasoning,
                  nonCompliance: c.nonCompliance,
                  actionableStep: c.actionableStep,
                  aeResponse: c.aeResponse || '',
                  commentResponsedBy: c.commentResponsedBy || ''
                }))
              };
            });

            return {
              commentsId: key, // using submit id as row key
              title: reviewCategory,
              reviewCategory: reviewCategory,
              internalReviewer: firstItem?.reviewer || '',
              cycleNumber: firstItem?.cycleNumber ?? '',
              internalVerificationStatus: submittalStatus || (firstItem?.submittalStatus || ''),
              reviewedDate: reviewedDate,
              completedDate: null,
              planReviews
            };
          }).sort((a, b) => {
            // Sort by reviewed date in descending order (latest first)
            if (!a.reviewedDate && !b.reviewedDate) return 0;
            if (!a.reviewedDate) return 1;
            if (!b.reviewedDate) return -1;
            return b.reviewedDate.getTime() - a.reviewedDate.getTime();
          });

          console.log('Final auditEntries:', this.auditEntries);

          if (this.auditEntries.length > 0) {
            this.selectedAuditIndex = 0;
            this.selectedAuditName = this.auditEntries[0].title;
            this.selectedAuditStatus = this.auditEntries[0].internalVerificationStatus || 'Pending';
          }
        }
        this.cdr.markForCheck();
      },
      error: (err: any) => {
        this.isLoading = false;
        console.error('Error loading internal reviews:', err);
        this.cdr.markForCheck();
      }
    });
  }

  public downloadInternalReviewsPdf(): void {
    // Build rows from auditEntries (Add Review Cycle fields)
    if (!this.auditEntries || this.auditEntries.length === 0) { return; }

    const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });
    const pageWidth = doc.internal.pageSize.getWidth();
    const margin = { left: 36, right: 36, top: 40 };

    // Header
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(10);
    doc.text(`Permit #: ${this.permit?.permitNumber || ''}` , margin.left, margin.top);

    // Single continuous table of cycles using Add Review Cycle fields
    const head = [[
      'REVIEW CYCLE NAME',
      'CYCLE #',
      'REVIEWER',
      'STATUS',
      'REVIEWED DATE',
      'NOTES'
    ]];

    const body = this.auditEntries.map((audit: any) => [
      audit?.title || audit?.reviewCategory || '',
      (audit?.cycleNumber ?? '').toString(),
      audit?.internalReviewer || '',
      audit?.internalVerificationStatus || '',
      audit?.reviewedDate ? this.formatDate(audit.reviewedDate) : '',
      audit?.notes || ''
    ]);

    autoTable(doc, {
      startY: margin.top + 18,
      head,
      body,
      margin: { left: margin.left, right: margin.right },
      styles: { 
        font: 'helvetica', 
        fontSize: 8, 
        cellPadding: 5, 
        overflow: 'linebreak',
        cellWidth: 'wrap',
        valign: 'top'
      },
      headStyles: { fillColor: [33, 150, 243], textColor: 255, halign: 'left', fontSize: 9 },
      columnStyles: {
        0: { cellWidth: 150, overflow: 'linebreak' },
        1: { cellWidth: 50, halign: 'center', overflow: 'linebreak' },
        2: { cellWidth: 80, overflow: 'linebreak' },
        3: { cellWidth: 70, halign: 'center', overflow: 'linebreak' },
        4: { cellWidth: 60, halign: 'center', overflow: 'linebreak' },
        5: { cellWidth: pageWidth - margin.left - margin.right - (150+50+80+70+60), overflow: 'linebreak' }
      },
      didParseCell: (data: any) => {
        if (data.section === 'body' && data.column.index === 3) {
          const value = String(data.cell.raw || '').toLowerCase();
          const isApproved = value === 'approved' || value === 'completed' || value === 'verified' || value === '1 cycle completed' || value === 'cycle completed';
          const bg = isApproved ? [22, 163, 74] : [220, 53, 69];
          const textColor = [255, 255, 255];
          data.cell.styles.fillColor = bg;
          data.cell.styles.textColor = textColor;
          data.cell.styles.fontStyle = 'bold';
          data.cell.styles.halign = 'center';
          data.cell.styles.valign = 'middle';
        }
      },
      theme: 'grid'
    });

    // Footer on each page: line + left date + centered company + right pagination
    const companyName = 'Pacifica Engineering Services';
    const pageCountInternal = doc.getNumberOfPages();
    const pageWidthInternal = doc.internal.pageSize.getWidth();
    const pageHeightInternal = doc.internal.pageSize.getHeight();
    const generatedOn = new Date().toLocaleString();
    for (let i = 1; i <= pageCountInternal; i++) {
      doc.setPage(i);
      doc.setDrawColor(200, 200, 200);
      doc.line(margin.left, pageHeightInternal - 30, pageWidthInternal - margin.right, pageHeightInternal - 30);
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(8);
      doc.setTextColor(100, 100, 100);
      doc.text(`${new Date().toLocaleDateString('en-US')}`, margin.left, pageHeightInternal - 15);
      doc.text(companyName, pageWidthInternal / 2, pageHeightInternal - 15, { align: 'center' });
      doc.text(`Page ${i} of ${pageCountInternal}`, pageWidthInternal - margin.right - 50, pageHeightInternal - 15);
    }

    const fileName = `Internal_Reviews_${this.permit?.permitNumber || ''}_${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(fileName);
  }

  public selectExternalSubmittal(id: any): void {
    this.selectedExternalSubmittalId = id;
    const sel = this.externalSubmittals.find(s => String(s.id) === String(id));
    if (sel) {
      this.selectedAuditName = sel.title;
      this.selectedAuditStatus = sel.submittalStatus;
    }
  }

  public getExternalReviewsForSelectedSubmittal(): any[] {
    if (!this.selectedExternalSubmittalId) { return []; }
    return this.getExternalReviewsForSubmittal(this.selectedExternalSubmittalId);
  }

  public getExternalReviewsForSubmittal(submittalId: any): any[] {
    const reviews = this.externalReviews.filter(r => String(r.submittalId) === String(submittalId));
    
    // Sort by FailureFlag: false reviews first (desc), then true reviews (desc)
    return reviews.sort((a, b) => {
      // If both have the same FailureFlag value, maintain original order (reverse for desc)
      if (a.FailureFlag === b.FailureFlag) {
        return 0;
      }
      
      // False reviews (FailureFlag = false) come first
      if (!a.FailureFlag && b.FailureFlag) {
        return -1;
      }
      
      // True reviews (FailureFlag = true) come after false reviews
      if (a.FailureFlag && !b.FailureFlag) {
        return 1;
      }
      
      return 0;
    }).reverse(); // Reverse to get descending order within each group
  }

  public toggleSubmittalAccordion(index: number): void {
    if (this.expandedSubmittals.has(index)) {
      this.expandedSubmittals.delete(index);
    } else {
      this.expandedSubmittals.add(index);
    }
  }

  public isSubmittalExpanded(index: number): boolean {
    return this.expandedSubmittals.has(index);
  }

  public areAllSubmittalsExpanded(): boolean {
    return this.externalSubmittals && this.externalSubmittals.length > 0 && this.expandedSubmittals.size === this.externalSubmittals.length;
  }

  public toggleAllSubmittals(): void {
    if (!this.externalSubmittals || this.externalSubmittals.length === 0) {
      return;
    }
    if (this.areAllSubmittalsExpanded()) {
      this.expandedSubmittals.clear();
    } else {
      this.expandedSubmittals = new Set(this.externalSubmittals.map((_, idx) => idx));
    }
    this.cdr.markForCheck();
  }

  public areAllInternalReviewsExpanded(): boolean {
    return (
      Array.isArray(this.auditEntries) &&
      this.auditEntries.length > 0 &&
      this.auditEntries.every((entry: any) => this.expandedReviews.has(entry.commentsId))
    );
  }

  public toggleAllInternalReviews(): void {
    if (!this.auditEntries || this.auditEntries.length === 0) {
      return;
    }
    if (this.areAllInternalReviewsExpanded()) {
      // Collapse all internal review rows
      this.auditEntries.forEach((entry: any) => this.expandedReviews.delete(entry.commentsId));
    } else {
      // Expand all internal review rows
      this.auditEntries.forEach((entry: any) => this.expandedReviews.add(entry.commentsId));
    }
    this.cdr.markForCheck();
  }

  // (Reverted) No global expand/collapse for internal reviews

  public toggleReviewAccordion(reviewId: string): void {
    if (this.expandedReviews.has(reviewId)) {
      this.expandedReviews.delete(reviewId);
    } else {
      this.expandedReviews.add(reviewId);
      // Set default tab for this review if not already set
      if (!this.reviewSelectedTabs[reviewId]) {
        this.reviewSelectedTabs[reviewId] = 'corrections';
      }
    }
  }

  public isReviewExpanded(reviewId: string): boolean {
    return this.expandedReviews.has(reviewId);
  }

  public showReviewTab(reviewId: string, tab: string, $event: any): void {
    $event.stopPropagation();
    this.reviewSelectedTabs[reviewId] = tab;
    this.cdr.markForCheck();
  }

  public updateReviewResponse(review: any): void {
    this.isLoading = true;
    const formData = {
      EORAOROwner_Response: review.EORAOROwner_Response,
      commentResponsedBy: review.commentResponsedBy,
      permitId: this.permitId,
      commentsId: review.commentsId,
      loggedInUserId: this.loginUser.userId
    };

    this.permitsService.updateExternalReview(formData).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res?.isFault === false) {
          //alert(res.responseData.message);
          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');
          // Refresh external reviews to get updated data
          this.fetchExternalReviews();
        } else {
          this.customLayoutUtilsService.showError(res.faultMessage||'❌ Error syncing permit', '');
          //alert(res.faultMessage || 'Failed to update review response');
        }
        this.cdr.markForCheck();
      },
      error: (err: any) => {
        this.isLoading = false;
        this.customLayoutUtilsService.showError('❌ Error updating review response', '');
        //alert('Error updating review response');
        console.error(err);
        this.cdr.markForCheck();
      }
    });
  }

  public isSelectedSubmittal(submittalId: any): boolean {
    return String(this.selectedExternalSubmittalId) === String(submittalId);
  }

  public selectAudit(index: number): void {
    this.selectedAuditIndex = index;
    this.selectedAuditName = this.auditEntries[this.selectedAuditIndex].title;
    this.selectedAuditStatus =
      this.auditEntries[this.selectedAuditIndex].submittalStatus;
  }

  public getReviewsForSelectedAudit(): any[] {
    if (
      this.selectedAuditIndex === null ||
      this.selectedAuditIndex >= this.auditEntries.length
    ) {
      return [];
    }

    return this.auditEntries[this.selectedAuditIndex].reviews || [];
  }



  public goBack(): void {
    console.log('goBack called - previousPage:', this.previousPage, 'projectId:', this.projectId);
    if (this.previousPage === 'project' && this.projectId) {
      // Navigate back to the specific project view with permits tab active
      console.log('Navigating to project view with permits tab active');
      this.router.navigate(['/projects/view', this.projectId], { 
        queryParams: { activeTab: 'permits' } 
      });
    } else {
      // Default to permit list
      console.log('Navigating to permit list');
      this.router.navigate(['/permits/list']);
    }
  }

  public goToPortal(): void {
    window.open(
      `${this.permit.cityReviewLink + this.permit.permitEntityID}`,
      '_blank'
    );
  }

  public openReviewDetails(review: any): void {
    const modalRef = this.modalService.open(ReviewDetailsModalComponent, {
      size: 'lg',
    });
    modalRef.componentInstance.review = review;
    modalRef.componentInstance.permitId = this.permitId;
    modalRef.componentInstance.permitDetails = this.permit;

    // Handle modal result
    modalRef.result.then((result) => {
      if (result === 'created' || result === 'updated') {
        // Refresh internal reviews
        this.fetchExternalReviews();
      }
    }).catch((error) => {
      // Modal was dismissed
      console.log('Modal dismissed');
    });
  }

  public getStatusClass(status: string): string {
    if (!status) return 'status-n-a';
    const normalized = status.toLowerCase().replace(/\s+/g, '-').replace(/\//g, '-');
    console.log('Status mapping - Original:', status, 'Normalized:', normalized);
    // Map common synonyms from API to a single class we style
    const aliasMap: { [k: string]: string } = {
      'in-review': 'in-review',
      'requires-re-submit': 'requires-re-submit',
      'approved-w-conditions': 'approved-w-conditions',
      'in-progress': 'in-progress',
      'completed': 'completed',
      'verified': 'verified',
      'pending': 'pending',
      'rejected': 'rejected',
      'approved': 'approved',
      'under-review': 'under-review',
      'requires-resubmit': 'requires-resubmit',
      'pacifica-verification': 'pacifica-verification',
      'dis-approved': 'dis-approved',
      'not-required': 'not-required',
      '1-cycle-completed': '1-cycle-completed',
      '1 cycle completed': '1-cycle-completed',
      'cycle completed': '1-cycle-completed'
    };
    const resolved = aliasMap[normalized] || normalized;
    const finalClass = 'status-' + resolved;
    console.log('Final status class:', finalClass);
    console.log('Available CSS classes for debugging:', [
      'status-pending', 'status-in-progress', 'status-completed', 'status-verified',
      'status-rejected', 'status-approved', 'status-under-review', 'status-requires-resubmit',
      'status-pacifica-verification', 'status-dis-approved', 'status-not-required',
      'status-in-review', 'status-1-cycle-completed'
    ]);
    return finalClass;
  }

  public hasValidPlanReviews(planReviews: any[]): boolean {
    if (!planReviews || planReviews.length === 0) {
      return false;
    }
    // Check if any plan review has a valid internalCommentsId (not null)
    return planReviews.some(plan => plan.internalCommentsId !== null && plan.internalCommentsId !== undefined);
  }

  public hasPdfDataForInternalReview(audit: any, plan: any): boolean {
    if (!audit || !plan) {
      return false;
    }
    
    // Check if there are details available for PDF generation
    return plan?.details && plan.details.length > 0;
  }

  public getStatusStyle(status: string): any {
    if (!status) return {};
    const normalized = status.toLowerCase().replace(/\s+/g, '-').replace(/\//g, '-');
    
    const styleMap: { [k: string]: any } = {
      '1-cycle-completed': { backgroundColor: '#e8f5e8', color: '#2e7d32', border: '1px solid #a5d6a7' },
      '1 cycle completed': { backgroundColor: '#e8f5e8', color: '#2e7d32', border: '1px solid #a5d6a7' },
      'cycle completed': { backgroundColor: '#e8f5e8', color: '#2e7d32', border: '1px solid #a5d6a7' },
      'pacifica-verification': { backgroundColor: '#e1f5fe', color: '#0277bd', border: '1px solid #81d4fa' },
      'dis-approved': { backgroundColor: '#ffebee', color: '#c62828', border: '1px solid #ffcdd2' },
      'not-required': { backgroundColor: '#f5f5f5', color: '#757575', border: '1px solid #e0e0e0' },
      'in-review': { backgroundColor: '#e8eaf6', color: '#3949ab', border: '1px solid #c5cae9' },
      'pending': { backgroundColor: '#fff3e0', color: '#e65100', border: '1px solid #ffcc02' },
      'approved': { backgroundColor: '#e8f5e8', color: '#1b5e20', border: '1px solid #c8e6c9' },
      'completed': { backgroundColor: '#e8f5e8', color: '#1b5e20', border: '1px solid #c8e6c9' },
      'rejected': { backgroundColor: '#ffebee', color: '#c62828', border: '1px solid #ffcdd2' }
    };
    
    return styleMap[normalized] || {};
  }

  showTab(tab: any, $event: any) {
    if (tab === 'internal' && !this.isInternalReviewEnabled()) {
      return;
    }
    this.selectedTab = tab;
    this.cdr.markForCheck();
  }

  public isInternalReviewEnabled(): boolean {
    const type = (this.permitReviewType || this.permit?.permitReviewType || '').toString().toLowerCase();
    // show only when type is 'internal' or 'both'
    return type === 'internal' || type === 'both';
  }

  public isExternalReviewEnabled(): boolean {
    const type = (this.permitReviewType || this.permit?.permitReviewType || '').toString().toLowerCase();
    // show only when type is 'external' or 'both'
    return type === 'external' || type === 'both';
  }

  addPopUp() {
    const NgbModalOptions: {
      size: string;
      backdrop: boolean | 'static';
      keyboard: boolean;
      scrollable: boolean;
    } = {
      size: 'lg', // Large modal size
      backdrop: 'static', // Prevents closing when clicking outside
      keyboard: false, // Disables closing with the Escape key
      scrollable: true, // Allows scrolling inside the modal
    };

    // Open the modal and load the AddEditInternalSubmitsComponent (PT_InternalSubmits fields only)
    const modalRef = this.modalService.open(
      AddEditInternalSubmitsComponent,
      NgbModalOptions
    );

    // Pass data to the modal
    modalRef.componentInstance.permitId = this.permitId;
    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID
    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';

    // Handle modal result
    modalRef.result.then((result) => {
      console.log('Add Review Cycle modal result:', result);
      if (result === 'created' || result === 'updated') {
        // Refresh internal reviews with a small delay to ensure database is updated
        console.log('Refreshing internal reviews after modal close');
        setTimeout(() => {
          this.fetchInternalReviews();
        }, 500);
      }
    }).catch((error) => {
      // Modal was dismissed
      console.log('Modal dismissed:', error);
    });
  }

  editInternalReview(reviewIndex: number) {
    if (reviewIndex < 0 || reviewIndex >= this.auditEntries.length) {
      return;
    }

    const NgbModalOptions: {
      size: string;
      backdrop: boolean | 'static';
      keyboard: boolean;
      scrollable: boolean;
    } = {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      scrollable: true,
    };

    const modalRef = this.modalService.open(
      AddEditInternalReviewComponent,
      NgbModalOptions
    );

    // Pass data to the modal for editing
    modalRef.componentInstance.permitId = this.permitId;
    modalRef.componentInstance.reviewData = this.auditEntries[reviewIndex];
    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID
    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';

    // Handle modal result
    modalRef.result.then((result) => {
      if (result === 'updated') {
        // Refresh internal reviews
        this.fetchInternalReviews();
      }
    }).catch((error) => {
      console.log('Modal dismissed');
    });
  }

  editInternalReviewCycle(reviewIndex: number) {
    if (reviewIndex < 0 || reviewIndex >= this.auditEntries.length) {
      return;
    }

    const audit = this.auditEntries[reviewIndex];

    const NgbModalOptions: {
      size: string;
      backdrop: boolean | 'static';
      keyboard: boolean;
      scrollable: boolean;
    } = {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      scrollable: true,
    };

    const modalRef = this.modalService.open(
      AddEditInternalSubmitsComponent,
      NgbModalOptions
    );

    modalRef.componentInstance.permitId = this.permitId;
    modalRef.componentInstance.loggedInUserId = this.loginUser.userId;
    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';
    modalRef.componentInstance.submitData = {
      submitId: audit?.commentsId,
      reviewCategory: audit?.title || audit?.reviewCategory || '',
      cycleNumber: audit?.cycleNumber ?? '',
      reviewer: audit?.internalReviewer || '',
      submittalStatus: audit?.internalVerificationStatus || '',
      reviewedDate: audit?.reviewedDate || '',
      notes: audit?.notes || ''
    };

    modalRef.result.then((result) => {
      if (result === 'updated') {
        this.fetchInternalReviews();
      }
    }).catch(() => {});
  }

  addOrEditInternalPlanReview(reviewIndex: number) {
    if (reviewIndex < 0 || reviewIndex >= this.auditEntries.length) {
      return;
    }

    const audit = this.auditEntries[reviewIndex];

    const NgbModalOptions: {
      size: string;
      backdrop: boolean | 'static';
      keyboard: boolean;
      scrollable: boolean;
    } = {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      scrollable: true,
    };

    const modalRef = this.modalService.open(
      AddEditInternalReviewPopupComponent,
      NgbModalOptions
    );

    // Inputs for create/edit
    modalRef.componentInstance.permitId = this.permitId;
    modalRef.componentInstance.internalSubmitId = audit.commentsId; // internalSubmitId
    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';
    modalRef.componentInstance.reviewSubmitCategory = audit.reviewCategory || audit.title || '';
    modalRef.componentInstance.loggedInUserId = this.loginUser.userId;

    // If there's a linked internalCommentsId on the underlying data, pass it as reviewData for edit
    // Find matching source in internalReviews if available
    // If user clicked from submit row, this opens create mode by default; editing is done from review-level UI

    modalRef.result.then((result) => {
      if (result === 'created' || result === 'updated') {
        this.fetchInternalReviews();
      }
    }).catch(() => {});
  }

  editInternalPlanReview(submitAudit: any, planReview: any) {
    const NgbModalOptions: {
      size: string;
      backdrop: boolean | 'static';
      keyboard: boolean;
      scrollable: boolean;
    } = {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      scrollable: true,
    };

    const modalRef = this.modalService.open(
      AddEditInternalReviewPopupComponent,
      NgbModalOptions
    );

    modalRef.componentInstance.permitId = this.permitId;
    modalRef.componentInstance.internalSubmitId = submitAudit.commentsId;
    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';
    modalRef.componentInstance.reviewSubmitCategory = submitAudit.reviewCategory || submitAudit.title || '';
    modalRef.componentInstance.loggedInUserId = this.loginUser.userId;
    modalRef.componentInstance.reviewData = {
      internalCommentsId: planReview.internalCommentsId,
      reviewCategory: planReview.reviewCategory,
      reviewer: planReview.reviewer,
      reviewStatus: planReview.reviewStatus,
      reviewedDate: planReview.reviewedDate,
      comments: planReview.comments
    };

    modalRef.result.then((result) => {
      if (result === 'created' || result === 'updated') {
        this.fetchInternalReviews();
      }
    }).catch(() => {});
  }

  addOrEditInternalPlanReviewDetail(submitAudit: any, planReview: any, detail?: any) {
    const NgbModalOptions: {
      size: string;
      backdrop: boolean | 'static';
      keyboard: boolean;
      scrollable: boolean;
    } = {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      scrollable: true,
    };

    const modalRef = this.modalService.open(
      AddEditInternalReviewDetailComponent,
      NgbModalOptions
    );

    modalRef.componentInstance.permitId = this.permitId;
    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';
    modalRef.componentInstance.reviewCategory = planReview.reviewCategory || submitAudit.reviewCategory || '';
    modalRef.componentInstance.internalCommentsId = planReview.internalCommentsId || null;
    modalRef.componentInstance.loggedInUserId = this.loginUser.userId;
    if (detail) {
      modalRef.componentInstance.detailData = detail;
    }

    modalRef.result.then((result) => {
      if (result === 'created' || result === 'updated') {
        this.fetchInternalReviews();
      }
    }).catch(() => {});
  }

  respondToInternalDetail(submitAudit: any, planReview: any, detail: any) {
    if (!submitAudit || !planReview || !detail) { return; }

    const modalRef = this.modalService.open(InternalResponseModalComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      scrollable: true
    });

    modalRef.componentInstance.permitId = this.permitId;
    modalRef.componentInstance.submitAudit = submitAudit;
    modalRef.componentInstance.planReview = planReview;
    modalRef.componentInstance.detail = detail;
    modalRef.componentInstance.loggedInUserId = this.loginUser.userId;

    modalRef.result.then((result: any) => {
      if (result === 'updated') {
        this.fetchInternalReviews();
      }
    }).catch(() => {});
  }

  editPopUp() {
    const NgbModalOptions: {
      size: string;
      backdrop: boolean | 'static';
      keyboard: boolean;
      scrollable: boolean;
    } = {
      size: 'lg', // Large modal size
      backdrop: 'static', // Prevents closing when clicking outside
      keyboard: false, // Disables closing with the Escape key
      scrollable: true, // Allows scrolling inside the modal
    };

    // Open the modal and load the ProjectPopup
    const modalRef = this.modalService.open(
      AddEditInternalReviewComponent,
      NgbModalOptions
    );
  }
  editPermit() {
    const NgbModalOptions: {
      size: string;
      backdrop: boolean | 'static';
      keyboard: boolean;
      scrollable: boolean;
    } = {
      size: 'lg', // Large modal size
      backdrop: 'static', // Prevents closing when clicking outside
      keyboard: false, // Disables closing with the Escape key
      scrollable: true, // Allows scrolling inside the modal
    };

    // Open the modal and load the ProjectPopup
    const modalRef = this.modalService.open(
      PermitPopupComponent,
      NgbModalOptions
    );
    // Pass the selected ID to the modal component (0 for new, existing ID for edit)
    modalRef.componentInstance.id = this.permitId;
    modalRef.componentInstance.isHideInternalReviewStatus = false;
    
    // Listen for passEntry event to refresh permit details when permit is saved
    modalRef.componentInstance.passEntry.subscribe((saved: boolean) => {
      if (saved) {
        this.fetchPermitDetails();
      }
    });
  }

  syncPermits(i: any) {
    this.isLoading = true;
    this.singlePermit = i || false;
    this.permitsService.syncPermits({ permitId: this.permitId, singlePermit: this.singlePermit, autoLogin: true }).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        console.log('Sync response:', res);
        console.log('Response type:', typeof res);
        console.log('Response keys:', Object.keys(res || {}));
        console.log('Response success:', res?.success);
        console.log('Response message:', res?.message);
        console.log('Response responseData:', res?.responseData);
        
        // Handle various response structures
        let responseData = res;
        
        // Check different possible response structures
        if (res?.responseData) {
          responseData = res.responseData;
        } else if (res?.body?.responseData) {
          responseData = res.body.responseData;
        } else if (res?.body) {
          responseData = res.body;
        }
        
        console.log('Final responseData:', responseData);
        console.log('Final success:', responseData?.success);
        console.log('Final message:', responseData?.message);
        
        if (responseData?.isFault) {
          //alert(responseData.faultMessage || 'Failed to sync permit');
          this.customLayoutUtilsService.showError(responseData.faultMessage, '');
        } else if (responseData?.success === false) {
          // Handle specific error messages from the API
          if (responseData.message === 'Permit not found in Energov system') {
                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');
          } else if (responseData.message === 'No permits found for any keywords') {
                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');
          } else {
                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

            //alert(`❌ ${responseData.message || 'Failed to sync permit'}`);
          }
        } else if (responseData?.success === true || responseData?.data) {
          this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');
          //alert('✅ Permit synced successfully');
          this.fetchPermitDetails();
          this.fetchExternalReviews();
        } else {
          // Fallback for unknown response structure
          console.log('Unknown response structure, showing generic success');
          //alert('✅ Permit synced successfully');
          this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');

          this.fetchPermitDetails();
          this.fetchExternalReviews();
        }
        this.cdr.markForCheck();
      },
      error: (err: any) => {
        this.isLoading = false;
        // Handle HTTP error responses
        console.log('Error response:', err);
        console.log('Error type:', typeof err);
        console.log('Error keys:', Object.keys(err || {}));
        console.log('Error status:', err?.status);
        console.log('Error message:', err?.message);
        console.log('Error error:', err?.error);
        
        // The interceptor passes err.error to the error handler
        // So err might actually be the response data
        if (err?.success === false) {
          // Handle specific error messages from the API
          if (err.message === 'Permit not found in Energov system') {
                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');
          } else if (err.message === 'No permits found for any keywords') {
                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');
          } else {
                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

            //alert(`❌ ${err.message || 'Failed to sync permit'}`);
          }
        } else if (err?.error?.message) {
          if (err.error.message === 'Permit not found in Energov system') {
                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');
          } else if (err.error.message === 'No permits found for any keywords') {
                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');
          } else {
                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

            //alert(`❌ ${err.error.message}`);
          }
        } else if (err?.status === 404) {
                                  this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');

          // Handle 404 specifically for permit not found
          //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');
        } else {
          //alert('❌ Error syncing permit');
        }
        console.error(err);
        this.cdr.markForCheck();
      }
    });
  }

  editExternalReview(review:any) {
    const NgbModalOptions: {
      size: string;
      backdrop: boolean | 'static';
      keyboard: boolean;
      scrollable: boolean;
    } = {
      size: 'lg', // Large modal size
      backdrop: 'static', // Prevents closing when clicking outside
      keyboard: false, // Disables closing with the Escape key
      scrollable: true, // Allows scrolling inside the modal
    };

    // Open the modal and load the AddEditInternalReviewComponent
    const modalRef = this.modalService.open(
      EditExternalReviewComponent,
      NgbModalOptions
    );

    console.log('reviewData ', review)
    // Pass data to the modal
    modalRef.componentInstance.permitId = this.permitId;
    modalRef.componentInstance.reviewData = review;
    modalRef.componentInstance.permitDetails = this.permit;
    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID

    // Handle modal result
    modalRef.result.then((result) => {
      if (result === 'created' || result === 'updated') {
        // Refresh internal reviews
        this.fetchExternalReviews();
      }
    }).catch((error) => {
      // Modal was dismissed
      console.log('Modal dismissed');
    });
  }

  // (Reverted) Removed external submittal add/edit inline action handlers

  public openResponseModal(correction: any, review: any): void {
    // Open the modal using NgbModal
    const modalRef = this.modalService.open(ResponseModalComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false
    });

    // Pass data to the modal
    modalRef.componentInstance.correction = correction;
    modalRef.componentInstance.review = review;
    modalRef.componentInstance.permitId = this.permitId;
    modalRef.componentInstance.loggedInUserId = this.loginUser.userId;
    modalRef.componentInstance.isAdmin = this.isAdmin;

    // Handle modal result
    modalRef.componentInstance.responseSubmitted.subscribe((formData: any) => {
      this.submitResponse(formData, modalRef);
    });

    // Handle response completion to reset loading state
    modalRef.componentInstance.responseCompleted.subscribe((success: boolean) => {
      if (!success) {
        // Reset loading state if submission failed
        modalRef.componentInstance.isLoading = false;
      }
    });

    modalRef.result.then(() => {
      // Modal was closed
    }).catch(() => {
      // Modal was dismissed
    });
  }

  public submitResponse(formData: any, modalRef?: any): void {
    if (!formData) {
      return;
    }

    this.isLoading = true;

    this.permitsService.updateExternalReview(formData).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res?.isFault === false) {
                                  this.customLayoutUtilsService.showSuccess(res.responseData.message, '');

          //alert(res.responseData.message || 'Response submitted successfully');
          this.fetchExternalReviews(); // Refresh external reviews to get updated data
          
          // Emit success completion event
          if (modalRef && modalRef.componentInstance) {
            modalRef.componentInstance.responseCompleted.emit(true);
          }
          
          // Close the modal if it was passed
          if (modalRef) {
            modalRef.close();
          }
        } else {
          //alert(res.faultMessage || 'Failed to submit response');
                                  this.customLayoutUtilsService.showError(res.faultMessage||'Failed to submit response', '');

          // Emit failure completion event
          if (modalRef && modalRef.componentInstance) {
            modalRef.componentInstance.responseCompleted.emit(false);
          }
        }
        this.cdr.markForCheck();
      },
      error: (err: any) => {
        this.isLoading = false;
        //alert('Error submitting response');
        console.error(err);
                                this.customLayoutUtilsService.showSuccess('Error submitting response', '');

        // Emit failure completion event
        if (modalRef && modalRef.componentInstance) {
          modalRef.componentInstance.responseCompleted.emit(false);
        }
        
        this.cdr.markForCheck();
      }
    });
  }

  public downloadReviewPDF(review: any): void {
    if (!review) {
      return;
    }

    try {
      const permitNumber = this.permit?.permitNumber || '';
      const reviewer = (review?.AssignedTo || review?.municipalityReviewer || review?.reviewer || '').toString();
      const cityComments = (review?.Comments || (review?.cityComments ?? '')).toString();
      const ownerResponse = (review?.EORAOROwner_Response ?? '').toString();
      
      // Calculate submittal count for this review
      const submittalCount = this.externalSubmittals ? this.externalSubmittals.length : 0;
      const cycle = submittalCount > 0 ? submittalCount.toString() : (review?.cycle || review?.Cycle || '').toString();
      
      const status = (review?.StatusName || review?.commentstatus || review?.status || '').toString();
      const displayDate = (review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate)
        ? this.formatDate(review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate)
        : this.formatDate(new Date().toISOString());

      const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });

      const pageWidth = doc.internal.pageSize.getWidth();
      const pageHeight = doc.internal.pageSize.getHeight();
      const margin = { left: 40, right: 40, top: 40, bottom: 40 };

      // Header: Permit #
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(10);
      doc.text(`Permit #: ${permitNumber || ''}`, margin.left, margin.top);

      const startY = margin.top + 20;

      // Table with one row matching the screenshot
      const headers = [
        'REVIEWED BY',
        'CITY COMMENTS',
        'EOR/AOR/OWNER COMMENT RESPONSE',
        'CYCLE',
        'STATUS',
        'DATE'
      ];

      // Build body rows. If there are corrections, show one row per correction; otherwise single row
      const rawCorrections: any = (review && (review as any).Corrections) ?? (review as any).corrections ?? [];
      const correctionsArray = Array.isArray(rawCorrections) ? rawCorrections : (rawCorrections ? [rawCorrections] : []);
      const bodyRows: any[] = correctionsArray.length > 0
        ? correctionsArray.map((c: any) => [
            reviewer || '',
            c?.Comments || cityComments || '',
            c?.Response || c?.EORAOROwner_Response || ownerResponse || '',
            cycle || '',
            status || '',
            (c?.ResolvedDate ? this.formatDate(c.ResolvedDate) : displayDate) || ''
          ])
        : [[
            reviewer || '',
            cityComments || '',
            ownerResponse || '',
            cycle || '',
            status || '',
            displayDate || ''
          ]];

      autoTable(doc, {
        startY,
        head: [headers],
        body: bodyRows,
        margin: { left: margin.left, right: margin.right },
        styles: { 
          font: 'helvetica', 
          fontSize: 8, 
          cellPadding: 5, 
          overflow: 'linebreak',
          cellWidth: 'wrap'
        },
        headStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold', fontSize: 8 },
        columnStyles: {
          0: { cellWidth: 90, overflow: 'linebreak' },
          1: { cellWidth: (pageWidth - margin.left - margin.right) * 0.26, overflow: 'linebreak' },
          2: { cellWidth: (pageWidth - margin.left - margin.right) * 0.24, overflow: 'linebreak' },
          3: { cellWidth: 45, halign: 'center', overflow: 'linebreak' },
          4: { cellWidth: 60, halign: 'center', overflow: 'linebreak' },
          5: { cellWidth: 60, halign: 'center', overflow: 'linebreak' }
        },
        didParseCell: (data: any) => {
          // City comments text in red
          if (data.section === 'body' && data.column.index === 1) {
            data.cell.styles.textColor = [192, 0, 0];
          }
          
          // Set background color and text color for status column
          if (data.section === 'body' && data.column.index === 4) {
            const value = String(data.cell.raw || '');
            const isApproved = value.toLowerCase() === 'approved';
            const bg = isApproved ? [22, 163, 74] : [220, 53, 69];
            const textColor = [255, 255, 255];
            
            data.cell.styles.fillColor = bg;
            data.cell.styles.textColor = textColor;
            data.cell.styles.fontStyle = 'bold';
            data.cell.styles.halign = 'center';
            data.cell.styles.valign = 'middle';
          }
        },
        theme: 'grid'
      });

      // Footer (existing info) + company name
      const pageCount = doc.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setDrawColor(200, 200, 200);
        doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(8);
        doc.setTextColor(100, 100, 100);
        doc.text(`${new Date().toLocaleDateString('en-US')}`, margin.left, pageHeight - 15);
        doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);
        // Company name centered
        doc.text('Pacifica Engineering Services', pageWidth / 2, pageHeight - 15, { align: 'center' });
      }

      const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;
      doc.save(fileName);
      
    } catch (error) {
      console.error('Error generating PDF:', error);
                              this.customLayoutUtilsService.showSuccess('Error generation PDF. Please try agaiin', '');

      //alert('Error generating PDF. Please try again.');
    }
  }

  public downloadExternalReviewsPdf(): void {
    // Generate a PDF for ALL external submittals' reviews (each submittal listed in external-submittal-row)
    if (!this.externalSubmittals || this.externalSubmittals.length === 0) { return; }

    try {
      const permitNumber = this.permit?.permitNumber || '';
      const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });

      const pageWidth = doc.internal.pageSize.getWidth();
      const margin = { left: 36, right: 36, top: 40 };

      // Header
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(10);
      doc.text(`Permit #: ${permitNumber}`, margin.left, margin.top);

      const headers = [
        'DISCIPLINE',
        'REVIEWER',
        'STATUS',
        'VERSION',
        'CYCLE DATE',
        'CITY ACTION\nNEEDED',
        'AEC ACTION\nNEEDED'
      ];

      // Build a single continuous body across all submittals
      const combinedBody: any[] = [];
      for (const sub of this.externalSubmittals) {
        const reviews = this.getExternalReviewsForSubmittal(sub.id);
        if (!reviews || reviews.length === 0) { continue; }
        for (const r of reviews) {
          combinedBody.push([
            r?.name || '',
            (r?.AssignedTo || r?.municipalityReviewer || r?.reviewer || '').toString(),
            (r?.StatusName || r?.commentstatus || r?.status || '').toString(),
            (this.externalSubmittals?.length || r?.cycle || r?.Cycle || '').toString(),
            (r?.CompletedDate || r?.completedDate || r?.DueDate || r?.createdDate)
              ? this.formatDate(r?.CompletedDate || r?.completedDate || r?.DueDate || r?.createdDate)
              : '',
            (r?.cityActionNeeded ? 'YES' : 'NO'),
            (r?.aocActionNeeded ? 'YES' : (r?.EORAOROwner_Response ? 'NO' : 'YES'))
          ]);
        }
      }

      if (combinedBody.length === 0) { return; }

      autoTable(doc, {
        startY: margin.top + 18,
        head: [headers],
        body: combinedBody,
        margin: { left: margin.left, right: margin.right },
        styles: { 
          font: 'helvetica', 
          fontSize: 8, 
          cellPadding: 5, 
          overflow: 'linebreak', 
          valign: 'top',
          cellWidth: 'wrap'
        },
        headStyles: { fillColor: [33, 150, 243], textColor: 255, halign: 'center', fontSize: 7, cellPadding: 3 },
        columnStyles: {
          0: { cellWidth: 90, overflow: 'linebreak' },
          1: { cellWidth: 103, overflow: 'linebreak' },
          2: { cellWidth: 65, halign: 'center', overflow: 'linebreak' },
          3: { cellWidth: 50, halign: 'center', overflow: 'ellipsize' },
          4: { cellWidth: 65, halign: 'center', overflow: 'linebreak' },
          5: { cellWidth: 60, halign: 'center', overflow: 'linebreak' },
          6: { cellWidth: 65, halign: 'center', overflow: 'linebreak' }
        },
        didParseCell: (data: any) => {
          if (data.section === 'body' && data.column.index === 2) {
            const value = String(data.cell.raw || '');
            const isApproved = value.toLowerCase() === 'approved';
            const bg = isApproved ? [22, 163, 74] : [220, 53, 69];
            const textColor = [255, 255, 255];
            data.cell.styles.fillColor = bg;
            data.cell.styles.textColor = textColor;
            data.cell.styles.fontStyle = 'bold';
            data.cell.styles.halign = 'center';
            data.cell.styles.valign = 'middle';
          }
        },
        theme: 'grid'
      });

      // Footer on each page: line + left date + centered company + right pagination
      const companyName = 'Pacifica Engineering Services';
      const pageCount = doc.getNumberOfPages();
      const pageWidthFooter = doc.internal.pageSize.getWidth();
      const pageHeightFooter = doc.internal.pageSize.getHeight();
      const generatedOnFooter = new Date().toLocaleString();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setDrawColor(200, 200, 200);
        doc.line(margin.left, pageHeightFooter - 30, pageWidthFooter - margin.right, pageHeightFooter - 30);
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(8);
        doc.setTextColor(100, 100, 100);
        doc.text(`${new Date().toLocaleDateString('en-US')}`, margin.left, pageHeightFooter - 15);
        doc.text(companyName, pageWidthFooter / 2, pageHeightFooter - 15, { align: 'center' });
        doc.text(`Page ${i} of ${pageCount}`, pageWidthFooter - margin.right - 50, pageHeightFooter - 15);
      }

      const fileName = `External_Reviews_${permitNumber || ''}_${new Date().toISOString().split('T')[0]}.pdf`;
      doc.save(fileName);
    } catch (e) {
      console.error('Error generating external reviews PDF', e);
    }
  }

  public downloadReviewSingleLinePDF(audit: any, plan: any): void {
    if (!audit || !plan) {
      return;
    }

    try {
      const permitNumber = this.permit?.permitNumber || '';
      const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });

      const pageWidth = doc.internal.pageSize.getWidth();
      const pageHeight = doc.internal.pageSize.getHeight();
      const margin = { left: 40, right: 40, top: 40, bottom: 40 };

      // Header: Permit #
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(10);
      doc.text(`Permit #: ${permitNumber || ''}`, margin.left, margin.top);

      const startY = margin.top + 20;

      // Add details section if there are any details
      if (plan?.details && plan.details.length > 0) {
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(10);
        doc.text('Review Details:', margin.left, startY);

        const detailsHeaders = [
          'SHEET #',
          'CODE REF',
          'REASONING',
          'NON-COMPLIANCE',
          'ACTIONABLE STEP',
          'A/E RESPONSE',
          'RESPONDED BY'
        ];

        const detailsBody = plan.details.map((detail: any) => [
          detail?.sheetNumber || '',
          detail?.codeRef || '',
          detail?.reasoning || '',
          detail?.nonCompliance || '',
          detail?.actionableStep || '',
          detail?.aeResponse || '',
          detail?.commentResponsedBy || ''
        ]);

        autoTable(doc, {
          startY: startY + 15,
          head: [detailsHeaders],
          body: detailsBody,
          margin: { left: margin.left, right: margin.right },
          styles: { 
            font: 'helvetica', 
            fontSize: 7, 
            cellPadding: 3, 
            overflow: 'linebreak',
            cellWidth: 'wrap'
          },
          headStyles: { fillColor: [33, 150, 243], textColor: 255, fontStyle: 'bold', fontSize: 7 },
          columnStyles: {
            0: { cellWidth: 50, halign: 'center', overflow: 'linebreak' },
            1: { cellWidth: 60, overflow: 'linebreak' },
            2: { cellWidth: (pageWidth - margin.left - margin.right) * 0.18, overflow: 'linebreak' },
            3: { cellWidth: (pageWidth - margin.left - margin.right) * 0.18, overflow: 'linebreak' },
            4: { cellWidth: (pageWidth - margin.left - margin.right) * 0.18, overflow: 'linebreak' },
            5: { cellWidth: (pageWidth - margin.left - margin.right) * 0.18, overflow: 'linebreak' },
            6: { cellWidth: 70, overflow: 'linebreak' }
          },
          theme: 'grid'
        });
      } else {
        // If no details, show a message
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(10);
        doc.text('No review details available for this review.', margin.left, startY);
      }

      // Footer on each page: line + left date + centered company + right pagination
      const pageCount = doc.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setDrawColor(200, 200, 200);
        doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(8);
        doc.setTextColor(100, 100, 100);
        doc.text(`${new Date().toLocaleDateString('en-US')}`, margin.left, pageHeight - 15);
        doc.text('Pacifica Engineering Services', pageWidth / 2, pageHeight - 15, { align: 'center' });
        doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);
      }

      const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${plan?.reviewCategory || 'Review'}_${new Date().toISOString().split('T')[0]}.pdf`;
      doc.save(fileName);
      
    } catch (error) {
      console.error('Error generating review single line PDF:', error);
      this.customLayoutUtilsService.showError('Error generating PDF. Please try again.', '');
    }
  }

  public checkIfAdmin(): boolean {
    // Check if the user is an admin based on roleId
    // Assuming roleId 1 is admin - adjust this based on your role system
    return this.loginUser && this.loginUser.roleId === 1;
  }

  public shouldShowEditResponseButton(correction: any): boolean {
    // Show edit response button if:
    // 1. User is admin (can always edit)
    // 2. User is not admin but lockResponse is false (unlocked by admin)
    if (this.isAdmin) {
      return true;
    }
    
    // For non-admin users, only show if lockResponse is explicitly false
    return correction.lockResponse === false;
  }
  onEdit(template: any){
 const NgbModalOptions: {
      size: string;
      backdrop: boolean | 'static';
      keyboard: boolean;
      scrollable: boolean;
    } = {
      size: 'lg', // Large modal size
      backdrop: 'static', // Prevents closing when clicking outside
      keyboard: false, // Disables closing with the Escape key
      scrollable: true, // Allows scrolling inside the modal
    };
    this.modalService.open(template,NgbModalOptions)

    // console.log("this.permit", this.permit)
    this.notesForm.patchValue({
      actionTaken:this.permit.actionTaken,
      attentionReason:this.permit.attentionReason,
      internalNotes:this.permit.internalNotes,
      internalReviewStatus:this.permit.internalReviewStatus,
      // actionTaken:'helo',
    })

  }

  closModal(){
    this.modalService.dismissAll()
  }

  
  public editNotesandactions(): void {
    this.isLoading = true;
    const formData = {
     permitId: this.permitId,
      actionTaken: this.notesForm.value.actionTaken,
      internalNotes: this.notesForm.value.internalNotes,
      attentionReason: this.notesForm.value.attentionReason,
      internalReviewStatus: this.notesForm.value.internalReviewStatus,
      
    };

    this.permitsService.editNotesAndActions(formData).subscribe({
      next: (res: any) => {
        this.closModal()
        this.isLoading = false;
        if (res?.isFault === false) {
          //alert(res.responseData.message);
          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');
          // Refresh permit details to get updated data from server
          this.fetchPermitDetails();
        } else {
          this.customLayoutUtilsService.showError(res.faultMessage||'❌ Error in update notes and actions', '');
          //alert(res.faultMessage || 'Failed to update review response');
        }
        this.cdr.markForCheck();
      },
      error: (err: any) => {
        this.isLoading = false;
        this.customLayoutUtilsService.showError('❌ Error in update notes and actions', '');
        //alert('Error updating review response');
        console.error(err);
        this.cdr.markForCheck();
      }
    });
  }

  public formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const year = date.getFullYear();
    return `${month}/${day}/${year}`;
  }
}
