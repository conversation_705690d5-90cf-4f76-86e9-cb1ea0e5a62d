<!-- Full Screen Loading Overlay -->
<div *ngIf="loading || isLoading" class="fullscreen-loading-overlay">
  <div class="loading-content">
    <div class="custom-colored-spinner" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-4 fs-5">Loading...</div>
  </div>
</div>

<div class="grid-container">
  <kendo-grid
    #normalGrid
    [data]="gridData"
    [pageSize]="page.size"
    [sort]="sort"
    [pageable]="{
      pageSizes: [15, 20, 50, 100],
      previousNext: true,
      info: true,
      type: 'numeric',
      buttonCount: 5
    }"
    [total]="page.totalElements"
    [sortable]="{ allowUnsort: true, mode: 'single' }"
    [groupable]="false"
    [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
    (selectionChange)="onSelectionChange($event)"
    [reorderable]="true"
    style="width: auto; overflow-x: auto"
    [resizable]="false"
    [height]="720"
    [skip]="skip"
    [filter]="filter"
    [columnMenu]="{ filter: true }"
    (filterChange)="filterChange($event)"
    (pageChange)="pageChange($event)"
    (sortChange)="onSortChange($event)"
    (columnVisibilityChange)="updateColumnVisibility($event)"
  >
    <ng-template kendoGridToolbarTemplate>
      <!-- Search Section -->
      <div class="d-flex align-items-center me-3 search-section">
        <kendo-textbox
          [style.width.px]="500"
          placeholder="Search..."
          [(ngModel)]="searchData"
          [clearButton]="true"
          (keydown)="onSearchKeyDown($event)"
          (blur)="onSearchBlur()"
          (valueChange)="onSearchValueChange()"
        ></kendo-textbox>
      </div>

      <kendo-grid-spacer></kendo-grid-spacer>

      <!-- Total Count - Repositioned to the right -->
      <div class="d-flex align-items-center me-3">
        <span class="text-muted">Total: </span>
        <span class="fw-bold ms-1">{{ page.totalElements || 0 }}</span>
      </div>

      <!-- Action Buttons -->
      <button type="button" class="btn btn-success btn-sm toolbar-btn" (click)="add()" title="Add Project">
        <i class="fas fa-plus text-white"></i>Add
      </button>

      <button
      type="button"
      class="btn btn-icon btn-sm toolbar-btn"
      (click)="toggleExpand()"
      title="Toggle Grid Expansion"
    >
      <i
        class="fas text-secondary"
        [class.fa-expand]="!isExpanded"
        [class.fa-compress]="isExpanded"
      ></i>
    </button>

     <!-- Excel Export Dropdown -->
     <div class="custom-dropdown toolbar-btn" [class.show]="isExcelDropdownOpen" #excelDropdown>
       <button class="btn btn-icon btn-sm" type="button" (click)="toggleExcelDropdown($event)" title="Export Excel" #excelButton>
         <i class="fas fa-file-excel text-success"></i>
       </button>
       <div class="custom-dropdown-menu" *ngIf="isExcelDropdownOpen" [style.top.px]="dropdownTop" [style.left.px]="dropdownLeft">
         <a class="custom-dropdown-item" href="#" (click)="onExportClick({value: 'all'}); closeExcelDropdown(); $event.preventDefault()">All</a>
         <a class="custom-dropdown-item" href="#" (click)="onExportClick({value: 'selected'}); closeExcelDropdown(); $event.preventDefault()">Page Results</a>
       </div>
     </div>


      <!-- Save Column Settings Button -->
      <!-- <button
        type="button"
        class="btn btn-icon btn-sm me-2"
        (click)="saveHead()"
        title="Save Column Settings"
      >
        <i class="fas fa-save text-success"></i>
      </button> -->

      <!-- Reset Button -->
      <button
        type="button"
        class="btn btn-icon btn-sm toolbar-btn"
        (click)="resetTable()"
        title="Reset to Default"
      >
        <i class="fas fa-undo text-warning"></i>
      </button>

      <!-- Refresh Button -->
      <button
        type="button"
        class="btn btn-icon btn-sm toolbar-btn"
        (click)="refreshGrid()"
        title="Refresh Grid Data"
      >
        <i class="fas fa-sync-alt text-info"></i>
      </button>
    </ng-template>

    <!-- Advanced Filters Panel -->
    <ng-template kendoGridToolbarTemplate>
      <div
        *ngIf="showAdvancedFilters"
        class="advanced-filters-panel p-3 bg-light border-bottom"
      >
        <div class="row">
          <div class="col-md-3">
            <label class="form-label">Status</label>

            <kendo-dropdownlist
              [data]="advancedFilterOptions.status"
              [(ngModel)]="appliedFilters.status"
              textField="text"
              valueField="value"
              placeholder="Select Status"
            >
            </kendo-dropdownlist>
          </div>
          <div class="col-md-3 d-flex align-items-end">
            <button
              kendoButton
              (click)="applyAdvancedFilters()"
              class="btn-primary me-2"
            >
              <i class="fas fa-check"></i> Apply Filters
            </button>
            <button
              kendoButton
              (click)="clearAllFilters()"
              class="btn-secondary"
            >
              <i class="fas fa-times"></i> Clear
            </button>
          </div>
        </div>
      </div>
    </ng-template>
    <ng-container *ngFor="let column of gridColumns">
      <!-- Action Column -->
      <kendo-grid-column
        *ngIf="column === 'action'"
        title="Actions"
        [width]="90"
        [includeInChooser]="false"
        [columnMenu]="false"
        [hidden]="getHiddenField('action')"
      >
        <ng-template kendoGridCellTemplate let-dataItem="dataItem">
          <div class="d-flex align-items-center justify-content-center gap-1" style="min-height: 32px;">
            <!-- <a
              title="View"
              class="btn btn-icon btn-sm"
              (click)="view(dataItem.patientId)"
            >
              <span
                [inlineSVG]="'./assets/media/icons/duotune/general/gen019.svg'"
                class="svg-icon svg-icon-3 svg-icon-primary"
              >
              </span>
            </a> -->
            <!-- View icon - always on the left -->
            <a
              title="View"
              class="btn btn-icon btn-sm d-flex align-items-center justify-content-center"
              style="width: 32px; height: 32px;"
              (click)="edit(dataItem.projectId)"
            >
              <i class="fas fa-edit text-primary"></i>
            </a>
            <!-- Delete icon - always on the right, with consistent spacing -->
            <a *ngIf="loginUser.roleName ==='Admin'" title="Delete"
              class="btn btn-icon  btn-sm d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;"
              (click)="deletePop(deleteModal, dataItem.projectId, dataItem.projectName,dataItem)">
               <i class="fas fa-trash text-danger"></i>
              <!-- [class.invisible]="!dataItem.isDeletable"
                          [class.disabled]="!dataItem.isDeletable"

                          (click)="dataItem.isDeletable && deletePop(deleteModal, dataItem.projectId, dataItem.projectName)" -->
              <!-- <span [inlineSVG]="'./assets/media/icons/duotune/general/gen027.svg'" class="svg-icon svg-icon-3 svg-icon-danger">
              </span> -->
            </a>

            <!-- Delete icon - always on the right, with consistent spacing -->
            <a *ngIf="loginUser.roleName ==='Internal PM'" title="Delete"
              class="btn btn-icon  btn-sm d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;"
              [class.invisible]="!dataItem.isDeletable && loginUser.userId === dataItem.createdBy" [class.disabled]="!dataItem.isDeletable && loginUser.userId === dataItem.createdBy"
              (click)="deletePop(deleteModal, dataItem.projectId, dataItem.projectName,dataItem)">
                <i class="fas fa-trash text-danger"></i>
              <!-- <span [inlineSVG]="'./assets/media/icons/duotune/general/gen027.svg'" class="svg-icon svg-icon-3 svg-icon-danger">
              </span> -->
            </a>
          </div>

          <!-- <div class="d-flex gap-1">
          <button class="btn btn-sm btn-light-primary" (click)="view(dataItem.patientId)" title="View">
            <i class="fas fa-eye"></i>
          </button>
          <button class="btn btn-sm btn-light-warning" (click)="edit(dataItem.patientId)" title="Edit">
            <i class="fas fa-edit"></i>
          </button>
          <button class="btn btn-sm btn-light-danger" (click)="delete(dataItem.patientId)" title="Delete">
            <i class="fas fa-trash"></i>
          </button>
        </div> -->
        </ng-template>
      </kendo-grid-column>
      <!-- Project Name Column -->
      <kendo-grid-column
        *ngIf="column === 'projectName'"
        field="projectName"
        title="Project Name"
        [width]="200"
        [includeInChooser]="false"
        [hidden]="getHiddenField('projectName')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
           <span class="fw-bold">{{ dataItem.projectName | truncateText:45 }}</span>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>
      <!-- Internal Project Number Column -->
      <kendo-grid-column
        *ngIf="column === 'internalProjectNumber'"
        field="internalProjectNumber"
        title="Internal Project #"
        [width]="150"
        [includeInChooser]="false"
        [hidden]="getHiddenField('internalProjectNumber')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
           <span class="fw-bold">{{ dataItem.internalProjectNumber | truncateText:45 }}</span>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Project Start Date Column -->
      <kendo-grid-column
        *ngIf="column === 'projectStartDate'"
        field="projectStartDate"
        title="Start Date"
        [width]="120"

        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('projectStartDate')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ formatDate(dataItem.projectStartDate) }}
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-date-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="true"
          >
            <kendo-filter-gte-operator></kendo-filter-gte-operator>
            <kendo-filter-lte-operator></kendo-filter-lte-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
          </kendo-grid-date-filter-menu>
        </ng-template>
      </kendo-grid-column>
      <!-- Project End Date Column -->
      <kendo-grid-column
        *ngIf="column === 'projectEndDate'"
        field="projectEndDate"
        title="End Date"
        [width]="120"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('projectEndDate')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
         {{ formatDate(dataItem.projectEndDate) }}
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-date-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="true"
          >
            <kendo-filter-gte-operator></kendo-filter-gte-operator>
            <kendo-filter-lte-operator></kendo-filter-lte-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
          </kendo-grid-date-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Project Location Column -->
      <kendo-grid-column
        *ngIf="column === 'projectLocation'"
        field="projectLocation"
        title="Location"
        [width]="180"

        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('projectLocation')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem.projectLocation | truncateText:45 }}
          <!-- <div>
            <span class="fw-bolder">

            </span>
          </div> -->
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Internal Project Manager Column -->
      <kendo-grid-column
        *ngIf="column === 'internalProjectManagerName'"
        field="internalProjectManagerName"
        title="Manager"
        [width]="180"

        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('internalProjectManagerName')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem.internalProjectManagerName | truncateText:45 }}
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- External PM Column -->
      <kendo-grid-column
        *ngIf="column === 'externalPMNames'"
        field="externalPMNames"
        title="External PM"
        [width]="220"

        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('externalPMNames')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
       {{ dataItem.externalPMNames | truncateText:45 }}
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <!-- <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator> -->
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Last Updated Date Column -->
      <kendo-grid-column
        *ngIf="column === 'lastUpdatedDate'"
        field="lastUpdatedDate"
        title="Updated Date"
        [width]="120"

        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('lastUpdatedDate')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
           {{ formatDate(dataItem.lastUpdatedDate) }}
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-date-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="true"
          >
            <kendo-filter-gte-operator></kendo-filter-gte-operator>
            <kendo-filter-lte-operator></kendo-filter-lte-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
          </kendo-grid-date-filter-menu>
        </ng-template>
      </kendo-grid-column>
    </ng-container>

    <!-- No Data Template -->
    <ng-template kendoGridNoRecordsTemplate>
      <div class="custom-no-records" *ngIf="!loading && !isLoading">
        <div class="text-center">
          <p class="text-muted">No data found</p>
        </div>
      </div>
    </ng-template>
  </kendo-grid>
</div>

<!-- Delete Confirmation Modal -->
<ng-template #deleteModal let-modal>
  <div class="modal-header bg-danger text-white">
    <h5 class="modal-title">Confirm Delete</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="modal.dismiss()"
    ></button>
  </div>

  <div class="delete-modal-body mt-4 text-center">
    <p class="fs-5">
      Are you sure you want to delete this project? - {{ this.projectName }}
    </p>
  </div>

  <div class="modal-footer delete-modal-footer ms-2">
    <button type="button" class="btn btn-danger" (click)="modal.dismiss()">
      Cancel
    </button>
    <button
      type="button"
      class="btn btn-primary "
      (click)="confirmDelete(); modal.close()"
    >
      Delete
    </button>
  </div>
</ng-template>

<!-- Delete Confirmation Modal -->
<ng-template #permitWarningModal let-modal>
  <div class="modal-header bg-danger text-white">
    <h5 class="modal-title">Warning</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="modal.dismiss()"
    ></button>
  </div>

  <div class="delete-modal-body mt-4 text-center">
    <p class="fs-5">
      This project ({{this.projectName }}) has permits. Please delete the permits first before deleting the project.
    </p>
  </div>

  <div class="modal-footer delete-modal-footer ms-2">

    <button
      type="button"
      class="btn btn-primary "
      (click)="modal.close()"
    >
      Ok
    </button>
  </div>
</ng-template>
