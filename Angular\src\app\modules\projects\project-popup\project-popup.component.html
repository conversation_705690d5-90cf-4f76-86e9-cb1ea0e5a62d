<div class="modal-content h-auto">
  <div class="modal-header bg-light-primary">
    <div class="modal-title h5 fs-3">
      <ng-container>
        <div *ngIf="id === 0">Add Project</div>
        <div *ngIf="id !== 0">Edit Project - {{ projectName }}</div>
      </ng-container>
    </div>
    <div class="float-right">
      <i
        class="fa-solid fs-2 fa-xmark text-white"
        (click)="modal.dismiss()"
      ></i>
    </div>
  </div>

  <div
    class="modal-body"
    
  >
    <!--   max-height: calc(100vh - 250px);
      overflow-y: auto;
      position: relative; -->
    <!-- Loading overlay removed; global loader handles this -->

    <!-- Initial loading state for form -->
    <div *ngIf="!projectForm && !isLoading" class="d-flex justify-content-center align-items-center h-100">
      <div class="text-center">
        <div class="custom-colored-spinner mb-3" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <div class="text-muted">Initializing form...</div>
      </div>
    </div>

    <div class="row">
      <div class="col-xl-12">
        <div class="d-flex">
          <ul
            class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap"
          >
            <li class="nav-item">
              <a
                class="nav-link text-active-primary me-6 cursor-pointer"
                data-toggle="tab"
                [ngClass]="{ active: selectedTab === 'basic' }"
                (click)="showTab('basic', $event)"
              >
                Project Details
              </a>
            </li>
            <li class="nav-item">
              <a
                class="nav-link text-active-primary me-6 cursor-pointer"
                data-toggle="tab"
                [ngClass]="{ active: selectedTab === 'role' }"
                (click)="showTab('role', $event)"
              >
                Project Manager
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <form class="form form-label-right" [formGroup]="projectForm" *ngIf="projectForm">
      <ng-container *ngIf="selectedTab == 'basic'">
        <div class="row mt-4">
          <!-- Project Name -->
          <div class="col-xl-8">
            <div class="form-group">
              <label class="fw-bold form-label mb-2">Project Name<sup class="text-danger">*</sup></label>
              <input
                type="text"
                class="form-control form-control-sm"
                formControlName="projectName"
                placeholder="Project Name"
              />
               <span
                class="custom-error-css"
                *ngIf="controlHasError('required', 'projectName')"
                >Required Field</span
              >
            </div>
          </div>

          <!-- Status -->
          <div class="col-xl-4">
            <div class="form-group">
              <label class="fw-bold form-label mb-2">Status<sup class="text-danger">*</sup></label>
              <ng-select
                [items]="statusOptions"
                [clearable]="false"
                [multiple]="false"
                bindLabel="label"
                bindValue="value"
                formControlName="status"
                placeholder="Select Status"
              >
              </ng-select>
              <span
                class="custom-error-css"
                *ngIf="controlHasError('required', 'status')"
                >Required Field</span
              >
            </div>
          </div>

          <!-- Internal Project Number -->

        </div>
        

        <div class="row mt-4">
          <div class="col-xl-6">
            <div class="form-group">
              <label class="fw-bold form-label mb-2"
                >Internal Project # <sup class="text-danger">*</sup></label
              >
              <input
                type="text"
                class="form-control form-control-sm"
                formControlName="internalProjectNo"
                placeholder="Internal Project"
              />
              <span
                class="custom-error-css"
                *ngIf="controlHasError('required', 'internalProjectNo')"
                >Required Field</span
              >
            </div>
          </div>
          <!-- Start Date -->
          <div class="col-xl-3">
            <label class="fw-bold form-label mb-2">Start Date</label>
            <input
              type="date"
              class="form-control form-control-sm"
              formControlName="startDate"
            />
          </div>

          <!-- End Date -->
          <div class="col-xl-3">
            <label class="fw-bold form-label mb-2">End Date</label>
            <input
              type="date"
              class="form-control form-control-sm"
              formControlName="endDate"
            />
          </div>
          <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Location<sup class="text-danger">*</sup></label>
            <input
              type="text"
              class="form-control form-control-sm"
              formControlName="location"
              placeholder="Location"
            />
             <span
                class="custom-error-css"
                *ngIf="controlHasError('required', 'location')"
                >Required Field</span
              >
          </div>
        </div>
        <div class="row mt-4">
          <div class="col-xl-12">
            <label class="fw-bold form-label mb-2">Description</label>
            <textarea
              class="form-control form-control-sm"
              rows="3"
              formControlName="projectDescription"
            ></textarea>
          </div>
        </div>
      </ng-container>

      <div class="row mt-4" *ngIf="selectedTab != 'basic'">
        <!-- Location -->

        <!-- Manager -->
        <div class="col-xl-6">
          <label class="fw-bold form-label mb-2"
            >Internal Project Manager <sup class="text-danger">*</sup></label
          >
          <ng-select
            [items]="managers"
            [clearable]="false"
            [multiple]="false"
            bindLabel="userFullName"
            name="manager"
            formControlName="manager"
            bindValue="userId"
            (change)="changeInternalManager($event)"
            placeholder="Select"
          >
          </ng-select>
          <span
            class="custom-error-css"
            *ngIf="controlHasError('required', 'manager')"
            >Required Field</span
          >
        </div>

        <!-- External PM -->
        <div class="col-xl-12 mt-4">
          <label class="fw-bold form-label mb-2"
            >External Project Manager (multiple)</label
          >
          <ng-select
            [items]="externalPMs"
            [clearable]="true"
            [multiple]="true"
            bindLabel="userFullName"
            name="externalPM"
            formControlName="externalPM"
            bindValue="userId"
            (change)="changeexternalPM($event)"
            placeholder="Select"
          >
          </ng-select>
        </div>
      </div>
    </form>
  </div>

<div class="modal-footer d-flex justify-content-between">
  <!-- Left Side -->
  <div>
    <button
      *ngIf="selectedTab != 'basic'"
      type="button"
      class="btn btn-secondary btn-sm btn-elevate"
      (click)="goToPreviousTab()"
    >
      Previous
    </button>
  </div>

  <!-- Right Side -->
  <div>
    <button
      class="btn btn-danger btn-sm btn-elevate mr-2"
      (click)="modal.dismiss()"
    >
      Cancel
    </button>
    &nbsp;
    <button
      *ngIf="selectedTab != 'basic'"
      type="button"
      class="btn btn-primary btn-sm"
      (click)="save()"
    >
      {{ id ? "Update" : "Save" }}
    </button>
    <button
      *ngIf="selectedTab == 'basic'"
      type="button"
      class="btn btn-primary btn-sm"
      (click)="showTab('role', $event)"
    >
      Next
    </button>
  </div>
</div>

</div>
