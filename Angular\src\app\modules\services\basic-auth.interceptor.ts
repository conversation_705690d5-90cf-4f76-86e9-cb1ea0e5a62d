import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, catchError, tap, throwError } from 'rxjs';
import { AppService } from './app.service';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationDialogComponent } from '../shared/confirmation-dialog/confirmation-dialog.component';
// import { LoginPopupComponent } from '../users/login-popup/login-popup.component';

@Injectable()
export class BasicAuthInterceptor implements HttpInterceptor {
  popupCount = 0;
  constructor(private appService: AppService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private modalService: NgbModal,) {

  }

  private handleAuthError(err: any): Observable<any> {
    //handle your auth error or rethrow
    // if (err.status === 401 || err.status === 403) {
    //   this.appService.logout()
    // }
    console.log('err.responseData.status ',err?.responseData?.status)
    if (err){
    if (err?.responseData?.status === 401 || err?.responseData?.status === 403) {
          this.appService.logout()
        }
    }

    return throwError(err);
  }


  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // add authorization header with basic auth credentials if available
    let currentUser = this.appService.getLocalStorageItem('permitUser', true);
    console.log("currentUser",currentUser)
    if (currentUser) {
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${currentUser.token}`,
          
          
        }
      });
    }

    return next.handle(request).pipe(
      tap(evt => {
        if (evt instanceof HttpResponse) {
          // console.log('evt ',evt)
          if (evt.body?.responseData?.status === 401) {
            this.appService.logout()
          }
        }
      }),
      catchError(err => {
        console.log('err ',err)
        return this.handleAuthError(err.error || err)
      }));
  }

  getLoginConfirmation() {
    this.popupCount = this.popupCount +1;
    var NgbModalOptions: any = {
      size: 'md', backdrop: 'static',
      keyboard: false, scrollable: true
    }
    if(this.popupCount === 1){
      const modalRef = this.modalService.open(ConfirmationDialogComponent, NgbModalOptions);
      modalRef.componentInstance.showClose = true;
      modalRef.componentInstance.description = "Session expired! Login again"
      modalRef.componentInstance.actionButtonText = "Login"
      modalRef.componentInstance.cancelButtonText = "Logout"
      modalRef.componentInstance.title = "Session expired! "

      modalRef.componentInstance.passEntry.subscribe((receivedEntry: any) => {
        if(receivedEntry === true){
          var NgbModalOptions: any = {
            size: 'md', backdrop: 'static',
            keyboard: false, scrollable: true
          }
          // this.modalService.open(LoginPopupComponent, NgbModalOptions);
        }else{
          this.appService.logout();
        }
      });
    }

  }

  openLoginPopup(){
    var NgbModalOptions: any = {
      size: 'md', backdrop: 'static',
      keyboard: false, scrollable: true
    }
    const modalRef = this.modalService.open(ConfirmationDialogComponent, NgbModalOptions);
    modalRef.componentInstance.passEntry.subscribe((receivedEntry: any) => {
      if(receivedEntry){
      }else{
        this.appService.logout();
      }
    });
  }



}
