import { ToastrService } from 'ngx-toastr';
import { Injectable} from '@angular/core';
import { NgbModal, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationDialogComponent } from '../shared/confirmation-dialog/confirmation-dialog.component';
//import { SessionAlertComponent } from '../shared/session-alert/session-alert.component';

export enum MessageType {
  Create,
  Read,
  Update,
  Delete
}

@Injectable()
export class CustomLayoutUtilsService {
  responseFromEdit : any;
  /**
   * Service constructor
   *
   * @param snackBar: MatSnackBar
   * @param dialog: MatDialog
   */
  constructor(
    private toastrService:ToastrService,
    private modalService: NgbModal,
    // private appService: AppService,
    // private layoutUtilService:CustomLayoutUtilsService,
    // private auth: AuthService,
    // private cdr:ChangeDetectorRef,
    // private httpUtilService: HttpUtilsService,
  ) {
  }

  showSuccess(message:string, title:string){
    this.toastrService.clear()
    return this.toastrService.success(message, title, {
      timeOut: 10000,
      closeButton:true,
      positionClass: 'toast-top-center',
    });
  };

  showError(message:string, title:string){
    this.toastrService.clear()
    return this.toastrService.error(message, title, {
      timeOut: 10000,
      closeButton:true,
      positionClass: 'toast-top-center'
    });
  }

  /**
   * Showing (Mat-Snackbar) Notification
   *
   * @param message: string
   * @param type: MessageType
   * @param duration: number
   * @param showCloseButton: boolean
   * @param showUndoButton: boolean
   * @param undoButtonDuration: number
   * @param verticalPosition: 'top' | 'bottom' = 'top'
   */
 
  /**
   * Showing Confirmation (Mat-Dialog) - to get action from user for performing some operation
   *
   * @param title: stirng
   * @param description: stirng
   * @param _waitDesciption: string
   */

  confirmMessage(title: string = '', description: string = '', actionButtonText: string = 'Confirm', cancelButtonText: string = 'Cancel', showClose: boolean) {
    const modalOption: NgbModalOptions = {};
    modalOption.backdrop = 'static';
    modalOption.keyboard = false;
    modalOption.size= 'md' ;
    const modalRef = this.modalService.open(ConfirmationDialogComponent,modalOption);
    modalRef.componentInstance.showClose = showClose;
    modalRef.componentInstance.title = title;
    modalRef.componentInstance.description = description;
    modalRef.componentInstance.actionButtonText = actionButtonText;
    modalRef.componentInstance.cancelButtonText = cancelButtonText;

    return modalRef;
  }

  confirmAlertMessage(title: string = '', description: string = '',waitDesc: string = '', actionButtonText: string = 'Confirm', cancelButtonText: string = 'Cancel', showClose: boolean) {
    const modalOption: NgbModalOptions = {};
    modalOption.backdrop = 'static';
    modalOption.keyboard = false;
    modalOption.size= 'md' ;
    const modalRef = this.modalService.open(ConfirmationDialogComponent,modalOption);
    modalRef.componentInstance.showClose = showClose;
    modalRef.componentInstance.title = title;
    modalRef.componentInstance.description = description;
    modalRef.componentInstance.actionButtonText = actionButtonText;
    modalRef.componentInstance.cancelButtonText = cancelButtonText;
    modalRef.componentInstance.waitDesc = waitDesc;
    return modalRef;
  }



//   sessionMessage(_title: string = '', _description: string = '', _waitDesciption: string = '', _actionButtonText: string = 'Confirm', _cancelButtonText: string = 'Cancel', _showClose: boolean) {
//     const modalOption: NgbModalOptions = {};
//     modalOption.backdrop = 'static';
//     modalOption.keyboard = false;
//     modalOption.size= 'md' ;
//     const modalRef = this.modalService.open(SessionAlertComponent, modalOption);
//     modalRef.componentInstance.showClose = _showClose;
//     modalRef.componentInstance.title = _title;
//     modalRef.componentInstance.description = _description;
//     modalRef.componentInstance.actionButtonText = _actionButtonText;
//     modalRef.componentInstance.waitDesciption = _waitDesciption;
//     modalRef.componentInstance.cancelButtonText = _cancelButtonText;
//     //get response from edit user modal
//     modalRef.componentInstance.passEntry.subscribe((receivedEntry:any) => {
      
//       this.responseFromEdit = receivedEntry;
//       return this.responseFromEdit
//     })
//     return modalRef;
//   }


}
