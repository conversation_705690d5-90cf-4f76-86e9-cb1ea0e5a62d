import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, finalize } from 'rxjs';
import { AppSettings } from '../../app.settings';
import { HttpUtilsService } from './http-utils.service';

export interface DashboardStats {
  totalProjects: number;
  activeProjects: number;
  totalPermits: number;
  internalPermits: number;
  externalPermits: number;
  bothPermits:number;
}

export interface PermitsChartData {
  series: number[];
  labels: string[];
}

export interface CycleTimesChartData {
  categories: string[];
  series: {
    name: string;
    data: number[];
  }[];
}

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  constructor(
    private http: HttpClient,
    private httpUtils: HttpUtilsService
  ) { }

  getDashboardStats(data:any): Observable<{ success: boolean; data: DashboardStats }> {
    // Show global loader
    this.httpUtils.loadingSubject.next(true);
    
    return this.http.post<{ success: boolean; data: DashboardStats }>(`${AppSettings.REST_ENDPOINT}/getDashboardStats`, data);
  }

  getPermitsChartData(data:any): Observable<{ success: boolean; data: PermitsChartData }> {
    return this.http.post<{ success: boolean; data: PermitsChartData }>(`${AppSettings.REST_ENDPOINT}/getPermitsChartData`, data);
  }

  getCycleTimesChartData(data:any): Observable<{ success: boolean; data: CycleTimesChartData }> {
    return this.http.post<{ success: boolean; data: CycleTimesChartData }>(`${AppSettings.REST_ENDPOINT}/getCycleTimesChartData`, data);
  }
  getChartsForUsersWithInternalPm(data:any): Observable<{ success: boolean; data: CycleTimesChartData }> {
    return this.http.post<{ success: boolean; data: CycleTimesChartData }>(`${AppSettings.REST_ENDPOINT}/getChartsForUsersWithInternalPm`, data);
  }
}
