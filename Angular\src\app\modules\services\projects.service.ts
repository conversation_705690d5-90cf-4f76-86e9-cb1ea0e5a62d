import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, catchError, from, mergeMap, of } from 'rxjs';
import { AppSettings } from 'src/app/app.settings';

@Injectable({
  providedIn: 'root'
})
export class ProjectsService {


  constructor(
    private http: HttpClient
  ) { }

  private handleError<T>(operation = 'operation', result?: any) {
    return (error: any): Observable<any> => {
      // TODO: send the error to remote logging infrastructure
      console.error(error); // log to console instead

      // Let the app keep running by returning an empty result.
      return from(result);
    };
  }

  public getAllProjects(queryParams: any): Observable<any> {
    const requestBody = {
      pageSize: queryParams.pageSize,
      pageNumber: queryParams.pageNumber,
      sortField: queryParams.sortField,
      sortOrder: queryParams.sortOrder,
      paginate: true,
      search: queryParams.filter?.search || '',
      columnFilter: queryParams.filter?.columnFilter || []
    };

    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllProjects`, requestBody)
      .pipe(
        mergeMap((res: any) => {
          return of(res);
        }),
        catchError((err: any) => {
          console.error('Error in getAllProjects:', err);
          return of({
            error: { isFault: true },
            message: 'Error retrieving Projects'
          });
        })
      );
  }

  public getProjectsForKendoGrid(state: any): Observable<any> {
    const requestBody = {
      take: state.take || 10,
      skip: state.skip || 0,
      sort: state.sort || [],
      filter: state.filter || { logic: 'and', filters: [] },
      search: state.search || '',
      role:state.role ,
      userId:state.userId
    };

    console.log('Making API request with state:', requestBody);

    return this.http.post(`${AppSettings.REST_ENDPOINT}/getProjectsForKendoGrid`, requestBody)
      .pipe(
        mergeMap((res: any) => {
          console.log('API response received:', res);
          return of(res);
        }),
        catchError((err: any) => {
          console.error('Error in getProjectsForKendoGrid:', err);
          
          // Handle specific error types
          if (err.name === 'TimeoutError' || err.status === 0) {
            console.error('Request timeout - server may be unresponsive');
            return of({
              isFault: true,
              responseData: {
                data: [],
                total: 0,
                errors: ['Request timeout - please try again']
              }
            });
          }
          
          return of({
            isFault: true,
            responseData: {
              data: [],
              total: 0,
              errors: ['Error retrieving Projects: ' + (err.message || 'Unknown error')]
            }
          });
        })
      );
  }

   public getAllProjectsData(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/getAllProjects', data);
  }

  public getCurrentProjectsData(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/getCurrentProjects', data);
  }
  public createProject(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/createProject', data);
  }

  public updateProject(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/editProject', data);
  }

  public getProject(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/getProjectById', data);
  }

  public deleteProject(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/deleteProject', data);
  }

  public getProjectstatistics(): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/getProjectstatistics', {});
  }

  public searchProjects(searchTerm: string): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/searchProjects`, { search: searchTerm });
  }

  public exportProjects(exportType: string, selectedIds?: number[]): Observable<any> {
    const requestBody = {
      exportType: exportType,
      selectedIds: selectedIds || []
    };
    return this.http.post(`${AppSettings.REST_ENDPOINT}/exportProjects`, requestBody);
  }
}
