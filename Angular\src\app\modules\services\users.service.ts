import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { AppSettings } from 'src/app/app.settings';

export interface User {
  userId: number;
  firstName: string;
  lastName: string;
  userFullName: string;
  roleName: string;
  roleId: number;
}

@Injectable({
  providedIn: 'root'
})
export class UsersService {

  constructor(
    private http: HttpClient
  ) { }

  public getInternalAndAdminUsers(): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/getInternalAndAdminUsers`, {});
  }

  public getUserlistForDropdown(roleName: string): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/getUserlistForDropdown`, { roleName });
  }
}
