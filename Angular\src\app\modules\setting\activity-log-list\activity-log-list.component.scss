.grid-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

/* Loading Overlay Styles handled globally in styles.scss */

.grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 500;
  }

  .search-container {
    width: 300px;
  }
}

.grid-toolbar {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
}

/* Toolbar button styles */
.k-grid-toolbar {
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
    min-width: 40px;
    height: 40px;
    border: 1px solid transparent;
    background-color: transparent;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      background-color: rgba(0, 0, 0, 0.05);
    }

    /* Icon-only buttons */
    &.btn-icon {
      padding: 0.5rem;
      min-width: 40px;
      width: 40px;

      i, .svg-icon {
        font-size: 1.25rem !important;

        svg {
          width: 1.25rem !important;
          height: 1.25rem !important;
        }
      }
    }

    /* Buttons with text - ensure consistent spacing */
    &:not(.btn-icon) {
      padding: 0.375rem 0.75rem;
      gap: 0.5rem;
    }
  }
}

/* Advanced filters panel */
.advanced-filters-panel {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin-bottom: 20px;
}

/* Statistics section */
.log-statistics {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin-bottom: 20px;

  .stat-item {
    padding: 15px;
    
    h4 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 5px;
    }
    
    small {
      font-size: 0.875rem;
      color: #6c757d;
    }
  }
}

/* Search section */
.search-section {
  .k-textbox {
    border-radius: 6px;
  }
}

/* Badge styles */
.badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-success {
  background-color: #d4edda;
  color: #155724;
}

.badge-danger {
  background-color: #f8d7da;
  color: #721c24;
}

.badge-info {
  background-color: #d1ecf1;
  color: #0c5460;
}

.badge-secondary {
  background-color: #e2e3e5;
  color: #383d41;
}

/* Button group styles */
.btn-group-sm .btn {
  padding: 4px 8px;
  font-size: 0.875rem;
  border-radius: 4px;
}

/* Custom no records template */
.custom-no-records {
  padding: 40px;
  text-align: center;
  
  .spinner-border {
    width: 2rem;
    height: 2rem;
  }
}
