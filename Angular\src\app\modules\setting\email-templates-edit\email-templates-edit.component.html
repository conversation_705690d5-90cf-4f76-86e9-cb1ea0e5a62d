<div class="modal-content">
  <div class="modal-header">
    <div class="modal-title" id="example-modal-sizes-title-lg">
      <div *ngIf="!id || id == 0" class="fw-bold fs-2 text-white">
        Add Email Template
      </div>
      <div *ngIf="id && id != 0" class="fw-bold fs-2 text-white">
        Edit Email Template
      </div>
    </div>
    <div class="float-right cursor-pointer ir-12">
      <a class="btn btn-icon btn-sm pl-08" (click)="modal.dismiss()">
        <i class="fa-solid fs-2 fa-xmark text-white"></i>
      </a>
    </div>
  </div>
  <div class="modal-body " >
    <!-- style="min-height: auto" -->
    <ng-container>
      <form class="form form-label-right" [formGroup]="formGroup">
        <div class="form-group row mb-2">
          <div class="col-lg-12">
            <label class="fw-bold form-label"
              >Template Name <sup class="text-danger">*</sup></label
            >
            <input
              type="text"
              class="form-control form-control-sm"
              name="templateName"
              placeholder="Type Here"
              autocomplete="off"
              formControlName="TemplateName"
              readonly
            />
            <div
              class="custom-error-css"
              *ngIf="controlHasError('required', 'TemplateName')"
            >
              Required Field
            </div>
            <!-- <p>{{ template.templateName }}</p> -->
          </div>
        </div>
        <div class="form-group row mb-2">
          <div class="col-lg-12">
            <label class="fw-bold form-label"
              >Email Subject <sup class="text-danger">*</sup></label
            >
            <input
              type="text"
              class="form-control form-control-sm"
              name="TemplateSubject"
              placeholder="Type Here"
              autocomplete="off"
              formControlName="TemplateSubject"
            />
            <div
              class="custom-error-css"
              *ngIf="controlHasError('required', 'TemplateSubject')"
            >
              Required Field
            </div>
          </div>
        </div>
        <div id="tabs" #tabs>
          <label class="fw-bold form-label"
            >Email Body <sup class="text-danger">*</sup></label
          >
          <div class="d-flex h-30px">
            <ul
              class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bold flex-nowrap"
            >
              <li class="nav-item">
                <a
                  class="nav-link text-active-primary me-6 cursor-pointer fs-4"
                  data-toggle="tab"
                  [ngClass]="{ active: selected === 'view' }"
                  (click)="showTab('view', $event)"
                >
                  View
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link text-active-primary me-6 cursor-pointer fs-4"
                  data-toggle="tab"
                  [ngClass]="{ active: selected === 'edit' }"
                  (click)="showTab('edit', $event)"
                >
                  Edit
                </a>
              </li>
            </ul>
          </div>
          <div class="tab-content mt-5" id="myTabContent">
            <div
              class="row"
              #edit
              id="edit"
              view="tabpanel"
              *ngIf="selected === 'edit'"
            >
              <div class="col-8">
                <div class="">
                  <textarea
                    type="text"
                    class="form-control"
                    rows="8"
                    cols="6"
                    formControlName="emailBody"
                  ></textarea>
                </div>
              </div>
              <div class="col-4">
                <span class="fw-semibold fs-6 mb-2"
                  >Fields that can be used for this template:</span
                >
                <ul>
                  <li class="fs-6" *ngFor="let field of FieldList">
                    {{ field }}
                  </li>
                </ul>
              </div>
            </div>
            <div
              class="view_box"
              #view
              id="view"
              view="tabpanel"
              *ngIf="selected === 'view'"
            >
              <p [innerHtml]="formGroup.value.emailBody"></p>
            </div>
          </div>
        </div>
      </form>
    </ng-container>
  </div>
  <div class="modal-footer">
    <button
      type="button"
      class="btn btn-danger btn-sm btn-elevate mr-2"
      (click)="modal.dismiss()"
    >
      Cancel
    </button>
    <ng-container *ngIf="!id || id == 0">
      <button
        type="submit"
        class="btn btn-primary btn-elevate btn-sm"
        [disabled]="formGroup.invalid"
        (click)="save()"
      >
        Save
      </button>
    </ng-container>
    <ng-container *ngIf="id && id != 0">
      <button
        type="submit"
        class="btn btn-primary btn-elevate btn-sm"
        [disabled]="formGroup.invalid"
        (click)="update()"
      >
        Update
      </button>
    </ng-container>
  </div>
</div>
