<!-- Full Screen Loading Overlay -->
<div *ngIf="isLoading$ | async" class="fullscreen-loading-overlay">
  <div class="loading-content">
    <div class="custom-colored-spinner" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-4 fs-5">Loading role data...</div>
  </div>
</div>

<div class="modal-content">
    <div class="modal-header">
        <div class="modal-title" id="example-modal-sizes-title-lg">
            <ng-container *ngIf="role">
                <div class="fw-bold fs-2 text-white" *ngIf="id !==0">Edit Role - {{roleName}}</div>
                <div class="fw-bold fs-2 text-white" *ngIf="id===0">Add Role</div>
            </ng-container>
        </div>
        <div class="float-right cursor-pointer ir-12 " >
            <a class="btn btn-icon  btn-sm mx-1" (click)="modal.dismiss()">
              <i class="fa-solid fs-2 fa-xmark text-white"></i>
            </a>
          </div>
    </div>
    <div class="modal-body ">
        <ng-container>
            <form class="form form-label-right" [formGroup]="editroles">
                <div class="card-body response-list">
                    <div class="form-group row mb-4 px-10 pt-2">
                        <div class="col-lg-12">
                            <label class="fw-semibold fs-6 mb-2">Name<sup class="text-danger">*</sup></label>
                            <input type="text" class="form-control form-control-sm" name="roleName" placeholder="Type Here" autocomplete="off" formControlName="roleName" />
                            <span class="custom-error-css" *ngIf="shouldShowValidationError('roleName')">Required Field</span>
                        </div>
                        <!-- <div class="col-lg-6">
                            <label class="fw-semibold fs-6 mb-2">System Role<sup class="text-danger">*</sup></label>
                            <ng-select [items]="systemRoles" (change)="changeSystemAccess($event)" [clearable]="false" [multiple]="false" bindLabel="RoleName"  bindValue="RoleId" formControlName="systemRole"
                              placeholder="Select" class="custom">
                            </ng-select>
                            <span class="custom-error-css" *ngIf="appService.controlHasError('required', 'systemRole',editroles)">Required Field</span>
                        </div> -->
                    </div>
                    <div class="form-group row mb-4 px-10 pt-2">
                        <div class="col-lg-12">
                            <label class="fw-semibold fs-6 mb-2">Description</label>
                            <textarea type="text" class="form-control" name="description" placeholder="Type Here" rows="2"
                              autocomplete="off"  formControlName="description"></textarea>
                        </div>
                    </div>

                    <div class="form-group row mb-4 px-10 pt-2">
                        <div class="col-12 d-flex justify-content-start">
                            <table class="w-100">
                                <th class="p-1 fw-bold fs-6" style="width:390px !important;">Permissions</th>
                                <th class="p-1 fw-bold fs-6" style="width:390px !important;">
                                    <div class="d-flex justify-content-between">
                                        <span>Read</span>
                                        <span>Write</span>
                                        <span>Delete</span>
                                    </div>
                                </th>
                                <tr *ngFor="let p of perNameArray">
                                    <td class="p-1">
                                      <label class="fw-semibold fs-6">{{p}}</label>
                                    </td>
                                    <td class="p-1">
                                      <checkbox-group [formControlName]="p">
                                        <checkbox value="Read"></checkbox>
                                        <checkbox style="padding-left:145px" value="Write"></checkbox>
                                        <checkbox style="padding-left:145px" value="Delete"></checkbox>
                                      </checkbox-group>
                                    </td>
                                  </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </form>
        </ng-container>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary btn-sm btn-elevate mr-2" (click)="modal.dismiss()">Cancel</button>
        <ng-container>
            <button type="button" class="btn btn-primary btn-elevate btn-sm" (click)="save()">
                Save</button>
        </ng-container>
    </div>
</div>



