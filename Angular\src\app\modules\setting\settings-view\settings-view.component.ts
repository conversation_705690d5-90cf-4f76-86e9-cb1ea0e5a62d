import { ChangeDetectorRef, Component } from '@angular/core';
import { Router } from '@angular/router';
import { UserService } from '../../services/user.service';
import { AppService } from '../../services/app.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AddUserComponent } from '../add-user/user-add.component';

@Component({
  selector: 'app-settings-view',
  standalone: true,
  imports: [],
  templateUrl: './settings-view.component.html',
  styleUrl: './settings-view.component.scss',
})
export class SettingsViewComponent {
  loginUser!: any;
  usersCount: Number = 0;
  rolesCount: Number = 0;
  municipalitiesCount: Number = 0;
  emailTemplatesCount: Number = 0;
  permissionArray: any;
  constructor(
    private router: Router,
    private userService: UserService,
    private AppService: AppService,
    private modalService: NgbModal,
    private cd: ChangeDetectorRef
  ) {
    this.loginUser = this.AppService.getLoggedInUser();
    let data = {
      loggedInUserId: this.loginUser.userId,
    };

    this.getCounts(data);
    // console.log('Login user loaded:', this.loginUser);
  }

  ngOnInit() {
     this.userService
      .getDefaultPermissions({})
      .subscribe((permissions: any) => {
        this.permissionArray = permissions.responseData;
      });
  }

  PermitNavigate() {
    this.router.navigate(['/permits/list']);
  }
  projectNavigate() {
    this.router.navigate(['/projects/list']);
  }
  userNavigate() {
    this.router.navigate(['/setting/list']);
  }
emailtemplateNavigate() {
    this.router.navigate(['/setting/email-list']);
  }

  getCounts(data: any) {
    console.log('data', data);
    this.userService.getAllCounts(data).subscribe((counts) => {
      if (counts && counts.isFault == false) {
        this.emailTemplatesCount =
          counts?.responseData?.emailTemplatesCount || 0;

        console.log(' this.emailTemplatesCount', counts.responseData.counts);
        this.municipalitiesCount =
          counts?.responseData?.municipalitiesCount || 0;
        this.rolesCount = counts?.responseData?.rolesCount || 0;
        this.usersCount = counts?.responseData?.usersCount || 0;
        this.cd.detectChanges();
        // emailTemplatesCount
        // municipalitiesCount
        // rolesCount
        // usersCount
        // console.log('counts', counts);
      }
    });
  }

  addUser(){
   const NgbModalOptions: {
      size: string;
      backdrop: boolean | 'static';
      keyboard: boolean;
      scrollable: boolean;
    } = {
      size: 'lg', // Large modal size
      backdrop: 'static', // Prevents closing when clicking outside
      keyboard: false, // Disables closing with the Escape key
      scrollable: true, // Allows scrolling inside the modal
    };
   const modalRef = this.modalService.open(AddUserComponent,NgbModalOptions)
    modalRef.componentInstance.id = 0
        modalRef.componentInstance.defaultPermissions = this.permissionArray;

    
  }
}
