import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { InlineSVGModule } from 'ng-inline-svg-2';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ShowImageComponent } from './show-image/show-image.component';
import { CheckboxGroupComponent } from './checkbox-group/checkbox-group.component';
import { CheckboxComponent } from './checkbox-group/checkbox.component';
import { TruncateTextPipe } from './truncate-text/truncate-text.pipe';
import { ConfirmationDialogComponent } from './confirmation-dialog/confirmation-dialog.component';


@NgModule({
  declarations: [
    CheckboxComponent,
    CheckboxGroupComponent,
    ShowImageComponent,
    TruncateTextPipe,
    ConfirmationDialogComponent
  ],
  imports: [
    CommonModule,
    InlineSVGModule,
    NgbModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  exports:[
     CheckboxComponent,
    CheckboxGroupComponent,
    TruncateTextPipe
  ]
})
export class SharedModule { }
