
<!--begin::Dashboard Stats Cards-->
<div class="row g-4 mb-6">
  <!--begin::Total Projects Card-->
  <div class="col-xl col-lg-4 col-md-6 col-sm-12">
    <div class="card card-custom card-stretch h-100">
      <div class="card-body d-flex flex-column justify-content-center align-items-center p-4">
        <div class="text-center">
          <div class="fs-2x fw-bold text-primary mb-2">{{ stats.totalProjects }}</div>
          <div class="fs-6 text-muted">Total Projects</div>
        </div>
      </div>
    </div>
  </div>
  <!--end::Total Projects Card-->

  <!--begin::Active Projects Card-->
  <div class="col-xl col-lg-4 col-md-6 col-sm-12">
    <div class="card card-custom card-stretch h-100">
      <div class="card-body d-flex flex-column justify-content-center align-items-center p-4">
        <div class="text-center">
          <div class="fs-2x fw-bold text-success mb-2">{{ stats.activeProjects }}</div>
          <div class="fs-6 text-muted">Active Projects</div>
        </div>
      </div>
    </div>
  </div>
  <!--end::Active Projects Card-->

  <!--begin::Total Permits Card-->
  <div class="col-xl col-lg-4 col-md-6 col-sm-12">
    <div class="card card-custom card-stretch h-100">
      <div class="card-body d-flex flex-column justify-content-center align-items-center p-4">
        <div class="text-center">
          <div class="fs-2x fw-bold text-info mb-2">{{ stats.totalPermits }}</div>
          <div class="fs-6 text-muted">Total Permits</div>
        </div>
      </div>
    </div>
  </div>
  <!--end::Total Permits Card-->

  <!--begin::Internal Permits Card-->
  <div class="col-xl col-lg-4 col-md-6 col-sm-12">
    <div class="card card-custom card-stretch h-100">
      <div class="card-body d-flex flex-column justify-content-center align-items-center p-4">
        <div class="text-center">
          <div class="fs-2x fw-bold text-warning mb-2">{{ stats.internalPermits }}</div>
          <div class="fs-6 text-muted">Internal Permits</div>
        </div>
      </div>
    </div>
  </div>
  <!--end::Internal Permits Card-->

  <!--begin::External Permits Card-->
  <div class="col-xl col-lg-4 col-md-6 col-sm-12">
    <div class="card card-custom card-stretch h-100">
      <div class="card-body d-flex flex-column justify-content-center align-items-center p-4">
        <div class="text-center">
          <div class="fs-2x fw-bold text-secondary mb-2">{{ stats.externalPermits }}</div>
          <div class="fs-6 text-muted">External Permits</div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-xl col-lg-4 col-md-6 col-sm-12">
    <div class="card card-custom card-stretch h-100">
      <div class="card-body d-flex flex-column justify-content-center align-items-center p-4">
        <div class="text-center">
          <div class="fs-2x fw-bold text-danger mb-2">{{ stats.bothPermits }}</div>
          <div class="fs-6 text-muted">Both Permits</div>
        </div>
      </div>
    </div>
  </div>
  <!--end::External Permits Card-->
</div>
<!--end::Dashboard Stats Cards-->

<!--begin::Charts Section-->
<div class="row g-4">
  <!--begin::Internal vs External Permits Pie Chart-->
  <div class="col-xl-6">
    <div class="card card-custom h-100">
      <div class="card-header">
        <div class="card-title">
          <h3 class="card-label fw-bold text-gray-900">Permits by Type</h3>
        </div>
      </div>
      <div class="card-body d-flex flex-column">
        <div #pieChart id="permits-pie-chart" class="flex-grow-1"></div>
      </div>
    </div>
  </div>
  <!--end::Internal vs External Permits Pie Chart-->

  <!--begin::Permit Cycle Times Bar Chart-->
  <div class="col-xl-6">
    <div class="card card-custom h-100">
      <div class="card-header">
        <div class="card-title">
          <h3 class="card-label fw-bold text-gray-900">Permit Cycle Times by Project</h3>
        </div>
      </div>
      <div class="card-body d-flex flex-column">
        <div #barChart id="cycle-times-bar-chart" class="flex-grow-1"></div>
      </div>
    </div>
  </div>
  <!--end::Permit Cycle Times Bar Chart-->
</div>
<div class="row g-4 mt-4" *ngIf="loginUser.roleName == 'Admin'" >
  <!-- <div class="col-xl-6">
    <div class="card card-custom h-100">
      <div class="card-header">
        <div class="card-title">
          <h3 class="card-label fw-bold text-gray-900">Permits by Type</h3>
        </div>
      </div>
      <div class="card-body d-flex flex-column">
        <div #pieChart id="permits-pie-chart" class="flex-grow-1"></div>
      </div>
    </div>
  </div> -->
  <!--end::Internal vs External Permits Pie Chart-->

  <!--begin::Permit Cycle Times Bar Chart-->
  <div class="col-xl-6">
    <div class="card card-custom h-100">
      <div class="card-header">
        <div class="card-title">
          <h3 class="card-label fw-bold text-gray-900">Internal PM Projects</h3>
        </div>
      </div>
      <div class="card-body d-flex flex-column">
        <div #barChartForUser id="project-for-user-bar-chart" class="flex-grow-1"></div>
      </div>
    </div>
  </div>
  <!--end::Permit Cycle Times Bar Chart-->
</div>
<!--end::Charts Section-->
