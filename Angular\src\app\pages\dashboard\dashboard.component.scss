:host {
  width: 100%;
}

.h-100 {
  height: 100%;
}

// Dashboard Cards Styling
.card-custom {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background: #ffffff;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
}

.card-stretch {
  height: 100%;
  min-height: 120px;
}

// Stats cards styling
.card-body {
  padding: 1.5rem !important;
  
  .fs-2x {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    line-height: 1.2;
  }
  
  .fs-6 {
    font-size: 0.95rem !important;
    font-weight: 500 !important;
    color: #6c757d !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

// Chart cards styling
.card-custom {
  .card-header {
    border-bottom: 1px solid #e4e6ea;
    padding: 1.5rem 2rem 1rem 2rem;
    
    .card-title {
      margin: 0;
      
      .card-label {
        font-size: 1.1rem;
        font-weight: 600;
        color: #181c32;
      }
    }
  }
  
  .card-body {
    padding: 1.5rem 2rem 2rem 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

// Color variations for different cards
.text-primary {
  color: #3699ff !important;
}

.text-success {
  color: #1bc5bd !important;
}

.text-info {
  color: #8950fc !important;
}

.text-warning {
  color: #ffa800 !important;
}

.text-danger {
  color: #f64e60 !important;
}

// Individual loading spinners removed - using global loader

// Chart styling
#permits-pie-chart,
#cycle-times-bar-chart {
  min-height: 300px;
  height: 100%;
  width: 100%;
}
#project-for-user-bar-chart {
  min-height: 300px;
  height: 100%;
  width: 100%;
}

// Ensure chart containers take full available space
.flex-grow-1 {
  flex: 1 1 auto;
  min-height: 0; // Important for flexbox to work properly
}

// Dashboard layout improvements
.row {
  margin-left: -0.75rem;
  margin-right: -0.75rem;
}

.col-xl {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

// Ensure proper spacing between sections
.mb-6 {
  margin-bottom: 3rem !important;
}

// Chart cards equal height
.h-100 {
  height: 100% !important;
}

// Responsive adjustments
@media (max-width: 1200px) {
  .col-xl {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@media (max-width: 768px) {
  .card-body {
    padding: 1rem !important;
    
    .fs-2x {
      font-size: 2rem !important;
    }
  }
  
  .col-xl {
    flex: 0 0 100%;
    max-width: 100%;
  }
  
  #permits-pie-chart,
  #cycle-times-bar-chart {
    min-height: 250px;
  }
  #project-for-user-bar-chart {
    min-height: 250px;
  }
}

@media (max-width: 576px) {
  .row {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }
  
  .col-xl {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
