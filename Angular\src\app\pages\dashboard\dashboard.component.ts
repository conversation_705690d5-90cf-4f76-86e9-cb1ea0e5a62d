import { Component, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { PageInfoService } from '../../_metronic/layout/core/page-info.service';
import { DashboardService, DashboardStats, PermitsChartData, CycleTimesChartData } from '../../modules/services/dashboard.service';
import * as ApexCharts from 'apexcharts';
import { getCSSVariableValue } from '../../_metronic/kt/_utils';
import { AppService } from 'src/app/modules/services/app.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent implements OnInit, AfterViewInit {
  @ViewChild('pieChart') pieChart: ElementRef<HTMLDivElement>;
  @ViewChild('barChart') barChart: ElementRef<HTMLDivElement>;
  @ViewChild('barChartForUser') barChartForUser: ElementRef<HTMLDivElement>;

  stats: DashboardStats = {
    totalProjects: 0,
    activeProjects: 0,
    totalPermits: 0,
    internalPermits: 0,
    externalPermits: 0,
    bothPermits:0
  };

  permitsChartData: PermitsChartData = {
    series: [0, 0],
    labels: ['Internal', 'External','Both']
  };

  cycleTimesChartData: CycleTimesChartData = {
    categories: [],
    series: [{
      name: 'Days',
      data: []
    }]
  };

  pieChartOptions: any = {};
  barChartOptions: any = {};
  barChartOptionsforUser: any = {};
  charDataForusers:any

  // Loading state tracking
  private cardsDataLoaded = false;
  private permitsChartLoaded = false;
  private cycleTimesChartLoaded = false;
  loginUser: any;

  constructor(
    private pageInfo: PageInfoService,
    private dashboardService: DashboardService,
    private appService:AppService
  ) {}

  ngOnInit(): void {
    this.pageInfo.updateTitle('Dashboard');
   this.loginUser =  this.appService.getLoggedInUser()

  //  console.log("this.loginUser dashboard",this.loginUser)
    this.loadDashboardStats();
    this.loadChartData();

  
  }

  ngAfterViewInit(): void {
    // Charts will be initialized after data is loaded
  }

  private checkAllDataLoaded(): void {
    if (this.cardsDataLoaded && this.permitsChartLoaded && this.cycleTimesChartLoaded) {
      // All data is loaded, hide the global loader
      this.dashboardService['httpUtils'].loadingSubject.next(false);
      console.log('All dashboard data loaded, hiding loader');
    }
  }

  loadDashboardStats(): void {
    console.log('Loading dashboard stats...');

    let data = {
role:this.loginUser.roleName,
userId:this.loginUser.userId
    } as any
    
    this.dashboardService.getDashboardStats(data).subscribe({
      next: (response) => {
        console.log('Dashboard API response:', response);
        if (response && response.success) {
          this.stats = response.data;
          console.log('Stats loaded:', this.stats);
        } else {
          console.error('API returned success: false or no response', response);
          // Set default values if API fails
          this.stats = {
            totalProjects: 0,
            activeProjects: 0,
            totalPermits: 0,
            internalPermits: 0,
            externalPermits: 0,
            bothPermits:0
          };
        }
        this.cardsDataLoaded = true;
        this.checkAllDataLoaded();
      },
      error: (error) => {
        console.error('Error loading dashboard stats:', error);
        console.error('Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          url: error.url
        });
        
        // Set default values on error
        this.stats = {
          totalProjects: 0,
          activeProjects: 0,
          totalPermits: 0,
          internalPermits: 0,
          bothPermits:0,
          externalPermits: 0
        };
        this.cardsDataLoaded = true;
        this.checkAllDataLoaded();
      }
    });
  }

  loadChartData(): void {
    // Load permits chart data
        let data = {
role:this.loginUser.roleName,
userId:this.loginUser.userId
    } as any
    this.dashboardService.getPermitsChartData(data).subscribe({
      next: (response) => {
        if (response && response.success) {
          this.permitsChartData = response.data;
          console.log('Permits chart data loaded:', this.permitsChartData);
          // Delay chart initialization to ensure DOM is ready
          setTimeout(() => this.initializePieChart(), 100);
        }
        this.permitsChartLoaded = true;
        this.checkAllDataLoaded();
      },
      error: (error) => {
        console.error('Error loading permits chart data:', error);
        this.permitsChartLoaded = true;
        this.checkAllDataLoaded();
      }
    });

    // Load cycle times chart data
    this.dashboardService.getCycleTimesChartData(data).subscribe({
      next: (response) => {
        if (response && response.success) {
          this.cycleTimesChartData = response.data;
          console.log('Cycle times chart data loaded:', this.cycleTimesChartData);
          // Delay chart initialization to ensure DOM is ready
          setTimeout(() => this.initializeBarChart(), 100);
        }
        this.cycleTimesChartLoaded = true;
        this.checkAllDataLoaded();
      },
      error: (error) => {
        console.error('Error loading cycle times chart data:', error);
        this.cycleTimesChartLoaded = true;
        this.checkAllDataLoaded();
      }
    });


    this.dashboardService.getChartsForUsersWithInternalPm(data).subscribe({
      next: (response) => {
        if (response && response.success) {

          this.charDataForusers = response.data
          // this.cycleTimesChartData = response.data;
          // console.log('Cycle times chart data loaded:', this.cycleTimesChartData);
          // Delay chart initialization to ensure DOM is ready
          // setTimeout(() => this.initializeBarChart(), 100);
            setTimeout(() => {
      this.initializeProjectForUserBarChart()
    }, 500);
        }
        // this.cycleTimesChartLoaded = true;
        // this.checkAllDataLoaded();
      },
      error: (error) => {
        console.error('Error loading cycle times chart data:', error);
        // this.cycleTimesChartLoaded = true;
        // this.checkAllDataLoaded();
      }
    });
  }

  initializePieChart(): void {
    if (!this.pieChart?.nativeElement) return;

    this.pieChartOptions = {
      series: this.permitsChartData.series,
      chart: {
        type: 'pie',
        height: '100%',
        fontFamily: 'inherit',
        toolbar: {
          show: false,
        },
      },
      labels: this.permitsChartData.labels,
      colors: [
        getCSSVariableValue('--bs-warning'),
        getCSSVariableValue('--bs-success'),
        getCSSVariableValue('--bs-danger'),

      ],
      legend: {
        position: 'bottom',
        horizontalAlign: 'center',
        fontSize: '14px',
        fontFamily: 'inherit',
        fontWeight: 500,
        labels: {
          colors: getCSSVariableValue('--bs-gray-600')
        }
      },
      dataLabels: {
        enabled: true,
        style: {
          fontSize: '12px',
          fontFamily: 'inherit',
          fontWeight: 'bold'
        }
      },
      tooltip: {
        style: {
          fontSize: '12px',
          fontFamily: 'inherit'
        }
      }
    };

    const chart = new ApexCharts(this.pieChart.nativeElement, this.pieChartOptions);
    chart.render();
  }

  initializeBarChart(): void {
    console.log('Initializing bar chart...');
    console.log('Bar chart element:', this.barChart?.nativeElement);
    console.log('Cycle times data:', this.cycleTimesChartData);
    
    if (!this.barChart?.nativeElement) {
      console.error('Bar chart element not found');
      return;
    }

    // Clear any existing chart
    if (this.barChart.nativeElement.innerHTML) {
      this.barChart.nativeElement.innerHTML = '';
    }

    this.barChartOptions = {
      series: this.cycleTimesChartData.series,
      chart: {
        type: 'bar',
        height: '100%',
        fontFamily: 'inherit',
        toolbar: {
          show: false,
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800,
        },
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '55%',
          borderRadius: 4,
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
      },
      xaxis: {
        categories: this.cycleTimesChartData.categories.map((_, index) => `Project ${index + 1}`),
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        labels: {
          show: false, // Hide individual project labels
        },
        title: {
          text: 'Projects',
          style: {
            color: getCSSVariableValue('--bs-gray-600'),
            fontSize: '13px',
            fontFamily: 'inherit',
            fontWeight: 600,
          },
        },
      },
      yaxis: {
        title: {
          text: 'Days',
          style: {
            color: getCSSVariableValue('--bs-gray-500'),
            fontSize: '13px',
            fontFamily: 'inherit',
          },
        },
        labels: {
          style: {
            colors: getCSSVariableValue('--bs-gray-500'),
            fontSize: '13px',
            fontFamily: 'inherit',
          },
        },
      },
      fill: {
        opacity: 1,
        colors: [getCSSVariableValue('--bs-warning')]
      },
      tooltip: {
        style: {
          fontSize: '12px',
          fontFamily: 'inherit'
        },
        x: {
          formatter: (val: any, { dataPointIndex }: any) => {
            // Get the actual project name from the original data
            const projectNames = this.cycleTimesChartData.categories;
            return projectNames[dataPointIndex] || `Project ${dataPointIndex + 1}`;
          }
        },
        y: {
          formatter: function (val: number) {
            return val + ' days';
          }
        }
      },
      grid: {
        borderColor: getCSSVariableValue('--bs-border-dashed-color'),
        strokeDashArray: 4,
        padding: {
          right: 20,
          bottom: 20,
        },
      },
    };

    console.log('Bar chart options:', this.barChartOptions);

    try {
      const chart = new ApexCharts(this.barChart.nativeElement, this.barChartOptions);
      chart.render();
      console.log('Bar chart rendered successfully');
    } catch (error) {
      console.error('Error rendering bar chart:', error);
    }
  }


  initializeProjectForUserBarChart(): void {
    // console.log('Initializing bar chart...');
    // console.log('Bar chart element:', this.barChartForUser?.nativeElement);
    // console.log('Cycle times data:', this.cycleTimesChartData);
    
    if (!this.barChartForUser?.nativeElement) {
      console.error('Bar chart element not found');
      return;
    }

    // Clear any existing chart
    if (this.barChartForUser.nativeElement.innerHTML) {
      this.barChartForUser.nativeElement.innerHTML = '';
    }

    // this.barChartOptionsforUser = {
    //   series: this.cycleTimesChartData.series,
    //   chart: {
    //     type: 'bar',
    //     height: '100%',
    //     fontFamily: 'inherit',
    //     toolbar: {
    //       show: false,
    //     },
    //     animations: {
    //       enabled: true,
    //       easing: 'easeinout',
    //       speed: 800,
    //     },
    //   },
    //   plotOptions: {
    //     bar: {
    //       horizontal: false,
    //       columnWidth: '55%',
    //       borderRadius: 4,
    //     }
    //   },
    //   dataLabels: {
    //     enabled: false
    //   },
    //   stroke: {
    //     show: true,
    //     width: 2,
    //     colors: ['transparent']
    //   },
    //   xaxis: {
    //     categories: this.cycleTimesChartData.categories.map((_, index) => `Project ${index + 1}`),
    //     axisBorder: {
    //       show: false,
    //     },
    //     axisTicks: {
    //       show: false,
    //     },
    //     labels: {
    //       show: false, // Hide individual project labels
    //     },
    //     title: {
    //       text: 'Projects',
    //       style: {
    //         color: getCSSVariableValue('--bs-gray-600'),
    //         fontSize: '13px',
    //         fontFamily: 'inherit',
    //         fontWeight: 600,
    //       },
    //     },
    //   },
    //   yaxis: {
    //     title: {
    //       text: 'Days',
    //       style: {
    //         color: getCSSVariableValue('--bs-gray-500'),
    //         fontSize: '13px',
    //         fontFamily: 'inherit',
    //       },
    //     },
    //     labels: {
    //       style: {
    //         colors: getCSSVariableValue('--bs-gray-500'),
    //         fontSize: '13px',
    //         fontFamily: 'inherit',
    //       },
    //     },
    //   },
    //   fill: {
    //     opacity: 1,
    //     colors: [getCSSVariableValue('--bs-warning')]
    //   },
    //   tooltip: {
    //     style: {
    //       fontSize: '12px',
    //       fontFamily: 'inherit'
    //     },
    //     x: {
    //       formatter: (val: any, { dataPointIndex }: any) => {
    //         // Get the actual project name from the original data
    //         const projectNames = this.cycleTimesChartData.categories;
    //         return projectNames[dataPointIndex] || `Project ${dataPointIndex + 1}`;
    //       }
    //     },
    //     y: {
    //       formatter: function (val: number) {
    //         return val + ' days';
    //       }
    //     }
    //   },
    //   grid: {
    //     borderColor: getCSSVariableValue('--bs-border-dashed-color'),
    //     strokeDashArray: 4,
    //     padding: {
    //       right: 20,
    //       bottom: 20,
    //     },
    //   },
    // };
// Suppose your API result is stored in this.data
const chartData = this.charDataForusers

this.barChartOptionsforUser = {
  series: [
    {
      name: 'Projects',
      data: chartData.map((item:any) => item.projectCount) // y-axis values
    }
  ],
  chart: {
    type: 'bar',
    height: '100%',
    fontFamily: 'inherit',
    toolbar: { show: false },
    animations: {
      enabled: true,
      easing: 'easeinout',
      speed: 800,
    },
  },
  plotOptions: {
    bar: {
      horizontal: false,
      columnWidth: '55%',
      borderRadius: 4,
    }
  },
  dataLabels: { enabled: false },
  stroke: {
    show: true,
    width: 2,
    colors: ['transparent']
  },
  xaxis: {
    categories: chartData.map((item:any) => item.internalPmName), // x-axis names
    axisBorder: { show: false },
    axisTicks: { show: false },
    labels: {
      show: true,
      style: {
        colors: getCSSVariableValue('--bs-gray-600'),
        fontSize: '13px',
        fontFamily: 'inherit',
        fontWeight: 600,
      },
    },
    title: {
      text: 'Internal Project Managers',
      style: {
        color: getCSSVariableValue('--bs-gray-600'),
        fontSize: '13px',
        fontFamily: 'inherit',
        fontWeight: 600,
      },
    },
  },
  yaxis: {
    title: {
      text: 'Project Count',
      style: {
        color: getCSSVariableValue('--bs-gray-500'),
        fontSize: '13px',
        fontFamily: 'inherit',
      },
    },
    labels: {
      style: {
        colors: getCSSVariableValue('--bs-gray-500'),
        fontSize: '13px',
        fontFamily: 'inherit',
      },
    },
  },
  fill: {
    opacity: 1,
    colors: [getCSSVariableValue('--bs-info')]
  },
  tooltip: {
    style: { fontSize: '12px', fontFamily: 'inherit' },
    x: {
      formatter: (val: string) => val // Show internalPmName directly
    },
    y: {
      formatter: (val: number) => `${val} projects`
    }
  },
  grid: {
    borderColor: getCSSVariableValue('--bs-border-dashed-color'),
    strokeDashArray: 4,
    padding: { right: 20, bottom: 20 },
  },
};

    console.log('Bar chart options:', this.barChartOptionsforUser);

    try {
      const chart = new ApexCharts(this.barChartForUser.nativeElement, this.barChartOptionsforUser);
      chart.render();
      console.log('Bar chart for users rendered successfully');
    } catch (error) {
      console.error('Error rendering bar chart:', error);
    }
  }
}
