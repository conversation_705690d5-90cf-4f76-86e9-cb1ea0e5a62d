// Common Toolbar Styles
// This file contains shared styles for toolbar buttons across all list pages

// Toolbar button spacing - ultra compact with slight overlap
.toolbar-btn {
  margin-right: -2px; // Slight overlap for maximum compactness
  padding: 0.25rem; // Reduced padding for smaller buttons
  
  &:last-child {
    margin-right: 0; // Remove negative margin from last button
  }

  // Ensure consistent icon sizing for toolbar buttons
  i {
    font-size: 14px !important;
    line-height: 1;
  }

  .svg-icon {
    font-size: 14px !important;
    
    svg {
      width: 14px !important;
      height: 14px !important;
    }
  }

  // Override any specific SVG icon classes
  .svg-icon-3 {
    font-size: 14px !important;
    
    svg {
      width: 14px !important;
      height: 14px !important;
    }
  }
}

// Add Button - Ensure green color shows by default
.toolbar-btn.btn-success {
  background-color: #28a745 !important;
  border-color: #28a745 !important;
  color: white !important;
  padding-top: 7px !important;
  padding-bottom: 7px !important;
  padding-left: 10px !important;
  padding-right: 10px !important;
  margin: 0 !important; // Remove margins
  min-width: auto !important; // Allow button to be as small as content
  height: auto !important; // Allow height to adjust to content
  gap: 4px !important; // Consistent spacing between icon and text
  
  // Ensure proper spacing for the plus icon
  i {
    margin-right: 0 !important; // Remove any default margin
  }
  
  &:hover {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
    color: white !important;
  }
  
  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5) !important;
  }
}

// Custom Dropdown Button
.custom-dropdown {
  .k-button {
    border-radius: 6px;
    font-weight: 500;
  }
}

// Icon-only buttons styling
.btn-icon {
  width: 32px;
  height: 32px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: none;

  &:hover {
    transform: none;
  }

  // Ensure consistent icon sizing
  i {
    font-size: 14px !important;
    line-height: 1;
  }

  .svg-icon {
    font-size: 14px !important;
    
    svg {
      width: 14px !important;
      height: 14px !important;
    }
  }
}

// General button styles - remove hover effects
.btn {
  border-radius: 6px;
  font-weight: 500;
  transition: none;

  &:hover {
    transform: none;
    box-shadow: none;
  }
}

// Excel dropdown z-index - much lower than loader
:host ::ng-deep .dropdown {
  z-index: 10 !important;
  position: relative !important;
}

:host ::ng-deep .excel-dropdown-menu {
  z-index: 10 !important;
  position: absolute !important;
}

/* Keep Kendo Grid z-index normal */
:host ::ng-deep .k-grid,
:host ::ng-deep .k-grid-header,
:host ::ng-deep .k-grid-header-wrap {
  z-index: auto !important;
}

// Kendo Grid Toolbar Button Consistency
:host ::ng-deep .k-grid-toolbar {
  .btn {
    // Ensure consistent icon sizing in Kendo Grid toolbar
    i {
      font-size: 14px !important;
      line-height: 1;
    }

    .svg-icon {
      font-size: 14px !important;
      
      svg {
        width: 14px !important;
        height: 14px !important;
      }
    }

    // Override any specific SVG icon classes
    .svg-icon-3 {
      font-size: 14px !important;
      
      svg {
        width: 14px !important;
        height: 14px !important;
      }
    }
  }
}

// Additional global overrides to ensure consistency
::ng-deep .k-grid-toolbar .btn-icon {
  i, .svg-icon {
    font-size: 14px !important;
    line-height: 1;
  }

  svg {
    width: 14px !important;
    height: 14px !important;
  }

  .svg-icon-3 {
    font-size: 14px !important;
    
    svg {
      width: 14px !important;
      height: 14px !important;
    }
  }
}

// Most specific overrides to ensure consistency across all pages
.k-grid-toolbar .btn-icon {
  i, .svg-icon {
    font-size: 14px !important;
    line-height: 1;
  }

  svg {
    width: 14px !important;
    height: 14px !important;
  }

  .svg-icon-3 {
    font-size: 14px !important;
    
    svg {
      width: 14px !important;
      height: 14px !important;
    }
  }
}

// Even more specific for toolbar buttons
.toolbar-btn.btn-icon {
  i, .svg-icon {
    font-size: 14px !important;
    line-height: 1;
  }

  svg {
    width: 14px !important;
    height: 14px !important;
  }

  .svg-icon-3 {
    font-size: 14px !important;
    
    svg {
      width: 14px !important;
      height: 14px !important;
    }
  }
}
