/* ========================================
   DROPDOWN STYLING OVERRIDES
   This file contains styles that override ng-select and Kendo themes
   ======================================== */

// Global ng-select styling with maximum specificity to override themes
::ng-deep .ng-select .ng-select-container {
  border: 1px solid #e9ecef !important;
  border-radius: 0.25rem !important;
  background-color: #fff !important;
  min-height: 31px !important;
  height: 31px !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  padding: 0.25rem 0.5rem !important;
  color: #495057 !important;
}

::ng-deep .ng-select .ng-select-container:hover {
  border-color: #adb5bd !important;
  background-color: #fff !important;
}

::ng-deep .ng-select .ng-select-container.ng-select-focused {
  border-color: #adb5bd !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
  background-color: #fff !important;
}

::ng-deep .ng-select .ng-placeholder {
  color: #6c757d !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
}

::ng-deep .ng-select .ng-value {
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  color: #495057 !important;
}

::ng-deep .ng-select .ng-value-label {
  color: #495057 !important;
  font-size: 0.875rem !important;
}

::ng-deep .ng-select .ng-arrow {
  border-color: #6c757d transparent transparent !important;
  border-width: 4px 4px 0 4px !important;
}

// ng-select dropdown panel styling
::ng-deep .ng-dropdown-panel {
  border: 1px solid #e9ecef !important;
  border-radius: 0.25rem !important;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
  background-color: #fff !important;
}

::ng-deep .ng-dropdown-panel .ng-option {
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  padding: 0.375rem 0.75rem !important;
  color: #495057 !important;
}

::ng-deep .ng-dropdown-panel .ng-option:hover {
  background-color: #f8f9fa !important;
  color: #495057 !important;
}

::ng-deep .ng-dropdown-panel .ng-option.ng-option-selected {
  background-color: #007bff !important;
  color: #fff !important;
}

::ng-deep .ng-dropdown-panel .ng-option.ng-option-highlighted {
  background-color: #e9ecef !important;
  color: #495057 !important;
}

// Global Kendo dropdownlist styling with maximum specificity
::ng-deep .k-dropdownlist {
  border: 1px solid #e9ecef !important;
  border-radius: 0.25rem !important;
  background-color: #fff !important;
  min-height: 31px !important;
  height: 31px !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  padding: 0.25rem 0.5rem !important;
  color: #495057 !important;
}

::ng-deep .k-dropdownlist:hover {
  border-color: #adb5bd !important;
  background-color: #fff !important;
}

::ng-deep .k-dropdownlist:focus {
  border-color: #adb5bd !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
  background-color: #fff !important;
}

::ng-deep .k-dropdownlist .k-input {
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  color: #495057 !important;
  background-color: transparent !important;
}

::ng-deep .k-dropdownlist .k-select {
  background-color: transparent !important;
}

// Kendo dropdown popup styling
::ng-deep .k-popup {
  border: 1px solid #e9ecef !important;
  border-radius: 0.25rem !important;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
  background-color: #fff !important;
}

::ng-deep .k-popup .k-list .k-item {
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  padding: 0.375rem 0.75rem !important;
  color: #495057 !important;
}

::ng-deep .k-popup .k-list .k-item:hover {
  background-color: #f8f9fa !important;
  color: #495057 !important;
}

::ng-deep .k-popup .k-list .k-item.k-state-selected {
  background-color: #007bff !important;
  color: #fff !important;
}

::ng-deep .k-popup .k-list .k-item.k-state-focused {
  background-color: #e9ecef !important;
  color: #495057 !important;
}
